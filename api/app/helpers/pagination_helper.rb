module PaginationHelper
  # Safely paginates an ActiveRecord collection with protection against incorrect inputs
  #
  # @param collection [ActiveRecord::Relation] The collection to paginate
  # @param options [Hash] Pagination options
  # @option options [String, Integer] :page The requested page number (sanitized to prevent injection)
  # @option options [Integer] :per_page Number of items per page (defaults to 12)
  #
  # @return [ActiveRecord::Relation] The paginated collection
  #
  # @example
  #   # In a controller:
  #   @articles = safe_paginate(Article.published, page: params[:page], per_page: 20)
  #
  # @note This method protects against invalid inputs by extracting only digits
  #       from the page parameter, preventing errors like "invalid value for Integer()"
  #       For example, "16'" would be treated as page 16
  def safe_paginate(collection, options = {})
    page = 1
    if options[:page].present?
      # Extract digits from the page parameter
      digits = options[:page].to_s.scan(/\d+/).join
      # Use the extracted digits if present, otherwise default to page 1
      page = digits.present? && digits.to_i != 0 ? digits : 1
    end
    
    per_page = options[:per_page] || 12

    total_records = begin
      count = collection.size
      count.is_a?(Hash) ? count.values.sum : count
    end

    # Use last page if page > total pages
    total_pages = (total_records / per_page.to_f).ceil
    page = [[page.to_i, total_pages].min, 1].max
    # Ensure there is at least 1 page - if collection is empty
    page = [page, 1].max

    collection.paginate(page: page, per_page: per_page)
  end
end
