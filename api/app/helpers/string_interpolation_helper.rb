module StringInterpolationHelper
  # Process interpolated data in a string, formatted as follows: `"<{type:arg0:arg1}>"`
  
  INTERPOLATION_MAP = {
    'school' => ->(*_args) { 'another user' },
    'spelling' => ->(pre_text = '', emphasis = '', post_text = '') do
      <<-HTML.html_safe
        <div style="margin: 4px 0px;">
          #{ERB::Util.html_escape(pre_text)}<strong>#{ERB::Util.html_escape(emphasis)}</strong>#{ERB::Util.html_escape(post_text)}
        </div>
      HTML
    end
  }.freeze

  def process_interpolation(string)
    return nil unless string.is_a?(String)

    string.split(/(<\{.*?\}>)/).map do |part|
      next part unless part.match?(/^<\{.*?\}>$/)
      
      type, *args = part[2..-3].split(':')
      handler = INTERPOLATION_MAP[type]
      
      handler ? handler.call(*args) : nil
    end.join.html_safe
  rescue StandardError => e
    Rails.logger.error("Error processing interpolation: #{e.message}")
    nil
  end
end
