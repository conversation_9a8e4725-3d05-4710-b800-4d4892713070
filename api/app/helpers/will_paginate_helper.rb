module WillPaginateHelper
    class HTMXLinkRenderer < WillPaginate::ActionView::LinkRenderer
        protected
        def link(text, target, attributes = {})
            if target.is_a?(Integer)
                attributes[:rel] = rel_value(target)
                target = url(target)
            end
            attributes["hx-get"] = target
            attributes["hx-trigger"] = "click"
            attributes["class"] = "cursor-pointer"
            tag(:a, text, attributes)
        end
    end

    def htmx_will_paginate(collection, options = {})
        will_paginate(collection, options.merge(:renderer => WillPaginateHelper::HTMXLinkRenderer))
    end
end