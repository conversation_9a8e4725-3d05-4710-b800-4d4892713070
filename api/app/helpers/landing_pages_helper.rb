module LandingPagesHel<PERSON>
  def landing_page_meta_tags(landing_page)
    content_for :title, landing_page.seo_title
    content_for :meta_description, landing_page.meta_description
    
    # Open Graph tags
    content_for :og_title, landing_page.title
    content_for :og_description, landing_page.meta_description
    content_for :og_type, "article"
  end
  
  def landing_page_breadcrumbs(landing_page)
    breadcrumbs = [
      { name: "Home", path: root_path },
      { name: landing_page.subject, path: "/resources?subject=#{landing_page.subject}" },
      { name: landing_page.title, path: nil }
    ]
    
    content_tag :nav, class: "breadcrumbs mb-4" do
      breadcrumbs.map.with_index do |crumb, index|
        if crumb[:path] && index < breadcrumbs.length - 1
          link_to crumb[:name], crumb[:path], class: "text-de-brand hover:underline"
        else
          content_tag :span, crumb[:name], class: "text-gray-400"
        end
      end.join(" > ").html_safe
    end
  end
  
  def landing_page_schema_data(landing_page)
    {
      "@context" => "https://schema.org",
      "@type" => "EducationalOccupationalProgram",
      "name" => landing_page.title,
      "description" => landing_page.meta_description,
      "provider" => {
        "@type" => "Organization",
        "name" => "Developing Experts",
        "url" => request.base_url
      },
      "educationalLevel" => landing_page.key_stage,
      "about" => {
        "@type" => "Thing",
        "name" => landing_page.subject
      },
      "audience" => {
        "@type" => "EducationalAudience",
        "educationalRole" => "teacher"
      },
      "dateModified" => landing_page.updated_at.iso8601,
      "occupationalCategory" => landing_page.career_categories
    }.to_json.html_safe
  end
  
  def resource_type_icon(resource_type)
    case resource_type
    when 'lesson_plans'
      "fa-solid fa-chalkboard-teacher"
    when 'worksheets'
      "fa-solid fa-file-text"
    when 'investigations'
      "fa-solid fa-search"
    when 'assessments'
      "fa-solid fa-clipboard-check"
    when 'careers'
      "fa-solid fa-briefcase"
    else
      "fa-solid fa-book"
    end
  end
  
  def key_stage_color(key_stage)
    case key_stage
    when 'EYFS'
      "bg-purple-500"
    when 'KS1'
      "bg-blue-500"
    when 'KS2'
      "bg-green-500"
    when 'KS3'
      "bg-orange-500"
    when 'KS4'
      "bg-red-500"
    else
      "bg-gray-500"
    end
  end
  
  def subject_color(subject)
    case subject.downcase
    when 'science'
      "bg-de-blue"
    when 'geography'
      "bg-de-pink"
    else
      "bg-de-brand"
    end
  end
end