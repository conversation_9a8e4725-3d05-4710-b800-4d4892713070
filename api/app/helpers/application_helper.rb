module ApplicationHelper
  def flip_order(current_order)
    current_order == 'asc' ? 'desc' : 'asc'
  end

  def sort_icon(column, current_sort, current_order)
    return unless column == current_sort

    icon_class = current_order == 'asc' ? 'fas fa-arrow-up' : 'fas fa-arrow-down'
    content_tag(:i, '', class: icon_class)
  end

  def checkmark(value)
    value ? '<i class="fas fa-check"></i>'.html_safe : ''
  end

  def get_audio_url(model, name)
    "https://www.developingexperts.com/file-cdn/files/get/#{model.audios.find_by(name: name)&.fileboy_audio_id}"
  end

  def get_audio_url_by_id(fileboy_audio_id)
    "https://www.developingexperts.com/file-cdn/files/get/#{fileboy_audio_id}"
  end

  def dev_login_as_button(user_type)
    return unless Rails.env.development?

    button_to "Login as #{user_type.capitalize}", dev_login_path(user_type: user_type), method: :post, class: 'admin-btn flex flex-1', form_class: 'flex'
  end

  def fileboy_cdn(id)
    "https://www.developingexperts.com/file-cdn/files/get/#{id}"
  end

  def ai_controllers
    ['career_builder']
  end

  def get_youtube_video_id(url)
    regex_pattern = %r{(?:embed/|live/|v=|youtu\.be/)([\w-]+)}
    match = url.match(regex_pattern)
    match ? match[1] : nil
  end

  # Helper method to determine badge color based on status
  def status_badge_color(status)
    case status
    when 'active'
      'bg-green-100 text-green-800'
    when 'past_due'
      'bg-yellow-100 text-yellow-800'
    when 'unpaid'
      'bg-orange-100 text-orange-800'
    when 'canceled'
      'bg-gray-100 text-gray-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def format_amount(amount, currency)
    return 'N/A' unless amount.present?

    amount_decimal = amount.to_f / 100.0
    case currency.to_s.downcase
    when 'gbp'
      "£#{format('%.2f', amount_decimal)}"
    when 'usd'
      "$#{format('%.2f', amount_decimal)}"
    when 'eur'
      "€#{format('%.2f', amount_decimal)}"
    else
      "#{format('%.2f', amount_decimal)} #{currency.to_s.upcase}"
    end
  end

  def lesson_subscription_path
    if @current_user.nil?
      accounts_new_path
    else
      subscription_path
    end
  end

  def lesson_subscription_prompt_text
    if @current_user.nil?
      'Sign up to access this content'
    else
      'Subscribe to access this content'
    end
  end

  def filtered_params(overrides = {})
    params.to_unsafe_h
          .reject { |k, v| %w[action controller page].include?(k) || v.blank? }
          .merge(overrides)
  end

  def universal_dashboard_link(user = @current_user)
    return '/' unless user
    if user.teacher?
      '/school/dashboard'
    elsif user.admin?
      '/admin'
    elsif user.pupil?
      '/pupil'
    else
      '/'
    end
  end

  def fileboy_image_url(fileboy_image_id, width: 400, height: '_', fit: 'cover')
    return nil unless fileboy_image_id
    transform = "resize:#{width}x#{height}~fit:#{fit};format:webp;quality:80"
    "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=#{transform}"
  end

  def page_title(suffix = nil, base = 'Developing Experts')
    title = content_for?(:title) ? content_for(:title).to_s : nil
    title_part = suffix ? "#{suffix} | #{base}" : base

    if title.present?
      safe_join([title, title_part], ' | ')
    else
      ErrorLog.create({
                        error: { errorData: 'Title not found', page: request.fullpath }
                      })
      title_part
    end
  end

  def preserved_params_for_sort(new_params = {})
    request.query_parameters.except('sort', 'order').merge(new_params)
  end

  def preserved_params_for_search
    request.query_parameters.except('search', 'page')
  end

  def sortable_link(column, title, path)
    css_class = column == params[:sort] ? "sorted #{params[:order]}" : nil

    link_to path.call(preserved_params_for_sort(sort: column, order: flip_order(params[:order]))),
            class: css_class do
      concat title
      concat sort_icon(column, params[:sort], params[:order])
    end
  end

  def report_media_options(source:, id:, reference:)
    {
      type: 'image',
      source: source,
      id: id,
      reference: reference
    }
  end

  def url_with_context(base_path, extra_params = {})
    context_params = params.permit(:from_context, :from_id).to_h
    all_params = context_params.merge(extra_params)

    return base_path if all_params.empty?

    separator = base_path.include?('?') ? '&' : '?'
    "#{base_path}#{separator}#{all_params.to_query}"
  end

  def set_seo_tags(url)
    content_for :hreflang_tags do
      tag.link rel: 'alternate', hreflang: 'en', href: url
      tag.link rel: 'canonical', href: url
    end
  end
end
