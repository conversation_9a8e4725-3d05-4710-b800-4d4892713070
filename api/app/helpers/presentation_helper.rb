module PresentationHelper
  ALLOWED_PARAMS = [:slide, :return_to]

  def search_query(merge_params=nil, omit_params=[])
    param_data = params.permit(PresentationHelper::ALLOWED_PARAMS).merge(merge_params || {}).reject! { |k, v| omit_params.include?(k.to_sym) }
    query = param_data.to_query
    if query.present?
      "?#{query}".html_safe
    else
      ""
    end
  end

  def exit_path
    params[:return_to] || '/accounts/dashboard'
  end
end
