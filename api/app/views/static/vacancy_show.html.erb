<% content_for :title, "#{@job_listing.title.strip()} | Career" %>

<div class="container mx-auto mt-8 mb-4 px-4">
  <div class="mb-2">
    <a href="/careers">
      <button class="grid gap-2 items-center px-4 py-2 rounded-lg duration-300 no-underline bg-transparent text-white hover:bg-blue-100/20" style="grid-template-columns: max-content 1fr;">
        <div class="rounded-full w-5 h-5 bg-blue-100 flex items-center justify-center p-2">
          <i class="fas fa-chevron-left text-black"></i>
        </div>
        <span class="text-white">Back to: <b>All careers</b></span>
      </button>
    </a>
  </div>
  <h1 class="text-3xl text-white mb-4"><%= @job_listing.title %></h1>
</div>

<div class="container mb-8 mx-auto p-4">
  <div class="bg-white shadow-sm rounded-xl flex flex-col gap-2">
    <div class="p-4 flex">
      <div class="prose max-w-none flex-1">
        <p><b>Salary: </b><%= @job_listing.salary %></p>
        <p><b>Duration: </b><%= @job_listing.length || "Not Specified" %></p>
        <%= sanitize @job_listing.body %>
      </div>
      <div class="flex-0">
        <div class="border-grey-900 border-t border-x">
          <div class="p-2">
            <p class="text-center">
              Closes
            </p>
          </div>
          <div class="p-2 bg-blue-500">
            <p class="text-bold text-center text-white">
              <%= @job_listing.end_date ? @job_listing.end_date.strftime("#{@job_listing.end_date.day.ordinalize} %b") : "" %>
              <%# "do MMM" %>
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="border-grey-900 border-b"></div>

    <div class="flex gap-4 p-4">
      <% if @pdf.present? %>
        <div>
          <a href="https://www.developingexperts.com/file-cdn/files/get/<%= @pdf[:id] %>/<%= @pdf[:name] %>?download">
            <button class="px-4 py-2 w-max rounded-md block text-center w-60 font-bold text-size-lg duration-300 no-underline bg-red-500 text-white hover:bg-red-400">
              <div class="grid gap-4 items-center" style="grid-template-columns: max-content 1fr;">
                <i class="fas fa-download"></i>
                PDF
              </div>
            </button>
          </a>
        </div>
      <% end %>

      <% if @application_form.present? %>
        <div>
          <a href="https://www.developingexperts.com/file-cdn/files/get/<%= @application_form[:id] %>/<%= @application_form[:name] %>?download">
            <button class="px-4 py-2 w-max rounded-md block text-center w-60 font-bold text-size-lg duration-300 no-underline bg-blue-500 text-white hover:bg-blue-400">
              <div class="grid gap-4 items-center" style="grid-template-columns: max-content 1fr;">
                <i class="fas fa-download"></i>
                Application
              </div>
            </button>
          </a>
        </div>
      <% end %>
    </div>
  </div>
</div>
