<% content_for :title, "#{@tour.name}" %>
<div id="tour-container" style="width: 100vw; height: 100vh; position: relative; overflow: hidden;">
  <div class="absolute w-full p-2 sm:p-4 top-0 z-50">
    <div class="backdrop-blur-lg bg-black/40 border border-white/10 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-xl">
      <div class="flex justify-between items-center gap-4">
        <div class="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
          <div class="min-w-0 flex-1">
            <div class="flex flex-col sm:flex-row sm:items-center sm:gap-2 flex-wrap">
              <h1 class="text-lg sm:text-2xl lg:text-3xl font-bold text-white truncate" style="text-shadow: 0 2px 8px rgba(0, 0, 0, 0.6);">
                <%= @tour.name %>
              </h1>
              <span class="text-white/60 text-lg sm:text-xl hidden sm:inline">•</span>
              <h3 data-scene-title="" class="text-sm sm:text-lg text-white/80 font-medium truncate"></h3>
            </div>
          </div>
          
          <!-- Desktop badge -->
          <div class="hidden sm:block">
            <div class="px-3 py-1.5 rounded-full bg-gradient-to-r from-purple-500/20 to-indigo-500/20 border border-purple-400/30">
              <span class="text-purple-200 text-sm font-medium">
                <i class="fas fa-globe mr-2"></i>360° Experience
              </span>
            </div>
          </div>
        </div>
        
        <div class="flex items-center gap-2 sm:gap-4 flex-shrink-0">
          <!-- Logo -->
          <div class="opacity-90 hover:opacity-100 transition-opacity">
            <img src="https://www.developingexperts.com/file-cdn/images/get/92ff4093-f09c-4ab0-9851-cf76204f019c+white_logo" alt="Developing Experts Logo" class="w-20 sm:w-32 lg:w-36"/>
          </div>
          
          <% unless params[:no_exit].present? %>
            <a href="<%= @return_to %>" class="w-9 h-9 sm:w-11 sm:h-11 rounded-full bg-white text-gray-800 hover:bg-gray-100 flex items-center justify-center transition-all duration-200 hover:scale-105 active:scale-95 shadow-lg">
              <i class="fas fa-times text-sm sm:text-lg"></i>
            </a>
          <% end %>
        </div>
      </div>
    </div>
  </div>
  <%# BOTTOM BAR %>
  <div class="absolute bottom-0 left-0 w-full p-2 sm:p-4 z-50">
    <div class="backdrop-blur-lg bg-black/40 border border-white/10 rounded-xl sm:rounded-2xl p-3 sm:p-4 shadow-xl">
      <div class="flex flex-col md:flex-row gap-3 md:gap-6">
        
        <!-- Primary Controls -->
        <div class="flex flex-wrap gap-2 sm:gap-3 items-center justify-center sm:justify-start">
          <button id="btn-zoom-in" class="w-10 h-10 sm:w-11 sm:h-11 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95 group" title="Zoom In">
            <i class="fas fa-search-plus group-hover:scale-110 transition-transform"></i>
          </button>

          <button id="btn-zoom-out" class="w-10 h-10 sm:w-11 sm:h-11 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95 group" title="Zoom Out">
            <i class="fas fa-search-minus group-hover:scale-110 transition-transform"></i>
          </button>

          <button id="btn-pan" data-tooltip="scene-pan-button" class="w-10 h-10 sm:w-11 sm:h-11 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95 group" title="Pan Controls">
            <i class="fas fa-arrows-alt group-hover:scale-110 transition-transform"></i>
          </button>
          
          <!-- Pan Tooltip -->
          <div id="scene-pan-button" style="visibility: hidden" class="absolute backdrop-blur-lg bg-white/10 border border-white/15 rounded-xl p-4 z-[100] shadow-2xl opacity-0 transition-all duration-300">
            <div class="text-center mb-3">
              <span class="text-white font-medium">Navigate View</span>
            </div>
            <div class="grid grid-cols-3 gap-2">
              <div></div>
              <button class="w-10 h-10 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95" data-pan="up"><i class="fas fa-chevron-up"></i></button>
              <div></div>
              <button class="w-10 h-10 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95" data-pan="left"><i class="fas fa-chevron-left"></i></button>
              <button class="w-10 h-10 rounded-xl flex items-center justify-center text-white bg-purple-500/30 border border-purple-400/50 backdrop-blur-sm hover:bg-purple-500/40 hover:border-purple-400/70 hover:scale-105 transition-all duration-200 ease-out active:scale-95" data-pan="center">
                <i class="fas fa-bullseye"></i>
              </button>
              <button class="w-10 h-10 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95" data-pan="right"><i class="fas fa-chevron-right"></i></button>
              <div></div>
              <button class="w-10 h-10 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95" data-pan="down"><i class="fas fa-chevron-down"></i></button>
              <div></div>
            </div>
          </div>

          <button id="btn-fullscreen" class="w-10 h-10 sm:w-11 sm:h-11 rounded-xl flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95 group" title="Fullscreen">
            <i class="fas fa-expand group-hover:scale-110 transition-transform"></i>
          </button>

          <!-- Mobile Scene Selector -->
          <button class="w-10 h-10 sm:w-11 sm:h-11 rounded-xl flex md:hidden items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95 group" data-tooltip="scene-selector-sm" title="Choose Scene">
            <i class="fa-solid fa-film group-hover:scale-110 transition-transform"></i>
          </button>
          
          <!-- Mobile Scene Selector Tooltip -->
          <div id="scene-selector-sm" style="visibility: hidden" class="absolute backdrop-blur-lg bg-white/10 border border-white/15 rounded-xl p-3 z-[100] shadow-2xl opacity-0 transition-all duration-300 max-h-64 overflow-y-auto min-w-48">
            <div class="mb-2">
              <h4 class="text-white font-medium text-sm">Scenes</h4>
            </div>
            <div class="space-y-1">
              <% @tour.data.keys.each do |key| %>
                <div id="<%= key %>" data-scene-selector="<%= key %>" class="w-full px-3 py-2 text-white/90 hover:text-white hover:bg-white/10 transition-all duration-200 cursor-pointer text-left text-sm rounded-lg">
                  <%= @tour.data.dig(key, 'name').presence || key %>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Separator -->
        <div class="hidden md:block w-px h-12 bg-white/20"></div>

        <!-- Video Controls -->
        <div class="flex rounded-xl overflow-hidden bg-white/10 border border-white/20 backdrop-blur-sm w-full md:w-auto" style="display: none">
          <button id="btn-view-video" class="flex-1 md:flex-none px-4 py-2.5 bg-transparent text-white hover:bg-white/10 transition-all duration-200 flex items-center justify-center md:justify-start gap-2 font-medium">
            <i class="fas fa-video"></i>
            <span>View Video</span>
          </button>
          <button id="btn-focus-video" data-modal="" class="w-11 h-11 flex items-center justify-center bg-white/10 hover:bg-white/20 transition-all duration-200 border-l border-white/20 text-white" title="Open in Modal">
            <i class="fas fa-expand"></i>
          </button>
        </div>

        <!-- Desktop Scene Selector -->
        <button class="px-3 py-2 h-10 sm:h-11 rounded-xl hidden md:flex items-center justify-center text-white bg-white/10 border border-white/20 backdrop-blur-sm hover:bg-white/20 hover:border-white/30 hover:scale-105 transition-all duration-200 ease-out active:scale-95 group" data-tooltip="scene-selector" title="Choose Scene">
          <i class="fa-solid fa-film group-hover:scale-110 transition-transform"></i>
          <span class="ml-2 text-sm font-medium">Change Scene</span>
        </button>
        
        <!-- Desktop Scene Selector Tooltip -->
        <div id="scene-selector" style="visibility: hidden" class="absolute backdrop-blur-lg bg-white/10 border border-white/15 rounded-xl p-3 z-[100] shadow-2xl opacity-0 transition-all duration-300 max-h-64 overflow-y-auto min-w-56">
          <div class="mb-2">
            <h4 class="text-white font-medium text-sm">Select Scene</h4>
          </div>
          <div class="space-y-1">
            <% @tour.data.keys.each do |key| %>
              <div id="<%= key %>" data-scene-selector="<%= key %>" class="w-full px-3 py-2 text-white/90 hover:text-white hover:bg-white/10 transition-all duration-200 cursor-pointer text-left text-sm rounded-lg">
                <%= @tour.data.dig(key, 'name').presence || key %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<dialog class="max-w-4xl p-8 w-full relative" data-tour-dialog="">
  <div class="absolute top-1 right-1">
    <button class="icon-btn" onclick="this.closest('dialog').close()">
      <i class="fas fa-times"></i>
    </button>
  </div>
  <div id="dialog-data" class="w-full h-full"></div>
</dialog>

<script type="module">
  import {
    computePosition,
    offset,
    shift,
  } from "https://cdn.jsdelivr.net/npm/@floating-ui/dom@latest/+esm"
  import { Viewer } from "@photo-sphere-viewer/core"
  import { MarkersPlugin } from "@photo-sphere-viewer/markers-plugin"

  const urlParams = new URLSearchParams(window.location.search)
  const autoFocusVideoId = urlParams.get("auto_focus_video_id")
  const tour = JSON.parse(`<%= @tour.to_json.html_safe %>`)
  const defaultSceneKey = "<%= @default_scene_key %>"
  const scenes = Object.keys(tour.data)
  let currentScene = null

  function sceneFromAutoFocusVideoId(autoFocusVideoId) {
    const dataSet = Object.entries(tour?.data ?? {})
    // [string, IScene["video"]] | undefined
    for (const [scene, data] of dataSet) {
      const videoIds = [data.video?.videoRecordId, data.video?.videoId].filter(Boolean).map(x => x.toString())
      if (videoIds.includes(autoFocusVideoId)) {
        console.log("AUTOFOCUS MATCH", [scene, data.video])
        return [scene, data.video]
      } else {
        const match = data.additionalVideos?.find((video) => {
          const videoIds = [video?.videoRecordId, video?.videoId].filter(Boolean).map(x => x.toString())
          return videoIds.includes(autoFocusVideoId)
        })
        if (match) {
          console.log("AUTOFOCUS MATCH", [scene, match])
          return [scene, match]
        }
      }
    }
    return null
  }

  function sortScenesByWeight(scenes) {
    return scenes.sort(([_, dataA], [__, dataB]) => {
      if (dataA.weight && dataB.weight) {
        return dataA.weight - dataB.weight
      } else if (!dataA.weight && !dataB.weight) {
        return 0
      } else if (dataA.weight && !dataB.weight) {
        return 1
      }
      return -1
    })
  }

  function getDefaultSceneKey() {
    if (autoFocusVideoId) {
      console.log("LOADED", "with autofocus", autoFocusVideoId)
      const sceneKey = sceneFromAutoFocusVideoId(autoFocusVideoId)
      if (sceneKey?.[0]) {
        return sceneKey[0]
      }
    }
    if (defaultSceneKey && Object.keys(tour).includes(defaultSceneKey)) {
      return defaultSceneKey
    }
    return sortScenesByWeight(Object.entries(tour.data))?.[0]?.[0] ?? Object.keys(tour.data)[0]
  }
  function getDefaultScene() {
    return tour.data[getDefaultSceneKey()]
  }

  function handleZoom(dir) {
    if (dir > 0) {
      window.viewer.zoomIn()
    } else {
      window.viewer.zoomOut()
    }
  }

  function handleFullScreen() {
    window.viewer.toggleFullscreen()
  }

  function centerOfCorners(corners) {
    const x = (corners.topRight.x + corners.topLeft.x) / 2
    const y = (corners.topLeft.y + corners.bottomLeft.y) / 2
    return { x, y }
  }

  function handleViewPrimaryVideo() {
    handlePanToResource(currentScene.video)
  }
  function handleFocusVideo() {
    openVideoDialog(currentScene.video)
  }

  function handlePanToResource(item) {
    console.log("Panning to item", item)
    const center = item.autoCenter ? centerOfCorners(item.corners) : { x: item.x, y: item.y }
    const latLng = window.viewer.dataHelper.textureCoordsToSphericalCoords(
      coordToTextureCoord(center),
    )
    return window.viewer.animate({
      ...latLng,
      zoom: item.zoom ?? 100,
      speed: 1000,
    })
  }

  let panToolTip
  function handlePan(e) {
    const button = e.currentTarget
    const direction = button.getAttribute("data-pan")
    const currentPos = window.viewer.getPosition()
    const speed = 0.2
    const delta = {
      yaw: direction === "left" ? -speed : direction === "right" ? speed : 0,
      pitch: direction === "up" ? speed : direction === "down" ? -speed : 0,
    }
    console.log("PANNING", direction, currentPos, delta)

    if (direction === "center") {
      window.viewer.animate({
        yaw: currentScene.defaultView?.long,
        pitch: currentScene.defaultView?.lat,
        duration: 0.3,
        easing: "easeOutQuart",
      })
      return
    }
    window.viewer.animate({
      yaw: currentPos.yaw + delta.yaw,
      pitch: currentPos.pitch + delta.pitch,
      duration: 0.3,
      easing: "easeOutQuart",
    })
  }

  function onPanClose() {
    if (panToolTip) {
      panToolTip.hide()
      panToolTip = null
    }
  }

  function onSceneLoaded(scene) {
    // stop any playing videos
    const videoNodes = document.querySelector("#tour-container").querySelectorAll("video")
    setTimeout(() => {
      videoNodes.forEach((video) => {
        video.pause()
      })
    }, 1000)

    // add primary video
    console.log("SCENE CHANGE", scene)
    if (scene.video) {
      addVideoToScene("primary-video", scene.video)
    }

    if (scene.additionalVideos && scene.additionalVideos.length > 0) {
      scene.additionalVideos.forEach((video, index) => {
        addVideoToScene(`additional-video-${index}`, video)
      })
    }

    fb.reloadVideoPlayers() // reload fileboy video players on slide change to ensure they are mounting
  }

  function handleChangeScene(scene, sceneKey, afterSceneChanged = (_) => {}) {
    // delete any existing markers & warpedRects
    const plugin = window.viewer.getPlugin(MarkersPlugin)
    const markerIds = Object.keys(plugin.markers)
    if (markerIds.length > 0) {
      markerIds.forEach(removeMarker)
    }

    window.viewer
      .setPanorama(scene.panorama, {
        position: {
          yaw: scene.defaultView?.long,
          pitch: scene.defaultView?.lat,
        },
        zoom: scene.defaultView?.zoom ?? 20,
        transition: {
          rotation: false,
          effect: "fade",
        }
      })
      .then(() => {
        currentScene = scene
        // Run actions that need to be manually done after each scene change like reloading video players
        onSceneLoaded(scene)
        // Optional passed in FN - used onReady to autofocus video player if set.
        afterSceneChanged(scene)
      })
    document.querySelector("[data-scene-title]").innerText = scene.title || sceneKey

    if (scene.video && scene.video) {
      document.querySelector("#btn-view-video").parentElement.style.display = "flex"
    } else {
      document.querySelector("#btn-view-video").parentElement.style.display = "none"
    }
  }

  function addMarker(id, position) {
    const plugin = viewer.getPlugin(MarkersPlugin)

    const properties = {
      id: id,
      html: `<div style="transform: translate(-50%, -50%);" />`,
      position: coordToTextureCoord(position),
    }
    if (id in plugin.markers) {
      plugin.updateMarker(properties)
    } else {
      plugin.addMarker(properties)
    }
    return document.querySelector(`#psv-marker-${id}`)
  }

  function syncWarpedRect(id, { width, height, transform, isUpsideDown }) {
    const childNode = document.querySelector(`#${id}`)

    if (!childNode || isUpsideDown) {
      return
    }

    childNode.style.transform = transform
    childNode.style.position = "absolute"
    childNode.style.top = "0px"
    childNode.style.left = "0px"
    // childNode.style.height = height + "px"
    childNode.style.width = width + "px"
    childNode.style.zIndex = "20"
  }

  function coordToTextureCoord(coord) {
    return {
      textureX: coord.x,
      textureY: coord.y,
    }
  }

  function buildWarpedRect(id, corners, size) {
    const { width, height } = size
    const plugin = window.viewer.getPlugin(MarkersPlugin)
    console.log(corners)
    const topLeftSphere = window.viewer.dataHelper.textureCoordsToSphericalCoords(
      coordToTextureCoord(corners.topLeft),
    )
    const topLeftVector = window.viewer.dataHelper.sphericalCoordsToVector3(topLeftSphere)

    const topRightSphere = window.viewer.dataHelper.textureCoordsToSphericalCoords(
      coordToTextureCoord(corners.topRight),
    )
    const topRightVector = window.viewer.dataHelper.sphericalCoordsToVector3(topRightSphere)

    const botLeftSphere = window.viewer.dataHelper.textureCoordsToSphericalCoords(
      coordToTextureCoord(corners.bottomLeft),
    )
    const botLeftVector = window.viewer.dataHelper.sphericalCoordsToVector3(botLeftSphere)

    const botRightSphere = window.viewer.dataHelper.textureCoordsToSphericalCoords(
      coordToTextureCoord(corners.bottomRight),
    )
    const botRightVector = window.viewer.dataHelper.sphericalCoordsToVector3(botRightSphere)

    const topLeftViewer = window.viewer.dataHelper.vector3ToViewerCoords(topLeftVector)
    const topRightViewer = window.viewer.dataHelper.vector3ToViewerCoords(topRightVector)
    const botLeftViewer = window.viewer.dataHelper.vector3ToViewerCoords(botLeftVector)
    const botRightViewer = window.viewer.dataHelper.vector3ToViewerCoords(botRightVector)

    const distort = new Distort({ width, height })

    distort.topLeft.x = topLeftViewer.x
    distort.topLeft.y = topLeftViewer.y

    distort.topRight.x = topRightViewer.x
    distort.topRight.y = topRightViewer.y

    distort.bottomLeft.x = botLeftViewer.x
    distort.bottomLeft.y = botLeftViewer.y

    distort.bottomRight.x = botRightViewer.x
    distort.bottomRight.y = botRightViewer.y

    plugin.addMarker({ id, position: coordToTextureCoord({ x: 0, y: 0 }), html: `<div></div>` })

    function update() {
      const topLeftViewer = window.viewer.dataHelper.vector3ToViewerCoords(topLeftVector)
      const topRightViewer = window.viewer.dataHelper.vector3ToViewerCoords(topRightVector)
      const botLeftViewer = window.viewer.dataHelper.vector3ToViewerCoords(botLeftVector)
      const botRightViewer = window.viewer.dataHelper.vector3ToViewerCoords(botRightVector)

      distort.topLeft.x = topLeftViewer.x
      distort.topLeft.y = topLeftViewer.y

      distort.topRight.x = topRightViewer.x
      distort.topRight.y = topRightViewer.y

      distort.bottomLeft.x = botLeftViewer.x
      distort.bottomLeft.y = botLeftViewer.y

      distort.bottomRight.x = botRightViewer.x
      distort.bottomRight.y = botRightViewer.y

      const isUpsideDown = topLeftViewer.y > botLeftViewer.y

      syncWarpedRect(id, { transform: distort.toString(), width, height, isUpsideDown })
    }

    const tick = () => {
      update()
      requestAnimationFrame(tick)
    }
    tick()
  }

  function addVideoToScene(id, video) {
    const autoPlay = video.videoId == autoFocusVideoId || video.videoRecordId === autoFocusVideoId
    console.log("ADDING VIDEO", id, video, autoPlay)

    const container = document.createElement("div")
    container.id = id

    const videoPopOut = "/assets/videoPopOut.png"
    let videoNode = ""
    if (video.videoRecordId) {
      const params = new URLSearchParams({ autoPlay: autoPlay ? "1" : "0" })
      const url = `/video/${video.videoRecordId}?${params.toString()}`
      videoNode = `
            <div data-video="${video.videoRecordId}" hx-trigger="load" hx-get=${url} hx-swap="outerHtml" hx-target="this" data-video-id="${video.videoId}" data-video-record-id="${video.videoRecordId}">
                <div style="width: 100%; min-height: 100%; display: flex; align-items: center; justify-content: center;">
                    <div>
                    <H3 blockSpacing={0} color="white" align="center">
                        Loading...
                    </H3>
                    </div>
                </div>
            </div>
        `
    } else {
      videoNode = `
      <div class="video-player" id="video-play-${video.videoId}" data-video-id="${video.videoId}" data-video-record-id="${video.videoRecordId}">
      <div data-fileboy-video="height=100% ${autoPlay ? "autoPlay=true" : ""}" id="${
        video.videoId
      }"></div></div>`
    }
    container.onclick = () => handlePanToResource(video)
    container.innerHTML = `
        <div style="height: 100%;">
            <div style="position: relative; height:100%; padding-bottom: 56.25%; ${
              video.wrapWithToolTip ? "" : "overflow: hidden;"
            }">
                <div style="position: absolute; width: 100%; height: 140%; background-position: center; background-repeat: no-repeat; background-size: contain; background-image: url(${videoPopOut});" />
                <div style="position: absolute; width: 1000px; height: 512px">
                <div style="${
                  video.wrapWithToolTip
                    ? "padding: 16px 36px; padding-top: 4%; height: 102.8%;"
                    : ""
                }">
                    <div style=${
                      video.wrapWithToolTip
                        ? "border-radius: 16px; overflow: hidden; height: 100%;"
                        : ""
                    }>
                        ${videoNode}
                    </div>
                </div>
                </div>
            </div>
        </div>
      `
    document.querySelector("#tour-container").appendChild(container)
    buildWarpedRect(id, video.corners, { width: 1000, height: 562 })
    console.log("Added video", id, video, container)

    const markerId = `${id}-marker`
    const marker = addMarker(markerId, video.button)
    console.log("MARKER", markerId, video.button, marker)
    marker.innerHTML = `<button class="icon-btn-sm"><i class="fas fa-video"></i></button>`
    marker.querySelector("button").addEventListener("click", () => {
      handlePanToResource(video)
    })
    setTimeout(() => {
      htmx.process(container)
    }, 200)
  }

  function openVideoDialog(video) {
    const autoPlay = false
    const videoPopOut = "/assets/videoPopOut.png"
    let videoNode = ""
    if (video.videoRecordId) {
      const params = new URLSearchParams({ autoPlay: autoPlay ? "1" : "0" })
      const url = `/video/${video.videoRecordId}?${params.toString()}`
      videoNode = `
            <div data-video="" hx-trigger="load" hx-get=${url} hx-swap="outerHtml" hx-target="this">
                <div style="width: 100%; min-height: 100%; display: flex; align-items: center; justify-content: center;">
                    <div>
                    <H3 blockSpacing={0} color="white" align="center">
                        Loading...
                    </H3>
                    </div>
                </div>
            </div>
        `
    } else {
      videoNode = `
      <div class="video-player" id="video-play-${video.videoId}">
      <div data-fileboy-video="height=100% ${autoPlay ? "autoPlay=true" : ""}" id="${
        video.videoId
      }"></div></div>`
    }
    const dialog = document.querySelector("[data-tour-dialog]")
    const dataNode = document.querySelector("#dialog-data")
    dataNode.innerHTML = videoNode
    dialog.showModal()

    setTimeout(() => {
      console.log("HTMX PROCESSING", dataNode)
      htmx.process(dataNode)
    }, 100)

    setTimeout(() => {
      fb.reloadVideoPlayers()
    }, 200)
  }

  function removeMarker(id) {
    const plugin = viewer.getPlugin(MarkersPlugin)
    try {
      plugin.removeMarker(id)
    } catch (e) {
      console.error(e)
    }
    document.querySelector(`#${id}`)?.remove()
  }

  document.addEventListener("DOMContentLoaded", () => {
    buildPSV()
    // zoom
    document.querySelector("#btn-zoom-in").addEventListener("click", () => handleZoom(1))
    document.querySelector("#btn-zoom-out").addEventListener("click", () => handleZoom(-1))

    // pan
    document.querySelectorAll("[data-pan]").forEach((button) => {
      button.addEventListener("click", handlePan)
    })

    // fullscreen
    document.querySelector("#btn-fullscreen").addEventListener("click", handleFullScreen)

    // turn view to face video
    document.querySelector("#btn-view-video").addEventListener("click", handleViewPrimaryVideo)

    // show video in modal
    document.querySelector("#btn-focus-video").addEventListener("click", handleFocusVideo)

    document.querySelectorAll("[data-scene-selector]").forEach((button) => {
      button.addEventListener("click", function () {
        const sceneKey = button.getAttribute("data-scene-selector")
        handleChangeScene(tour.data[sceneKey], sceneKey)
      })
    })
  })

  function onReady() {
    initializeTooltips()
    handleChangeScene(getDefaultScene(), getDefaultSceneKey(), () => {
      setTimeout(() => {
        if (autoFocusVideoId) {
          const match = sceneFromAutoFocusVideoId(autoFocusVideoId)
          if (match) {
            handlePanToResource(match[1])
          }
        }
      }, 100)
    })
  }

  // Function to initialize tooltips
  function initializeTooltips() {
    document.querySelectorAll("[data-tooltip]").forEach((button) => {
      let isOpen = false
      const tooltip = document.getElementById(button.getAttribute("data-tooltip"))
      console.log("TOOLTIP", tooltip)
      tooltip.classList.add("tour-tooltip")
      tooltip.style.visibility = "hidden" // Ensure tooltip starts hidden
      tooltip.style.opacity = "0" // Ensure tooltip starts fully transparent

      function handleOpen() {
        computePosition(button, tooltip, {
          placement: "top",
          middleware: [offset(10), shift({ padding: 5 })],
        }).then(({ x, y }) => {
          tooltip.style.left = `${x}px`
          tooltip.style.top = `${y}px`
          tooltip.style.visibility = "visible"
          tooltip.style.opacity = "1" // Fade in
        })
        isOpen = true
      }
      function handleClose() {
        tooltip.style.visibility = "hidden"
        tooltip.style.opacity = "0" // Fade out
        isOpen = false
      }

      document.addEventListener("click", (event) => {
        const clickedOutside = !button.contains(event.target) && !tooltip.contains(event.target)
        if (clickedOutside) {
          handleClose()
        }
      })

      button.addEventListener("click", function (event) {
        if (isOpen) {
          handleClose()
        } else {
          handleOpen()
        }
      })

      //   button.addEventListener("mouseout", handleClose)
    })
  }

  function buildPSV() {
    const defaultScene = getDefaultScene()
    console.log("Building with", defaultScene)
    const image = defaultScene.panorama
    const viewer =
      window.viewer ||
      new Viewer({
        container: "tour-container",
        panorama: image,
        loadingImg: "https://photo-sphere-viewer-data.netlify.app/assets/loader.gif",
        touchmoveTwoFingers: false,
        mousewheelCtrlKey: false,
        mousewheel: true,
        navbar: null,
        plugins: [MarkersPlugin],
      })
    const container = document.getElementById("tour-container")
    viewer.enterFullscreen = () => {
      if (!container) {
        return
      }
      if (document.fullscreenElement) {
        viewer.exitFullscreen()
      } else {
        container.requestFullscreen()
        viewer.autoSize()
        viewer.eventsHandler.__fullscreenToggled(true)
      }
    }
    viewer.exitFullscreen = () => {
      document.exitFullscreen()
      viewer.autoSize()
      viewer.eventsHandler.__fullscreenToggled(false)
    }

    viewer.addEventListener("ready", () => {
      setTimeout(() => {
        viewer.autoSize()
        onReady()
      }, 100)
    })

    window.viewer = viewer
    return window.viewer
  }
  console.log("LOADED", tour)
</script>
