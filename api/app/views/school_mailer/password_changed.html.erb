<!-- app/views/school_mailer/password_changed.html.erb -->
<% content_for :title do %>
  Password Changed - Developing Experts
<% end %>
<div style="text-align: center; margin-bottom: 30px;">
  <h1 style="color: #333333; margin: 0; font-size: 24px;">Password Changed</h1>
</div>
<div style="background-color: #e7f3ff; border: 1px solid #b3d7ff; color: #0c5460; padding: 15px; border-radius: 5px; margin-bottom: 25px;">
  <p style="margin: 0;"><strong>Your password has been changed by an administrator.</strong></p>
</div>
<p style="color: #333333; line-height: 1.6; margin-bottom: 20px;">
  Dear <%= @teacher.name %>,
</p>
<p style="color: #333333; line-height: 1.6; margin-bottom: 20px;">
  Your password for your Developing Experts account has been changed by an administrator.
</p>
<div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; margin: 25px 0;">
  <p style="margin: 0 0 10px 0; font-weight: bold; color: #333333;">Your new password is:</p>
  <p style="font-family: monospace; font-size: 16px; background-color: #ffffff; padding: 10px; border: 1px solid #ccc; border-radius: 3px; margin: 0; color: #333333;">
    <%= @new_password %>
  </p>
</div>
<p style="color: #333333; line-height: 1.6; margin-bottom: 20px;">
  Please log in to your account and consider changing your password to something memorable once you're logged in.
</p>
<div style="text-align: center; margin: 30px 0;">
  <a href="<%= ENV['APP_URL'] || 'https://www.developingexperts.com' %>/accounts/sign-in" 
         style="display: inline-block; padding: 12px 30px; background-color: #556cd6; color: #ffffff; text-decoration: none; border-radius: 5px; font-weight: bold;">
    Log In Now
  </a>
</div>
<hr style="border: none; border-top: 1px solid #eeeeee; margin: 30px 0;">
<p style="color: #666666; font-size: 12px; line-height: 1.4; margin: 0;">
  If you have any questions or concerns, please contact your school administrator or our support team.
</p>
<p style="color: #666666; font-size: 12px; line-height: 1.4; margin: 10px 0 0 0;">
  This is an automated message from Developing Experts.
</p>