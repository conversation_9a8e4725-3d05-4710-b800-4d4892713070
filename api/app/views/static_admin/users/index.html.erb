<!-- app/views/admin/users/index.html.erb -->
<% content_for :title, "Users" %>
<div class="users-dashboard">
  <!-- Header -->
  <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-2xl font-bold text-white">All Users</h1>
      <p class="text-gray-200">Manage user accounts</p>
    </div>
  </div>

  <!-- User Type Tabs -->
  <div class="border-b border-gray-200 mb-6">
    <nav class="-mb-px flex space-x-8 overflow-x-auto" aria-label="User Types">
      <% ["", "Admin", "IndividualUser", "Pupil", "Teacher"].each do |type| %>
        <% label = type.blank? ? 'All Users' : type %>
        <% badge_color = 'bg-gray-100 text-gray-900' %>
        <%= link_to admin_users_path(filtered_params(type: type)), class: "#{params[:type].to_s == type ? 'border-de-brand text-de-brand' : 'border-transparent text-gray-300 hover:text-gray-500 hover:border-gray-300'} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" do %>
          <%= label %>
        <% end %>
      <% end %>
    </nav>
  </div>

  <!-- Main Content -->
  <div class="flex gap-4 flex-col">
    <!-- Filters Panel -->
    <div class="flex-shrink-0">
      <div class="bg-white shadow rounded-lg sticky top-4">
        <div class="px-4 py-5 border-b border-gray-200">
          <h3 class="text-lg font-medium leading-6 text-gray-900">Filter Users</h3>
        </div>
        <%= form_with(url: admin_users_path, method: :get, local: true, class: "px-4 py-5 flex gap-4 flex-col xl:flex-row") do %>
          <%= hidden_field_tag :type, params[:type] %>
          <%= hidden_field_tag :order, params[:order] %>
          <%= hidden_field_tag :sort, params[:sort] %>
          <div class="flex flex-col md:flex-row gap-4 flex-1">
            <div class="flex-1">
              <%= label_tag :query, 'Search by Name/Email', class: "block text-sm font-medium text-gray-700" %>
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <%= text_field_tag :query, params[:query], placeholder: "Search...", class: "block w-full !pl-10 border-gray-300 rounded-md py-2 text-sm" %>
              </div>
            </div>

            <div class="flex-1">
              <%= label_tag :subscription_type, 'Subscription Type', class: "block text-sm font-medium text-gray-700" %>
              <%= select_tag :subscription_type, 
                  options_for_select([
                    ['Select...', nil], 
                    ["AI", 'ai'], 
                    ["Science", 'science'], 
                    ["Geography", 'geography']
                  ], params[:subscription_type]), 
                  class: "mt-1 block w-full pl-3 pr-10 py-2 border-gray-300 rounded-md text-sm" %>
            </div>

            <div class="flex-1">
              <%= label_tag :school, 'Search by School Name', class: "block text-sm font-medium text-gray-700" %>
              <%= text_field_tag :school, params[:school], placeholder: "Search...", class: "mt-1 block w-full pl-3 pr-10 py-2 border-gray-300 rounded-md text-sm" %>
            </div>
          </div>
          <div class="flex flex-col sm:flex-row gap-2 pt-4 justify-end">
              <%= render ButtonComponent::Submit.new(text: "Search") %>
              <%= render ButtonComponent::Base.new(
                url: admin_users_path(type: params[:type]),
                variant: :secondary,
                text: "Reset Filters"
              ) %>
            </div>
        <% end %>
      </div>
    </div>

    <!-- Users Table -->
    <div class="flex-1">
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 border-b border-gray-200 sm:px-6 flex justify-between items-center">
          <h3 class="text-lg font-medium leading-6 text-gray-900">User List</h3>
          <div class="flex items-center space-x-4">
            <%= link_to new_admin_user_path, class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-de-brand hover:bg-de-brand-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
              <i class="fa-solid fa-plus mr-2"></i>
              Add User
            <% end %>
            <div class="text-sm text-gray-500">
              <%= render AdminPaginateComponent.new(items: @users) %>
            </div>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="admin-table-header">
                  <%= sortable_link('name', 'Name'.strip, ->(params) { admin_users_path(params) }) %>

                </th>
                <th scope="col" class="admin-table-header">
                  <%= sortable_link('email', 'Email'.strip, ->(params) { admin_users_path(params) }) %>

                </th>
                <th scope="col" class="admin-table-header">Subscription Types</th>
                <th scope="col" class="admin-table-header">
                  <%= sortable_link('created_at', 'Created At'.strip, ->(params) { admin_users_path(params) }) %>

                </th>
                <th scope="col" class="admin-table-header">
                  <%= sortable_link('last_sign_in_at', 'Last Activity'.strip, ->(params) { admin_users_path(params) }) %>

                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <% if @users.empty? %>
                <tr>
                  <td colspan="9" class="px-6 py-8 text-center text-sm text-gray-500">
                    No users found matching your criteria.
                  </td>
                </tr>
              <% else %>
                <% @users.each do |user| %>
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4">
                      <%= render AvatarComponent.new(text: user&.name || "Name not found", include_text: true, sub_text: user.school&.name.present? ? user.school.name : nil, link: edit_admin_user_path(id: user.id), tag: user[:type].titleize) %>
                    </td>
                    <td class="px-6 py-4">
                      <div class="text-sm text-gray-900"><%= user.email %></div>
                    </td>
                    <td class="px-6 py-4">
                      <% if user.subscribed_to_service?(:ai) %>
                        <span class="admin-badge bg-blue-100 text-blue-800">AI</span>
                      <% end %>
                      <% if user.subscribed_to_service?(:science) %>
                        <span class="admin-badge bg-green-100 text-green-800">Science</span>
                      <% end %>
                      <% if user.subscribed_to_service?(:geography) %>
                        <span class="admin-badge bg-yellow-100 text-yellow-800">Geography</span>
                      <% end %>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                      <%= user.created_at.strftime("%b %d, %Y") %>
                      <div class="text-xs text-gray-400">
                        <%= time_ago_in_words(user.created_at) %> ago
                      </div>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500">
                      <% if user.last_sign_in_at.present? %>
                        <%= user.last_sign_in_at.strftime("%b %d, %Y") %>
                        <div class="text-xs text-gray-400">
                          <%= time_ago_in_words(user.last_sign_in_at) %> ago
                        </div>
                      <% else %>
                        <span class="text-xs">Never</span>
                      <% end %>
                    </td>
                  </tr>
                <% end %>
              <% end %>
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <% if @users.total_pages > 1 %>
          <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing <span class="font-medium"><%= @users.offset + 1 %></span> to
                  <span class="font-medium"><%= [@users.offset + @users.per_page, @users.total_entries].min %></span> of
                  <span class="font-medium"><%= @users.total_entries %></span> results
                </p>
              </div>
              <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <%= will_paginate @users, 
                      renderer: WillPaginate::ActionView::LinkRenderer,
                      previous_label: '&laquo;',
                      next_label: '&raquo;',
                      inner_window: 2,
                      outer_window: 0,
                      class: 'pagination' %>
                </nav>
              </div>
            </div>
            <div class="flex sm:hidden">
              <div class="flex justify-between flex-1">
                <% if @users.previous_page %>
                  <%= link_to admin_users_path(page: @users.previous_page),
                          class: "relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
                    Previous
                  <% end %>
                <% else %>
                  <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100">
                    Previous
                  </span>
                <% end %>
                <% if @users.next_page %>
                  <%= link_to admin_users_path(page: @users.next_page),
                          class: "ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" do %>
                    Next
                  <% end %>
                <% else %>
                  <span class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-500 bg-gray-100">
                    Next
                  </span>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>