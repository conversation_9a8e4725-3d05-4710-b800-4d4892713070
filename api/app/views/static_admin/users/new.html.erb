<%= javascript_include_tag 'scripts/password-guidance' %>
<%= javascript_include_tag "nice-select", type: "module" %>

<div class="max-w-4xl mx-auto">
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium leading-6 text-gray-900">Create New User</h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Add a new user to the system. Different fields will be shown based on the user type selected.
          </p>
        </div>
        <%= link_to admin_users_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" do %>
          <i class="fa-solid fa-arrow-left mr-2"></i>
          Back to Users
        <% end %>
      </div>
    </div>

    <div class="px-4 py-5 sm:p-6">
      <%= render "shared/form_errors", model: @user %>
      
      <%= form_with model: @user, url: admin_users_path, local: true, multipart: true, autocomplete: "none", id: "user-form", scope: :user do |form| %>
        <div class="space-y-6">
          <!-- User Type Selection -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-3">User Type</h4>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
              <% ['Admin', 'Teacher', 'IndividualUser', 'Pupil'].each do |user_type| %>
                <label class="relative flex cursor-pointer rounded-lg border bg-white p-4 shadow-sm focus:outline-none user-type-option" data-type="<%= user_type %>">
                  <%= form.radio_button :type, user_type, class: "sr-only", checked: @user.type == user_type %>
                  <span class="flex flex-1">
                    <span class="flex flex-col">
                      <span class="block text-sm font-medium text-gray-900">
                        <%= user_type == 'IndividualUser' ? 'Individual' : user_type %>
                      </span>
                      <span class="mt-1 flex items-center text-sm text-gray-500">
                        <% case user_type %>
                        <% when 'Admin' %>
                          System administrator
                        <% when 'Teacher' %>
                          School teacher/staff
                        <% when 'IndividualUser' %>
                          Independent user
                        <% when 'Pupil' %>
                          Student/pupil
                        <% end %>
                      </span>
                    </span>
                  </span>
                  <i class="fa-solid fa-check h-5 w-5 text-de-brand hidden check-icon"></i>
                  <span class="pointer-events-none absolute -inset-px rounded-lg border-2 border-transparent" aria-hidden="true"></span>
                </label>
              <% end %>
            </div>
          </div>

          <!-- Dynamic Form Fields -->
          <div id="dynamic-fields">
            <div class="space-y-4">
              <!-- Common Fields (shown for all user types) -->
              <div data-user-types="all">
                <%= form.label :name, 'Name', required: true %>
                <%= form.text_field :name, required: true, class: "field field-text" %>
              </div>

              <div data-user-types="all">
                <%= form.label :email, 'Email', required: true %>
                <%= form.email_field :email, required: true, class: "field field-text" %>
              </div>

              <div data-user-types="all">
                <%= form.label :password, 'Password', required: @user.new_record? %>
                <div class="relative">
                  <%= form.password_field :password, class: "field field-text pr-20", id: 'password', autocomplete: "new-password", required: @user.new_record? %>
                  <span class="absolute text-gray-600 inset-y-0 right-0 flex items-center mr-1 px-2 cursor-pointer" data-password-id="password" onclick="togglePasswordVisibility(this)">
                    <i id="toggle-password-icon" class="fa-solid fa-eye"></i>
                  </span>
                  <span class="absolute text-gray-600 inset-y-0 right-0 flex items-center mr-10 px-2 cursor-pointer" onclick="generateRandomPassword()" title="Generate random password">
                    <i class="fa-solid fa-arrows-rotate"></i>
                  </span>
                </div>
                <% if @user.new_record? %>
                  <div id="pwdReqs" class="bg-white p-4 text-gray-500 rounded-xl mt-2" style="display: none;">
                    <p><i id="pwdLen" class="mr-1 fa fa-xmark text-red-500"></i>Contains at least eight characters</p>
                    <p><i id="pwdNum" class="mr-1 fa fa-xmark text-red-500"></i>Contains a number</p>
                    <p><i id="pwdLetter" class="mr-1 fa fa-xmark text-red-500"></i>Contains a letter</p>
                    <p><i id="pwdSpecial" class="mr-1 fa fa-xmark text-red-500"></i>Contains a special character</p>
                  </div>
                  <div id="generated-password" class="hidden mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <p class="text-sm text-green-800 font-medium">Generated Password:</p>
                    <p class="text-sm font-mono bg-white p-2 rounded border mt-1" id="password-display"></p>
                    <p class="text-xs text-green-600 mt-1">Password has been filled in the field above. Make sure to save it!</p>
                  </div>
                <% end %>
              </div>

              <!-- School Selection (for Teachers and Pupils) -->
              <div data-user-types="Teacher,Pupil" data-field="school" style="display: none;">
                <%= form.label :school_id, 'School', required: true %>
                <%= form.select :school_id,
                  options_for_select(
                    [['Select a school', nil]] +
                    (@user.school ? [[@user.school.postcode.present? ? "#{@user.school.name} (#{@user.school.postcode})" : @user.school.name, @user.school.id]] : [])
                  ),
                  {},
                  {
                    class: "field field-select",
                    data: {
                      nice_select: true,
                      ajax_url: "/admin/schools/search",
                      min_search_length: 2
                    }
                  }
                %>
              </div>

              <!-- Pupil-specific fields -->
              <div data-user-types="Pupil" style="display: none;">
                <%= form.label :alias, 'Display Name' %>
                <%= form.text_field :alias, class: "field field-text" %>
              </div>

              <div data-user-types="Pupil" style="display: none;">
                <%= form.label :identifier, 'Login Code' %>
                <%= form.text_field :identifier, class: "field field-text" %>
                <p class="mt-1 text-sm text-gray-500">Leave blank to auto-generate a login code.</p>
              </div>

              <!-- Teacher-specific fields -->
              <div data-user-types="Teacher" style="display: none;">
                <%= form.label :job_title, 'Job Title' %>
                <%= form.select :job_title,
                  options_for_select(
                    [['Select a job title', nil]] + User::JOB_TITLES,
                    @user.job_title
                  ),
                  {},
                  { class: "field field-select" }
                %>
              </div>

              <div data-user-types="Teacher" style="display: none;">
                <div class="field">
                  <%= form.label :is_school_admin do %>
                    <%= form.check_box :is_school_admin %>
                    <span class="label">Is School Admin</span>
                  <% end %>
                </div>
              </div>

              <div data-user-types="Teacher" style="display: none;">
                <div class="field">
                  <%= form.label :use_new_presentation_player do %>
                    <%= form.check_box :use_new_presentation_player %>
                    <span class="label">Can view new presentation where available</span>
                  <% end %>
                </div>
              </div>

              <!-- Optional fields for most user types -->
              <div data-user-types="Admin,Teacher,IndividualUser">
                <%= form.label :dob, 'Date of Birth' %>
                <%= form.date_field :dob, class: "field field-date" %>
              </div>

              <div data-user-types="Admin,Teacher,IndividualUser,Pupil">
                <%= form.label :gender %>
                <%= form.select :gender,
                  options_for_select([
                    ['Select a gender', nil],
                    ['Male', 'male'],
                    ['Female', 'female'],
                    ['Other', 'other']
                  ], @user.gender),
                  {},
                  { class: "field field-select" }
                %>
              </div>

              <!-- Working days for non-pupil, non-admin users -->
              <div data-user-types="Teacher,IndividualUser" style="display: none;">
                <%= form.label :working_days, 'Working Days per Week' %>
                <%= form.number_field :working_days, class: "field field-number", min: 1, max: 7 %>
              </div>

              <!-- Stripe Customer ID for non-pupil, non-admin users -->
              <div data-user-types="Teacher,IndividualUser" style="display: none;">
                <%= form.label :stripe_customer_id, 'Stripe Customer ID' %>
                <%= form.text_field :stripe_customer_id, class: "field field-text" %>
                <p class="mt-1 text-sm text-gray-500">Only set this if the user already has a Stripe customer account.</p>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end space-x-3">
            <%= link_to admin_users_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50" do %>
              Cancel
            <% end %>
            <%= form.submit "Create User", class: "inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-de-brand hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-de-brand" %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const userTypeOptions = document.querySelectorAll('.user-type-option');
  const dynamicFields = document.getElementById('dynamic-fields');

  // Password validation
  const pwdInput = document.getElementById('password');
  const pwdReqDiv = document.getElementById('pwdReqs');
  const pwdLen = document.getElementById('pwdLen');
  const pwdNum = document.getElementById('pwdNum');
  const pwdLetter = document.getElementById('pwdLetter');
  const pwdSpecial = document.getElementById('pwdSpecial');

  if (pwdInput) {
    pwdInput.addEventListener('input', function() {
      const pwd = pwdInput.value;

      if (pwdReqDiv) {
        pwdReqDiv.style.display = pwd.length ? 'block' : 'none';
      }

      const hasEightChars = pwd.length >= 8;
      const hasNumber = /\d/.test(pwd);
      const hasLetter = /[a-z]/i.test(pwd);
      const hasSpecialChar = /[!-\/:-@[-`{-~]/.test(pwd);

      checkRequirement(pwdLen, hasEightChars);
      checkRequirement(pwdNum, hasNumber);
      checkRequirement(pwdLetter, hasLetter);
      checkRequirement(pwdSpecial, hasSpecialChar);
    });
  }

  function checkRequirement(element, condition) {
    if (!element) return;
    if (condition) {
      element.classList.remove('fa-xmark', 'text-red-500');
      element.classList.add('fa-check', 'text-green-500');
    } else {
      element.classList.remove('fa-check', 'text-green-500');
      element.classList.add('fa-xmark', 'text-red-500');
    }
  }
  
  // Handle user type selection
  userTypeOptions.forEach(option => {
    option.addEventListener('click', function() {
      const userType = this.dataset.type;
      const radioButton = this.querySelector('input[type="radio"]');
      
      // Update visual selection
      userTypeOptions.forEach(opt => {
        opt.classList.remove('ring-2', 'ring-de-brand', 'border-de-brand');
        opt.querySelector('.check-icon').classList.add('hidden');
        opt.querySelector('.pointer-events-none').classList.remove('border-de-brand');
      });
      
      this.classList.add('ring-2', 'ring-de-brand', 'border-de-brand');
      this.querySelector('.check-icon').classList.remove('hidden');
      this.querySelector('.pointer-events-none').classList.add('border-de-brand');
      
      // Check the radio button
      radioButton.checked = true;
      
      // Update form fields based on user type
      updateFormFields(userType);
    });
  });
  
  // Initialize with current selection
  const checkedOption = document.querySelector('input[name="user[type]"]:checked');
  if (checkedOption) {
    const selectedOption = checkedOption.closest('.user-type-option');
    selectedOption.click();
  } else {
    // Default to IndividualUser if none selected
    document.querySelector('[data-type="IndividualUser"]').click();
  }
  
  function updateFormFields(userType) {
    // Show/hide fields based on user type
    const allFields = dynamicFields.querySelectorAll('[data-user-types]');

    allFields.forEach(field => {
      const allowedTypes = field.dataset.userTypes.split(',');
      if (allowedTypes.includes(userType) || allowedTypes.includes('all')) {
        field.style.display = 'block';
      } else {
        field.style.display = 'none';
      }
    });

    // Update school field requirement
    const schoolField = document.querySelector('[data-field="school"]');
    if (schoolField) {
      const schoolSelect = schoolField.querySelector('select');
      if (userType === 'Teacher' || userType === 'Pupil') {
        schoolField.style.display = 'block';
        if (schoolSelect) schoolSelect.required = true;

        // Re-initialize nice-select for school field if it's shown
        setTimeout(() => {
          const schoolSelectElement = schoolField.querySelector('select[data-nice-select]');
          if (schoolSelectElement && !schoolSelectElement.hasAttribute('data-nice-select-initialized')) {
            // Mark as initialized to prevent double initialization
            schoolSelectElement.setAttribute('data-nice-select-initialized', 'true');

            // Re-run the nice-select enhancement for newly visible elements
            document.querySelectorAll("[data-nice-select]:not([data-nice-select-initialized])").forEach($el => {
              if ($el instanceof HTMLSelectElement) {
                $el.setAttribute('data-nice-select-initialized', 'true');
                // The nice-select.js will handle both regular and AJAX enhancement
                if (window.enhanceWithAjax) {
                  window.enhanceWithAjax();
                }
              }
            });
          }
        }, 100);
      } else {
        schoolField.style.display = 'none';
        if (schoolSelect) schoolSelect.required = false;
      }
    }
  }
});

// Password generation function
function generateRandomPassword(length = 12) {
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const allCharacters = uppercase + lowercase + numbers + symbols;

  // Ensure password contains at least one of each required type
  let password = "";
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // Fill the remaining length with random characters
  for (let i = 4; i < length; i++) {
    password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
  }

  // Shuffle the password to avoid predictable patterns
  password = password.split('').sort(() => Math.random() - 0.5).join('');

  // Set the password in the field
  const passwordField = document.getElementById('password');
  if (passwordField) {
    passwordField.value = password;

    // Trigger the input event to update validation
    passwordField.dispatchEvent(new Event('input'));

    // Show the generated password
    const passwordDisplay = document.getElementById('password-display');
    const generatedPasswordDiv = document.getElementById('generated-password');

    if (passwordDisplay && generatedPasswordDiv) {
      passwordDisplay.textContent = password;
      generatedPasswordDiv.classList.remove('hidden');

      // Hide the generated password display after 10 seconds
      setTimeout(() => {
        generatedPasswordDiv.classList.add('hidden');
      }, 10000);
    }
  }

  return password;
}
</script>

<style>
.user-type-option:hover {
  @apply shadow-md;
}

.user-type-option.selected {
  @apply ring-2 ring-de-brand border-de-brand;
}
</style>
