<div class="space-y-4">
  <!-- Common Fields (shown for all user types) -->
  <div data-user-types="all">
    <%= form.label :name, 'Name', required: true %>
    <%= form.text_field :name, required: true, class: "field field-text" %>
  </div>

  <div data-user-types="all">
    <%= form.label :email, 'Email', required: true %>
    <%= form.email_field :email, required: true, class: "field field-text" %>
  </div>

  <div data-user-types="all">
    <%= form.label :password, 'Password', required: @user.new_record? %>
    <div class="relative">
      <%= form.password_field :password, class: "field field-text pr-10", id: 'password', autocomplete: "new-password", required: @user.new_record? %>
      <span class="absolute text-gray-600 inset-y-0 right-0 flex items-center mr-1 px-2 cursor-pointer" data-password-id="password" onclick="togglePasswordVisibility(this)">
        <i id="toggle-password-icon" class="fa-solid fa-eye"></i>
      </span>
    </div>
    <% if @user.new_record? %>
      <p class="mt-1 text-sm text-gray-500">Password must be at least 8 characters and contain a number, letter, and special character.</p>
    <% end %>
  </div>

  <!-- School Selection (for Teachers and Pupils) -->
  <div data-user-types="Teacher,Pupil" data-field="school" style="display: none;">
    <%= form.label :school_id, 'School', required: true %>
    <%= form.select :school_id,
      options_for_select(
        [['Select a school', nil]] +
        (@user.school ? [[@user.school.postcode.present? ? "#{@user.school.name} (#{@user.school.postcode})" : @user.school.name, @user.school.id]] : [])
      ),
      {},
      {
        class: "field field-select",
        data: {
          nice_select: true,
          ajax_url: "/admin/schools/search",
          min_search_length: 2
        }
      }
    %>
  </div>

  <!-- Pupil-specific fields -->
  <div data-user-types="Pupil" style="display: none;">
    <%= form.label :alias, 'Display Name' %>
    <%= form.text_field :alias, class: "field field-text" %>
  </div>

  <div data-user-types="Pupil" style="display: none;">
    <%= form.label :identifier, 'Login Code' %>
    <%= form.text_field :identifier, class: "field field-text" %>
    <p class="mt-1 text-sm text-gray-500">Leave blank to auto-generate a login code.</p>
  </div>

  <!-- Teacher-specific fields -->
  <div data-user-types="Teacher" style="display: none;">
    <%= form.label :job_title, 'Job Title' %>
    <%= form.select :job_title,
      options_for_select(
        [['Select a job title', nil]] + User::JOB_TITLES,
        @user.job_title
      ),
      {},
      { class: "field field-select" }
    %>
  </div>

  <div data-user-types="Teacher" style="display: none;">
    <div class="field">
      <%= form.label :is_school_admin do %>
        <%= form.check_box :is_school_admin %>
        <span class="label">Is School Admin</span>
      <% end %>
    </div>
  </div>

  <div data-user-types="Teacher" style="display: none;">
    <div class="field">
      <%= form.label :use_new_presentation_player do %>
        <%= form.check_box :use_new_presentation_player %>
        <span class="label">Can view new presentation where available</span>
      <% end %>
    </div>
  </div>

  <!-- Optional fields for most user types -->
  <div data-user-types="Admin,Teacher,IndividualUser">
    <%= form.label :dob, 'Date of Birth' %>
    <%= form.date_field :dob, class: "field field-date" %>
  </div>

  <div data-user-types="Admin,Teacher,IndividualUser,Pupil">
    <%= form.label :gender %>
    <%= form.select :gender,
      options_for_select([
        ['Select a gender', nil],
        ['Male', 'male'],
        ['Female', 'female'],
        ['Other', 'other']
      ], @user.gender),
      {},
      { class: "field field-select" }
    %>
  </div>

  <!-- Working days for non-pupil, non-admin users -->
  <div data-user-types="Teacher,IndividualUser" style="display: none;">
    <%= form.label :working_days, 'Working Days per Week' %>
    <%= form.number_field :working_days, class: "field field-number", min: 1, max: 7 %>
  </div>

  <!-- Stripe Customer ID for non-pupil, non-admin users -->
  <div data-user-types="Teacher,IndividualUser" style="display: none;">
    <%= form.label :stripe_customer_id, 'Stripe Customer ID' %>
    <%= form.text_field :stripe_customer_id, class: "field field-text" %>
    <p class="mt-1 text-sm text-gray-500">Only set this if the user already has a Stripe customer account.</p>
  </div>

  <% unless defined?(skip_submit) && skip_submit %>
    <%= render ButtonComponent::Submit.new %>
  <% end %>
</div>
