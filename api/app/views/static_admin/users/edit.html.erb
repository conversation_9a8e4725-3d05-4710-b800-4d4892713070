<% content_for :title, "#{@user.name} | Edit User" %>
<%= render partial: 'edit_header', locals: { active_tab: :edit } %>

<div>
  <%= render AdminFormSectionComponent.new do %>
    <%= form_with model: @user, url: admin_user_path(@user), local: true, multipart: true, autocomplete: "none", scope: :user do |form| %>
      <%= render 'form', form: form %>
    <% end %>
  <% end %>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Password validation for edit form
  const pwdInput = document.getElementById('password-edit');
  const pwdReqDiv = document.getElementById('pwdReqsEdit');
  const pwdLen = document.getElementById('pwdLenEdit');
  const pwdNum = document.getElementById('pwdNumEdit');
  const pwdLetter = document.getElementById('pwdLetterEdit');
  const pwdSpecial = document.getElementById('pwdSpecialEdit');

  if (pwdInput) {
    pwdInput.addEventListener('input', function() {
      const pwd = pwdInput.value;

      if (pwdReqDiv) {
        pwdReqDiv.style.display = pwd.length ? 'block' : 'none';
      }

      const hasEightChars = pwd.length >= 8;
      const hasNumber = /\d/.test(pwd);
      const hasLetter = /[a-z]/i.test(pwd);
      const hasSpecialChar = /[!-\/:-@[-`{-~]/.test(pwd);

      checkRequirement(pwdLen, hasEightChars);
      checkRequirement(pwdNum, hasNumber);
      checkRequirement(pwdLetter, hasLetter);
      checkRequirement(pwdSpecial, hasSpecialChar);
    });
  }

  function checkRequirement(element, condition) {
    if (!element) return;
    if (condition) {
      element.classList.remove('fa-xmark', 'text-red-500');
      element.classList.add('fa-check', 'text-green-500');
    } else {
      element.classList.remove('fa-check', 'text-green-500');
      element.classList.add('fa-xmark', 'text-red-500');
    }
  }
});

// Password generation function for edit form
function generateRandomPasswordEdit(length = 12) {
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const numbers = "0123456789";
  const symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const allCharacters = uppercase + lowercase + numbers + symbols;

  // Ensure password contains at least one of each required type
  let password = "";
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];

  // Fill the remaining length with random characters
  for (let i = 4; i < length; i++) {
    password += allCharacters[Math.floor(Math.random() * allCharacters.length)];
  }

  // Shuffle the password to avoid predictable patterns
  password = password.split('').sort(() => Math.random() - 0.5).join('');

  // Set the password in the field
  const passwordField = document.getElementById('password-edit');
  if (passwordField) {
    passwordField.value = password;

    // Trigger the input event to update validation
    passwordField.dispatchEvent(new Event('input'));

    // Show the generated password
    const passwordDisplay = document.getElementById('password-display-edit');
    const generatedPasswordDiv = document.getElementById('generated-password-edit');

    if (passwordDisplay && generatedPasswordDiv) {
      passwordDisplay.textContent = password;
      generatedPasswordDiv.classList.remove('hidden');

      // Hide the generated password display after 10 seconds
      setTimeout(() => {
        generatedPasswordDiv.classList.add('hidden');
      }, 10000);
    }
  }

  return password;
}
</script>
