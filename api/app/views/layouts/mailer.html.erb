<!-- app/views/layouts/mailer.html.erb -->
<!DOCTYPE html>
<html lang="<%= I18n.locale.presence || 'en' %>">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title><%= yield(:title) %></title>
  <style type="text/css">
    /* Base styles */
    body {
      margin: 0;
      padding: 10px 0;
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      font-size: 16px;
      line-height: 1.5;
      color: #333;
      background-color: #f6f8fa;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
    }
    
    /* Container */
    .email-container {
      max-width: 600px;
      margin: 20px auto;
      background-color: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    /* Header */
    .email-header {
      padding: 25px;
      background-color: #ffffff;
      border-bottom: 1px solid #eaeaea;
      text-align: center;
    }
    
    .email-header img {
      max-width: 200px;
      height: auto;
    }
    
    /* Content */
    .email-content {
      padding: 30px 25px;
      background-color: #ffffff;
    }
    
    /* Footer */
    .email-footer {
      padding: 20px 25px;
      background-color: #f6f8fa;
      border-top: 1px solid #eaeaea;
      font-size: 14px;
      color: #666;
      text-align: center;
    }
    
    /* Typography */
    h1 {
      margin: 0 0 20px;
      color: #1a1a1a;
      font-size: 24px;
      font-weight: 600;
      line-height: 1.25;
    }
    
    h2 {
      margin: 25px 0 15px;
      color: #1a1a1a;
      font-size: 20px;
      font-weight: 600;
      line-height: 1.25;
    }
    
    p {
      margin: 0 0 15px;
    }

    p:last-child {
      margin-bottom: 0;
    }
    
    a {
      color: #556cd6;
      text-decoration: none;
    }
    
    a:hover {
      text-decoration: underline;
    }
    
    /* Buttons */
    .button {
      display: inline-block;
      padding: 12px 20px;
      background-color: #556cd6;
      border-radius: 4px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      text-decoration: none;
    }
    
    .button:hover {
      background-color: #4559be;
      text-decoration: none;
    }
    
    /* Alert boxes */
    .alert {
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 4px;
    }
    
    .alert-info {
      background-color: #f0f7ff;
      border: 1px solid #cce5ff;
      color: #0c5460;
    }
    
    .alert-warning {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
      color: #856404;
    }
    
    .alert-danger {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }
    
    .alert-success {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }
    
    /* Tables */
    table {
      width: 100%;
      margin-bottom: 20px;
      border-collapse: collapse;
    }
    
    th {
      padding: 12px 15px;
      background-color: #f6f8fa;
      border-bottom: 2px solid #e1e4e8;
      text-align: left;
      font-weight: 600;
    }
    
    td {
      padding: 12px 15px;
      border-bottom: 1px solid #e1e4e8;
    }
    
    /* Utilities */
    .text-center {
      text-align: center;
    }
    
    .mt-0 { margin-top: 0; }
    .mb-0 { margin-bottom: 0; }
    .mt-4 { margin-top: 20px; }
    .mb-4 { margin-bottom: 20px; }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="email-header">
      <img src="https://api.fileboy.io/files/get/92ff4093-f09c-4ab0-9851-cf76204f019c+logo" alt="Developing Experts Logo">
    </div>
    
    <div class="email-content">
      <%= yield %>
    </div>
    
    <div class="email-footer">
      <p>&copy; <%= Date.today.year %> Developing Experts Limited</p>
      <p>Exchange Street Buildings, 35-37 Exchange Street, Norwich NR2 1DP, UK</p>
      <p>
        <a href="https://www.developingexperts.com/terms">Terms of Service</a> •
        <a href="https://www.developingexperts.com/privacy">Privacy Policy</a>
      </p>
    </div>
  </div>
</body>
</html>