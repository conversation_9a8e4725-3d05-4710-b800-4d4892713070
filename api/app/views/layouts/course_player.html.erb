<!DOCTYPE html>
<html lang="en">
<head>
  <title><%= page_title("Course") %></title>
  <link rel="alternate" hreflang="en" href="<%= root_url %>" />
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= javascript_importmap_tags %>
  <%= stylesheet_link_tag 'application' %>
  <%= javascript_include_tag 'application', type: "module" %>
  <%= javascript_include_tag "application", "data-turbo-track": "reload" %>
  <%= stylesheet_link_tag "tailwind", "inter-font" %>

  <script defer src="https://kit.fontawesome.com/7fae4ae66d.js" crossorigin="anonymous"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/htmx.org@1.9.6"></script>
  <script src="https://analytics.ahrefs.com/analytics.js" data-key="bJUnpJ5re7xSm/pN4cVtqQ" async></script>
  
  <!-- Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-99JG1WS1Z7"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag('js', new Date());
    gtag('config', 'G-99JG1WS1Z7');
  </script>
  <%= render './layouts/common_header' %>
</head>

  <body class="bg-gray-900 text-white overflow-hidden h-screen">
    <!-- Course Player Header -->
    <header class="bg-black border-b-2 border-gray-600 px-4 py-2 flex items-center justify-between relative z-50">

      <div class="flex items-center space-x-4 pr-4 border-r border-gray-600">
        <%= link_to flow_path(@flow),
            class: "p-2 text-gray-400 hover:text-white transition-colors",
            aria: { label: "Back to Course" } do %>
          <i class="fas fa-arrow-left fa-lg"></i>
        <% end %>

        <%= image_tag "logo_white.svg", alt: "Site Logo", class: "h-8 hidden sm:block" %>

        <div class="hidden md:block">
          <h1 class="font-semibold text-white max-w-xs">
            <%= @flow.name %>
          </h1>
        </div>
      </div>
      
      
      <div class="flex-1 max-w-lg mx-4">
        <div class="bg-gray-700 rounded-full h-2.5 overflow-hidden shadow-inner">
          <div class="bg-gradient-to-r from-cyan-400 to-blue-500 h-full transition-all duration-500 ease-out"
              style="width: <%= @user_progress %>%;"></div>
        </div>
        <div class="mt-2 text-center text-sm font-semibold tracking-wide">
          <span class="bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent min-w-[110px] inline-block">
            <%= @user_progress.round %>% Complete
          </span>
        </div>
      </div>
      
      <% course_is_complete = @completed_steps == @all_steps.count %>
      <div class="flex items-center gap-4">
        <% if course_is_complete %>
          <%= link_to summary_flow_path(@flow), class: "px-2 py-2 bg-green-600 text-white rounded text-sm flex items-center justify-center" do %>
            <i class="fas fa-circle-check mr-2"></i>
            Course Completed
          <% end %>
        <% end %>
        <div class="flex items-center space-x-4 pl-4 border-l border-gray-600">
          <div class="flex items-center space-x-2">
            <% if @previous_step %>
              <%= link_to flow_flow_step_path(@flow, @previous_step),
                  class: "flex items-center justify-center h-10 w-10 rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-all transform hover:scale-110" do %>
                <i class="fas fa-chevron-left"></i>
              <% end %>
            <% else %>
              <div class="flex items-center justify-center h-10 w-10 rounded-full bg-gray-800 text-gray-600 cursor-not-allowed">
                <i class="fas fa-chevron-left"></i>
              </div>
            <% end %>

            <span class="text-sm font-semibold text-gray-400 w-12 text-center font-mono">
              <%= @current_step_index + 1 %>/<%= @all_steps.count %>
            </span>

            <% is_completed = @flow_step.completed_by?(@current_user) %>
            <% if @next_step && is_completed %>
              <%= link_to flow_flow_step_path(@flow, @next_step),
                  class: "flex items-center justify-center h-10 w-10 rounded-full bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white transition-all transform hover:scale-110" do %>
                <i class="fas fa-chevron-right"></i>
              <% end %>
            <% else %>
              <div class="flex items-center justify-center h-10 w-10 rounded-full bg-gray-800 text-gray-600 cursor-not-allowed">
                <i class="fas fa-chevron-right"></i>
              </div>
            <% end %>
          </div>

          <button id="sidebar-toggle" class="lg:hidden p-2 text-gray-400 hover:text-white transition-colors">
            <i class="fas fa-bars fa-lg"></i>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content Area -->
    <div class="flex h-[calc(100vh-64px)]">
      <!-- Main Content -->
      <main class="flex-1 flex flex-col overflow-hidden">
        <% if flash.any? %>
          <div class="px-4 py-2">
            <% flash.each do |type, message| %>
              <div class="alert alert-<%= type == 'notice' ? 'success' : type %> mb-2">
                <%= message %>
              </div>
            <% end %>
          </div>
        <% end %>
        
        <!-- Step Content -->
        <div class="flex-1 overflow-auto">
          <%= yield %>
        </div>
        
        <div class="bg-gray-800 border-t border-gray-700 px-4 py-3">
          <div class="max-w-4xl mx-auto flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 flex-shrink-0 rounded-full flex items-center justify-center <%= @progress&.completed? ? 'bg-green-600' : 'bg-gray-600' %>">
                <% if @progress&.completed? %>
                  <i class="fas fa-check text-white text-sm"></i>
                <% else %>
                  <span class="text-white text-sm font-medium"><%= @current_step_index + 1 %></span>
                <% end %>
              </div>
              <div>
                <h3 class="font-medium text-white truncate"><%= @flow_step.name %></h3>
                <p class="text-xs text-gray-400">
                  <%= @flow_step.step_type.humanize %>
                  <% if @flow_step.estimated_duration_minutes.present? && @flow_step.estimated_duration_minutes > 0 %>
                    • <%= @flow_step.estimated_duration_minutes %> min
                  <% end %>
                </p>
              </div>
            </div>
            
            <div class="flex items-center space-x-3 self-end sm:self-auto">
              <% unless @progress&.completed? %>
                <%= button_to complete_flow_flow_step_path(@flow, @flow_step), 
                    method: :post,
                    class: "flex items-center px-3 sm:px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors",
                    id: 'mark-complete-button' do %>
                  <i class="fas fa-check sm:mr-2"></i>
                  <span class="hidden sm:inline">Mark Complete</span>
                <% end %>
              <% end %>

              <% if @next_step %>
                <%= link_to @progress.completed? ? flow_flow_step_path(@flow, @next_step) : 'javascript:void(0)', 
                    class: "flex items-center px-3 sm:px-4 py-2 text-white font-medium rounded-lg transition-colors #{@progress.completed? ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-400 opacity-50 cursor-not-allowed'}" do %>
                  <span class="hidden sm:inline">Next Step</span>
                  <i class="fas fa-chevron-right sm:ml-2"></i>
                <% end %>
              <% else %>
                <%= link_to summary_flow_path(@flow), class: "flex items-center px-3 sm:px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors" do %>
                  <i class="fas fa-trophy sm:mr-2"></i>
                  <span class="hidden sm:inline">Finish Course</span>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </main>

      <!-- Sidebar - Course Steps -->
      <aside id="course-sidebar" class="w-80 bg-gray-900 border-l-2 border-gray-600 overflow-y-auto transform lg:transform-none transition-transform duration-300 lg:translate-x-0 translate-x-full fixed lg:relative inset-y-0 right-0 z-40">
        <div class="p-5 border-b-2 border-gray-600">
          <h2 class="text-lg font-bold text-white tracking-wide">Course Content</h2>
          <p class="text-sm text-gray-400">
            <%= pluralize(@all_steps.count, 'step') %> total
          </p>
        </div>

        <div class="divide-y divide-gray-800">
          <% @all_steps.each_with_index do |step, index| %>
            <% is_current = step.id == @flow_step.id %>
            <% is_completed = step.completed_by?(@current_user) %>
            <% previous_completed = @all_steps[index-1]&.completed_by?(@current_user) %>
            <% is_first = index == 0 %>
            <% is_disabled = !(is_completed || previous_completed) && !is_first %>
            <div class="
              p-4 transition-colors
              <%= 'opacity-50 cursor-not-allowed' if is_disabled %>
              <%= 'bg-gray-800 border-l-4 border-cyan-400' if is_current %>
              <%= 'hover:bg-gray-800/50' unless is_current %>
            "
            data-sidebar-step-id="<%= step.id %>"
            >
              <%= link_to is_completed || previous_completed ? flow_flow_step_path(@flow, step) : 'javascript:void(0)', class: "block #{'cursor-not-allowed' if is_disabled}" do %>
                <div class="flex items-start space-x-4">
                  
                  <div class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold
                    <%= 'animate-pulse' if is_current %>
                    <%= 
                      if is_completed
                        'bg-green-500 text-white'
                      elsif is_current
                        'bg-cyan-500 text-white'
                      else
                        'bg-gray-700 text-gray-300'
                      end
                    %>">
                    <% if is_completed %>
                      <i class="fas fa-check"></i>
                    <% else %>
                      <%= index + 1 %>
                    <% end %>
                  </div>
                  
                  <div class="flex-1 min-w-0">
                    <h3 class="font-semibold text-base line-clamp-2
                      <%= 
                        if is_current
                          'text-white'
                        elsif is_completed
                          'text-gray-300'
                        else
                          'text-gray-200'
                        end
                      %>">
                      <%= step.name %>
                    </h3>
                    
                    <p class="text-sm text-gray-400 mt-1 line-clamp-2">
                      <%= step.description %>
                    </p>
                    
                    <div class="flex items-center mt-2 space-x-3 text-xs text-gray-500">
                      <span class="flex items-center">
                        <i class="fas fa-<%= case step.step_type
                                            when 'video' then 'play-circle'
                                            when 'quiz' then 'question-circle'
                                            when 'rich_text' then 'file-alt'
                                            end %> mr-1.5"></i>
                        <%= step.step_type.humanize %>
                      </span>
                      
                      <% if step.estimated_duration_minutes.present? && step.estimated_duration_minutes > 0 %>
                        <span class="flex items-center">
                          <i class="far fa-clock mr-1.5"></i>
                          <span><%= step.estimated_duration_minutes %>min</span>
                        </span>
                      <% end %>
                      <% has_notes = step.progress_for(@current_user)&.notes&.present? %>                
                      <% if has_notes %>
                        <span class="flex items-center">
                          <i class="fas fa-sticky-note mr-1.5"></i>
                          <span>Notes</span>
                        </span>
                      <% end %>
                    </div>
                  </div>
                  
                  <% if is_current %>
                    <div class="flex-shrink-0 pt-1">
                      <i class="fas fa-play text-cyan-400"></i>
                    </div>
                  <% end %>
                </div>
              <% end %>
            </div>
          <% end %>
          <% if course_is_complete %>
            <%= link_to course_is_complete ? summary_flow_path(@flow) : 'javascript:void(0)', 
              class: "block p-4 flex items-center space-x-4 transition-colors #{course_is_complete ? 'bg-green-600 border-l-4 border-green-400 hover:bg-green-700' : 'bg-green-400 border-l-4 border-green-300 opacity-60 cursor-not-allowed'}" do %>
              <div class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-white text-green-600 text-lg font-bold">
                <i class="fas fa-trophy"></i>
              </div>
              <div>
                <h3 class="font-semibold text-white text-base">Course Complete</h3>
                <p class="text-sm text-white mt-1">Congratulations! You have finished all steps.</p>
              </div>
            <% end %>
          <% end %>
        </div>
      </aside>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden"></div>

    <script>
      // Sidebar toggle for mobile
      document.addEventListener('DOMContentLoaded', function() {
        const toggle = document.getElementById('sidebar-toggle');
        const sidebar = document.getElementById('course-sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (toggle && sidebar && overlay) {
          toggle.addEventListener('click', function() {
            sidebar.classList.toggle('translate-x-full');
            overlay.classList.toggle('hidden');
          });
          
          overlay.addEventListener('click', function() {
            sidebar.classList.add('translate-x-full');
            overlay.classList.add('hidden');
          });
        }

        // scroll sidebar into view
        // sidebar-step-id
        const currentStepNode = document.querySelector('[data-sidebar-step-id="<%= @flow_step.id %>"]');
        if (currentStepNode) {
          currentStepNode.scrollIntoView({ block: 'center', inline: 'nearest' });
        }
      });
    </script>
    <script defer src="https://cdn.jsdelivr.net/npm/@cd2/fileboy-browser@0.12.1-alpha.7"></script>
  </body>
</html>
