@tailwind base;
@tailwind components;
@tailwind utilities;



/*

@layer components {
  .btn-primary {
    @apply py-2 px-4 bg-blue-200;
  }
}

*/
* {
  font-feature-settings: "ss01" 1;
}

body {
  interpolate-size: allow-keywords;
}

h1,
h2,
h3,
h4 {
  @apply font-headings;
}

::backdrop {
  @apply bg-black bg-opacity-50;
}

dialog {
  @apply bg-white rounded-lg shadow-lg;

  & .modal-confirm-btn {
    @apply bg-cyan-500 text-white py-2 px-6 rounded-full shadow-lg border border-b-2 border-cyan-500 hover:bg-cyan-400 cursor-pointer inline-block duration-300 font-bold;
  }

  & .modal-neutral-btn {
    @apply bg-gray-100 py-2 px-6 rounded-full shadow-lg border border-b-2 border-gray-100 hover:bg-gray-200 cursor-pointer inline-block duration-300 font-bold;
  }
}

.rounded-white-background {
  @apply bg-white rounded-xl p-8 text-black;
}

.standard-form-layout {
  & label {
    @apply font-bold;
  }
}

.standard-form-section {
  @apply bg-slate-400/10 p-4 text-sm text-slate-600 rounded-md border border-slate-200;
}

.tab-heading {
  @apply flex-1 font-bold text-xl text-center border-b-4 border-white text-white pb-4 cursor-pointer;
}

.active-tab-heading {
  @apply text-yellow-300 border-yellow-300;
}

.video-player {
  aspect-ratio: 16/9;
  @apply w-full;

  & .fv-player-container {
    border-radius: 0!important;
  }

  >div {
    height: 100%;

    >div {
      height: 100%;

      >div {
        height: 100%;
      }
    }
  }
}

.aspect-ratio-2-1 {
  aspect-ratio: 2 / 1;
}

@layer components {
  .prose p {
    @apply mb-3 mt-0;
  }
}

input::placeholder,
textarea::placeholder {
  color: #aaa;
}

input[disabled],
textarea[disabled],
select[disabled],
input[disabled="disabled"],
select[disabled="disabled"] {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-form {
  @apply container mx-auto;

  & input[type="text"],
  & input[type="email"],
  & input[type="password"],
  & input[type="number"],
  & input[type="date"],
  & input[type="time"],
  & input[type="url"],
  & input[type="tel"],
  & input[type="search"],
  & input[type="month"],
  & input[type="week"],
  & input[type="datetime-local"],
  & input[type="color"],
  & textarea,
  & select {
    appearance: none;
    background-color: rgb(249, 250, 251);
    border: 1px solid rgb(192, 196, 201);
    border-bottom-width: 2px;
    border-radius: 4px;
    color: rgb(32, 36, 47);
    cursor: text;
    font-size: 14px;
    height: 44px;
    line-height: normal;
    padding: 12px;
    text-align: start;
    transition: 0.15s ease-out;
    width: 100%;
    max-width: 900px;

    &:focus {
      @apply border-de-brand;
      outline: none;
      box-shadow: none;
    }
  }

  & input[type="checkbox"] {
    display: inline-block;

    &+label,
    &+span {
      display: inline-block;
      font-weight: 500;
      margin-left: 0.25rem;
      user-select: none;
    }
  }

  & label {
    display: block;
    font-size: 13px;
    font-weight: 600;
  }

  & .field {
    margin-bottom: 1rem;
  }

  & fieldset {
    @apply border border-gray-300 rounded p-4 mb-4;
  }

  & legend {
    @apply text-gray-700 text-sm font-semibold;
  }

  & .field-image {
    @apply flex items-center justify-between border border-gray-300 rounded p-4;
  }

  & .field-image img {
    @apply w-20 rounded;
  }
}

.admin-table {
  @apply bg-white rounded-lg shadow-lg max-w-full w-full overflow-x-auto overflow-y-hidden;

  & table {
    @apply w-full text-sm text-left text-gray-500 cursor-default relative;
  }

  & thead {
    @apply text-xs text-gray-700 uppercase bg-gray-100;
  }

  & th {
    @apply px-4 py-3;
  }

  & tbody tr {
    @apply hover:bg-gray-100 border-b;
  }

  & td {
    @apply px-4 py-3;
  }

  & th a {
    @apply underline;
  }

  .admin-table-thumbnail-column {
    @apply pl-3 pr-1 py-1;
  }
}

.admin-table.lg>table {
  min-width: 1200px;
}

.admin-table.xl>table {
  min-width: 1500px;
}

.admin-table.xxl>table {
  min-width: 1900px;
}

.admin-table-filters {
  @apply flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 md:space-x-4 p-4;
}

.admin-table-primary-col {
  @apply text-slate-800 font-bold;
}

.admin-scope-buttons {
  @apply flex gap-2 items-center mb-4;

  & a {
    @apply inline-block bg-gray-100 text-gray-700 hover:bg-gray-200 rounded py-2 px-4 text-xs border border-b-2 border-gray-300;

    &.active {
      @apply bg-gray-700 text-white border-gray-800;
    }
  }
}

.scope-links {
  @apply flex gap-2 items-center flex-wrap;

  a {
    @apply inline-block text-white px-2 text-sm font-normal;
  }

  .active {
    @apply font-bold;
  }
}

.admin-btn {
  @apply text-white bg-de-brand focus:ring-4 focus:outline-none brightness-100 hover:brightness-125 transition-all focus:ring-blue-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-block cursor-pointer;
}

.admin-btn-small-white {
  @apply text-slate-800 border border-slate-300 bg-white focus:ring-4 focus:outline-none hover:bg-gray-200 transition-all focus:ring-blue-800 font-medium rounded text-xs text-center inline-block;
  padding: 6px 14px;
}

.admin-btn-secondary {
  @apply bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200;
}

.admin-delete-btn {
  @apply bg-red-600 border-red-800 hover:bg-red-700;
}

.admin-disabled-btn {
  @apply opacity-25 cursor-not-allowed;
}

.ck-editor p,
.ck-editor ul,
.ck-editor ol,
.ck-editor h1,
.ck-editor h2,
.ck-editor h3,
.ck-editor h4,
.ck-editor h5,
.ck-editor h6,
.ck-editor blockquote,
.ck-editor pre,
.ck-editor table,
.ck-editor hr {
  margin-bottom: 1rem;
}

.mark-book-table {
  @apply w-full border-collapse rounded shadow-[0_0_0_1px_rgba(229_231_235)] table-fixed bg-white;

  & .header {
    @apply text-gray-500 text-left font-semibold text-xs border-b border-gray-200 p-2;
  }

  & th {
    @apply text-gray-500 text-left font-semibold text-xs tracking-wider border-b border-gray-200 p-2;
  }

  & td {
    @apply text-gray-900 text-sm border-b border-gray-200 p-2;
  }

  & a {
    @apply text-blue-600 hover:text-blue-800 underline;
  }

  & th a {
    @apply text-gray-500 hover:text-gray-700;
  }

  & thead,
  .collapsible-row {
    @apply bg-gray-100;
  }

  & tbody .collapsible-content {
    @apply text-center;
  }
}

.admin-form-sticky-submit {
  @apply sticky bottom-4 left-0 w-full max-w-lg mx-auto bg-white shadow-lg p-4 rounded-lg z-10 border border-gray-300;
}

& input[type="text"],
& input[type="email"],
& input[type="password"],
& input[type="number"],
& input[type="date"],
& input[type="time"],
& input[type="url"],
& input[type="tel"],
& input[type="search"],
& input[type="month"],
& input[type="week"],
& input[type="datetime-local"],
& input[type="color"],
& textarea,
& select,
.field-appearance {
  @apply bg-gray-50 border border-gray-300 text-gray-900 rounded-lg block w-full p-2.5;
}

label {
  @apply block mb-1 text-sm font-medium text-gray-900;
}

input[type="search"] {
  @apply pl-10;
}

.admin-form-section {
  & label {
    @apply block mb-2 text-sm font-medium text-gray-900;
  }

  & input[type="text"],
  & input[type="email"],
  & input[type="password"],
  & input[type="number"],
  & input[type="date"],
  & input[type="time"],
  & input[type="url"],
  & input[type="tel"],
  & input[type="search"],
  & input[type="month"],
  & input[type="week"],
  & input[type="datetime-local"],
  & input[type="color"],
  & textarea,
  & select,
  & .field-appearance {
    @apply text-sm;
  }

  & fieldset {
    @apply md:col-span-2 pb-6;
  }

  & legend {
    @apply text-gray-700 text-lg font-bold pt-2;
    font-family: "aktiv-grotesk-extended", sans-serif;
  }

  .field_with_errors {

    & input[type="text"],
    & input[type="email"],
    & input[type="password"],
    & input[type="number"],
    & input[type="date"],
    & input[type="time"],
    & input[type="url"],
    & input[type="tel"],
    & input[type="search"],
    & input[type="month"],
    & input[type="week"],
    & input[type="datetime-local"],
    & input[type="color"],
    & textarea,
    & select {
      @apply border-red-300 bg-red-100 text-red-800;
    }

    & label {
      @apply text-red-800;
    }
  }

  .checkbox-pill {
    & input {
      @apply my-auto;
    }

    @apply flex text-gray-200 gap-x-2 my-auto border rounded-lg p-2 bg-white/5;
  }
}

.admin-form-option-box {
  @apply rounded border p-2 border-dashed border-gray-400;
}

.fields-grid {
  container-type: inline-size;

  &> :first-child {
    @apply flex flex-col gap-4;
  }
}

.form-fields-container {
  @apply space-y-4;

  & input[type="text"],
  & input[type="email"],
  & input[type="password"],
  & input[type="number"],
  & input[type="date"],
  & input[type="time"],
  & input[type="url"],
  & input[type="tel"],
  & input[type="search"],
  & input[type="month"],
  & input[type="week"],
  & input[type="datetime-local"],
  & input[type="color"],
  & textarea,
  & select {
    @apply w-full max-w-lg;
  }
}

.check-container {
  @apply text-center align-middle h-full p-3 border-2 text-sm font-semibold peer-checked:bg-sky-400 peer-checked:text-white rounded-lg hover:bg-gray-300;
}

.career-path-container {
  @apply border rounded-xl p-2 gap-4;
}

.career-path-stage-container {
  @apply border rounded-xl grid grid-cols-3 p-2 gap-4 mb-2;
}

.career-path-step-container {
  @apply border rounded-xl overflow-hidden text-center;

  & img {
    @apply w-full h-32 object-cover;
  }

  & h4,
  p {
    @apply px-3 py-2;
  }
}

.gradient-btn {
  @apply text-white bg-gradient-to-br from-purple-600 to-blue-500 focus:ring-4 focus:outline-none brightness-100 hover:brightness-125 transition-all focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg px-5 py-2.5 text-center;
}

.gradient-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.outline-btn-sm {
  @apply text-white bg-transparent rounded-lg px-3 py-2 text-center transition-all rounded relative bg-slate-700 hover:brightness-125;
}

.outline-btn-sm::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 4px;
  padding: 2px;
  background: linear-gradient(45deg, #9333ea, #3b82f6);
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* pagination.css */
.pagination {
  @apply flex flex-wrap sm:flex-nowrap list-none border border-gray-200 rounded-lg overflow-hidden max-w-max text-sm bg-gray-100;
}

.pagination a,
.pagination span,
.pagination em {
  @apply px-4 py-2 border-r border-gray-200 bg-white text-gray-700 hover:bg-gray-50 font-semibold;
}

.pagination .previous_page,
.pagination .next_page {
  @apply text-sm font-semibold px-4 py-2;
}

.pagination .disabled {
  @apply bg-gray-100 text-gray-400 cursor-not-allowed;
}

.pagination .current {
  @apply bg-de-brand text-white font-semibold;
}

.tooltip {
  position: fixed;
  background-color: black;
  color: white;
  text-align: center;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  visibility: hidden;
  /* Hide tooltip by default */
  pointer-events: none;
  /* Ensure tooltip doesn't block cursor events */
  z-index: 50;
  transition: visibility 0.2s;
}

.presentation-intro-text-shadow {
  text-shadow:
    -1px 0 #444,
    0 1px #444,
    1px 0 #444,
    0 -1px #444,
    0 0 16px #000;
}

.presentation-video-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  padding: 10px;
  /* Optional: to ensure there's a little space around the content */
}

.presentation-video-container::before {
  content: "";
  display: block;
  padding-top: 56.25%;
  /* for a 16:9 aspect ratio */
  height: 0;
}

.presentation-video-container>* {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  /* This will ensure the content fits within the container maintaining the aspect ratio */
  -webkit-object-fit: contain;
  /* For compatibility with Chrome */
}

.presentation-video-container .video-player {
  border-radius: 0;
  height: 100%;
}

#slides-navigation {
  @apply px-2;
  width: 160px;
  height: calc(100dvh - 120px);
  position: fixed;
  top: 60px;
  bottom: 60px;
  right: 0;
  z-index: 1000;
  transform: translateX(100%);
  border-radius: 8px 0 0 8px;
  overflow-y: auto;
  box-shadow: 0 0 13px #1e29388f;
}

#slides-navigation.transition {
  transition: transform 0.3s ease-in-out;
}

#slides-navigation.visible {
  transform: translateX(0);
}

#slides-navigation.pinned {
  height: 100dvh;
  top: 0;
  border-radius: 0;
}

#slides-navigation i {
  opacity: 0.4;
}

#slides-navigation.pinned i {
  opacity: 1;
}

.thumbnail {
  width: 100%;
  height: 100px;
  background-size: cover;
  background-position: center;
}

.form-errors {
  @apply border border-red-300 bg-red-100 text-sm text-red-800 p-4 rounded w-full mb-4;
}

.narration-wrapper.player-only {
  .narration-voice-selector {
    @apply hidden;
  }

  .narration-play-button i {
    @apply text-black;
  }

  .narration-auto-play-button {
    @apply hidden;
  }
}

.narration-voice-selector {
  @apply relative hidden md:block;

  .primary-button {
    @apply text-white rounded-b px-2 py-1;
  }

  .voice-list {
    @apply hidden w-fit bg-white overflow-hidden rounded-t absolute top-0 -translate-y-full;
  }
}

.narration-voice-selector.active {
  .primary-button {
    @apply bg-white text-black;
  }

  .voice-list {
    @apply block;
  }
}

.narration-voice-selector:not(.active) {
  .primary-button {
    @apply hover:rounded hover:bg-white hover:text-black;
  }
}

.back-button {
  @apply rounded py-2 px-4 hover:bg-gray-200 hover:bg-opacity-20 mb-2 w-fit;
}

.card {
  @apply bg-white shadow-lg rounded-lg p-4;
}

.sidebar-accordion {
  height: 100vh;
  width: 0;
  overflow: hidden;
  transition: width 0.3s ease-in-out;
  position: fixed;
  top: 0;
  left: 0;
  box-shadow: 0 2px 2px 1px rgba(0, 0, 0, 0.6);
  z-index: 500;

  &.active {
    width: 16rem;
  }

  &> :first-child {
    width: 16rem;
  }
}

.sidebar-scroll-section::-webkit-scrollbar {
  width: 8px;
}

.sidebar-scroll-section::-webkit-scrollbar-track {
  background: transparent;
  box-shadow: inset 0 0 5px #5fb2af;
  border-radius: 4px;
  border-left: 3.5px solid transparent;
  border-right: 3.5px solid transparent;
}

.sidebar-scroll-section::-webkit-scrollbar-thumb {
  background: #5FB2AF;
  border-radius: 10px;
}

.sidebar-accordion-btn {
  display: flex;
}

.content-container {
  transition: margin 0.3s ease-in-out;
}

@media screen and (min-width: 1100px) {
  .sidebar-accordion {
    z-index: 500;
    width: 16rem;
    box-shadow: none;
  }

  .sidebar-accordion-btn {
    display: none;
  }

  .content-container {
    margin-left: 16rem;
  }
}

.subscription-card {
  @apply bg-gradient-to-r from-blue-100 via-white to-blue-100 rounded-lg shadow-2xl p-8 w-full flex flex-col lg:flex-row items-center justify-between space-x-6 mb-4;

  & div:first-child h2 {
    @apply text-3xl font-bold text-gray-900 mb-4;
  }

  & div:first-child h4 {
    @apply text-xl font-bold text-gray-900 mb-4;
  }

  &>div:first-child p {
    @apply text-gray-700;
  }

  &>div:last-child {
    @apply min-w-36 mt-8 lg:mt-0;
  }
}

.active-subscription-card {
  @apply bg-gradient-to-r from-blue-100 via-white to-blue-100 rounded-lg shadow-2xl p-8 w-full flex justify-between mb-4 flex-col md:flex-row;

  & h2 {
    @apply text-3xl font-bold text-gray-900 mb-4;
  }

  & p {
    @apply text-gray-700;
  }
}

.icon-btn-lg {
  @apply bg-de-brand text-white rounded-full p-2 flex items-center justify-center h-10 w-10;
}

.icon-btn {
  @apply bg-de-brand text-white rounded-full p-2 flex items-center justify-center h-8 w-8;
}

.icon-btn-sm {
  @apply bg-de-brand text-white rounded-full p-2 flex items-center justify-center h-6 w-6 text-sm;
}

.nav-separator {
  @apply w-full;
  flex: 1;
}

.multi-btn {
  @apply flex rounded-lg overflow-hidden;

  .multi-btn-action {
    @apply text-white bg-de-brand focus:ring-4 focus:outline-none brightness-100 hover:brightness-125 transition-all font-medium rounded-l-lg px-5 py-2.5 text-center flex gap-2 items-center;
  }

  .multi-btn-icon {
    @apply text-black bg-white focus:ring-4 focus:outline-none brightness-100 hover:brightness-125 transition-all font-medium rounded-r-lg px-5 py-2.5 text-center;
  }
}

.tour-btn {
  @apply text-white bg-de-brand focus:ring-4 focus:outline-none brightness-100 hover:brightness-125 transition-all font-medium rounded-lg px-5 py-2.5 text-center;
}

.tour-tooltip {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  text-align: center;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  visibility: hidden;
  /* Hide tooltip by default */
  z-index: 100;
  transition: visibility 0.2s;
  padding: 8px;
  border-radius: 6px;
  box-sizing: content-box;
}

.tour-tooltip button {
  @apply rounded-full bg-transparent duration-100 hover:bg-de-blue hover:bg-opacity-50 text-lg font-semibold px-3 py-4 text-wrap;
}

.aspect-teaser {
  aspect-ratio: 7/4;
}

/* ####### */
/* Buttons */
/* ####### */
.btn {
  @apply hover:brightness-110 focus:ring-4 focus:outline-none focus:ring-cyan-800 font-medium text-center inline-flex justify-center items-center transition-[filter] cursor-pointer;

  &.btn-cyan {
    @apply from-de-brand to-blue-500 text-white rounded-lg bg-gradient-to-r;
  }

  &.btn-flat-cyan {
    @apply bg-de-brand text-white rounded-lg;
  }

  &.btn-purple {
    @apply from-purple-500 to-blue-500 text-white rounded-lg bg-gradient-to-r;
  }

  &.btn-indigo {
    @apply from-indigo-600 to-indigo-400 text-white rounded-lg bg-gradient-to-r;
  }

  &.btn-lime {
    @apply from-teal-200 to-lime-200 text-dark-blue rounded-lg bg-gradient-to-r;
  }

  &.btn-green {
    @apply from-green-600 to-green-400 text-white rounded-lg bg-gradient-to-r;
  }

  &.btn-mid-blue {
    @apply bg-mid-blue text-white rounded-lg;
  }

  &.btn-white {
    @apply from-white to-slate-200 text-dark-blue rounded-lg bg-gradient-to-r;
  }

  &.btn-flat-white {
    @apply bg-white outline outline-zinc-300 text-dark-blue rounded-lg hover:outline-zinc-500 outline-1;
  }

  &.btn-flat-red {
    @apply bg-red-600 outline outline-red-800 text-white rounded-lg hover:outline-zinc-500;
  }

  &.btn-dark-blue {
    @apply bg-dark-blue text-white rounded-lg;
  }

  &.btn-white-outline {
    @apply bg-transparent text-white border border-slate-400 rounded-lg;
  }

  &.btn-transparent {
    @apply bg-transparent text-white rounded-lg hover:bg-white/10;
  }

  &.btn-text-base {
    @apply text-base px-2 py-1.5 border-b-2 text-dark-blue border-dark-blue;
  }

  &.btn-base {
    @apply text-base px-5 py-2.5;
  }

  &.btn-sm {
    @apply text-sm px-4 py-2;
  }

  &.sm\:btn-base {
    @media (min-width: 640px) {
      font-size: 1rem;
      padding-left: 1.25rem;
      padding-right: 1.25rem;
      padding-top: 0.75rem;
      padding-bottom: 0.75rem;
    }
  }

  &.btn-lg {
    @apply text-lg px-6 py-3;
  }

  &.btn-block {
    @apply block;
  }

  &.btn-disabled {
    @apply opacity-50 pointer-events-none;
  }
}

.btn:disabled {
  @apply opacity-50 pointer-events-none;
}

.random-div {
  width: 100px;
  height: 100px;
  transition: transform 0.5s ease;
}

.square {
  border-radius: 0;
}

.circle {
  border-radius: 50%;
}

.rounded-square {
  border-radius: 15px;
}

.aspect-square {
  aspect-ratio: 1/1;
}

.tab-content {
  opacity: 0;
  transform: translateY(50px);
  transition:
    opacity 0.4s ease,
    transform 0.4s ease;
  height: 0;
  pointer-events: none;
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  visibility: hidden;
}

.tab-content.active {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
  position: relative;
  height: auto;
  pointer-events: all;
}

.tab-heading-active {
  @apply text-secondary-dark-blue border-secondary-dark-blue;
}

.swiper-container {
  --swiper-navigation-color: #fff;
  --swiper-navigation-size: 28;
  --swiper-navigation-top-offset: 25%;
}

.swiper-container {
  width: 100%;
}

.swiper-slide {
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-button-next,
.swiper-button-prev {
  background: #0000004f;
  height: 40px !important;
  width: 40px !important;
  border-radius: 50%;
}

/* PAGINATION */
.schools-pagination {
  & .pagination {
    @apply rounded-none border-0 gap-x-1 flex;
  }

  & a,
  & span,
  & em {
    @apply flex h-8 w-8 text-white p-0 justify-center items-center overflow-ellipsis border-r-0;
  }

  & .current,
  & .current:hover {
    @apply font-normal font-bold bg-dark-blue;
  }

  & a {
    @apply bg-dark-blue/50 no-underline;
  }

  & a:hover,
  & a:focus {
    @apply bg-mid-blue text-white;
  }

  & .page_info {
    @apply text-gray-500 pt-2;
  }

  & .gap {
    @apply text-black;
  }

  & .previous_page,
  & .next_page {
    @apply text-dark-blue bg-transparent;
  }

  & .disabled {
    @apply text-gray-500;
  }
}

div[name="share-to-classroom"] {
  :first-child {
    width: 60px !important;
    height: 48px !important;

    iframe {
      width: 100% !important;
      height: 100% !important;
    }
  }
}

.choices__list--multiple .choices__item {
  background-color: #282256 !important;
}


.force-white-text {
  color: white !important;

  * {
    color: white !important;
  }
}

.force-black-text {
  color: black !important;

  * {
    color: black !important;
  }
}

.feature-table {
  @apply max-w-full w-full overflow-x-auto overflow-y-hidden text-sm lg:text-base;

  & table {
    @apply w-full text-left text-gray-500 cursor-default relative;
    min-width: 900px;
  }

  & th {
    @apply px-4 py-3;
  }

  & tbody tr:not(:last-child) {
    @apply border-b border-gray-500;
  }

  & td {
    @apply px-4 py-3;
  }

  & td:not(:first-child) {
    @apply text-center;
  }
}

.admin-table-header {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.admin-badge {
  @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
  width: max-content;
}

/* Base styles for settings panel */
#settings-panel {
  display: none;
  z-index: 50;
  font-family: proxima-soft, sans-serif;
  letter-spacing: normal;
}

#settings-panel.visible {
  display: flex;
}

/* Settings content container */
#settings-panel .settings-content {
  max-width: 28rem;
  /* md:max-w-md = 28rem */
  width: 100%;
  position: relative;
  margin: 1rem;
  background: linear-gradient(to bottom right, #1a202c, #2d3748);
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  padding: 1.5rem;
  border: 1px solid #4a5568;
  transition: transform 0.3s ease, opacity 0.3s ease;
  max-height: 100dvh;
  overflow-y: auto;
}

/* Mobile full-screen styles */
@media (max-width: 640px) {
  #settings-panel .settings-content {
    max-width: 100%;
    width: 100%;
    height: 100%;
    margin: 0;
    border-radius: 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    padding-bottom: 0;
  }

  /* Ensure buttons at bottom remain visible */
  #settings-panel .settings-footer {
    background: linear-gradient(to bottom, rgba(26, 32, 44, 0.8), #1a202c);
    margin: 1rem -1.5rem -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
    backdrop-filter: blur(8px);
    border-top: 1px solid rgba(74, 85, 104, 0.5);
    position: sticky;
    bottom: 0;
  }
}

/* Decorative accent bar with gradient */
#settings-panel .accent-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, #3182ce, #805ad5, #d53f8c);
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

/* Settings sections styling */
#settings-panel .settings-section {
  margin-bottom: 1.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(74, 85, 104, 0.5);
}

#settings-panel .settings-section:last-of-type {
  border-bottom: none;
}

.shepherd-content {
  @apply bg-secondary-dark-blue text-white border border-gray-400;

  & .shepherd-header {
    @apply !bg-dark-blue;
  }

  & .shepherd-title {
    @apply !bg-dark-blue text-white;
  }

  & .shepherd-text {
    @apply text-white p-4;
  }

  & .shepherd-footer {
    @apply justify-end;
  }
}

.shepherd-modal-overlay-container.shepherd-modal-is-visible {
  @apply !opacity-80;
}

.ck-content ol,
.ck-content ul {
  @apply pl-8
}

.text-shadow-lg {
  text-shadow: 0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1), 0px 4px 8px rgb(0 0 0 / 0.1);
}

.text-shadow-md {
  text-shadow: 0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1), 0px 2px 4px rgb(0 0 0 / 0.1);
}


.flip-card-container {
  & .flip-card {
    perspective: 1000px;
    transition: transform 0.5s;
  }

  & .flipped {
      transform: rotateY(180deg);
  }

  & .flip-card-inner {
      transform-style: preserve-3d;
      transition: transform 0.5s;
  }

  & .flip-card-inner div {
      backface-visibility: hidden;
  }

  & .flip-card-front,
  .flip-card-back {
      position: absolute;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      & p {
          font-size: 1.2rem;
          line-height: 1.5rem;
      }
  }

  & .flip-card-front {
      transform: rotateY(0deg);
  }

  & .flip-card-back {
      transform: rotateY(180deg);
  }

  & h1, h2, h3, h4 {
      font-weight: bold;
  }

  & .flip-card, .flip-card-nohover {
      min-height: 336px;
      padding: 16px;
      border-radius: 16px;
      overflow: hidden;
      border: 1px solid #1e1945;

      & h3 {
          font-size: 1.875rem;
          line-height: 2.25rem;
      }

      & p {
          font-size: 1.25rem;
      }
  }

  & .flip-card:hover {
      border: 1px solid #322e56;
      transition: 0.2s ease-in-out;
  }
}

/*
========================================
DE AI Assistant - Unified Stylesheet
========================================
*/

@keyframes swirl-border {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes typing-dot {

  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }

  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* --- Base Container (Common styles for both versions) --- */
.de-ai-assistant-container {
  position: relative;
  z-index: 999;
  overflow: hidden;
  transition: max-height 0.4s cubic-bezier(0.25, 1, 0.5, 1);
}

.de-ai-assistant-container::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: -2;
  width: 700%;
  height: 700%;
  background: conic-gradient(from 90deg, #007cf0, #00dfd8, #ff0080, #ff8c00, #007cf0);
  animation: swirl-border 15s linear infinite;
}

.de-ai-assistant-content-wrapper {
  background-color: rgba(23, 27, 46, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-radius: inherit;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
}

/*
========================================
MODIFIER: .is-floating (Original Popup Editor)
========================================
*/

.de-ai-assistant-container.is-floating {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 2px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  transition: width 0.4s cubic-bezier(0.25, 1, 0.5, 1),
    max-height 0.4s cubic-bezier(0.25, 1, 0.5, 1),
    border-radius 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  width: 240px;
  max-height: 48px;
  border-radius: 24px;
}

.de-ai-assistant-container.is-floating.is-expanded {
  width: 100%;
  max-width: 700px;
  max-height: 70vh;
  border-radius: 0.85rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

.de-ai-assistant-container.is-floating:not(.is-expanded) .de-ai-trigger-btn {
  justify-content: center;
}

/*
========================================
MODIFIER: .is-static (New Lesson Assistant)
========================================
*/

.de-ai-assistant-container.is-static {
  width: 100%;
  border-radius: 0.85rem;
  padding: 2px;
  max-height: 48px;
}

/* Since the static widget is always expanded, its max-height is larger */
.de-ai-assistant-container.is-static.is-expanded {
  max-height: 60vh;
}

/* --- NEW: Make the trigger header non-clickable on the always-on static widget --- */
.de-ai-assistant-container.is-static.is-expanded .de-ai-trigger-btn {
  cursor: default;
}

/*
========================================
Child Elements (Styled Commonly)
========================================
*/

.de-ai-trigger-btn {
  font-weight: 600;
  color: white;
  background: none;
  border: none;
  cursor: pointer;
  width: 100%;
  min-height: 44px;
  padding: 0 1.5rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.125rem;
  justify-content: flex-start;
}

.de-ai-trigger-btn img {
  width: 24px;
}

.de-ai-assistant-container.is-expanded .de-ai-trigger-btn {
  padding: 0.75rem 1rem;
}

.de-ai-expandable-content {
  visibility: hidden;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease-in-out, opacity 0.3s ease-in 0.2s, visibility 0s 0.8s;
  padding: 0.75rem 1rem 1rem 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.de-ai-assistant-container.is-expanded .de-ai-expandable-content {
  visibility: visible;
  opacity: 1;
  max-height: calc(70vh - 52px);
  transition-delay: 0s, 0.2s, 0s;
}

.de-ai-assistant-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.de-ai-prompts {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  /* Now that the title is gone, remove the top border/padding */
  border-top: none;
  padding-top: 0;
}

#ai-lesson-initial-prompts {
  display: flex;
  flex-direction: column;
}

.de-ai-prompt-btn {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.7);
  padding: 0.35rem 0.75rem;
  font-size: 0.8rem;
  border-radius: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.de-ai-prompt-btn:hover:not(:disabled) {
  background-color: rgba(0, 124, 240, 0.3);
  border-color: rgba(0, 124, 240, 0.5);
  color: white;
}

.de-ai-prompt-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.de-ai-input-wrapper {
  position: relative;
  width: 100%;
}

.de-ai-textarea {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: 0.75rem 2.75rem 0.75rem 1rem;
  color: white;
  width: 100%;
  outline: none;
  transition: all 0.3s ease;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  line-height: 1.5;
  min-height: 50px;
  overflow-y: hidden;
}

.de-ai-textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.de-ai-textarea:focus:not(:disabled) {
  border-color: rgba(0, 124, 240, 0.8);
  box-shadow: 0 0 15px -2px rgba(0, 124, 240, 0.5);
}

.de-ai-textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.de-ai-submit-btn {
  position: absolute;
  right: 8px;
  bottom: 8px;
  width: 34px;
  height: 34px;
  background: #007cf0;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.de-ai-submit-btn:hover:not(:disabled) {
  background: #0056b3;
  transform: scale(1.1);
}

.de-ai-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* --- Chat History & Preview Panel --- */
.de-ai-chat-history {
  display: none;
  flex-direction: column;
  gap: 1rem;
  max-height: 250px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.de-ai-chat-message {
  display: flex;
  flex-direction: column;
  padding: 0.75rem 1rem;
  border-radius: 0.75rem;
  line-height: 1.5;
  color: white;
  max-width: 85%;
}

.de-ai-chat-message.is-user {
  background-color: #007cf0;
  align-self: flex-end;
  border-bottom-right-radius: 0.25rem;
}

.de-ai-chat-message.is-ai {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  align-self: flex-start;
  border-bottom-left-radius: 0.25rem;
}

.de-ai-chat-message.is-error {
  background-color: rgba(220, 38, 38, 0.2);
  border-color: rgba(220, 38, 38, 0.4);
}

.de-ai-chat-message .ai-label {
  font-size: 0.8rem;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 0.25rem;
}

.de-ai-chat-message.is-error .ai-label {
  color: rgba(255, 255, 255, 0.8);
}

/* --- AI Response Content Formatting --- */
.ai-response-content {
  margin-top: 0.5rem;
}

.ai-response-content p {
  margin: 0 0 1rem 0;
}

.ai-response-content p:last-child {
  margin-bottom: 0;
}

.ai-response-content ol,
.ai-response-content ul {
  margin: 0.75rem 0;
  padding-left: 1.5rem;
}

.ai-response-content ol {
  list-style-type: decimal;
}

.ai-response-content ul {
  list-style-type: disc;
}

.ai-response-content li {
  margin: 0.5rem 0;
  line-height: 1.5;
}

.ai-response-content strong {
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
}

.ai-response-content em {
  font-style: italic;
  color: rgba(255, 255, 255, 0.9);
}

/* --- Typing Indicator --- */
.de-ai-chat-message.is-typing {
  opacity: 0.8;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
  margin-top: 0.5rem;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.6);
  animation: typing-dot 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.16s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.32s;
}

#ai-preview-panel {
  display: none;
  flex-direction: column;
  gap: 0.75rem;
  background: rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(0, 124, 240, 0.4);
  border-radius: 0.75rem;
  position: relative;
  overflow: hidden;
}

.de-ai-preview-title {
  font-weight: 600;
  color: white;
  font-size: 0.9rem;
}

.de-ai-preview-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
}

.de-ai-assistant-container.has-response .de-ai-chat-history,
.de-ai-assistant-container.has-response #ai-preview-panel {
  display: flex;
}

/* --- "has-response" State Logic --- */
.de-ai-assistant-container.has-response .de-ai-chat-history,
.de-ai-assistant-container.has-response #ai-preview-panel {
  display: flex;
}

.de-ai-assistant-container.has-response #ai-lesson-initial-prompts {
  display: none;
}

.de-ai-assistant-container.has-response .de-ai-toggle-container,
.de-ai-assistant-container.has-response .de-ai-prompts {
  display: none;
}

.de-ai-toggle-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.de-ai-toggle-input {
  display: none;
}

.de-ai-toggle-switch {
  display: flex;
  position: relative;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 99px;
  padding: 4px;
  cursor: pointer;
  width: 100%;
}

.de-ai-toggle-highlighter {
  position: absolute;
  top: 4px;
  left: 4px;
  width: calc(50% - 4px);
  height: calc(100% - 8px);
  background: #007cf0;
  border-radius: 99px;
  transition: transform 0.3s cubic-bezier(0.25, 1, 0.5, 1);
}

.de-ai-toggle-option {
  flex: 1;
  text-align: center;
  font-size: 0.875rem;
  padding: 0.5rem 0;
  z-index: 1;
  transition: color 0.3s ease;
  color: white;
}

#ai-scope-toggle:checked+.de-ai-toggle-switch .de-ai-toggle-highlighter {
  transform: translateX(100%);
}

.de-ai-tooltip-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  font-size: 11px;
  font-style: italic;
  font-family: serif;
  color: rgba(255, 255, 255, 0.8);
  cursor: help;
}

.de-ai-tooltip-content {
  position: absolute;
  background-color: #1f2440;
  color: #fff;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 400;
  line-height: 1.4;
  width: 250px;
  text-align: left;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
}

.gradient-text {
  background: linear-gradient(135deg, #007cf0, #00dfd8, #ff0080);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease-in-out infinite;
}

/* This is for tables that are within a white background */
.internal-table {
  @apply w-full text-left outline outline-stone-200 rounded-lg;

  & thead {
    @apply bg-stone-100 font-semibold text-sm uppercase
  }

  & th {
    @apply px-2 py-4 text-gray-500 font-semibold;
  }

  & .sm-hidden {
    @apply hidden sm:table-cell;
  }

  & .md-hidden {
    @apply hidden md:table-cell;
  }

  & tbody tr {
    @apply border-t border-stone-200 hover:bg-stone-50 transition-colors duration-200;
  }

  & td {
    @apply px-2 py-4 text-gray-700;
  }
}

.full-height-video-container .video-player {
  height: 100%;
}
