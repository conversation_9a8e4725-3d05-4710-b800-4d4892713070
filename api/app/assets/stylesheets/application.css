.contains-link a:link {
  color: #39b5b1;
}

.contains-link a:visited {
  color: #551a8b;
}

#toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.toast {
  background-color: #f5f5f5;
  color: #666;
  padding: 12px 20px 12px;
  margin-bottom: 10px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  width: 300px;
  opacity: 0;
  transition:
    opacity 0.5s,
    transform 0.5s;
  transform: translateX(100%);
  overflow: hidden;
}

.toast .close-icon {
  margin-left: 20px;
  cursor: pointer;
  position: relative;
  z-index: 2;
}

.toast .progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #e5e5e5;
  height: 100%;
  width: 0%;
  transition: width 0.5s ease-in-out;
}

.toast .message {
  position: relative;
  z-index: 2;
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

input.choices__input.choices__input--cloned {
  display: inline !important;
  width: auto !important;
  border: 0 !important;
  padding: 4px !important;
}
label[required]::after {
  content: " *";
  color: red;
}

svg > g > g.google-visualization-tooltip {
  pointer-events: none
}