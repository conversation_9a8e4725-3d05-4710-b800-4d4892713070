# == Schema Information
#
# Table name: template_recommendations
#
#  id                      :bigint           not null, primary key
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  recommended_by_id       :bigint
#  recommended_template_id :bigint
#
# Indexes
#
#  index_template_recommendations_on_recommended_by_id        (recommended_by_id)
#  index_template_recommendations_on_recommended_template_id  (recommended_template_id)
#
# Foreign Keys
#
#  fk_rails_...  (recommended_by_id => lesson_templates.id)
#  fk_rails_...  (recommended_template_id => lesson_templates.id)
#
class TemplateRecommendation < ApplicationRecord
  belongs_to :recommended_template, class_name: "Lesson::Template"
  belongs_to :recommended_by, class_name: "Lesson::Template"
end
