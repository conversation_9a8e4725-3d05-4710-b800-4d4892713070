# == Schema Information
#
# Table name: user_rank_events
#
#  id              :bigint           not null, primary key
#  event_name      :string
#  is_global       :boolean
#  notified        :boolean          default(FALSE)
#  points          :integer
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  user_id         :integer
#  user_ranking_id :bigint
#
# Indexes
#
#  index_user_rank_events_on_user_ranking_id  (user_ranking_id)
#
class UserRankEvent < ApplicationRecord
  validates :event_name, :points, presence: true

  def self.add_points pupil, points, event_name
    return unless pupil&.pupil?

    self.create!(
      user_id: pupil.id,
      points: points,
      event_name: event_name,
      is_global: true
    )
  end

  def self.user_rank(user_id, date, user_ids = nil, aliases = true)
    return { rank: nil, score: 0 } if user_ids.is_a?(Array) && user_ids.empty?

    start_date = date.beginning_of_week
    end_date = date.end_of_week

    query = <<-SQL
      SELECT *
      FROM (
        SELECT
          user_id,
          SUM(points) AS score,
          RANK() OVER (ORDER BY SUM(points) DESC) AS rank,
          (SELECT #{aliases ? 'alias' : 'name'} FROM users WHERE id = user_id) AS name,
          CASE WHEN user_id IS NOT NULL AND user_id = #{user_id} THEN true ELSE false END AS me
        FROM user_rank_events
        WHERE is_global = true
          AND created_at BETWEEN '#{start_date}' AND '#{end_date}'
          #{user_ids ? "AND user_id IN (#{user_ids.join(',')})" : ''}
        GROUP BY user_id
      ) ranked_users
      WHERE user_id = #{user_id}
      LIMIT 1
    SQL

    result = find_by_sql(query).first

    if result
      { rank: result.rank, score: result.score }
    else
      { rank: nil, score: 0, user_id: user_id, me: false, name: User.find(user_id)[aliases ? :alias : :name] }
    end
  end

  def self.all_user_ranks(date, user_ids = nil, aliases=true)
    return [] if user_ids.is_a?(Array) && user_ids.empty?

    start_of_week = date.beginning_of_week
    end_of_week = date.end_of_week

    query = <<-SQL
      SELECT
        user_id,
        (SELECT #{aliases ? "alias" : "name"} FROM users WHERE id = user_id) AS name,
        SUM(points) AS score,
        RANK() OVER (ORDER BY SUM(points) DESC) AS rank,
        false AS me
      FROM user_rank_events
      WHERE is_global = true
        AND created_at BETWEEN '#{start_of_week}' AND '#{end_of_week}'
        #{user_ids ? "AND user_id IN (#{user_ids.join(',')})" : ''}
      GROUP BY user_id
      ORDER BY rank
    SQL

    find_by_sql(query)
  end

  def self.top_n_users(n, date, user_ids = nil, aliases=true)
    return [] if user_ids.is_a?(Array) && user_ids.empty?

    start_of_week = date.beginning_of_week
    end_of_week = date.end_of_week

    query = <<-SQL
      SELECT *
      FROM (
        SELECT
          user_id,
          (SELECT #{aliases ? "alias" : "name"} FROM users WHERE id = user_id) AS name,
          SUM(points) AS score,
          RANK() OVER (ORDER BY SUM(points) DESC) AS rank,
          false AS me
        FROM user_rank_events
        WHERE is_global = true
          AND created_at BETWEEN '#{start_of_week}' AND '#{end_of_week}'
          #{user_ids ? "AND user_id IN (#{user_ids.join(',')})" : ''}
        GROUP BY user_id
      ) ranked_users
      WHERE rank <= #{n}
      ORDER BY rank
    SQL

    find_by_sql(query)
  end

  def self.users_around(user_id, date, user_ids = nil)
    return [] if user_ids.is_a?(Array) && user_ids.empty?

    start_of_week = date.beginning_of_week
    end_of_week = date.end_of_week

    base_query = <<-SQL
      SELECT
        user_id,
        (SELECT alias FROM users WHERE id = user_id) AS name,
        SUM(points) AS score,
        RANK() OVER (ORDER BY SUM(points) DESC) AS rank,
        user_id=#{user_id} AS me
      FROM user_rank_events
      WHERE is_global = true
        AND created_at BETWEEN '#{start_of_week}' AND '#{end_of_week}'
        #{user_ids ? "AND user_id IN (#{user_ids.join(',')})" : ''}
      GROUP BY user_id
    SQL

    # Get the rank of the specified user
    user_rank_result = find_by_sql(<<-SQL).first
      SELECT rank FROM (#{base_query}) ranked_users WHERE user_id = #{user_id}
    SQL

    return [] unless user_rank_result

    user_rank = user_rank_result.rank

    # Get users within the range of the target rank ±4
    find_by_sql(<<-SQL)
      SELECT * FROM (#{base_query}) ranked_users
      WHERE rank BETWEEN #{user_rank - 4} AND #{user_rank + 4}
      ORDER BY rank
    SQL
  end

  def as_json(options = {})
    super(options).merge('me' => self['me'] || false)
  end

  def self.gen_data
    return if Rails.env.production?
    UserRankEvent.delete_all
    school_ids = [
      1,
      50,
    ]
    school_ids.each do |school_id|
      p = 0
      user_ids = School.find(school_id).pupil_ids

      start_date = 3.weeks.ago.beginning_of_week
      end_date = Time.now.end_of_week

      user_ranking_id = UserRanking.first.id
      user_ids.each do |user_id|
        20.times do

          # random between start and end
          date =  rand(start_date..end_date)
          p += 1
          points = rand(1..100)

          UserRankEvent.create!(
            event_name: 'test',
            points: points,
            created_at: date,
            user_id: user_id,
            is_global: true
          )
        end
      end
    end
  end

  def self.leaderboard_data(date, user_id, user_ids = nil)
    return [] if user_ids.is_a?(Array) && user_ids.empty?

    own_rank = user_rank(user_id, date, user_ids)
    own_rank[:me] = true

    # get the rows for top 10 scores
    top_rows = top_n_users(10, date, user_ids)
    # get top 10 rows
    top_rows_inc_self = top_rows.slice(0, 10)
    # ensure the user is still included in this data. if they aren't shove them on the end
    top_rows_inc_self.push(own_rank) unless top_rows_inc_self.detect { |row| row['user_id'] == user_id }

    surrounding_rows = users_around(user_id, date, user_ids)

    top_row = top_rows.detect { |row| row['user_id'] == user_id }
    top_row.me = true if top_row.respond_to?(:me) # check if responds to me as the own_rank is a hash

    s_row = surrounding_rows.detect { |row| row['user_id'] == user_id }
    s_row.me = true if s_row.respond_to?(:me) # check if responds to me as the own_rank is a hash

    return {
      topRows: top_rows,
      rows: surrounding_rows,
      top10: top_rows_inc_self
    }
  end
end
