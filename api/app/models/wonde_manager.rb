require 'uri'
require 'net/http'

class WondeManager
  class << self
    def request_access school
      wonde_school = get_wonde_school(school)
      if !wonde_school && school.wonde_request_status == 'not_requested'
        request('post', "schools/#{school.wonde_id}/request-access", data: {
          contacts: school.teachers.map do |teacher|
            {
              first_name: teacher.name.split(" ")[0],
              last_name: teacher.name.split(" ")[1],
              email_address: teacher.email
            }
          end
        })
        school.update(wonde_request_status: :pending)
        puts "access requested for #{school.name} #{school.wonde_id}"
        return { message: "access requested for #{school.name} #{school.wonde_id}" }
      end
      if wonde_school
        return { message: "This school can already be viewed" }
      else
        return { message: "This school has already already been requested" }
      end
    end

    def get_wonde_school school
      if school.wonde_request_status != 'active'
        wonde_schools = request('get', 'schools') # list of approved schools
        selected = wonde_schools.dig('data').select { |s| s.dig('id') == school.wonde_id }[0]
        if selected
          school.update!(wonde_request_status: :active)
        else
          return nil
        end
      end

      wonde_school = request("get", "schools/#{school.wonde_id}")
      if wonde_school && wonde_school.dig("errored").present?
        raise wonde_school.dig("errored")
      end

      if wonde_school && school.wonde_request_status != "active"
        school.update!(wonde_request_status: :active)
      end

      return wonde_school.dig("data") if wonde_school
    end

    def valid_school school
      wonde_school = get_wonde_school(school)
      return !!wonde_school && school.wonde_request_status == "active"
    end

    def sync_school school
      return if !school || !school.wonde_id
      wonde_school = get_wonde_school(school)
      return unless wonde_school
      school.update!({ name: wonde_school.dig("name"), postcode: wonde_school.dig("address", "address_postcode") }.compact)
      return wonde_school
    end

    def import_was_cancelled(import_id)
      import = WondeImport.find(import_id)
      if import.import_status == "cancelled"
        if !import.wonde_import_error
          WondeImportError.create({ error_data: { fatal: ["Import was cancelled"] }, warning_data: {}, wonde_import: import })
        end
        return true
      end
      return false
    end

    def load_paginated_data(wonde_id, page, data_groups)
      puts "WONDE: load paginted data for #{wonde_id} - page #{page}"
      classes = request(
        "get",
        "schools/#{wonde_id}/groups",
        params: {
          include: "students,employees,employees.contact_details",
          type: "REGISTRATION",
          page: page
        }
      )

      raise classes['error_description'] if classes && classes['error']

      data_groups << classes
      if classes.dig("meta", "pagination", "more") == true
        load_paginated_data(wonde_id, page + 1, data_groups)
      end
      data_groups
    end

    def sync_data(school, retain_data = false, disable_error_email: false)
      return if !school || !school.wonde_id || !valid_school(school)
      import_record = school.wonde_imports.create(success: false, retain_data: retain_data)
      sync_data_from_record(import_record, school, disable_error_email: disable_error_email)
    end

    def sync_data_from_record(import_record, school, disable_error_email: false)
      return if !school || !school.wonde_id || !valid_school(school)
      import_id = import_record.id
      begin
        return if import_was_cancelled(import_id)

        # fetch data as array of classes data from wonde request
        data_groups = load_paginated_data(school.wonde_id, 1, [])

        import_record.update(raw_wonde_cache: data_groups)

        forms = []
        teachers = []
        pupils = []

        # calculate counts for teachers, pupils, classes
        data_groups.each do |classes|
          break if import_was_cancelled(import_id)
          raise "WondeManager: sync_school(#{school.id}) - classes does not contain data" if !classes.dig("data")
          # Build counts for front end before starting import
          forms += classes.dig("data")

          classes.dig("data").each do |class_data|
            teachers += class_data.dig("employees", "data")
            pupils += class_data.dig("students", "data")
          end
        end

        forms = forms.uniq { |form| form.dig("id") }
        teachers = teachers.uniq { |teacher| teacher.dig("id") }
        pupils = pupils.uniq { |pupil| pupil.dig("id") }

        count_forms = forms.length
        teacher_ids_arr = teachers.map { |teacher| teacher.dig("id") }
        pupil_ids_arr = pupils.map { |pupil| pupil.dig("id") }

        # Set counts for front end display
        import_record.update(
          wonde_teachers_count: teacher_ids_arr.flatten.uniq.length,
          wonde_pupils_count: pupil_ids_arr.flatten.uniq.length,
          wonde_forms_count: count_forms,
        )

        wonde_pupil_ids_to_devex_ids = insert_many_wonde_pupils(import_record, pupils)
        wonde_teacher_ids_to_devex_ids = insert_many_wonde_teachers(import_record, teachers)
        # ----------------------------------------------------

        # Map over the data groups and build clases. functionally not changed, just now is run on
        # multiple sets of data.
        data_groups.each do |classes|
          break if import_was_cancelled(import_id)

          classes.dig("data")&.each do |class_data|
            break if import_was_cancelled(import_id)
            wonde_form = wonde_class(import_record, class_data)

            #############
            ###### PUPILS
            #############
            pupil_sql_values = class_data.dig("students", "data")&.map do |student|
              break if import_was_cancelled(import_id)
              pupil_id = wonde_pupil_ids_to_devex_ids[student.dig("id")]
              "(#{pupil_id}, #{wonde_form.id}, #{quote(Time.now)}, #{quote(Time.now)})"
            end
            sql = "" "
              INSERT INTO wonde_import_pupil_forms (wonde_import_pupil_id, wonde_import_form_id, created_at, updated_at)
              VALUES #{pupil_sql_values.compact.join(",")}
            " ""
            ApplicationRecord.connection.execute(sql) if pupil_sql_values.any?

            ###############
            ###### TEACHERS
            ###############

            teacher_sql_values = class_data.dig("employees", "data")&.map do |employee|
              break if import_was_cancelled(import_id)
              teacher_id = wonde_teacher_ids_to_devex_ids[employee.dig("id")]
              "(#{teacher_id}, #{wonde_form.id}, #{quote(Time.now)}, #{quote(Time.now)})"
            end
            sql = "" "
              INSERT INTO wonde_import_teacher_forms (wonde_import_teacher_id, wonde_import_form_id, created_at, updated_at)
              VALUES #{teacher_sql_values.compact.join(",")}
            " ""

            ApplicationRecord.connection.execute(sql) if teacher_sql_values.any?
          end
        end

        return if import_was_cancelled(import_id)
        WondeImport.find(import_id).update!(success: true, import_status: 'complete')
      rescue => e
        if Rails.env.production?
          ErrorLog.create(error: { errorData: e, backtrace: e.backtrace }.to_json, skip_email: disable_error_email)
        end
        puts e
        record = WondeImport.find(import_id)
        WondeImportError.create({
          error_data: { fatal: ['A fatal error occurred', e.message], raw: e&.cause&.message },
          warning_data: {},
          wonde_import: record
        })
        new_attempt_count = record.school.wonde_attempt_count + 1
        if new_attempt_count > 1
          record.school&.update(
            wonde_attempt_count: new_attempt_count,
            admin_notes: "Wonde import has failed #{new_attempt_count} times, automatic sync has been disabled.",
            sync_wonde_weekly: false
          )
        else
          record.school&.update(wonde_attempt_count: new_attempt_count)
        end
        record.update(success: false, import_status: 'cancelled')
      end
      import_id
    end

    def wonde_class import_record, class_data
      school = import_record.school
      form = school.forms.current_school_year.find_by_wonde_id(class_data.dig("id"))
      form = school.forms.current_school_year.find_by_name(class_data.dig("name")) unless form
      import_record.wonde_import_forms.create!({
                                                 wonde_id: class_data.dig("id"),
                                                 wonde_data: class_data.except("students", "employees"),
                                                 form: form,
                                                 name: class_data.dig("name")
                                               })
    end

    def quote str
      ApplicationRecord.connection.quote(str)
    end

    def insert_many_wonde_pupils(import_record, pupils)
      values = []

      # Construct an array of all wonde_ids we're interested in.
      wonde_ids = pupils.map { |pupil_data| pupil_data.dig("id") }

      # Fetch all pupils with those wonde_ids in one query.
      existing_pupils = import_record.school.pupils.where(wonde_id: wonde_ids).index_by(&:wonde_id)

      pupils.each do |pupil_data|
        pupil = existing_pupils[pupil_data.dig("id")]

        values << wonde_pupil_sql_values(import_record, pupil_data, pupil)
      end
      if values.any?
        sql = "" "
          INSERT INTO wonde_import_pupils (wonde_id, wonde_data, wonde_import_id, created_at, updated_at, user_id, name, dob, gender)
          VALUES #{values.join(',')}
          RETURNING id, wonde_id
        " ""

        results = ApplicationRecord.connection.execute(sql)

        wonde_pupil_ids_to_devex_ids = {}
        results.each do |result|
          wonde_pupil_ids_to_devex_ids[result["wonde_id"]] = result["id"]
        end

        return wonde_pupil_ids_to_devex_ids
      else
        return {}
      end
    end

    def wonde_pupil import_record, pupil_data
      school = import_record.school
      pupil = school.pupils.find_by_wonde_id(pupil_data.dig("id"))
      pupil = school.pupils.find_by_name("#{pupil_data.dig("forename")} #{pupil_data.dig("surname")}") if !pupil

      sql = "" "
          INSERT INTO wonde_import_pupils (wonde_id, wonde_data, wonde_import_id, created_at, updated_at, user_id, name, dob, gender)
          VALUES %s
          RETURNING id, wonde_id
        " "" % [
        wonde_pupil_sql_values(import_record, pupil_data, pupil)
      ]

      result = ApplicationRecord.connection.execute(sql)

      return { id: result.first["id"], wonde_id: result.first["wonde_id"] }
    end

    def wonde_pupil_sql_values import_record, pupil_data, pupil
      school = import_record.school

      return "(%s, %s, %s, %s, %s, %s, %s, %s, %s)" % [
        quote(pupil_data.dig("id")),
        quote(pupil_data.to_json),
        quote(import_record.id),
        quote(Time.now),
        quote(Time.now),
        quote(pupil&.id),
        quote("#{pupil_data.dig("forename")} #{pupil_data.dig("surname")}"),
        quote(pupil_data.dig("date_of_birth", "date")),
        quote(pupil_data.dig("gender")&.downcase || '')
      ]
    end

    def insert_many_wonde_teachers import_record, teachers
      values = []

      # Construct an array of all wonde_ids we're interested in.
      wonde_ids = teachers.map { |teacher_data| teacher_data.dig("id") }
      emails = teachers.map { |teacher_data| get_email_from_teacher_data(teacher_data) }

      # Fetch all teachers with those wonde_ids in one query.
      existing_teachers = import_record.school.teachers.where(wonde_id: wonde_ids).index_by(&:wonde_id)
      existing_teachers_by_email = import_record.school.teachers.where("lower(email) in (?)", emails.select(&:present?).map(&:downcase)).index_by { |user| user.email.downcase }

      teachers.each do |teacher_data|
        teacher = existing_teachers[teacher_data.dig("id")]
        teacher = existing_teachers_by_email[get_email_from_teacher_data(teacher_data)&.downcase] if !teacher

        values << wonde_teacher_sql_values(import_record, teacher_data, teacher)
      end

      if values.any?
        sql = "" "
          INSERT INTO wonde_import_teachers (wonde_id, wonde_data, wonde_import_id, created_at, updated_at, user_id, name, email)
          VALUES #{values.join(',')}
          RETURNING id, wonde_id
        " ""

        results = ApplicationRecord.connection.execute(sql)

        wonde_teacher_ids_to_devex_ids = {}
        results.each do |result|
          wonde_teacher_ids_to_devex_ids[result["wonde_id"]] = result["id"]
        end

        return wonde_teacher_ids_to_devex_ids
      else
        return {}
      end
    end

    def get_email_from_teacher_data teacher_data
      teacher_data.dig("contact_details", "data", "emails", "work") || teacher_data.dig("contact_details", "data", "emails", "primary") || teacher_data.dig("contact_details", "data", "emails", "email")
    end

    def wonde_teacher_sql_values import_record, teacher_data, teacher
      school = import_record.school

      return "(%s, %s, %s, %s, %s, %s, %s, %s)" % [
        quote(teacher_data.dig("id")),
        quote(teacher_data.to_json),
        quote(import_record.id),
        quote(Time.now),
        quote(Time.now),
        quote(teacher&.id),
        quote("#{teacher_data.dig("forename")} #{teacher_data.dig("surname")}"),
        quote(teacher&.email || get_email_from_teacher_data(teacher_data))
      ]
    end

    def wonde_teacher import_record, teacher_data
      school = import_record.school
      teacher = school.teachers.find_by_wonde_id(teacher_data.dig("id"))
      email = get_email_from_teacher_data(teacher_data)
      if !teacher
        teacher = school.teachers.find_by("email ILIKE ?", email)
      end
      import_record.wonde_import_teachers.create!({
                                                    wonde_id: teacher_data.dig("id"),
                                                    wonde_data: teacher_data,
                                                    user: teacher,
                                                    name: "#{teacher_data.dig("forename")} #{teacher_data.dig("surname")}",
                                                    email: email,
                                                  })
    end

    def get_access_token_sign_up(code)
      params = {
        grant_type: 'authorization_code',
        client_id: ENV["WONDE_CLIENT_ID"],
        client_secret: ENV["WONDE_CLIENT_SECRET"],
        redirect_uri: ENV["WONDE_REDIRECT_URI"],
        code: code,
      }
      url = URI('https://api.wonde.com/oauth/token')
      http = Net::HTTP.new(url.host, url.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      request = Net::HTTP::Post.new(url)
      request["accept"] = 'application/json'
      request["content-type"] = 'application/json'
      request.body = params.to_json
      response = http.request(request)
      if response.kind_of? Net::HTTPSuccess
        body = JSON.parse(response.body)
        return body["access_token"]
      else
        raise response.message
        return nil
      end
    end

    def get_access_token_connect_account(code)
      params = {
        grant_type: 'authorization_code',
        client_id: ENV["WONDE_CLIENT_ID"],
        client_secret: ENV["WONDE_CLIENT_SECRET"],
        redirect_uri: ENV["WONDE_REDIRECT_CONNECT_URI"],
        code: code,
      }
      url = URI('https://api.wonde.com/oauth/token')
      http = Net::HTTP.new(url.host, url.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      request = Net::HTTP::Post.new(url)
      request["accept"] = 'application/json'
      request["content-type"] = 'application/json'
      request.body = params.to_json
      response = http.request(request)
      if response.kind_of? Net::HTTPSuccess
        body = JSON.parse(response.body)
        return body["access_token"]
      else
        raise response.message
        return nil
      end
    end

    def get_wonde_sso_user(access_token)
      query = <<-GRAPHQL
        {
          Me {
            id
            Person {
              __typename
              ... on Student {
                id
                type
                forename
                surname
                School {
                  id
                  name
                  address_postcode
                  country
                  phase_of_education
                  urn
                }
              }
              ... on Contact {
                id
                type
                forename
                surname
                School {
                  id
                  name
                  address_postcode
                  country
                  phase_of_education
                  urn
                }
              }
              ... on Employee {
                id
                ContactDetails {
                  email
                  email_primary
                }
                type
                forename
                surname
                School {
                  id
                  name
                  address_postcode
                  country
                  phase_of_education
                  urn
                }
              }
            }
          }
        }
      GRAPHQL

      params = {
        query: query,
      }
      url = URI('https://api.wonde.com/graphql/me')
      http = Net::HTTP.new(url.host, url.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      request = Net::HTTP::Post.new(url)
      request["Authorization"] = "Bearer #{access_token}"
      request["accept"] = 'application/json'
      request["content-type"] = 'application/json'
      request.body = params.to_json
      response = http.request(request)
      if response.kind_of? Net::HTTPSuccess
        return JSON.parse(response.body)
      else
        puts "Request Failure!"
        return nil
      end
    end

    private

    def request(method, url, data: nil, params: nil, access_token: ENV["WONDE_API_TOKEN"])
      base_url = "https://api.wonde.com/v1.0/"
      url = URI(base_url + url)
      if params.present?
        params_data = Hash[URI.decode_www_form(url.query || '')].merge(params)
        url.query = URI.encode_www_form(params_data)
      end
      http = Net::HTTP.new(url.host, url.port)
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      if method == "get"
        request = Net::HTTP::Get.new(url)
      elsif method == "patch"
        request = Net::HTTP::Patch.new(url)
      elsif method == "post"
        request = Net::HTTP::Post.new(url)
      else
        return nil
      end
      request["accept"] = 'application/json'
      request["content-type"] = 'application/json'
      request["Authorization"] = "bearer #{access_token}"
      request.body = data.to_json if data
      response = http.request(request)
      JSON.parse(response.body)
    end
  end
end
