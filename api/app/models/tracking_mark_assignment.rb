# == Schema Information
#
# Table name: tracking_mark_assignments
#
#  id                 :bigint           not null, primary key
#  comments           :string
#  mark               :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_template_id :bigint
#  user_id            :bigint
#
# Indexes
#
#  index_tracking_mark_assignments_on_lesson_template_id  (lesson_template_id)
#  index_tracking_mark_assignments_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (user_id => users.id)
#
class TrackingMarkAssignment < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :user
end
