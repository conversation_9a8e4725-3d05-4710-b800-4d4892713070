# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                                    :bigint           not null, primary key
#  admin_permissions                     :jsonb
#  alias                                 :string
#  avatar_configuration                  :jsonb
#  beta_features                         :jsonb
#  blocked_at                            :datetime
#  can_access_career_builder             :boolean
#  can_access_lesson_ai                  :boolean
#  can_create_lessons                    :boolean          default(FALSE)
#  can_view_school_data                  :boolean          default(FALSE)
#  communication_preferences             :jsonb
#  deleted                               :boolean          default(FALSE), not null
#  dob                                   :datetime
#  education                             :string           default("")
#  email                                 :citext           not null
#  ethnicity                             :integer
#  gender                                :string
#  geography_lead                        :boolean          default(FALSE)
#  glossary_list                         :string           default([]), is an Array
#  has_career_profile                    :boolean          default(FALSE), not null
#  hs_visitor_cache                      :jsonb
#  hubspot_marketing_email_name_received :string
#  identifier                            :string
#  import_data                           :jsonb
#  is_blocked                            :boolean          default(FALSE)
#  is_restricted                         :boolean          default(FALSE), not null
#  is_school_admin                       :boolean
#  job_hunter_interests                  :string
#  job_title                             :string
#  last_activity_at                      :datetime
#  last_sign_in_at                       :datetime
#  last_sign_out_at                      :datetime
#  last_sync_hubspot_activity_at         :datetime
#  lead_source                           :integer
#  linked_in_url                         :string           default("")
#  location                              :string
#  login_count                           :integer          default(0)
#  mailchimp_last_error                  :text
#  mailchimp_last_sync                   :datetime
#  mailchimp_last_sync_attempt           :datetime
#  mailchimp_list                        :string
#  mailchimp_sync_status                 :string           default("pending")
#  name                                  :string
#  onboarded                             :boolean
#  password_digest                       :string           not null
#  phone                                 :string
#  points                                :integer          default(0), not null
#  prefered_presentation_voice           :string           default("fable")
#  preferred_location                    :string
#  presentation_settings                 :jsonb
#  profile_color                         :jsonb
#  provider                              :string           not null
#  questionnaire_taken                   :boolean          default(FALSE)
#  recovery_requested_at                 :datetime
#  recovery_token                        :string
#  referral_actioned                     :boolean          default(FALSE)
#  referral_code                         :string
#  referral_share_name                   :boolean          default(TRUE), not null
#  require_password_reset                :boolean
#  science_lead                          :boolean          default(FALSE)
#  session_token                         :string
#  sign_in_token                         :string
#  specified_class                       :string
#  specified_year                        :string
#  tasks_completed_cache                 :jsonb
#  tokens                                :json
#  type                                  :string
#  uid                                   :string           default(""), not null
#  unique_wonde_identifier               :string
#  use_new_presentation_player           :boolean
#  work_experience                       :jsonb            is an Array
#  working_days                          :string
#  years_of_experience                   :string           default("")
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  cv_fileboy_id                         :string
#  fileboy_image_id                      :string
#  hubspot_id                            :string
#  mailchimp_id                          :string
#  my_login_id                           :string
#  organisation_id                       :bigint
#  referred_from_user_id                 :integer
#  school_id                             :bigint
#  stripe_customer_id                    :string
#  white_label_organisation_id           :bigint
#  wonde_id                              :string
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE WHERE (NOT deleted)
#  index_users_on_identifier_and_school_id        (identifier,school_id) UNIQUE WHERE (NOT deleted)
#  index_users_on_organisation_id                 (organisation_id)
#  index_users_on_school_id                       (school_id)
#  index_users_on_sign_in_token                   (sign_in_token) UNIQUE
#  index_users_on_uid_and_provider                (uid,provider) UNIQUE WHERE (NOT deleted)
#  index_users_on_white_label_organisation_id     (white_label_organisation_id)
#  index_users_unique_wonde_id_where_not_deleted  (wonde_id) UNIQUE WHERE ((deleted = false) AND (wonde_id IS NOT NULL) AND ((wonde_id)::text <> ''::text))
#
# Foreign Keys
#
#  fk_rails_...  (referred_from_user_id => users.id)
#  fk_rails_...  (white_label_organisation_id => organisations.id)
#
class Teacher < User
  has_many :lessons, through: :forms

  has_many :pupils, through: :forms
  has_many :pupil_results, through: :pupils, source: :lesson_results
  has_many :all_pupil_results, through: :pupils, source: :all_lesson_results

  has_many :school_invites, foreign_key: :inviter_id, dependent: :destroy

  scope :for_users, -> (user_ids) { joins(forms: :enrollments).where(forms: { enrollments: { user_id: user_ids } }) }

  scope :real_school, -> { where(id: real) }

  scope :active, -> { where('last_activity_at >= ?', 6.months.ago) }

  attr_accessor :should_validate_domain
  validate :validate_domain, if: :should_validate_domain

  after_create do
    next unless school && !school.setup_checklist[:create_teachers]
    school.complete_checklist_item(:create_teachers)
  end

  after_create do
    next if Rails.env.development?
    next unless school.generic?
    next if school&.is_industry_account?
    begin
      Mailchimp::SyncService.new.sync_user(self, trigger_type: 'user_update')
    rescue => e
      puts e
      puts e.backtrace
    end
  end

  after_create do
    next unless school.generic?
    next if school.is_industry_account?

    SyncTeacherHubspotJob.perform_later(self) unless Rails.env.development?
  end

  after_update do
    next unless school&.generic?
    next if school.is_industry_account?

    SyncTeacherHubspotJob.perform_later(self) unless Rails.env.development?
  end

  def domain_validation
    !!school&.domain_validation(email)
  end

  def validate_domain
    errors.add(:email, "invalid email domain, please contact support to use the #{email.downcase.split("@").last} domain") unless domain_validation
  end

  def self.real
    joins(:school).where('schools.school_type = ?', School.school_types[:generic])
  end

  def send_welcome_email
    new_password = SecureRandom.hex(6).to_s + 'aA1@'
    update(password: new_password)
    SchoolMailer.welcome_email(self, new_password).deliver_later
  end

  # results from this teacher's lessons
  def lesson_results
    Lesson::Result.where(id:
                           Lesson::Result.joins(<<-SQL.squish)
        JOIN lesson_lessons
          ON lesson_lessons.template_id = lesson_results.template_id
        JOIN enrollments pupil_enrollments
          ON pupil_enrollments.form_id = lesson_lessons.form_id
          AND pupil_enrollments.user_id = lesson_results.pupil_id
        JOIN enrollments teacher_enrollments
          ON teacher_enrollments.form_id = lesson_lessons.form_id
          AND teacher_enrollments.user_id = #{id}
                           SQL
    )
  end

  def task_progress
    has_xls_import = XlsImport.where(school: school, complete: true).exists?
    task_list = {
      profile: true,
      class: forms.where('name NOT ILIKE ?', '%example%').exists?,
      pupils: pupils.count >= 20,
      course: lessons.where('lesson_lessons.created_at > ?', school.created_at + 1.minute).exists?,
      marks: lesson_results.where.not(mark: [nil, 0]).exists?,
      import: has_xls_import || school.forms.count >= 3,
      library: tracking_presentation_views.count >= 10,
    }
    return task_list
  end

  def purchased_or_public_lessons
    return Lesson::Template.all
  end

  def school_pupils
    school.pupils
  end

  def school_only_pupils
    school.pupils.where.not(id: pupils)
  end

  def school_forms
    school.forms
  end

  def all_school_forms
    school.all_forms
  end

  def school_only_forms
    school.forms.where.not(id: forms)
  end

  def all_school_only_forms
    school.all_forms.where.not(id: all_forms)
  end

  def school_lessons
    school.lessons
  end

  def all_school_lessons
    school.all_lessons
  end

  def school_only_lessons
    school.lessons.where.not(id: lessons)
  end

  def all_school_only_lessons
    school.all_lessons.where.not(id: all_lessons)
  end

  def accessible_schools
    school
  end

  def accessible_teachers
    school&.teachers
  end

  def accessible_forms
    school.all_forms
  end

  def accessible_lesson_lessons
    school.all_lessons
  end

  def accessible_lesson_results
    school.all_lesson_results
  end

  def accessible_pupils
    school.pupils
  end

  def user_templates
    Lesson::Template.all.where(user_id: school.teacher_ids, user_generated: true)
  end

  def accessible_lesson_templates
    if school.generic? || school.unregistered?
      Lesson::Template.all.available.or(user_templates)
    else
      Lesson::Template.anonymous_or_demo.or(Lesson::Template.where(id: all_lessons.select(:template_id))).available
    end
  end

  def accessible_lesson_slides
    Lesson::Slide.where(template_id: purchased_or_public_lessons)
  end

  def accessible_lesson_documents
    Lesson::Document.where(template_id: purchased_or_public_lessons)
  end

  def accessible_lesson_keywords
    Lesson::Keyword.where(template_id: purchased_or_public_lessons)
  end

  def accessible_quiz_questions
    QuizOld::Question.where(lesson_template_id: purchased_or_public_lessons)
  end

  def self.serialized_for_index_request
    includes(:school).map do |x|
      x.as_json.merge({ 'school' => { name: x.school&.name, id: x.school&.id, demo: !x.school&.subscribed? } })
    end
  end

  def can_invite_to_school?
    true
  end
  
  # School invite methods
  def pending_school_invites
    school_invites.active
  end
  
  def accepted_school_invites
    school_invites.accepted
  end
end
