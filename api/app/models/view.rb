# == Schema Information
#
# Table name: views
#
#  id            :bigint           not null, primary key
#  deleted       :boolean          default(FALSE), not null
#  resource_type :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  resource_id   :bigint
#  user_id       :bigint
#
# Indexes
#
#  index_views_on_resource_type_and_resource_id  (resource_type,resource_id)
#  index_views_on_user_id                        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class View < ApplicationRecord
  belongs_to :user
  belongs_to :resource, polymorphic: true

  scope :ordered, -> { order(created_at: :asc) }
end
