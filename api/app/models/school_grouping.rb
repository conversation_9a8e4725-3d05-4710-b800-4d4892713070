# frozen_string_literal: true

# == Schema Information
#
# Table name: school_groupings
#
#  id              :bigint           not null, primary key
#  deleted         :boolean          default(FALSE), not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  school_group_id :bigint
#  school_id       :bigint
#
# Indexes
#
#  index_school_groupings_on_school_group_id  (school_group_id)
#  index_school_groupings_on_school_id        (school_id)
#
# Foreign Keys
#
#  fk_rails_...  (school_group_id => school_groups.id)
#  fk_rails_...  (school_id => schools.id)
#
class SchoolGrouping < ApplicationRecord
  belongs_to :school, inverse_of: :school_groupings
  belongs_to :school_group, inverse_of: :school_groupings
end
