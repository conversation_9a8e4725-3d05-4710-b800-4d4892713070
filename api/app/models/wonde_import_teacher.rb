# == Schema Information
#
# Table name: wonde_import_teachers
#
#  id              :bigint           not null, primary key
#  wonde_id        :string
#  wonde_data      :jsonb
#  wonde_import_id :bigint
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  user_id         :bigint
#  name            :string
#  email           :string
#
class WondeImportTeacher < ApplicationRecord
  validates :wonde_id, presence: true
  belongs_to :user, optional: true
  belongs_to :wonde_import

  has_many :wonde_import_teacher_forms, dependent: :destroy
  has_many :wonde_import_forms, through: :wonde_import_teacher_forms

  has_many :wonde_teacher_real_forms, dependent: :destroy
  has_many :forms, through: :wonde_teacher_real_forms

  has_one :matching_record, class_name: "Teacher", primary_key: :wonde_id, foreign_key: :wonde_id

  def v2_as_json(options=nil)
    as_json()
      .merge({
        forms: wonde_import_forms,
        real_forms: forms,
        teacher: user
      })
  end
end
