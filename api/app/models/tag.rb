# == Schema Information
#
# Table name: tags
#
#  id         :bigint           not null, primary key
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class Tag < ApplicationRecord
  has_many :taggings
  has_many :videos, through: :taggings, source: :taggable, source_type: 'Video'
  has_many :articles, through: :taggings, source: :taggable, source_type: 'Article'
  # Add more associations here for other taggable models
end
