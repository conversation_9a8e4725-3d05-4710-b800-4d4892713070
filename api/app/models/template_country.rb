# == Schema Information
#
# Table name: template_countries
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  country_id         :bigint
#  lesson_template_id :bigint
#
# Indexes
#
#  index_template_countries_on_country_id                         (country_id)
#  index_template_countries_on_lesson_template_id                 (lesson_template_id)
#  index_template_countries_on_lesson_template_id_and_country_id  (lesson_template_id,country_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#
class TemplateCountry < ApplicationRecord
  belongs_to :lesson_template, class_name: "Lesson::Template"
  belongs_to :country
end
