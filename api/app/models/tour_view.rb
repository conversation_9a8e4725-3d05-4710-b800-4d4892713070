# == Schema Information
#
# Table name: tour_views
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  tour_id    :bigint
#  user_id    :bigint
#
# Indexes
#
#  index_tour_views_on_tour_id  (tour_id)
#  index_tour_views_on_user_id  (user_id)
#
class TourView < ApplicationRecord
  belongs_to :tour, counter_cache: true
  belongs_to :user, optional: true

  def v2_as_json(options=nil)
    as_json()
      .merge({
        tour: { name: tour.name, id: tour.id }
      })
  end
end
