# == Schema Information
#
# Table name: related_glossaries
#
#  id                  :bigint           not null, primary key
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  glossary_id         :bigint
#  related_glossary_id :bigint
#
# Indexes
#
#  index_related_glossaries_on_glossary_id          (glossary_id)
#  index_related_glossaries_on_related_glossary_id  (related_glossary_id)
#
# Foreign Keys
#
#  fk_rails_...  (related_glossary_id => glossaries.id)
#
class RelatedGlossary < ApplicationRecord
end
