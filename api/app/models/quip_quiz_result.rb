# == Schema Information
#
# Table name: quip_quiz_results
#
#  id                 :bigint           not null, primary key
#  result_type        :integer          default("quip_quiz")
#  results_json       :jsonb
#  score              :decimal(, )
#  time_seconds       :integer
#  total              :decimal(, )
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  flow_progress_id   :bigint
#  lesson_template_id :bigint
#  pupil_id           :bigint
#  quip_quiz_id       :bigint
#  user_id            :bigint
#
# Indexes
#
#  index_quip_quiz_results_on_flow_progress_id    (flow_progress_id)
#  index_quip_quiz_results_on_lesson_template_id  (lesson_template_id)
#  index_quip_quiz_results_on_pupil_id            (pupil_id)
#  index_quip_quiz_results_on_quip_quiz_id        (quip_quiz_id)
#  index_quip_quiz_results_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (pupil_id => users.id)
#
class QuipQuizResult < ApplicationRecord
  belongs_to :pupil, optional: true
  belongs_to :user, optional: true

  belongs_to :lesson_template, class_name: 'Lesson::Template', optional: true
  belongs_to :flow_progress, optional: true
  belongs_to :quip_quiz, optional: true

  enum result_type: { quip_quiz: 0, rocket_word_quiz: 1, presentation: 2, homework_task: 3, old_homework_task: 4 }

  validates :score, :total, :results_json, presence: true

  def self.serialized_for_index_request
    includes(:lesson, :pupil).map do |x|
      x.as_json.merge({
                        'pupil_name' => x.pupil.name,
                        'lesson_name' => x.lesson.template.name
                      })
    end
  end
end
