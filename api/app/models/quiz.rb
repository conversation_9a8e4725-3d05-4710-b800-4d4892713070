# == Schema Information
#
# Table name: quizzes
#
#  id              :bigint           not null, primary key
#  name            :string
#  noodle_quiz_key :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  campaign_id     :bigint
#
# Indexes
#
#  index_quizzes_on_campaign_id  (campaign_id)
#
class Quiz < ApplicationRecord
  has_many :campaign_units
  has_many :tracking_summative_quizzes
  has_many :quiz_views
  belongs_to :campaign

  validates :noodle_quiz_key, :name, presence: true

  def v2_as_json(options=nil)
    as_json()
      .merge({
        campaign: campaign ? { name: campaign.name, id: campaign.id } : nil,
        views: quiz_views.count,
        completions: tracking_summative_quizzes.count,
        campaignUnitIds: campaign_units.ids
      })
  end
end
