# frozen_string_literal: true

# == Schema Information
#
# Table name: school_groups
#
#  id               :bigint           not null, primary key
#  name             :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  last_modified_by :integer
#  last_modified_at :datetime
#  deleted          :boolean          default(FALSE), not null
#
class SchoolGroup < ApplicationRecord
  has_many :school_groupings, dependent: :destroy, inverse_of: :school_group
  has_many :schools, through: :school_groupings, inverse_of: :school_groups
  has_many :forms, through: :schools
end
