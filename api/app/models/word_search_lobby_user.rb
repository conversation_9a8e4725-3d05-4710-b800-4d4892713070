# frozen_string_literal: true

# == Schema Information
#
# Table name: word_search_lobby_users
#
#  id                   :bigint           not null, primary key
#  finishedAt           :datetime
#  score                :integer          default(0)
#  words                :string           default([]), is an Array
#  user_id              :bigint
#  word_search_lobby_id :bigint
#
# Indexes
#
#  index_word_search_lobby_users_on_user_id               (user_id)
#  index_word_search_lobby_users_on_word_search_lobby_id  (word_search_lobby_id)
#
class WordSearchLobbyUser < ApplicationRecord
  belongs_to :word_search_lobby
  belongs_to :user

  validates :user_id, uniqueness: { scope: :word_search_lobby_id, message: 'already exists in this lobby' }

  def generate_words number_of_words
   user = self.user

   # Words from lessons
   words_from_done_lessons = words_pool_from_lessons_taken
   words = select_words_from_word_pool words_from_done_lessons, number_of_words

   # Words from all lessons
   if (words.length < number_of_words)
     words_from_all_lessons = words_pool_from_lessons
     required_words = number_of_words - words.length
     words = words + select_words_from_word_pool(words_from_all_lessons, required_words)
   end

   # words from year of pupil
   if (words.length < number_of_words)
     words_from_year = words_pool_from_year
     required_words = number_of_words - words.length
     words = words + select_words_from_word_pool(words_from_year, required_words)
   end

   # - random words for a pool
   if (words.length < number_of_words)
     random_words = words_pool_from_random
     required_words = number_of_words - words.length
     words = words + select_words_from_word_pool(random_words, required_words)
   end

   self.update(words: words)
  end

  def words_pool_from_lessons_taken
    words = self.user.lessons.includes(template: :keywords).map { |l| l.template&.keywords&.pluck(:name) }.flatten.compact
    remove_too_long_too_short_words words
  end

  def words_pool_from_lessons
    words = self.user.all_lessons.includes(template: :keywords).map { |l| l.template&.keywords&.pluck(:name) }.flatten.compact
    remove_too_long_too_short_words words
  end

  def words_pool_from_year
    templates = Lesson::Template.joins(:new_library_years).where(country: user.country, new_library_years: { id: user.years }, available: true)
    words = Lesson::Keyword.where(template_id: templates).pluck('DISTINCT name')
    remove_too_long_too_short_words words
  end

  def words_pool_from_random
    ["skate", "tower", "glue", "moral", "month", "rough", "menu", "galaxy", "muggy", "release", "beard", "lobby", "cord", "wall", "harsh", "reach"]
  end

  private

  def remove_too_long_too_short_words words
    words.map { |x| x.length > 2 && x.length < 8 ? x : nil }.compact.delete_if { |data| data.match (/\d/) }
  end

  def select_words_from_word_pool words, required_number
    words.sample(required_number)
  end
end
