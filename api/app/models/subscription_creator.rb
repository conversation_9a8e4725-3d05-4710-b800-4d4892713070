# == Schema Information
#
# Table name: subscription_creators
#
#  id            :bigint           not null, primary key
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  subscriber_id :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_subscription_creators_on_subscriber_id              (subscriber_id)
#  index_subscription_creators_on_user_id                    (user_id)
#  index_subscription_creators_on_user_id_and_subscriber_id  (user_id,subscriber_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (subscriber_id => subscribers.id)
#  fk_rails_...  (user_id => users.id)
#
class SubscriptionCreator < ApplicationRecord
  belongs_to :user
  belongs_to :subscriber

  validates :user_id, uniqueness: { scope: :subscriber_id }
end
