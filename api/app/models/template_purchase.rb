# frozen_string_literal: true

# == Schema Information
#
# Table name: template_purchases
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  billing_account_id :bigint
#  lesson_template_id :bigint
#
# Indexes
#
#  index_template_purchases_on_billing_account_id  (billing_account_id)
#  index_template_purchases_on_lesson_template_id  (lesson_template_id)
#
# Foreign Keys
#
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#
class TemplatePurchase < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
end
