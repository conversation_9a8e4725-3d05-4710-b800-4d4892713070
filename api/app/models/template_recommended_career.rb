# == Schema Information
#
# Table name: template_recommended_careers
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  career_id          :bigint
#  lesson_template_id :bigint
#
# Indexes
#
#  index_template_recommended_careers_on_career_id           (career_id)
#  index_template_recommended_careers_on_lesson_template_id  (lesson_template_id)
#
class TemplateRecommendedCareer < ApplicationRecord
  belongs_to :lesson_template, class_name: "Lesson::Template"
  belongs_to :career
end
