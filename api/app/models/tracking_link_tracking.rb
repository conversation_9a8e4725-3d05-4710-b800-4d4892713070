# frozen_string_literal: true

# == Schema Information
#
# Table name: tracking_link_trackings
#
#  id                 :bigint           not null, primary key
#  link_type          :integer
#  url                :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_id          :bigint
#  lesson_template_id :bigint
#  pupil_id           :bigint
#
# Foreign Keys
#
#  fk_rails_...  (lesson_id => lesson_lessons.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (pupil_id => users.id)
#
class TrackingLinkTracking < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :lesson, class_name: 'Lesson::Lesson'
  belongs_to :pupil, class_name: 'User'

  enum link_type: %i[employer training_pathway career]

  validates :url, :link_type, presence: :true
end
