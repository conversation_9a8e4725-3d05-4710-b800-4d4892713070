# frozen_string_literal: true

# == Schema Information
#
# Table name: tracking_summative_quizzes
#
#  id                 :bigint           not null, primary key
#  answers_json       :json
#  score              :integer
#  time_taken         :integer
#  total_score        :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  campaign_quiz_id   :bigint
#  lesson_id          :bigint
#  lesson_template_id :bigint
#  noodle_quiz_id     :string
#  pupil_id           :bigint
#  quiz_id            :bigint
#
# Foreign Keys
#
#  fk_rails_...  (lesson_id => lesson_lessons.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (pupil_id => users.id)
#
class TrackingSummativeQuiz < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :lesson, class_name: 'Lesson::Lesson'
  belongs_to :pupil, class_name: 'User'
  belongs_to :quiz, optional: true
  validates :time_taken, :score, :total_score, :answers_json, presence: true
end
