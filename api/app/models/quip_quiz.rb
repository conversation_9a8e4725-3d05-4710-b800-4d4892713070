# == Schema Information
#
# Table name: quip_quizzes
#
#  id                   :bigint           not null, primary key
#  name                 :string
#  published            :boolean          default(FALSE)
#  quip_questions_count :integer          default(0)
#  user_generated       :boolean          default(FALSE)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  lesson_template_id   :bigint
#  organisation_id      :bigint
#  user_id              :bigint
#
# Indexes
#
#  index_quip_quizzes_on_lesson_template_id  (lesson_template_id)
#  index_quip_quizzes_on_organisation_id     (organisation_id)
#  index_quip_quizzes_on_user_id             (user_id)
#
class QuipQuiz < ApplicationRecord
  validates :name, presence: true

  belongs_to :user, optional: true
  belongs_to :organisation, optional: true
  belongs_to :lesson_template, class_name: 'Lesson::Template', optional: true

  has_one :school, through: :user
  has_one :flow_step, dependent: :destroy, foreign_key: :quiz_id, class_name: 'FlowStep'

  has_many :quip_questions

  scope :platform_quizzes, -> { where(user_generated: false) }

  def format_for_builder
    {
      id: id.to_s,
      name: name,
      questions: quip_questions
    }
  end

  def format_for_static
    {
      id: id.to_s,
      name: name,
      questions: quip_questions.ordered.map(&:format_for_static),
      is_quip_quiz: true
    }.with_indifferent_access
  end

  def self.duplicate_from_quip(quip_quiz_key, template)
    user = template.user
    raise 'Missing required params' if !quip_quiz_key.present? || !template.present?

    puts 'Cloning quiz from quip'

    question_type_map = {
      "multi-choice": :multi_choice,
      "fill-in-blanks": :fill_in_blanks,
      "sort-list": :sort_list,
      "image-bucket": :image_bucket,
      "free-text": :free_text
    }

    quip_quiz_url = "https://quip-api.herokuapp.com/quizzes/by-quiz-key/#{quip_quiz_key}"
    public_key = 'cadbc93c-bb2f-4159-a105-0b11135ec826'

    url = URI(quip_quiz_url)
    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true
    http.verify_mode = OpenSSL::SSL::VERIFY_NONE
    request = Net::HTTP::Post.new(url)
    request['x-quip-public'] = public_key
    request['content-type'] = 'application/json'

    # timeout after 15s so we don't hang the original request, as this is a task run after duplicating a template
    http.read_timeout = 15

    begin
      response = http.request(request)
      unless response.code.to_i == 200
        puts "Error fetching quiz data got #{response.code} #{response.message}"
        raise response.message unless response.code.to_i == 200
      end
    rescue => e
      Rails.logger.error("Error fetching quiz data: #{e.message}\n#{e.backtrace.join("\n")}")
      ErrorLog.create(error: { errorData: e, backtrace: e.backtrace, lesson_template_id: template.id, quip_quiz_key: quip_quiz_key }.to_json)
      return { saved: false, error: 'Failed to fetch quiz data' }
    end

    raw_body = response.read_body
    body = JSON.parse(raw_body.present? ? raw_body : '{}')

    ActiveRecord::Base.transaction do
      quiz_name = body.dig('data', 'name').presence || template.name
      quiz = QuipQuiz.new(name: quiz_name, lesson_template_id: template.id, user: user)

      quiz.user_id = user&.id
      quiz.organisation_id = user&.organisation&.id
      quiz.user_generated = !!template.user_generated
      quiz.published = true

      quiz.save!

      body.dig('data', 'questions').map do |question|
        type = question_type_map[question['type'].to_sym]
        weight = question['weight']
        data_json = question['dataJson']

        if type == :image_bucket
          # The alternate formatting is to deal with buckets that do not match the standard format of
          # containing a validResponse object, which looks like the data snippet below the formatter

          is_object_buckets = data_json['buckets'][0].class.to_s === 'Hash'
          data_json['buckets'] = data_json['buckets'].map do |b|
            { name: is_object_buckets ? b.dig('name') : b, key: rand.to_s, old_key: b.class.to_s === 'String' ? b : b.dig('key') }
          end

          data_json['options'] = data_json['options'].map do |o|
            if data_json['validResponse']&.present?
              bucket_name = data_json['validResponse'].detect { |b| b[1].include? o['value'] }[0]
              bucket = data_json['buckets'].detect { |b| b[:name] == bucket_name }
              unless bucket.present?
                puts "invalid bucket for #{o}"
                next
              end
              {
                key: rand.to_s,
                label: o['label'].to_s,
                value: o['value'].to_s.presence || o['label'].to_s.presence || rand.to_s,
                bucket: bucket[:key],
                fileboyId: o['fileboyId'].to_s,
              }
            else
              bucket = data_json['buckets'].detect { |b| b[:old_key] == o['bucket'] || b[:old_key] == o['key'] }
              unless bucket.present?
                puts "invalid bucket for #{o}"
                next
              end
              {
                key: rand.to_s,
                label: o['label'].to_s,
                value: o['value'].to_s.presence || o['label'].to_s.presence || rand.to_s,
                bucket: bucket[:key],
                fileboyId: o['fileboyId'].to_s,
              }
            end
          end.compact
          # validResponse is no longer required as bucket value is stored on option
          data_json.except!('validResponse')
        end

        if type == :multi_choice
          data_json['options'] = data_json['options'].map do |o|
            o.merge({ key: rand.to_s })
          end
        end

        unless type.present? && weight.present? && data_json.present?
          puts "invalid question data for #{question}"
          next
        end

        prompt = ActionController::Base.helpers.strip_tags(data_json['prompt'])
        data_json['prompt'] = prompt

        q = QuipQuestion.new(weight: weight, question_type: type, data_json: data_json)

        puts data_json

        q.quip_quiz_id = quiz.id
        q.save!
      end
      puts "Quip quiz successfully duplicated - #{quiz.id}"
      return { saved: true, record: quiz }
    rescue RuntimeError => e
      puts "Error inside transaction: #{e.message}"
      Rails.logger.error("duplicate_from_quip error: #{e.message}\n#{e.backtrace.join("\n")}")
      return { saved: false, error: [e.message] }
    rescue => e
      puts "Error inside transaction: #{e.message}"
      Rails.logger.error("duplicate_from_quip error: #{e.message}\n#{e.backtrace.join("\n")}")
      error_message = e.respond_to?(:full_messages) ? e.full_messages : [e.message]
      return { saved: false, error: error_message }
    end
  end

  def self.duplicate_from_local(source_quiz, template)
    raise 'Missing required params' if !source_quiz.present? || !template.present?

    puts 'Cloning quiz from local'

    ActiveRecord::Base.transaction do
      quiz = QuipQuiz.new(name: template.name, lesson_template_id: template.id)
      quiz.user_id = template.user&.id
      quiz.organisation_id = template.user&.organisation&.id
      quiz.user_generated = !!template.user_generated
      quiz.published = true

      quiz.save!

      source_quiz.quip_questions.each do |question|
        q = question.dup
        q.quip_quiz_id = quiz.id
        q.save!
      end

      return { saved: true, record: quiz }
    rescue => e
      Rails.logger.error("Error duplicating quiz: #{e.message}\n#{e.backtrace.join("\n")}")
      return { saved: false, error: e.respond_to?(:full_messages) ? e.full_messages : [e.message] }
    end
  end

  def self.fetch_from_quip(key)
    uri = URI("https://quip-api.herokuapp.com/quizzes/by-quiz-key/#{key}")

    req = Net::HTTP::Post.new(uri, { "content-type": 'application/json', "x-quip-public": 'cadbc93c-bb2f-4159-a105-0b11135ec826' })
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = (uri.scheme == 'https')
    response = http.request(req)

    unless response.code.to_i == 200
      puts "Error fetching quiz data got #{response.code} #{response.message}"
      raise response.message
    end

    JSON.parse(response.body)['data']
  end
end
