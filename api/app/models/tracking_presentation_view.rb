# == Schema Information
#
# Table name: tracking_presentation_views
#
#  id                 :bigint           not null, primary key
#  time_viewed        :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_id          :bigint
#  lesson_template_id :bigint
#  user_id            :bigint
#
# Foreign Keys
#
#  fk_rails_...  (lesson_id => lesson_lessons.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (user_id => users.id)
#
class TrackingPresentationView < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :lesson, class_name: 'Lesson::Lesson', optional: true
  belongs_to :user

  after_save do
    if self.time_viewed && self.user.pupil? && self.time_viewed > 60
      points = [(self.time_viewed / 60) * 2, 10].min()
      UserRankEvent.add_points(self.user, points, "presentation")
    end
  end
end
