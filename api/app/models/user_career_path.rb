# == Schema Information
#
# Table name: user_career_paths
#
#  id              :bigint           not null, primary key
#  further_careers :jsonb
#  is_favourite    :boolean          default(FALSE)
#  is_saved        :boolean          default(FALSE)
#  log_uuid        :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  career_path_id  :bigint           not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_user_career_paths_on_career_path_id  (career_path_id)
#  index_user_career_paths_on_user_id         (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (career_path_id => career_paths.id)
#  fk_rails_...  (user_id => users.id)
#
class UserCareerPath < ApplicationRecord
  belongs_to :user
  belongs_to :career_path

  validates :user_id, presence: true
  validates :career_path_id, presence: true
end
