# == Schema Information
#
# Table name: user_rankings
#
#  id             :bigint           not null, primary key
#  notified       :integer
#  points         :integer          default(0)
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  leaderboard_id :bigint
#  user_id        :bigint
#
# Indexes
#
#  index_user_rankings_on_leaderboard_id  (leaderboard_id)
#  index_user_rankings_on_user_id         (user_id)
#
class UserRanking < ApplicationRecord
end
