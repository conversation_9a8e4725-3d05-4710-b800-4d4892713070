# == Schema Information
#
# Table name: user_preferred_careers
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  career_id  :bigint
#  user_id    :bigint
#
# Indexes
#
#  index_user_preferred_careers_on_career_id  (career_id)
#  index_user_preferred_careers_on_user_id    (user_id)
#
class UserPreferredCareer < ApplicationRecord
  belongs_to :user
  belongs_to :career
end
