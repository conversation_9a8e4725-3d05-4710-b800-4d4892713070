# == Schema Information
#
# Table name: tours
#
#  id                  :bigint           not null, primary key
#  available_to_pupils :boolean          default(FALSE)
#  data                :jsonb
#  name                :string
#  slug                :string
#  tour_views_count    :integer          default(0), not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  campaign_id         :bigint
#  fileboy_image_id    :string
#
# Indexes
#
#  index_tours_on_campaign_id  (campaign_id)
#  index_tours_on_slug         (slug) UNIQUE
#
class Tour < ApplicationRecord
  extend FriendlyId
  friendly_id :name, use: :slugged

  belongs_to :campaign, optional: true
  has_one :organisation, through: :campaign
  has_many :campaign_units
  has_many :tour_views
  validates :name, presence: true
  has_many :careers
  has_many :lesson_slides
  has_many :videos

  has_many :career_tours
  has_many :careers, through: :career_tours

  def v2_as_json(_options=nil)
    as_json
      .merge({
               campaign: campaign ? { name: campaign.name, id: campaign.id } : nil,
               organisation: organisation ? { name: organisation&.name, id: organisation&.id } : nil,
               views: tour_views.count,
               campaignUnitIds: campaign_units.ids,
               careerIds: careers.ids,
             })
  end

  def views
    tour_views_count
  end

  def tour_videos
    videos = []

    data.each_key do |key|
      video = data.dig(key, 'video').presence || {}
      next unless (video['videoRecordId'].presence || video['videoId']).present?
      video_id = video['videoRecordId'].presence || video['videoId']
      name = data.dig(key, 'name').presence || key
      videos << {
        label: name,
        value: video_id,
        sub: video_id,
        full_name: "#{name} (#{video_id})"
      }

      additional_videos = data.dig(key, 'additionalVideos')
      next unless additional_videos.present?
      additional_videos.each.with_index do |sub_video, index|
        video_id = sub_video['videoRecordId'].presence || sub_video['videoId']
        name = "#{data.dig(key, 'name').presence || key} [#{index + 1}]"
        videos << {
          label: name,
          value: video_id,
          sub: video_id,
          full_name: "#{name} (#{video_id})"
        }
      end
    end

    videos
  end

  def scene_by_video_id(video_id)
    return nil unless video_id.present?
    scene_key = nil
    data.each do |key, value|
      if value.dig('video', 'videoId')&.to_s == video_id.to_s || value.dig('video', 'videoRecordId')&.to_s == video_id.to_s
        scene_key = key
        break
      end

      matched_additional = value['additionalVideos']&.detect do |video|
        video['videoId']&.to_s == video_id.to_s || video['videoRecordId']&.to_s == video_id.to_s
      end
      if matched_additional
        scene_key = key
        break
      end
    end
    data[scene_key].merge({ 'name' => scene_key }) if scene_key
  end

  def self.seed_tour(tour_data, tour_name: '', org_name: '', campaign_name: '', preview_image_fb_id: '')
    if !name || name == '' || !org_name || org_name == '' || !campaign_name || campaign_name == ''
      puts 'Tour not seeded, missing name, org_name or campaign_name'
      return
    end

    puts "Seeding #{tour_name}"

    organisation = Organisation.find_by(name: org_name, published: true) || Organisation.find_or_create_by(name: org_name)
    campaign = Campaign.find_by(name: campaign_name, organisation: organisation)

    unless campaign
      campaign = Campaign.create(name: campaign_name, organisation: organisation)
      campaign.update(
        number_of_units: 2,
        ethnicities: %w[white mixed_or_multiple_ethnic_groups asian_or_asian_british black_african_caribbean_or_black_british other],
        placements: %w[units lessons],
        gender: 'any',
        employment_year: DateTime.now.year
      )
    end
    tour_record = Tour.find_by(name: tour_name)
    tour_record ||= Tour.create!(name: tour_name)
    tour_record.update(name: tour_name, data: tour_data, fileboy_image_id: preview_image_fb_id, campaign: campaign, available_to_pupils: true)
    tour_record.videos_for_tour(campaign)
    puts "seeded #{tour_name}"
    tour_record
  end

  def videos_for_tour(campaign)
    org_name = campaign.organisation.name
    tag = Tag.find_or_create_by(name: org_name)
    data.keys.each do |key|
      sceneVideo = data[key]['video']['videoId']
      video1 = Video.find_or_create_by!(name: key, fileboyVideoId: sceneVideo, tour_scene: key, tour_id: id, source: 'fileboy', external_id: sceneVideo, tags: [tag])
      video1.update(campaign: campaign)
      data[key]['video']['videoRecordId'] = video1.id
      puts "generated tour video #{key}"
    end
    save!
  end

  def to_param
    if RequestStore.store[:admin_request]
      id.to_s
    else
      super  # returns slug
    end
  end
end
