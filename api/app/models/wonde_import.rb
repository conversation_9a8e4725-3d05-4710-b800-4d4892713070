# == Schema Information
#
# Table name: wonde_imports
#
#  id                   :bigint           not null, primary key
#  import_status        :integer          default("running")
#  raw_wonde_cache      :jsonb
#  retain_data          :boolean          default(FALSE)
#  success              :boolean
#  wonde_forms_count    :integer          default(0)
#  wonde_pupils_count   :integer          default(0)
#  wonde_teachers_count :integer          default(0)
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  school_id            :bigint
#
# Indexes
#
#  index_wonde_imports_on_school_id  (school_id)
#
class WondeImport < ApplicationRecord
  # Import statuses:
  # running - pulling data,
  # complete - finished pulling data,
  # imported - actually saved data
  # importing - saving data
  # cancelled - stopped saving data / errored saving
  enum import_status: %i[running complete imported importing cancelled queued]
  belongs_to :school
  has_many :wonde_import_forms, dependent: :destroy
  has_many :wonde_import_pupils, dependent: :destroy
  has_many :wonde_import_teachers, dependent: :destroy
  has_many :teachers, through: :wonde_import_teachers, source: "user"
  has_one :wonde_import_error

  def v2_as_json(options = nil)
    as_json().merge(
      formCount: wonde_import_forms.size,
      pupilCount: wonde_import_pupils.size,
      teacherCount: wonde_import_teachers.size,
      error: wonde_import_error
    )
  end

  def invalid_teachers
    wonde_import_teachers.where("(email IS NULL OR email = '') AND user_id IS NULL")
  end

  def undo_wonde_import
    return if import_status != "imported"
    end_time = created_at + 1.hour

    # Collecting all the records that were deleted
    deleted_pupils = Pupil.deleted.where(school: school)
    deleted_pupils = deleted_pupils.select do |pupil|
      pupil.import_data&.dig("action") == "delete" && pupil.import_data&.dig("import_id") == id
    end

    deleted_forms = Form.deleted.where(school: school)
    deleted_forms = deleted_forms.select do |form|
      form.import_data&.dig("action") == "delete" && form.import_data&.dig("import_id") == id
    end

    deleted_enrollments = Enrollment.deleted.where(form: deleted_forms, user: deleted_pupils).where("updated_at >= ? AND updated_at < ?", created_at, end_time)
    deleted_lessons = Lesson::Lesson.deleted.where(form_id: deleted_forms.pluck(:id)).where("updated_at >= ? AND updated_at < ?", created_at, end_time)

    # Collecting all the records that were created
    created_teachers = school.teachers.select do |teacher|
      teacher.import_data&.dig("action") == "create" && teacher.import_data&.dig("import_id") == id
    end
    created_pupils = school.pupils.select do |pupil|
      pupil.import_data&.dig("action") == "create" && pupil.import_data&.dig("import_id") == id
    end
    created_forms = school.forms.select do |form|
      form.import_data&.dig("action") == "create" && form.import_data&.dig("import_id") == id
    end
    ActiveRecord::Base.transaction do
      # Purge records that were created
      created_teachers.each { |teacher| teacher.destroy }
      created_pupils.each { |pupil| pupil.destroy }
      created_forms.each { |form| form.destroy }

      # Restore deleted records
      Pupil.deleted.where(id: deleted_pupils.pluck(:id)).update_all(deleted: false, updated_at: DateTime.now)
      Form.deleted.where(id: deleted_forms.pluck(:id)).update_all(deleted: false, updated_at: DateTime.now)
      Enrollment.deleted.where(id: deleted_enrollments.pluck(:id)).update_all(deleted: false, updated_at: DateTime.now)
      Lesson::Lesson.deleted.where(id: deleted_lessons.pluck(:id)).update_all(deleted: false, updated_at: DateTime.now)
    end
  end

  def save_import_data
    update!(import_status: "importing")
    begin
      errors = { pupils: [], teachers: [], forms: [] }
      warnings = { pupils: [], teachers: [], forms: [] }

      if !retain_data
        to_destroy_forms = Form.where(school_id: school.id, wonde_id: nil)
        to_destroy_pupils = Pupil.where(school_id: school.id, wonde_id: nil)

        to_destroy_forms.update_all({ import_data: { import_id: self.id, action: "delete", date: DateTime.now } })
        to_destroy_pupils.update_all({ import_data: { import_id: self.id, action: "delete", date: DateTime.now } })

        to_destroy_forms.destroy_all
        to_destroy_pupils.destroy_all
      end

      return if import_was_cancelled(self)

      # Update existing forms
      ApplicationRecord.connection.execute("" "
        UPDATE forms
        SET
          name = wonde_import_forms.name,
          import_data = #{ApplicationRecord.escape({ import_id: self.id, action: "update", date: DateTime.now }.to_json)}
        FROM wonde_import_forms
        WHERE wonde_import_id = #{self.id}
        AND (forms.wonde_id = wonde_import_forms.wonde_id OR (wonde_import_forms.form_id IS NOT NULL AND forms.id = wonde_import_forms.form_id))
        AND forms.school_id = #{school.id}
        AND deleted = false
      " "")

      current_forms = school.forms.current_school_year.pluck(:wonde_id)

      # Create new forms
      not_in_clause = ''
      unless current_forms.empty?
        quoted_ids = current_forms.map { |id| "'#{id}'" }.join(',')
        not_in_clause = "AND wonde_import_forms.wonde_id NOT IN (#{quoted_ids})"
      end

      query = %Q(
        INSERT INTO forms (school_id, name, wonde_id, created_at, updated_at, deleted)
        SELECT #{school.id}, name, wonde_id, '#{DateTime.now}', '#{DateTime.now}', false
        FROM wonde_import_forms
        WHERE wonde_import_id = #{self.id}
        #{not_in_clause}
        AND (wonde_import_forms.form_id IS NULL OR wonde_import_forms.form_id NOT IN (SELECT id FROM forms WHERE school_id = #{school.id} AND deleted = false))
      )
      ApplicationRecord.connection.execute(query)

      # Update all the wonde_import_forms with the newly made form ids
      current_year_form_ids = school.forms.current_school_year.pluck(:id)
      query = %Q(
        UPDATE wonde_import_forms SET form_id = forms.id
        FROM forms
        WHERE wonde_import_forms.wonde_id = forms.wonde_id
        AND forms.id IN (#{current_year_form_ids.join(',')})
        AND wonde_import_forms.wonde_import_id = #{self.id}
        AND forms.school_id = #{school.id}
        AND forms.deleted = false
      )
      ApplicationRecord.connection.execute(query)

      # Update existing teachers
      ApplicationRecord.connection.execute("" "
        UPDATE users
        SET
          import_data = #{ApplicationRecord.escape({ import_id: self.id, action: "update", date: DateTime.now }.to_json)},
          wonde_id = wonde_import_teachers.wonde_id,
          deleted = false
        FROM wonde_import_teachers
        WHERE (users.wonde_id = wonde_import_teachers.wonde_id OR (wonde_import_teachers.user_id IS NOT NULL AND users.id = wonde_import_teachers.user_id))
        AND users.type = 'Teacher'
        AND wonde_import_id = #{self.id}
        AND users.school_id = #{school.id}
        AND deleted = false
      " "")

      # Create new teachers
      ApplicationRecord.connection.execute("" "
        INSERT INTO users (
          type, email, uid, provider, name, wonde_id, password_digest, require_password_reset,
          import_data, school_id, deleted, updated_at, created_at
        )
        SELECT
          'Teacher',
          LOWER(wonde_import_teachers.email),
          LOWER(wonde_import_teachers.email),
          'email',
          wonde_import_teachers.name,
          wonde_import_teachers.wonde_id,
          md5(random()::text),
          true,
          #{ApplicationRecord.escape({ import_id: self.id, action: "create", date: DateTime.now }.to_json)},
          #{school.id},
          false,
          '#{DateTime.now}',
          '#{DateTime.now}'
        FROM wonde_import_teachers
        WHERE wonde_import_id = #{self.id}
        AND wonde_import_teachers.email IS NOT NULL
        AND wonde_import_teachers.email != ''
        AND wonde_import_teachers.wonde_id NOT IN (
          SELECT wonde_id FROM users WHERE type = 'Teacher' AND school_id = #{school.id} AND deleted = false AND wonde_id IS NOT NULL
        )
        AND (
          (wonde_import_teachers.user_id IS NULL AND wonde_import_teachers.email != '')
          OR
          (wonde_import_teachers.user_id IS NOT NULL AND wonde_import_teachers.user_id NOT IN (SELECT id FROM users WHERE type = 'Teacher' AND school_id = #{school.id} AND deleted = false))
        )
      " "")

      # Update existing pupils
      ApplicationRecord.connection.execute("" "
        UPDATE users
        SET
          name = wonde_import_pupils.name,
          dob = wonde_import_pupils.dob,
          import_data = #{ApplicationRecord.escape({ import_id: self.id, action: "update", date: DateTime.now }.to_json)},
          wonde_id = wonde_import_pupils.wonde_id,
          deleted = false
        FROM wonde_import_pupils
        WHERE wonde_import_id = #{self.id}
        AND (users.wonde_id = wonde_import_pupils.wonde_id OR (wonde_import_pupils.user_id IS NOT NULL AND users.id = wonde_import_pupils.user_id))
        AND users.type = 'Pupil'
        AND users.school_id = #{school.id}
        AND deleted = false
      " "")

      # Create new pupils
      ApplicationRecord.connection.execute("" "
        WITH identifiers AS (
          SELECT
            wonde_import_pupils.wonde_id,
            CONCAT(
              CHR((65 + FLOOR(RANDOM() * 26))::INTEGER)::TEXT,
              LPAD(ROUND(RANDOM() * 9)::INTEGER::TEXT, 1,'0'),
              LPAD(ROUND(RANDOM() * 9)::INTEGER::TEXT, 1,'0'),
              CHR((65 + FLOOR(RANDOM() * 26))::INTEGER)::TEXT,
              CHR((65 + FLOOR(RANDOM() * 26))::INTEGER)::TEXT,
              LPAD(ROUND(RANDOM() * 9)::INTEGER::TEXT, 1, '0')
            ) AS identifier
          FROM wonde_import_pupils
          WHERE
            wonde_import_pupils.wonde_id NOT IN (
              SELECT wonde_id FROM users WHERE type = 'Pupil' AND users.school_id = #{school.id} AND deleted = false AND wonde_id IS NOT NULL
            )
          AND wonde_import_pupils.wonde_import_id = #{self.id}
        )
        INSERT INTO users (
          type, school_id, name, wonde_id, identifier,
          email, uid, dob, gender, provider, password_digest,
          deleted, updated_at, created_at
        )
        SELECT
          'Pupil',
          #{school.id},
          wonde_import_pupils.name,
          wonde_import_pupils.wonde_id,
          identifiers.identifier,
          LOWER(CONCAT(REPLACE(wonde_import_pupils.name, ' ', '-'), identifiers.identifier, '@developingexperts.com')),
          LOWER(CONCAT(REPLACE(wonde_import_pupils.name, ' ', '-'), identifiers.identifier, '@developingexperts.com')),
          wonde_import_pupils.dob,
          wonde_import_pupils.gender,
          'email',
          '$2a$10$ylAoa22s4pfTJKXtX79RG.EMjKaI9jOjPELg482tzb8xAOzW/zkem',
          false,
          '#{DateTime.now}',
          '#{DateTime.now}'
        FROM wonde_import_pupils
        JOIN identifiers ON wonde_import_pupils.wonde_id = identifiers.wonde_id
        WHERE wonde_import_id = #{self.id}
        AND wonde_import_pupils.wonde_id NOT IN (
          SELECT wonde_id FROM users WHERE type = 'Pupil' AND users.school_id = #{school.id} AND deleted = false AND wonde_id IS NOT NULL
        )
        AND (wonde_import_pupils.user_id IS NULL OR wonde_import_pupils.user_id NOT IN (SELECT id FROM users WHERE type = 'Pupil' AND users.school_id = #{school.id} AND deleted = false))
      " "")

      # clear old enrollments
      clear_enrollment_q = %Q(
        WITH new_pupils AS (
          SELECT id
          FROM users
          WHERE type = 'Pupil' AND wonde_id IN (
            SELECT wonde_id
            FROM wonde_import_pupils
            WHERE wonde_import_pupils.wonde_import_id = #{self.id}
          )
          AND school_id = #{school.id} AND deleted = false
        )
        UPDATE enrollments SET deleted = TRUE WHERE user_id IN (SELECT id FROM new_pupils) AND deleted = FALSE;
      )
      ApplicationRecord.connection.execute(clear_enrollment_q)

      # Link new pupils to schools
      query = %Q(
        WITH new_pupils AS (
          SELECT id, wonde_id
          FROM users
          WHERE type = 'Pupil' AND wonde_id IN (
            SELECT wonde_id
            FROM wonde_import_pupils
            WHERE wonde_import_pupils.wonde_import_id = #{self.id}
          )
          AND school_id = #{school.id} AND deleted = false
        ),
        linked_forms AS (
          SELECT new_pupils.id AS pupil_id, forms.id AS form_id
          FROM new_pupils
          JOIN wonde_import_pupils ON wonde_import_pupils.wonde_id = new_pupils.wonde_id
          JOIN wonde_import_pupil_forms ON wonde_import_pupils.id = wonde_import_pupil_forms.wonde_import_pupil_id
          JOIN wonde_import_forms ON wonde_import_pupil_forms.wonde_import_form_id = wonde_import_forms.id
          JOIN forms ON wonde_import_forms.form_id = forms.id
          WHERE wonde_import_forms.wonde_import_id = #{self.id}
          AND forms.school_id = #{school.id} AND deleted = false
        ),
        to_insert AS (
          SELECT pupil_id, form_id
          FROM linked_forms
          WHERE NOT EXISTS (
            SELECT 1
            FROM enrollments
            WHERE enrollments.user_id = linked_forms.pupil_id
              AND enrollments.form_id = linked_forms.form_id
              AND enrollments.deleted = false
          )
        )
        INSERT INTO enrollments (user_id, form_id, created_at, updated_at)
        SELECT pupil_id, form_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        FROM to_insert;
      )
      ApplicationRecord.connection.execute(query)

      # Link new teachers to forms
      query = %Q(
        WITH new_teachers AS (
          SELECT id, wonde_id
          FROM users
          WHERE type = 'Teacher' AND wonde_id IN (
            SELECT wonde_id
            FROM wonde_import_teachers
            WHERE wonde_import_teachers.wonde_import_id = #{self.id}
          )
          AND school_id = #{school.id} AND deleted = false
        ),
        linked_forms AS (
          SELECT new_teachers.id AS teacher_id, forms.id AS form_id
          FROM new_teachers
          JOIN wonde_import_teachers ON wonde_import_teachers.wonde_id = new_teachers.wonde_id
          JOIN wonde_import_teacher_forms ON wonde_import_teachers.id = wonde_import_teacher_forms.wonde_import_teacher_id
          JOIN wonde_import_forms ON wonde_import_teacher_forms.wonde_import_form_id = wonde_import_forms.id
          JOIN forms ON wonde_import_forms.form_id = forms.id
          WHERE wonde_import_forms.wonde_import_id = #{self.id}
          AND forms.school_id = #{school.id} AND deleted = false
        ),
        to_insert AS (
          SELECT teacher_id, form_id
          FROM linked_forms
          WHERE NOT EXISTS (
            SELECT 1
            FROM enrollments
            WHERE enrollments.user_id = linked_forms.teacher_id
              AND enrollments.form_id = linked_forms.form_id
              AND enrollments.deleted = false
          )
        )
        INSERT INTO enrollments (user_id, form_id, created_at, updated_at)
        SELECT teacher_id, form_id, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        FROM to_insert;
      )
      ApplicationRecord.connection.execute(query)

      return if import_was_cancelled(self)
      update!(import_status: "imported")
      return nil
    rescue => error
      puts "a fatal error occured on a wonde import"
      puts error
      update({ import_status: 'cancelled', success: false })
      return WondeImportError.create({
                                       error_data: { fatal: ['A fatal error occurred', error.message], raw: error&.cause&.message },
                                       warning_data: {},
                                       wonde_import: self
                                     })
    end
  end

  def import_was_cancelled(import)
    # Load the status manually so rails doesn't cache it
    result = ApplicationRecord.connection.execute("" "
          SELECT import_status, wonde_import_errors is not null as has_error
          FROM wonde_imports
          LEFT JOIN wonde_import_errors ON wonde_imports.id = wonde_import_errors.wonde_import_id
          WHERE wonde_imports.id = #{import.id}
        " "")

    status = WondeImport.import_statuses.key(result.first["import_status"])
    return false unless status == "cancelled"

    if !result.first["has_error"]
      WondeImportError.create({ error_data: { fatal: ["Import was cancelled"] }, warning_data: {}, wonde_import: import })
    end

    return true
  end

  # Given records as an array of hashes, inserts them into the table and returns their ids
  def insert_values(table, records = [])
    records = Array.wrap(records).flatten.compact
    return [] unless records.any?

    time = Time.current
    keys = Set.new
    records.each { |record| keys += record.keys }
    values = records.map { |record| keys.map { |k| record[k] } + [time, time] }
    columns = (keys.to_a + %i[created_at updated_at]).join(', ')
    values = values.map { |school| +'(' + school.map { |v| escape(v) }.join(', ') + ')' }.join(', ')
    self.class.connection.execute(<<-SQL.squish).values.flatten
      INSERT INTO #{table} (#{columns}) VALUES #{values} RETURNING id
    SQL
  end

  def sample_letter
    (('a'..'z').to_a - %w[o l z i]).sample(1).join
  end

  def sample_number
    ((3..9).to_a).sample(1).join
  end

  def gen_identifier
    [sample_letter, sample_number, sample_number, sample_letter, sample_letter, sample_number].join("")
  end
end
