# == Schema Information
#
# Table name: teacher_invites
#
#  id         :bigint           not null, primary key
#  email      :string           not null
#  token      :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  school_id  :bigint           not null
#  user_id    :bigint
#
# Foreign Keys
#
#  fk_rails_...  (school_id => schools.id)
#
class TeacherInvite < ApplicationRecord
  belongs_to :school
  belongs_to :user
  validates :email, :token, presence: true
  validates :email, uniqueness: { scope: :school_id, message: "has already been invited" }

  validate do
    if (email.present? && User.where(email: email).exists?)
      errors.add(:email, "already has an account")
    end
  end

  before_validation { self.token ||= SecureRandom.hex }

  def v2_as_json(options = nil)
    as_json(only: [:id, :email, :created_at]).merge(school: { id: school.id, name: school.name })
  end
end
