# == Schema Information
#
# Table name: tracking_career_paths
#
#  id             :bigint           not null, primary key
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  career_path_id :bigint           not null
#  user_id        :bigint           not null
#
# Indexes
#
#  index_tracking_career_paths_on_career_path_id  (career_path_id)
#  index_tracking_career_paths_on_user_id         (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (career_path_id => career_paths.id)
#  fk_rails_...  (user_id => users.id)
#
class TrackingCareerPath < ApplicationRecord
  belongs_to :career_path
  belongs_to :user
end
