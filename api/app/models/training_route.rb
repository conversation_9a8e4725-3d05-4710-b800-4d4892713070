# == Schema Information
#
# Table name: training_routes
#
#  id             :bigint           not null, primary key
#  name           :string
#  qualifications :string
#  route_type     :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  career_id      :bigint
#
# Indexes
#
#  index_training_routes_on_career_id  (career_id)
#
class TrainingRoute < ApplicationRecord
  belongs_to :career
  validates :name, presence: true
end
