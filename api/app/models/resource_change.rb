# == Schema Information
#
# Table name: resource_changes
#
#  id             :bigint           not null, primary key
#  attribute_name :string
#  new_value      :string
#  old_value      :string
#  reason         :string
#  source_name    :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  school_id      :bigint
#
# Indexes
#
#  index_resource_changes_on_school_id  (school_id)
#
# Foreign Keys
#
#  fk_rails_...  (school_id => schools.id)
#
class ResourceChange < ApplicationRecord
  belongs_to :school, optional: true
  validates :source_name, :attribute_name, presence: true
end
