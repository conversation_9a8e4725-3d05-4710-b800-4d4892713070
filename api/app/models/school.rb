# frozen_string_literal: true

# == Schema Information
#
# Table name: schools
#
#  id                              :bigint           not null, primary key
#  account_number                  :string
#  admin_notes                     :string
#  admin_stats_cache               :jsonb
#  allow_stripe_subscription       :boolean          default(TRUE), not null
#  annual_subscription             :boolean
#  approaching_renewal             :boolean          default(FALSE)
#  approved_domains                :string
#  billing_email                   :string
#  cache_activity_30_days          :integer          default(0)
#  cache_activity_60_days          :integer          default(0)
#  can_access_homework             :boolean          default(FALSE)
#  can_see_youtube_streams         :boolean          default(FALSE)
#  can_view_glossary               :boolean          default(FALSE)
#  category                        :integer
#  checkout_progress               :integer
#  comments_body                   :string
#  comments_status                 :string
#  created_by_email                :string
#  creation_email                  :string
#  customer_type                   :integer
#  default_billing_address_line_1  :string
#  default_billing_address_line_2  :string
#  default_billing_city            :string
#  default_billing_postal_code     :string
#  default_billing_state           :string
#  deleted                         :boolean          default(FALSE), not null
#  demo_content_generated          :boolean          default(FALSE)
#  enquiry_type                    :integer
#  expires_at                      :datetime
#  finance_email                   :string
#  finance_name                    :string
#  free_subscription               :boolean
#  hubspot_ai_subscription_status  :boolean          default(FALSE)
#  hubspot_errors                  :jsonb
#  hubspot_geography_subscription  :boolean
#  hubspot_renewal_date            :datetime
#  hubspot_subscription_status     :string
#  image_name                      :string
#  image_uid                       :string
#  independent                     :boolean          default(FALSE), not null
#  is_example_school               :boolean
#  last_modified_at                :datetime
#  last_modified_by                :integer
#  last_sync_hubspot_activity_at   :datetime
#  latitude                        :float
#  lead_source                     :string
#  longitude                       :float
#  memo                            :text
#  name                            :string
#  onboarding_date                 :datetime
#  onboarding_parent_data_stage    :boolean
#  onboarding_pupil_data_stage     :boolean
#  onboarding_teacher_communicated :boolean
#  onboarding_teacher_data_stage   :boolean
#  po_number                       :string
#  postcode                        :string
#  region                          :integer
#  renewal_month                   :integer
#  restricted_access               :boolean          default(FALSE)
#  restricted_access_blocked       :boolean          default(FALSE)
#  school_type                     :integer          default("generic")
#  science                         :boolean
#  setup_checklist                 :jsonb            not null
#  show_lesson_plan_pupil_area     :boolean          default(FALSE)
#  show_mark_book                  :boolean          default(TRUE), not null
#  social                          :jsonb
#  subdomain                       :string
#  subscription_end_date           :date
#  subscription_externally_covered :boolean
#  subscription_start_date         :date
#  subscription_status             :integer          default(0)
#  sync_wonde_weekly               :boolean
#  telephone                       :string
#  temporary                       :boolean          default(FALSE)
#  term_length                     :integer
#  trial_end_date                  :datetime
#  trial_handouts_viewed           :boolean
#  trial_lessons_started           :boolean
#  trial_library_browsed           :boolean
#  trial_start_date                :datetime
#  trial_videos_watched            :boolean
#  uk_region                       :string
#  wonde_attempt_count             :integer          default(0)
#  wonde_enabled                   :boolean          default(FALSE)
#  wonde_request_status            :integer          default("not_requested")
#  year_start                      :date
#  created_at                      :datetime         not null
#  updated_at                      :datetime         not null
#  actual_country_id               :bigint
#  country_id                      :bigint
#  created_by_id                   :integer
#  default_billing_country_id      :bigint
#  distributor_id                  :bigint
#  fileboy_image_id                :string
#  hubspot_deal_id                 :string
#  hubspot_id                      :string
#  new_library_curriculum_id       :bigint
#  organisation_id                 :bigint
#  pipedrive_deal_id               :bigint
#  pipedrive_id                    :bigint
#  sign_up_event_id                :bigint
#  uk_school_id                    :bigint
#  wonde_id                        :string
#
# Indexes
#
#  index_schools_on_account_number                  (account_number) UNIQUE
#  index_schools_on_actual_country_id               (actual_country_id)
#  index_schools_on_country_id                      (country_id)
#  index_schools_on_deleted                         (deleted)
#  index_schools_on_distributor_id                  (distributor_id)
#  index_schools_on_name                            (name) UNIQUE WHERE ((school_type = 2) AND (NOT deleted))
#  index_schools_on_new_library_curriculum_id       (new_library_curriculum_id)
#  index_schools_on_organisation_id                 (organisation_id)
#  index_schools_on_pipedrive_id                    (pipedrive_id) UNIQUE
#  index_schools_on_school_type                     (school_type)
#  index_schools_on_sign_up_event_id                (sign_up_event_id)
#  index_schools_on_subdomain                       (subdomain) UNIQUE WHERE (NOT deleted)
#  index_schools_on_uk_school_id                    (uk_school_id)
#  index_schools_unique_wonde_id_where_not_deleted  (wonde_id) UNIQUE WHERE ((deleted = false) AND (wonde_id IS NOT NULL) AND ((wonde_id)::text <> ''::text))
#  index_unique_wonde_id_where_not_deleted          (wonde_id) UNIQUE WHERE ((deleted = false) AND (wonde_id IS NOT NULL) AND ((wonde_id)::text <> ''::text))
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#  fk_rails_...  (uk_school_id => uk_schools.id)
#
require_dependency 'demo_generator'

class School < ApplicationRecord
  audited

  include SchoolStats
  include HasOptionalAddress
  include Subscribable
  include SubscriptionAccess

  HS_SUBSCRIPTION_STATUS = {
    subscribed: 'Subscribed',
    in_trial: 'In Trial',
    trial_pending: 'Trial Pending',
    trial_ended: 'Trial Ended',
    restricted: 'Restricted'
  }.with_indifferent_access.freeze

  LEAD_SOURCE_OPTIONS = [
    ['Event or Exhibition', 'tradeshow'],
    ['Search Engine', 'search_engine'],
    ['Social Media', 'social_media'],
    ['Referral or recommendation', 'referral'],
    ['Other', 'other'],
  ].freeze

  UK_REGION_OPTIONS = [
    ['Greater London', 'greater_london'],
    ['South East', 'south_east'],
    ['South West', 'south_west'],
    ['West Midlands', 'west_midlands'],
    ['North West', 'north_west'],
    ['North East', 'north_east'],
    ['Yorkshire and the Humber', 'yorkshire_and_the_humber'],
    ['East Midlands', 'east_midlands'],
    ['East of England', 'east_of_england'],
    ['Scotland', 'scotland'],
    ['Wales', 'wales'],
    ['Northern Ireland', 'northern_ireland'],
  ].freeze

  def subscribed?
    hubspot_subscription_status == HS_SUBSCRIPTION_STATUS[:subscribed]
  end

  def in_trial?
    hubspot_subscription_status == HS_SUBSCRIPTION_STATUS[:in_trial]
  end

  def trial_pending?
    hubspot_subscription_status == HS_SUBSCRIPTION_STATUS[:trial_pending]
  end

  def trial_ended?
    hubspot_subscription_status == HS_SUBSCRIPTION_STATUS[:trial_ended]
  end

  def restricted?
    hubspot_subscription_status == HS_SUBSCRIPTION_STATUS[:restricted]
  end

  DEMO_LIFESPAN = 28.days

  CHECKLIST_ITEMS = %w[
    create_teachers create_pupils create_classes assign_pupils send_emails
  ].freeze
  belongs_to :country
  belongs_to :actual_country, class_name: 'Country', optional: true
  belongs_to :uk_school, optional: true
  belongs_to :organisation, optional: true
  belongs_to :sign_up_event, optional: true
  belongs_to :new_library_curriculum, class_name: 'NewLibrary::Curriculum'
  belongs_to :default_billing_country, class_name: 'Country', optional: true
  enum renewal_month: %i[not_set january february march april may june july august september october november december]

  enum wonde_request_status: %i[not_requested pending active declined]
  enum school_type: { generic: 0, demo: 1, unregistered: 3 }
  enum category: %i[primary_school secondary_school special_school all_through international home_school_category industry_account mat]

  enum region: %i[greater_london south_east south_west west_midlands north_west north_east
                  yorkshire_and_the_humber east_midlands east_of_england outside_the_uk scotland wales northern_ireland]

  scope :subscribed, -> { where(hubspot_subscription_status: HS_SUBSCRIPTION_STATUS[:subscribed]) }
  scope :unsubscribed, -> { where.not(hubspot_subscription_status: HS_SUBSCRIPTION_STATUS[:subscribed]) }

  scope :active, -> { joins(:teachers).where('last_activity_at >= ?', 6.months.ago).distinct }

  belongs_to :created_by, class_name: 'User', foreign_key: 'created_by_id', optional: true

  has_many :school_sponsors
  has_many :sponsors, through: :school_sponsors

  has_many :school_groupings, dependent: :destroy, inverse_of: :school
  has_many :school_groups, through: :school_groupings, inverse_of: :schools
  has_many :resource_changes

  has_many :teachers
  has_many :pupils
  has_many :unassigned_pupils, -> { no_form }, class_name: 'Pupil'
  has_many :users
  has_many :tutors, inverse_of: :school

  has_many :campaign_lesson_views, through: :users
  has_many :ai_subscriptions, -> { where(subscription_type: :ai) }, class_name: 'StripeSubscription', through: :users

  has_many :user_tracking_lesson_template_vieweds, through: :users

  has_many :forms, -> { current_school_year }
  has_many :all_forms, class_name: 'Form'

  has_many :live_stream_schools, dependent: :destroy
  has_many :live_streams, through: :live_stream_schools

  has_many :lessons, through: :forms
  has_many :all_lessons, through: :all_forms, source: :lessons

  has_many :user_lesson_templates, through: :teachers

  has_many :lesson_results, through: :pupils
  has_many :all_lesson_results, through: :pupils

  has_many :quiz_attempts, through: :pupils
  has_many :all_quiz_attempts, through: :pupils

  has_many :wonde_imports

  has_many :leaderboards

  has_many :form_units, through: :all_forms, dependent: :destroy

  has_many :science_leaders, -> { where(science_lead: true) }, class_name: 'User'
  has_many :geography_leaders, -> { where(geography_lead: true) }, class_name: 'User'

  has_many :schools_affiliations
  has_many :affiliates, through: :schools_affiliations

  has_many :quip_quizzes, through: :users

  has_many :school_invites, dependent: :destroy, inverse_of: :school

  has_one :subscriber, as: :subscriber, dependent: :destroy

  before_create do
    self.hubspot_subscription_status = 'In Trial'
  end

  after_create_commit :after_create_callbacks

  def after_create_callbacks
    if generic?
      update(trial_end_date: (DateTime.now + 2.weeks).beginning_of_day)
      generic_notify
    end

    return unless Rails.env.production?
    return if is_industry_account?
    return unless generic?
    # Create the Hubspot company
    sync_hubspot
  end

  after_update do
    next if is_industry_account?
    next unless generic?
    sync_hubspot if Rails.env.production? && saved_changes.keys.length > 0 && hubspot_id && !saved_change_to_hubspot_id? && !saved_change_to_hubspot_deal_id? && !saved_change_to_hubspot_errors? && !deleted?
  end

  before_save do
    # override details with uk school info on save
    self.name = uk_school.name if uk_school && uk_school.name.present?
    self.postcode = uk_school.postcode if uk_school && uk_school.postcode.present?
    self.telephone = uk_school.phone if uk_school && uk_school.phone.present?
    begin
      # if we already have a location and the postcode has not changed then skip this
      next if !postcode_changed? && latitude.present?

      unless postcode.present?
        self.latitude = nil
        self.longitude = nil
        next
      end

      response = Net::HTTP.post(
        URI('https://api.postcodes.io/postcodes'),
        { postcodes: [postcode] }.to_json,
        'Content-Type' => 'application/json'
      )

      if response.code != '200'
        logger.error 'Failed to fetch postcode'
        logger.error response.body
      end

      result = JSON.parse(response.body)['result'][0]

      next if result.nil?

      self.latitude = result['result']['latitude']
      self.longitude = result['result']['longitude']
    rescue => e
      logger.error e

      self.latitude = nil
      self.longitude = nil
    end
  end

  validates :name, presence: true
  validates :term_length, if: :term_length?, numericality: { greater_than_or_equal_to: 1 }

  def self.categories_for_select
    School.categories.to_a.map do |category, _|
      name = category.humanize # default to humanize
      name = category.upcase if category == 'mat'
      [name, category]
    end
  end

  def self.lead_source_options_for_select
    choices = [
      { label: 'Event or Exhibition', value: 'tradeshow' },
      { label: 'Search Engine', value: 'search_engine' },
      { label: 'Social Media', value: 'social_media' },
      { label: 'Referral or recommendation', value: 'referral' },
      { label: 'Other', value: 'other' },
    ]
    choices.map { |choice| [choice[:label], choice[:value]] }
  end

  def domain_validation(email)
    domains = approved_domains&.split(',')&.map(&:strip)&.map(&:downcase)&.compact || []
    return true if domains.length == 0
    !!domains.detect { |d| d == email.downcase.split('@').last }
  end

  def name
    uk_school&.name || super
  end

  def postcode
    return uk_school&.postcode if uk_school && uk_school.postcode.present?
    super
  end

  def telephone
    return uk_school&.phone if uk_school && uk_school.phone.present?
    super
  end

  def category
    return uk_school&.category if uk_school && uk_school.category.present?
    super
  end

  def uk_region
    return uk_school&.region if uk_school && uk_school.region.present?
    super
  end

  def is_industry_account?
    category === 'industry_account'
  end

  def hubspot_update_finance
    return unless ENV['HUBSPOT_PRIVATE_APP_TOKEN'].present?
    return if is_industry_account?
    return unless hubspot_id
    HubspotManager.wrap_with_error_handler({ school: self }) do |log_error|
      HubspotManager.update_finance_contact(self)
    end
  end

  def sync_hubspot
    return unless ENV['HUBSPOT_PRIVATE_APP_TOKEN'].present?
    return if is_industry_account?
    HubspotManager.wrap_with_error_handler({ school: self }) do |log_error|
      if hubspot_id.present?
        HubspotManager.update_company_from_school(self)
      else
        hubspot_company_id = HubspotManager.create_company_from_school(self)
        # This will update the column without triggering the after_update callbacks
        update_column :hubspot_id, hubspot_company_id
        reload
      end

      if created_at >= 1.day.ago
        begin
          sync_hubspot_deal!
        rescue => e
          puts e
          log_error.call(e)
        end
      end

      teachers.each do |teacher|
        SyncTeacherHubspotJob.perform_now(teacher)
      end

      sync_hubspot_school_subscribed! if generic? && !is_industry_account? && finance_name.present? && finance_email.present?
    end
  end

  def sync_hubspot_school_subscribed
    return if is_industry_account?
    HubspotManager.wrap_with_error_handler({ school: self, context: 'Subscribing' }) do |log_error|
      sync_hubspot_school_subscribed!
    end
  end

  def sync_hubspot_school_subscribed!
    return if is_industry_account?
    raise StandardError, 'School is not generic' unless generic?
    raise StandardError, 'School is an example school' if is_industry_account?
    raise StandardError, 'Missing finance_name and finance_email' unless finance_name.present? && finance_email.present?

    sync_hubspot_deal!
    HubspotManager.close_deal_for_school(self)
    HubspotManager.create_finance_contact_from_school(self)
    HubspotManager.update_deal_associations_from_school(self)
  end

  def sync_hubspot_deal!
    return if is_industry_account?
    if hubspot_deal_id.nil?
      deal_id = HubspotManager.create_deal_from_school(self)
      update_column :hubspot_deal_id, deal_id
      reload
    else
      # HubspotManager.update_deal_from_school(self)
    end

    # NOTE: this shouldn't be necessary, but is needed for existing/bad data
    # HubspotManager.update_school_subscription_start_date_from_deal(self)
  end

  def expired?
    in_trial? && trial_end_date < Time.now
  end

  def setup_checklist
    return super if @ensured_checklist
    @ensured_checklist = true
    list = CHECKLIST_ITEMS.map { |i| [i, false] }.to_h.merge(super).slice(*CHECKLIST_ITEMS)
    self.setup_checklist = list
  end

  def self.default_checklist
    CHECKLIST_ITEMS.map { |i| [i, false] }.to_h
  end

  before_validation :setup_checklist

  before_save do
    next unless deleted_was == false && deleted == true
    teachers.each do |teacher|
      teacher.update(email: "#{teacher.email}_deleted_#{rand}")
    end
    tutors.each do |tutor|
      tutor.update(email: "#{tutor.email}_deleted_#{rand}")
    end
  end

  after_save(if: :saved_change_to_year_start?) do
    [
      all_forms, all_lessons, all_lesson_results, all_quiz_attempts
    ].each(&:update_school_year)
  end

  def complete_checklist_item(item)
    setup_checklist[item] = true
    save!
  end

  def current_year
    return unless year_start
    year = Date.today.year
    year_start.change(year: year).past? ? year : year - 1
  end

  def clear_content
    ActiveDay.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    PresentationProgress.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    Lesson::Result.unscoped.where(id: all_lesson_results.unscope(where: :deleted)).delete_all
    QuizOld::Attempt.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    Enrollment.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    View.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    PupilQuipAttempt.unscoped.where(pupil_id: users.unscope(where: :deleted)).delete_all
    TrackingPresentationView.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    TrackingLogin.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    TrackingMarkAssignment.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    TrackingAccountEdit.unscoped.where(user_id: users.unscope(where: :deleted)).delete_all
    User.unscoped.where(id: users.unscope(where: :deleted)).delete_all
    Lesson::Lesson.unscoped.where(id: all_lessons.unscope(where: :deleted)).delete_all
    Form.unscoped.where(id: all_forms.unscope(where: :deleted)).delete_all
    XlsImport.unscoped.where(school_id: id).delete_all
    TeacherInvite.unscoped.where(school_id: id).delete_all
    Leaderboard.unscoped.where(school_id: id).delete_all
    ResourceChange.unscoped.where(school_id: id).delete_all
    WondeImport.unscoped.where(school_id: id).delete_all
  end

  def main_teacher
    return @main_teacher if @main_teacher
    users = teachers.order(created_at: :asc)
    @main_teacher =
      users.find_by(email: creation_email) ||
        users.find_by(is_school_admin: true) ||
        users.first
  end

  def geography_leader_ids=(value)
    science_leaders.update_all(geography_lead: false)
    teachers.where(id: value).update_all(geography_lead: true, is_school_admin: true)
  end

  # These methods now do the same thing - combine them
  def science_leader_ids=(value)
    science_leaders.update_all(science_lead: false)
    teachers.where(id: value).update_all(science_lead: true, is_school_admin: true)
  end

  def admin_science_leader_ids=(value)
    science_leaders.update_all(science_lead: false)
    teachers.where(id: value).update_all(science_lead: true, is_school_admin: true)
  end

  def generic_notify
    return if is_industry_account?
    
    AdminMailer.new_school_activation(self).deliver_now
  end

  def sponsor_ids
    attributes['sponsor_ids'] || super
  end

  def v1_as_json(options = nil)
    if generic?
      as_json(options).merge(
        uk_school: uk_school,
        pupils_count: attributes['pupil_count'] || pupils.count,
        teachers_count: attributes['teacher_count'] || teachers.count,
        classes_count: attributes['form_count'] || forms.count,
        subscribed: subscribed?,
        sponsor_ids: sponsor_ids,
        science_leaders: science_leaders.map { |user| { id: user.id, email: user.email, name: user.name, science_lead: user.science_lead, is_school_admin: user.is_school_admin } }
      )
    else
      as_json(options)
    end
  end

  def self.serialized_for_index_request
    left_joins(:users, :forms, :school_sponsors)
      .group(:id)
      .select(
        'schools.*',
        "COUNT(DISTINCT users.id) FILTER (WHERE users.type = 'Pupil') AS pupil_count",
        "COUNT(DISTINCT users.id) FILTER (WHERE users.type = 'Teacher') AS teacher_count",
        'COUNT(DISTINCT forms.id) FILTER (WHERE forms.id IS NOT NULL) AS form_count',
        "array_remove(coalesce(ARRAY_AGG(DISTINCT school_sponsors.sponsor_id), '{}'), NULL) AS sponsor_ids",
        "array_remove(coalesce(ARRAY_AGG(DISTINCT users.email) FILTER (WHERE users.type = 'Tutor'), '{}'), NULL) AS tutor_emails",
        <<-SQL.squish
          BOOL_OR(
            CASE school_type
            WHEN 0 THEN
              hubspot_subscription_status = '#{HS_SUBSCRIPTION_STATUS[:subscribed]}'
            WHEN 2 THEN
              true
            ELSE
              false
            END
          ) AS subscribed
        SQL
    )
  end

  # locations = { radiusMiles: 5; postcode: string }[]
  def self.within_locations(postcodes)
    return unless postcodes.is_a?(Array) && postcodes.length > 0
    latlngs = convert_postcodes_to_latlngs(postcodes)
    return [] unless latlngs.present?


    latlngsWithRadius = latlngs.map do |latlng|
      location = postcodes.find { |postcode| postcode == latlng[:postcode] }

      next unless location

      {
        latitude: latlng[:latitude],
        longitude: latlng[:longitude],
        radiusMiles: Campaign::POSTCODE_RADIUS,
      }
    end

    return School.generic if latlngs.length === 0

    sqlConditions = latlngsWithRadius.map do |latlngRadius|
      next unless latlngRadius[:latitude] && latlngRadius[:longitude] && latlngRadius[:radiusMiles]

      lat = latlngRadius[:latitude]
      lng = latlngRadius[:longitude]
      rad = latlngRadius[:radiusMiles]

      Arel.sql(
        <<-SQL
          (
            3959 * acos (
              cos ( radians( #{lat} ) )
              * cos( radians( latitude ) )
              * cos( radians( longitude ) - radians( #{lng} ) )
              + sin ( radians( #{lat} ) )
              * sin( radians( latitude ) )
            )
          ) < #{rad}
        SQL
      )
    end

    # must have latlng
    School.generic.where
                  .not(latitude: nil, longitude: nil)
                  .where(
                    sqlConditions
                      .map { |sql| "(#{sql})" }
                      .join(' OR ')
                  )
  end

  def user_emails_valid
    invalid_emails = []
    users.where.not(type: :pupil).each do |user|
      invalid_emails << user.email unless user.is_valid_email
    end
    [invalid_emails.length == 0, invalid_emails]
  end

  def merge_with_school(other_school_id)
    # must lookup first otherwise it's already deleted when you try to access it
    other_school = School.find(other_school_id)
    load_sql('schools/merge_school').compact.assign(
      source_id: other_school_id,
      target_id: id
    ).run
    update(finance_email: other_school.finance_email) if finance_email.blank?
    update(finance_name: other_school.finance_name) if finance_name.blank?
    update(po_number: other_school.po_number) if po_number.blank?
  end

  def top_active
    collection = School.joins(pupils: :active_days).having('COUNT(users.id) > 0 AND count(active_days.id) > 0').group('schools.id')

    collection = collection.group('schools.id')
                           .select(
                             'schools.id',
                             'schools.name',
                             "(
          (coalesce(
            COUNT(
              distinct(
                active_days.id
              )
            ), 0
          ))
          /
          (COUNT(distinct(users.id)))
        ) AS activity"
                           )

    csv = JSONString.generate(sql(<<-SQL, collection))
      SELECT
        id AS "School ID",
        name AS "School Name",
        activity AS "Avg Activity"
      FROM (?) AS data
      ORDER BY activity DESC
      LIMIT 3
    SQL
  end

  def has_had_recent_activity?
    users.joins(:active_days).where('active_days.created_at > ?', 180.days.ago).count > 0
  end

  def purge_content
    ActiveRecord::Base.transaction do
      queries = []
      school = self
      mt = school.main_teacher
      queries << "WITH user_ids AS (SELECT id FROM users WHERE school_id = #{school.id})"
      queries << 'u_rank_delete AS (DELETE FROM user_rank_events WHERE user_ranking_id IN (SELECT id FROM user_rankings WHERE user_id IN (SELECT id FROM user_ids)) RETURNING user_rank_events.id)'
      queries << 'clear_referred_id AS (UPDATE users SET referred_from_user_id = NULL WHERE referred_from_user_id IN (SELECT id FROM user_ids) RETURNING users.id)'
      queries << 'user_interest_tag_related_words as (DELETE FROM related_words WHERE user_interest_tag_id IN (SELECT id FROM user_interest_tags WHERE user_id IN (SELECT id FROM user_ids)) RETURNING related_words.id)'
      user_tables = [
        { table: 'presentation_progresses', id: 'user_id' },
        { table: 'enrollments', id: 'user_id' },
        { table: 'views', id: 'user_id' },
        { table: 'user_templates', id: 'user_id' },
        { table: 'tracking_logins', id: 'user_id' },
        { table: 'active_days', id: 'user_id' },
        { table: 'lesson_word_search_results', id: 'user_id' },
        { table: 'pupil_quip_attempts', id: 'pupil_id' },
        { table: 'tracking_presentation_views', id: 'user_id' },
        { table: 'tracking_lesson_template_vieweds', id: 'pupil_id' },
        { table: 'user_achievements', id: 'user_id' },
        { table: 'user_rankings', id: 'user_id' },
        { table: 'questionnaire_answers', id: 'user_id' },
        { table: 'live_stream_messages', id: 'user_id' },
        { table: 'questionnaire_users', id: 'user_id' },
        { table: 'lesson_plan_views', id: 'user_id' },
        { table: 'pupil_homeworks', id: 'user_id' },
        { table: 'user_preferred_careers', id: 'user_id' },
        { table: 'user_interest_tags', id: 'user_id' },
        { table: 'tracking_rocket_words', id: 'pupil_id' },
        { table: 'tracking_films', id: 'pupil_id' },
        { table: 'tracking_word_searches', id: 'pupil_id' },
        { table: 'tracking_summative_quizzes', id: 'pupil_id' },
        { table: 'tracking_link_trackings', id: 'pupil_id' },
        { table: 'tracking_documents', id: 'pupil_id' },
        { table: 'tracking_lesson_template_favourites', id: 'pupil_id' },
        { table: 'word_search_lobby_users', id: 'user_id' },
        { table: 'tracking_account_edits', id: 'user_id' },
        { table: 'lesson_results', id: 'pupil_id' },
        { table: 'tour_views', id: 'user_id' },
        { table: 'quip_quiz_results', id: 'pupil_id' },
        { table: 'pupil_unit_marks', id: 'user_id' },
        { table: 'pupil_subscriptions', id: 'pupil_id' },
        { table: 'tracking_mark_assignments', id: 'user_id' },
        { table: 'invite_requests', id: 'pupil_id' },
        { table: 'invite_requests', id: 'guardian_id' },
        { table: 'invites', id: 'invite_creator_id' },
        { table: 'invites', id: 'pupil_id' },
        { table: 'invites', id: 'user_id' },
      ]

      school_tables = [
        { table: 'forms', id: 'school_id' },
        { table: 'xls_imports', id: 'school_id' },
        { table: 'teacher_invites', id: 'school_id' },
        { table: 'resource_changes', id: 'school_id' },
        { table: 'school_groupings', id: 'school_id' },
      ]

      queries += user_tables.map.with_index do |table, idx|
        table_name = table[:table]
        id_ref = table[:id]
        '' "#{table_name}_delete_#{idx} AS (DELETE FROM #{table_name} USING user_ids WHERE #{table_name}.#{id_ref} IN (SELECT id FROM user_ids) RETURNING #{table_name}.id)" ''
      end

      queries += school_tables.map do |table|
        table_name = table[:table]
        id_ref = table[:id]
        '' "#{table_name}_delete AS (DELETE FROM #{table_name} WHERE #{table_name}.#{id_ref} = #{school.id} RETURNING #{table_name}.id)" ''
      end

      # Don't delete the main teacher.
      queries << "users_school_delete AS (DELETE FROM users USING user_ids WHERE users.id IN (SELECT id FROM user_ids)#{mt ? " AND users.id != #{mt.id}" : ''} RETURNING users.id)"

      query = queries.join(",\n") + "\n"
      query += '' 'SELECT id FROM user_ids;' ''
      time = Time.now
      ApplicationRecord.connection.exec_query(query)
      puts "#{school.wonde_imports.count} wonde imports to remove"
      school.wonde_imports.each(&:destroy)
      school.update(hubspot_errors: [])
      puts "- #{Time.now - time}s"
      return Time.now - time
    end
  end

  def self.local_authorities
    [
      'BFPO Overseas Establishments',
      'Barking and Dagenham',
      'Barnet',
      'Barnsley',
      'Bath and North East Somerset',
      'Bedford',
      'Bexley',
      'Birmingham',
      'Blackburn with Darwen',
      'Blackpool',
      'Blaenau Gwent',
      'Bolton',
      'Bournemouth, Christchurch and Poole',
      'Bracknell Forest',
      'Bradford',
      'Brent',
      'Bridgend',
      'Brighton and Hove',
      'Bristol, City of',
      'Bromley',
      'Buckinghamshire',
      'Bury',
      'Caerphilly',
      'Calderdale',
      'Cambridgeshire',
      'Camden',
      'Cardiff',
      'Carmarthenshire',
      'Central Bedfordshire',
      'Ceredigion',
      'Cheshire East',
      'Cheshire West and Chester',
      'City of London',
      'Conwy',
      'Cornwall',
      'County Durham',
      'Coventry',
      'Croydon',
      'Cumbria',
      'Darlington',
      'Denbighshire',
      'Derby',
      'Derbyshire',
      'Devon',
      'Does not apply',
      'Doncaster',
      'Dorset',
      'Dudley',
      'Ealing',
      'East Riding of Yorkshire',
      'East Sussex',
      'Enfield',
      'Essex',
      'Fieldwork Overseas Establishments',
      'Flintshire',
      'Gateshead',
      'Gibraltar Overseas Establishments',
      'Gloucestershire',
      'Greenwich',
      'Guernsey Offshore Establishments',
      'Gwynedd',
      'Hackney',
      'Halton',
      'Hammersmith and Fulham',
      'Hampshire',
      'Haringey',
      'Harrow',
      'Hartlepool',
      'Havering',
      'Herefordshire, County of',
      'Hertfordshire',
      'Hillingdon',
      'Hounslow',
      'Isle of Anglesey',
      'Isle of Man Offshore Establishments',
      'Isle of Wight',
      'Isles Of Scilly',
      'Islington',
      'Jersey Offshore Establishments',
      'Kensington and Chelsea',
      'Kent',
      'Kingston upon Hull, City of',
      'Kingston upon Thames',
      'Kirklees',
      'Knowsley',
      'Lambeth',
      'Lancashire',
      'Leeds',
      'Leicester',
      'Leicestershire',
      'Lewisham',
      'Lincolnshire',
      'Liverpool',
      'Luton',
      'Manchester',
      'Medway',
      'Merthyr Tydfil',
      'Merton',
      'Middlesbrough',
      'Milton Keynes',
      'Monmouthshire',
      'Neath Port Talbot',
      'Newcastle upon Tyne',
      'Newham',
      'Newport',
      'Norfolk',
      'North East Lincolnshire',
      'North Lincolnshire',
      'North Northamptonshire',
      'North Somerset',
      'North Tyneside',
      'North Yorkshire',
      'Northumberland',
      'Nottingham',
      'Nottinghamshire',
      'Oldham',
      'Oxfordshire',
      'Pembrokeshire',
      'Peterborough',
      'Plymouth',
      'Portsmouth',
      'Powys',
      'Reading',
      'Redbridge',
      'Redcar and Cleveland',
      'Rhondda Cynon Taf',
      'Richmond upon Thames',
      'Rochdale',
      'Rotherham',
      'Rutland',
      'Salford',
      'Sandwell',
      'Scotland Offshore Establishments',
      'Sefton',
      'Sheffield',
      'Shropshire',
      'Slough',
      'Solihull',
      'Somerset',
      'South Gloucestershire',
      'South Tyneside',
      'Southampton',
      'Southend-on-Sea',
      'Southwark',
      'St. Helens',
      'Staffordshire',
      'Stockport',
      'Stockton-on-Tees',
      'Stoke-on-Trent',
      'Suffolk',
      'Sunderland',
      'Surrey',
      'Sutton',
      'Swansea',
      'Swindon',
      'Tameside',
      'Telford and Wrekin',
      'Thurrock',
      'Torbay',
      'Torfaen',
      'Tower Hamlets',
      'Trafford',
      'Vale of Glamorgan',
      'Wakefield',
      'Walsall',
      'Waltham Forest',
      'Wandsworth',
      'Warrington',
      'Warwickshire',
      'West Berkshire',
      'West Northamptonshire',
      'West Sussex',
      'Westminster',
      'Wigan',
      'Wiltshire',
      'Windsor and Maidenhead',
      'Wirral',
      'Wokingham',
      'Wolverhampton',
      'Worcestershire',
      'Wrexham',
      'York',
    ]
  end

  def sync_hubspot_single_school
    require 'hubspot-api-client'
    access_token = ENV["HUBSPOT_PRIVATE_APP_TOKEN"]
    client = Hubspot::Client.new(access_token: access_token)

    hubspot_id = self.hubspot_id
    company = client.crm.companies.basic_api.get_by_id(company_id: hubspot_id, properties: ["renewal_date", "annual_subscription", "subscription_status", "trial_end_date", "trial_start_date", "finance_contact_email", "finance_contact_name", "geography_subscription"])

    return { success: false, message: "No company found for HubSpot ID: #{hubspot_id}" } unless company
    self.sync_hubspot_school_attrs company
    { success: true }
  end

  def sync_hubspot_school_attrs company
    updates = {}

    updates[:finance_email] = company.properties["finance_contact_email"]
    updates[:finance_name] = company.properties["finance_contact_name"]

    ai_subscription_enabled = company.properties["ai_subscription_enabled"]
    if ai_subscription_enabled.present?
      updates[:hubspot_ai_subscription_status] = ai_subscription_enabled
      puts "Updating AI subscription status for #{self.name} to #{ai_subscription_enabled}"
    end

    ren_date = company.properties["renewal_date"]
    if ren_date.present?
      renewal_date = Date.parse(ren_date)
      renewal_month = renewal_date.month
      updates[:renewal_month] = renewal_month
      puts "Updating renewal month for #{self.name} to #{renewal_month}"
    end

    annual_subscription = company.properties['annual_subscription']
    if annual_subscription.present?
      updates[:annual_subscription] = annual_subscription
      puts "Updating annual subscription for #{self.name} to #{annual_subscription}"
    end

    status = company.properties['subscription_status']

    # Check if the subscription status is present
    if status.present?
      # If the status is "Trial", we need to check the trial start and end dates
      if status == "Trial"
        # Parse the trial end and start dates from the company properties
        trial_end_date = company.properties["trial_end_date"].present? ? Date.parse(company.properties["trial_end_date"]) : nil
        trial_start_date = company.properties["trial_start_date"].present? ? Date.parse(company.properties["trial_start_date"]) : nil

        # If the trial end date is in the past, the trial has ended
        if trial_end_date.present? && trial_end_date < Date.today
          updates[:hubspot_subscription_status] = "Trial Ended"
          # If the trial start date is in the future, the trial is pending
        elsif trial_start_date.present? && trial_start_date > Date.today
          updates[:hubspot_subscription_status] = "Trial Pending"
          # If neither of the above conditions are met, the trial is currently active
        else
          updates[:hubspot_subscription_status] = "In Trial"
        end
        # If the status is not "Trial", we simply update the subscription status to the current status
      else
        updates[:hubspot_subscription_status] = status
      end
      # Log the updated subscription status for the school
      puts "Updating subscription status for #{self.name} to #{status}"
    end

    geography_subscription = company.properties['geography_subscription']
    if geography_subscription.present?
      Rails.logger.info "Updating geography subscription for #{self.name} to #{geography_subscription}"
      updates[:hubspot_geography_subscription] = geography_subscription
    end

    updates.reject! { |k, v| self[k] == v }
    self.update_columns(updates) if updates.any?
  end

  private

  def self.convert_postcodes_to_latlngs(postcodes)
    return [] if postcodes.length == 0

    response = Net::HTTP.post(
      URI('https://api.postcodes.io/postcodes'),
      { postcodes: postcodes }.to_json,
      'Content-Type' => 'application/json'
    )

    if response.code != '200'
      logger.error 'Failed to fetch postcode'
      logger.error response.body
      # return all schools
      return School.generic
    end

    JSON.parse(response.body)['result'].map do |result|
      next unless result['result'].present?
      {
        postcode: result['query'],
        latitude: result['result']['latitude'],
        longitude: result['result']['longitude'],
      }
    end.compact
  end
end
