# == Schema Information
#
# Table name: users
#
#  id                                    :bigint           not null, primary key
#  admin_permissions                     :jsonb
#  alias                                 :string
#  avatar_configuration                  :jsonb
#  beta_features                         :jsonb
#  blocked_at                            :datetime
#  can_access_career_builder             :boolean
#  can_access_lesson_ai                  :boolean
#  can_create_lessons                    :boolean          default(FALSE)
#  can_view_school_data                  :boolean          default(FALSE)
#  communication_preferences             :jsonb
#  deleted                               :boolean          default(FALSE), not null
#  dob                                   :datetime
#  education                             :string           default("")
#  email                                 :citext           not null
#  ethnicity                             :integer
#  gender                                :string
#  geography_lead                        :boolean          default(FALSE)
#  glossary_list                         :string           default([]), is an Array
#  has_career_profile                    :boolean          default(FALSE), not null
#  hs_visitor_cache                      :jsonb
#  hubspot_marketing_email_name_received :string
#  identifier                            :string
#  import_data                           :jsonb
#  is_blocked                            :boolean          default(FALSE)
#  is_restricted                         :boolean          default(FALSE), not null
#  is_school_admin                       :boolean
#  job_hunter_interests                  :string
#  job_title                             :string
#  last_activity_at                      :datetime
#  last_sign_in_at                       :datetime
#  last_sign_out_at                      :datetime
#  last_sync_hubspot_activity_at         :datetime
#  lead_source                           :integer
#  linked_in_url                         :string           default("")
#  location                              :string
#  login_count                           :integer          default(0)
#  mailchimp_last_error                  :text
#  mailchimp_last_sync                   :datetime
#  mailchimp_last_sync_attempt           :datetime
#  mailchimp_list                        :string
#  mailchimp_sync_status                 :string           default("pending")
#  name                                  :string
#  onboarded                             :boolean
#  password_digest                       :string           not null
#  phone                                 :string
#  points                                :integer          default(0), not null
#  prefered_presentation_voice           :string           default("fable")
#  preferred_location                    :string
#  presentation_settings                 :jsonb
#  profile_color                         :jsonb
#  provider                              :string           not null
#  questionnaire_taken                   :boolean          default(FALSE)
#  recovery_requested_at                 :datetime
#  recovery_token                        :string
#  referral_actioned                     :boolean          default(FALSE)
#  referral_code                         :string
#  referral_share_name                   :boolean          default(TRUE), not null
#  require_password_reset                :boolean
#  science_lead                          :boolean          default(FALSE)
#  session_token                         :string
#  sign_in_token                         :string
#  specified_class                       :string
#  specified_year                        :string
#  tasks_completed_cache                 :jsonb
#  tokens                                :json
#  type                                  :string
#  uid                                   :string           default(""), not null
#  unique_wonde_identifier               :string
#  use_new_presentation_player           :boolean
#  work_experience                       :jsonb            is an Array
#  working_days                          :string
#  years_of_experience                   :string           default("")
#  created_at                            :datetime         not null
#  updated_at                            :datetime         not null
#  cv_fileboy_id                         :string
#  fileboy_image_id                      :string
#  hubspot_id                            :string
#  mailchimp_id                          :string
#  my_login_id                           :string
#  organisation_id                       :bigint
#  referred_from_user_id                 :integer
#  school_id                             :bigint
#  stripe_customer_id                    :string
#  white_label_organisation_id           :bigint
#  wonde_id                              :string
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE WHERE (NOT deleted)
#  index_users_on_identifier_and_school_id        (identifier,school_id) UNIQUE WHERE (NOT deleted)
#  index_users_on_organisation_id                 (organisation_id)
#  index_users_on_school_id                       (school_id)
#  index_users_on_sign_in_token                   (sign_in_token) UNIQUE
#  index_users_on_uid_and_provider                (uid,provider) UNIQUE WHERE (NOT deleted)
#  index_users_on_white_label_organisation_id     (white_label_organisation_id)
#  index_users_unique_wonde_id_where_not_deleted  (wonde_id) UNIQUE WHERE ((deleted = false) AND (wonde_id IS NOT NULL) AND ((wonde_id)::text <> ''::text))
#
# Foreign Keys
#
#  fk_rails_...  (referred_from_user_id => users.id)
#  fk_rails_...  (white_label_organisation_id => organisations.id)
#
class Tutor < User
end
