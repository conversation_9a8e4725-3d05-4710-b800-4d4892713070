# == Schema Information
#
# Table name: user_templates
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_template_id :bigint           not null
#  user_id            :bigint           not null
#
# Indexes
#
#  index_user_templates_on_lesson_template_id  (lesson_template_id)
#  index_user_templates_on_user_id             (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (user_id => users.id)
#
class UserTemplate < ApplicationRecord
  belongs_to :user
  belongs_to :lesson_template, class_name: "Lesson::Template"
end
