# frozen_string_literal: true

# == Schema Information
#
# Table name: tracking_lesson_template_vieweds
#
#  id                 :bigint           not null, primary key
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_id          :bigint
#  lesson_template_id :bigint
#  pupil_id           :bigint
#  user_id            :bigint
#
# Foreign Keys
#
#  fk_rails_...  (lesson_id => lesson_lessons.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (pupil_id => users.id)
#
class TrackingLessonTemplateViewed < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :lesson, class_name: 'Lesson::Lesson', optional: true
  belongs_to :user, optional: true
  belongs_to :pupil, class_name: 'User', optional: true
end
