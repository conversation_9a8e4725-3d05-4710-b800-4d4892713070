# == Schema Information
#
# Table name: user_interest_tags
#
#  id         :bigint           not null, primary key
#  name       :string           not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  user_id    :bigint
#
# Indexes
#
#  index_user_interest_tags_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require "data_muse"

class UserInterestTag < ApplicationRecord
  belongs_to :user
  has_many :related_words, dependent: :destroy

  after_save :populate_related_words

  validates :name, presence: true


  def populate_related_words
    RelatedWord.where(user_interest_tag_id: id).destroy_all

    related_words.create!(name: name)

    DataMuse.means_like(self.name).each do |result|
      next unless result[:score] && result[:score] > 0
      related_words.create!(name: result[:word])
    end
  end
end
