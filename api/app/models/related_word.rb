# == Schema Information
#
# Table name: related_words
#
#  id                       :bigint           not null, primary key
#  name                     :string
#  campaign_interest_tag_id :bigint
#  career_tag_id            :integer
#  user_interest_tag_id     :bigint
#
# Indexes
#
#  index_related_words_on_campaign_interest_tag_id  (campaign_interest_tag_id)
#  index_related_words_on_career_tag_id             (career_tag_id)
#  index_related_words_on_user_interest_tag_id      (user_interest_tag_id)
#
# Foreign Keys
#
#  fk_rails_...  (campaign_interest_tag_id => campaign_interest_tags.id)
#  fk_rails_...  (career_tag_id => career_tags.id)
#  fk_rails_...  (user_interest_tag_id => user_interest_tags.id)
#
class RelatedWord < ApplicationRecord
  belongs_to :career_tag, optional: true
  belongs_to :user_interest_tag, optional: true
  belongs_to :campaign_interest_tag, optional: true

  validates :name, presence: true

  before_save do
    self.name = name.downcase
  end
end
