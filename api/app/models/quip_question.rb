# == Schema Information
#
# Table name: quip_questions
#
#  id            :bigint           not null, primary key
#  data_json     :jsonb
#  question_type :integer
#  weight        :integer
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  quip_quiz_id  :bigint
#
# Indexes
#
#  index_quip_questions_on_quip_quiz_id  (quip_quiz_id)
#
class QuipQuestion < ApplicationRecord
  # this was copied directly from the saved data format that is generated by making a new quiz
  # then adding a question of each type.
  DEFAULT_QUESTION_DATA = {
    "fill_in_blanks": {
      "key": 'q_1747998101376_21y1pq1c',
      "prompt": 'Fill in the blanks',
      "options": {
        "sets": [
          { "key": 'o_1747998101376_kqq1m2ni', "label": 'North', "value": 'North', "correct": false },
          { "key": 'o_1747998101376_0rgb3eaq', "label": 'East', "value": 'East', "correct": false },
          { "key": 'o_1747998101376_wt64jyfk', "label": 'South', "value": 'South', "correct": false },
          { "key": 'o_1747998101376_rdnczunk', "label": 'West', "value": 'West', "correct": true }
        ],
        "rises": [
          { "key": 'o_1747998101376_vsj4ua1j', "label": 'North', "value": 'North', "correct": false },
          { "key": 'o_1747998101376_yulznh0t', "label": 'East', "value": 'East', "correct": true },
          { "key": 'o_1747998101376_a2wv0r3m', "label": 'South', "value": 'South', "correct": false },
          { "key": 'o_1747998101376_1yn2rajt', "label": 'West', "value": 'West', "correct": false }
        ]
      },
      "feedback": '',
      "template": 'The sun rises in the {{rises}} and sets in the {{sets}}.',
      "validResponse": { "sets": 'West', "rises": 'East' },
      "fileboyImageId": ''
    },
    "text_bucket": {
      "key": 'q_1747998100070_666nkndy',
      "prompt": 'Place the descriptions in the correct groups',
      "buckets": [
        { "key": 'bucket1', "name": 'Bucket A' },
        { "key": 'bucket2', "name": 'Bucket B' }
      ],
      "options": [
        { "key": 'bucket1', "label": 'A', "value": 'bucket1', "bucket": 'bucket1' },
        { "key": 'bucket2', "label": 'B', "value": 'bucket2', "bucket": 'bucket2' }
      ],
      "feedback": '',
      "fileboyImageId": ''
    },
    "image_bucket": {
      "key": 'q_1747998097958_fsjq8m1u',
      "prompt": 'Place the images into the correct bucket',
      "buckets": [
        { "key": 'bucket1', "name": 'Foxes', "label": 'Foxes' },
        { "key": 'bucket2', "name": 'Dogs', "label": 'Dogs' }
      ],
      "options": [
        {
          "key": 'o_1747998097958_padyp6uz',
          "label": 'Animal 1',
          "value": 'o_1747998097958_padyp6uz',
          "bucket": 'bucket1',
          "fileboyId": '8cd79520-4756-48a6-9d2e-bf3f68c82f89'
        },
        {
          "key": 'o_1747998097958_58z8ud1q',
          "label": 'Animal 2',
          "value": 'o_1747998097958_58z8ud1q',
          "bucket": 'bucket2',
          "fileboyId": '5b601c2a-7b03-4c0e-be2a-9253a7584c86'
        }
      ],
      "feedback": '',
      "fileboyImageId": ''
    },
    "sort_list": {
      "key": 'q_1747998096339_76cldk1d',
      "prompt": 'Sort the items',
      "options": [
        { "key": 'o_1747998096339_0itlaljw', "label": 'Item 1', "value": 'Item 1', "weight": 0, "fileboyId": nil },
        { "key": 'o_1747998096339_5431v8n0', "label": 'Item 2', "value": 'Item 2', "weight": 1, "fileboyId": nil },
        { "key": 'o_1747998096339_zip91a6m', "label": 'Item 3', "value": 'Item 3', "weight": 2, "fileboyId": nil },
        { "key": 'o_1747998096339_1cmu8cg7', "label": 'Item 4', "value": 'Item 4', "weight": 3, "fileboyId": nil }
      ],
      "feedback": '',
      "validResponse": ['Item 1', 'Item 2', 'Item 3', 'Item 4'],
      "fileboyImageId": '',
      "shuffleOptions": true
    },
    "free_text": {
      "key": 'q_1747998095372_6m64qo9n',
      "prompt": 'Enter your free text question here',
      "feedback": '',
      "fileboyImageId": ''
    },
    "multi_choice": {
      "key": 'q_1747998093834_6apdwdwp',
      "prompt": 'Select the correct answer',
      "options": [
        { "key": 'o_1747998093834_k9f1lcv8', "label": 'Answer 1', "value": 'o_1747998093834_k9f1lcv8', "correct": true },
        { "key": 'o_1747998093834_x9r99qvo', "label": 'Answer 2', "value": 'o_1747998093834_x9r99qvo', "correct": false },
        { "key": 'o_1747998093834_9i0kf9a7', "label": 'Answer 3', "value": 'o_1747998093834_9i0kf9a7', "correct": false },
        { "key": 'o_1747998093834_yxjgynaf', "label": 'Answer 4', "value": 'o_1747998093834_yxjgynaf', "correct": false }
      ],
      "feedback": '',
      "limitChoices": { "sameNumberAsCorrectAnswers": true },
      "validResponse": ['o_1747998093834_k9f1lcv8'],
      "fileboyImageId": ''
    }
  }.with_indifferent_access.freeze

  validates :question_type, :weight, presence: true
  enum question_type: { multi_choice: 0, fill_in_blanks: 1, sort_list: 2, image_bucket: 3, free_text: 4, text_bucket: 5 }
  belongs_to :lesson_slide, optional: true
  has_many :lesson_slides, primary_key: :id, foreign_key: :quip_question_id, class_name: 'Lesson::Slide'
  belongs_to :quip_quiz, optional: true, counter_cache: true

  scope :ordered, -> { order(:weight) }

  # TODO: eliminate this method after all quip quizzes are moved in
  def type_to_static
    case question_type
    when 'multi_choice'
      'multi-choice'
    when 'fill_in_blanks'
      'fill-in-blanks'
    when 'sort_list'
      'sort-list'
    when 'image_bucket'
      'image-bucket'
    when 'free_text'
      'free-text'
    when 'text_bucket'
      'text-bucket'
    end
  end

  # TODO: eliminate this method after all quip quizzes are moved in
  def format_for_static
    { type: type_to_static, id: id.to_s, dataJson: data_json }.deep_transform_keys!(&:to_s)
  end
end
