# frozen_string_literal: true

# == Schema Information
#
# Table name: tracking_documents
#
#  id                 :bigint           not null, primary key
#  document_type      :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  document_id        :bigint
#  lesson_id          :bigint
#  lesson_template_id :bigint
#  pupil_id           :bigint
#
# Foreign Keys
#
#  fk_rails_...  (document_id => lesson_documents.id)
#  fk_rails_...  (lesson_id => lesson_lessons.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (pupil_id => users.id)
#
class TrackingDocument < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :lesson, class_name: 'Lesson::Lesson'
  belongs_to :pupil, class_name: 'User'
  belongs_to :document, class_name: 'Lesson::Document'

  enum document_type: %i[handout]

  validates :document_type, presence: :true
end
