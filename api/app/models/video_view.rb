# == Schema Information
#
# Table name: video_views
#
#  id                   :bigint           not null, primary key
#  resource_type        :string
#  time_viewing_seconds :integer
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  resource_id          :bigint
#  user_id              :bigint
#  videos2_id           :bigint
#
# Indexes
#
#  index_video_views_on_resource_type_and_resource_id  (resource_type,resource_id)
#  index_video_views_on_user_id                        (user_id)
#  index_video_views_on_videos2_id                     (videos2_id)
#
class VideoView < ApplicationRecord
  belongs_to :video, foreign_key: :videos2_id
  belongs_to :user, optional: true
  belongs_to :resource, polymorphic: true, optional: true

  def v2_as_json(options = nil)
    as_json()
      .merge({
               video: video,
             })
  end
end
