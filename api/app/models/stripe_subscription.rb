# == Schema Information
#
# Table name: stripe_subscriptions
#
#  id                     :bigint           not null, primary key
#  free_subscription      :boolean          default(FALSE)
#  stripe_cache           :jsonb
#  subscription_type      :integer          default("ai")
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  stripe_session_id      :string
#  stripe_subscription_id :string
#  user_id                :bigint
#
# Indexes
#
#  index_stripe_subscriptions_on_user_id  (user_id)
#
require 'stripe'
# Stripe.api_key = ENV['STRIPE_AI_KEY']

class StripeSubscription < ApplicationRecord
  CURRENCY_MAP = {
    'gbp' => '£',
    'usd' => '$',
    'eur' => '€',
  }.freeze

  STRIPE_PARAMS = {
    api_key: ENV['STRIPE_AI_KEY'],
    stripe_version: '2024-06-20',
  }.freeze

  PRICES = {
    ai: {
      id: ENV['STRIPE_AI_PRICE_ID'].presence,
      price: 4.95
    },
    curriculum: {
      id: ENV['STRIPE_CURRICULUM_PRICE_ID'].presence,
      price: 5.95
    },
  }.with_indifferent_access.freeze

  enum subscription_type: { ai: 0, curriculum: 1 }

  SUBSCRIPTION_TYPE_OPTIONS = subscription_types.keys.map { |k| [k.to_s.humanize, k] }

  belongs_to :user, optional: true

  validate :unique_subscription_type_per_user

  def stripe_subscription
    return nil unless stripe_subscription_id.present?
    Stripe::Subscription.retrieve(stripe_subscription_id, STRIPE_PARAMS)
  end

  def active?
    stripe_cache && stripe_cache['status'] == 'active'
  end

  def checkout_cancel
    return nil unless stripe_session_id
    if stripe_session_id
      session = Stripe::Checkout::Session.retrieve(stripe_session_id, STRIPE_PARAMS)
      Stripe::Checkout::Session.expire(stripe_session_id, STRIPE_PARAMS) if session.status == 'open'
    end
    update(stripe_session_id: nil)
    sync_down(force: true)
  end

  def resume
    raise 'Subscription not found' unless stripe_subscription_id.present?
    raise 'Subscription not active' if stripe_subscription.status != 'active'
    Stripe::Subscription.update(stripe_subscription_id, { cancel_at_period_end: false }, STRIPE_PARAMS)
    sync_down(force: true)
  end

  def cancel
    # if free account, return nil if there isn't actually a subscription
    return nil if free_subscription && !(stripe_subscription_id.present? || active?)

    raise 'Subscription not found' unless stripe_subscription_id.present?
    raise 'Subscription already cancelled' if stripe_subscription&.status == 'cancelled'
    StripeSubscription.cancel_end_of_billing(stripe_subscription_id)
    sync_down(force: true)
  end

  def cancel_now
    # if free account, return nil if there isn't actually a subscription
    return nil if free_subscription && !(stripe_subscription_id.present? || active?)

    raise 'Subscription not found' unless stripe_subscription_id.present?
    raise 'Subscription already cancelled' if stripe_subscription&.status == 'cancelled'
    StripeSubscription.cancel_now(stripe_subscription_id)
    sync_down(force: true)
  end

  def destroy
    # only allow deleting a subscription that has been cancelled
    # if we don't have a stripe ID we can assume it was not created on stripe so just delete
    existing_subscription = stripe_subscription
    return super if !stripe_subscription_id.present? || !existing_subscription || existing_subscription && existing_subscription&.status == 'canceled'
    raise 'Subscription must be cancelled on stripe before deleting'
  end

  def sync_down(force: false)
    # do not run if we have a cache that is less than 10 minutes old
    return if !force && stripe_cache && Time.at(stripe_cache['cache_date']) > 10.minutes.ago

    unless stripe_subscription_id
      update(stripe_cache: nil)
      if user.present?
        Thread.new do
          begin
            Mailchimp::SyncService.new.sync_user(user, trigger_type: 'user_update', force: force)
          rescue => e
            Sentry.capture_exception(e) if defined?(Sentry)
            Rails.logger.error("Thread error for sync_user user_id=#{user.id}: #{e.message}")
          end
        end
      end
      return
    end

    subscription = stripe_subscription
    if !subscription
      update(stripe_cache: nil)
    else
      keys_to_extract = %i[
        id
        cancel_at
        cancel_at_period_end
        canceled_at
        current_period_end
        current_period_start
        status
        cancellation_details
      ]
      keep = {}
      keys_to_extract.each do |key|
        keep[key] = subscription[key]
      end
      keep[:cache_date] = Time.now.to_i
      update(stripe_cache: keep)
    end
    if user.present?
      Thread.new do
        begin
          Mailchimp::SyncService.new.sync_user(user, trigger_type: 'user_update', force: force)
        rescue => e
          Sentry.capture_exception(e) if defined?(Sentry)
          Rails.logger.error("Thread error for sync_user user_id=#{user.id}: #{e.message}")
        end
      end
    end
  end

  # ================= Methods to setup stripe data =================

  def self.subscriptions(user, status: nil)
    return nil unless user.stripe_customer_id.present?
    # status cancelled ended all // If no value is supplied, all subscriptions that have not been cancelled are returned.
    subscription = Stripe::Subscription.list({ customer: user.stripe_customer_id, status: status }, STRIPE_PARAMS)
    subscription.data
  end

  def self.invoices(user, starting_after: nil, ending_before: nil)
    return nil unless user.stripe_customer_id.present?
    request_params = { customer: user.stripe_customer_id, limit: 30 }
    request_params[:starting_after] = starting_after if starting_after.present?
    request_params[:ending_before] = ending_before if ending_before.present?
    Stripe::Invoice.list(request_params, STRIPE_PARAMS)
  end

  def self.format_currency(price_pennies, currency)
    ActionController::Base.helpers.number_to_currency(
      price_pennies / 100.0,
      unit: StripeSubscription::CURRENCY_MAP[currency]
    )
  end

  def self.cancel_now(stripe_subscription_id)
    Stripe::Subscription.delete(
      stripe_subscription_id,
      {
        prorate: true,
        invoice_now: true # Create a final invoice for any unbilled charges
      }
    )
    StripeSubscription.find_by(stripe_subscription_id: stripe_subscription_id).sync_down(force: true)
  end

  def self.cancel_end_of_billing(stripe_subscription_id)
    Stripe::Subscription.update(stripe_subscription_id, { cancel_at_period_end: true }, STRIPE_PARAMS)
  end

  def self.checkout(current_user, subscription_type: nil)
    throw 'Subscription type required' if subscription_type.nil?
    # handle if we are passed a string
    subscription_type = subscription_type.to_i if subscription_type.is_a?(String) && subscription_type.match?(/^\d+$/)

    # find or create a new subscription record & checkout that
    if current_user.stripe_subscriptions.where(subscription_type: subscription_type).exists? # rubocop:disable Style/ConditionalAssignment
      subscription = current_user.stripe_subscriptions.find_by(subscription_type: subscription_type)
    else
      subscription = StripeSubscription.create!(user: current_user, subscription_type: subscription_type)
    end

    # if we have an inactive subscription, clear out the stripe id so we can restart it.
    # if a sub is cancelled, we can't resume it
    # if you need to see previous subscriptions - look them up by the customer id
    existing_subscription = subscription.stripe_subscription
    subscription.update(stripe_subscription_id: nil) if existing_subscription && existing_subscription&.status == 'cancelled'

    subscription.checkout(current_user: current_user)
  end

  def self.sync_down
    StripeSubscription.where.not(stripe_subscription_id: nil).each(&:sync_down)
  end

  private

  def unique_subscription_type_per_user
    return unless user && user.stripe_subscriptions.where(subscription_type: subscription_type).where.not(id: id).exists?
    errors.add(:subscription_type, 'has already been taken for this user')
  end
end
