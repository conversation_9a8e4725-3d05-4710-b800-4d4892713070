# == Schema Information
#
# Table name: scientific_enquiry_types
#
#  id              :bigint           not null, primary key
#  title           :string
#  body            :string
#  fileboy_icon_id :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
class ScientificEnquiryType < ApplicationRecord
  validates :title, :fileboy_icon_id, :body, presence: true
  has_many :scientific_enquiry_lessons
  has_many :lesson_templates, through: :scientific_enquiry_lessons

  def fileboy_url
    "https://www.developingexperts.com/file-cdn/files/get/#{fileboy_icon_id}?transform=resize:80x_;format:webp;quality:75"
  end

  def to_html
    html = ""
    html += "<h4>#{title}</h4>"
    html += "<p>#{body}</p>"
    html
  end
end
