# == Schema Information
#
# Table name: user_referrals
#
#  id               :bigint           not null, primary key
#  accepted_at      :datetime
#  email            :string           not null
#  expires_at       :datetime         not null
#  invited_at       :datetime
#  paid_at          :datetime
#  reward_amount    :decimal(8, 2)    default(10.0), not null
#  status           :integer          default("pending"), not null
#  token            :string           not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  referred_user_id :bigint
#  referrer_id      :bigint           not null
#
# Indexes
#
#  index_user_referrals_on_email                  (email)
#  index_user_referrals_on_referred_user_id       (referred_user_id)
#  index_user_referrals_on_referrer_id            (referrer_id)
#  index_user_referrals_on_referrer_id_and_email  (referrer_id,email) UNIQUE
#  index_user_referrals_on_token                  (token) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (referred_user_id => users.id)
#  fk_rails_...  (referrer_id => users.id)
#
class UserReferral < ApplicationRecord
  BASE_REFERRAL_AMOUNT = 20.0
  belongs_to :referrer, class_name: 'User'
  belongs_to :referred_user, class_name: 'User', optional: true
  
  enum status: { pending: 0, accepted: 1, expired: 2, cancelled: 3, completed: 4 }
  
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :token, presence: true, uniqueness: true
  validates :expires_at, presence: true
  validates :reward_amount, presence: true, numericality: { greater_than: 0 }
  
  before_validation :generate_token, on: :create
  before_validation :set_expiration, on: :create
  
  scope :active, -> { where(status: :pending).where('expires_at > ?', Time.current) }
  scope :paid, -> { where.not(paid_at: nil) }
  scope :unpaid, -> { where(paid_at: nil) }

  def request_referral_payout
    update(status: :completed)
    # TODO actually pay out?
  end

  def random_token
    SecureRandom.urlsafe_base64(32)
  end

  private

  def generate_token
    self.token = random_token
  end

  def set_expiration
    self.expires_at = 30.days.from_now # Longer expiry for referrals
  end
end
