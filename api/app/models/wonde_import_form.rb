# == Schema Information
#
# Table name: wonde_import_forms
#
#  id              :bigint           not null, primary key
#  wonde_id        :string
#  form_id         :bigint
#  wonde_data      :jsonb
#  wonde_import_id :bigint
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  name            :string
#
class WondeImportForm < ApplicationRecord
  validates :wonde_id, presence: true
  belongs_to :form, optional: true
  belongs_to :wonde_import

  has_many :wonde_import_pupil_forms, dependent: :destroy
  has_many :wonde_import_pupils, through: :wonde_import_pupil_forms

  has_many :wonde_import_teacher_forms, dependent: :destroy
  has_many :wonde_import_teachers, through: :wonde_import_teacher_forms

  has_one :matching_record, class_name: "Form", primary_key: :wonde_id, foreign_key: :wonde_id

  def v2_as_json(options=nil)
    as_json()
      .merge({
        pupil_ids: wonde_import_pupils.ids,
        teacher_ids: wonde_import_teachers.ids,
        form: form
      })
  end
end
