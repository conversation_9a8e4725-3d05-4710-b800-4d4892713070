# == Schema Information
#
# Table name: uk_schools
#
#  id                      :bigint           not null, primary key
#  academy_trust           :string
#  address                 :string
#  age_range               :string
#  category                :string
#  conflict_sign_ups_count :integer          default(0)
#  county                  :string
#  local_authority         :string
#  name                    :string
#  number_of_pupils        :integer
#  ofsted_last_inspection  :datetime
#  ofsted_rating           :text
#  open_date               :datetime
#  phase_of_education      :string
#  phone                   :string
#  postcode                :string
#  region                  :string
#  sponsors                :string
#  urn                     :integer
#  website                 :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
# Indexes
#
#  index_uk_schools_on_name_and_urn  (name,urn) UNIQUE
#
class UkSchool < ApplicationRecord
  validates :name, presence: true
  has_many :schools
  validates :postcode, presence: true

  after_save do
    update_data = { name: name }
    update_data[:postcode] = postcode if postcode.present?
    update_data[:telephone] = phone if phone.present?
    schools.update_all(update_data)
  end

  def school 
    schools.first
  end
end
