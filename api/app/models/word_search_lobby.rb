# frozen_string_literal: true

# == Schema Information
#
# Table name: word_search_lobbies
#
#  id         :bigint           not null, primary key
#  startsAt   :datetime
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class WordSearchLobby < ApplicationRecord
  has_many :word_search_lobby_users, dependent: :destroy
  has_many :users, through: :word_search_lobby_users

  def purge_complete_lobbies
    # get lobbies where all users have completed the game
    # and the lobby was created recently (so we don't get a huge backlog that will make this task)
    # take a long time to run
    WordSearchLobby
      .where('"startsAt" <= ?', Time.now - 20.minutes) # at least 20 minutes old
      .left_outer_joins(:word_search_lobby_users)
      .where.not(word_search_lobby_users: { id: nil }) # has users
      .where.not(word_search_lobby_users: { finishedAt: nil }) # all users have finished
      .destroy_all
  end

  def delete_cache
    custom_cache.delete(cache_key)
  end

  def generate_cache
    # Fetch or cache the JSON response for 10 seconds
    custom_cache.fetch(cache_key, expires_in: 10.seconds) do
      lobby_to_json
    end
  end

  private

  def cache_key
    "lobby-show-#{id}"
  end

  def lobby_to_json
    # Create a hash for fast lookup of lobby_user by user_id
    lobby_users_hash = word_search_lobby_users.includes(:user).index_by(&:user_id)

    {
      id: id,
      startsAt: startsAt,
      users: users.map do |user|
        lobby_user = lobby_users_hash[user.id] # Use the pre-loaded hash instead of querying
        {
          user_id: user.id,
          name: user.name,
          gameName: user.alias,
          id: lobby_user&.id,
          score: lobby_user&.score,
          words: lobby_user&.words,
          finishedAt: lobby_user&.finishedAt,
        }
      end
    }
  end
end
