# frozen_string_literal: true

# == Schema Information
#
# Table name: quiz_questions
#
#  id                        :bigint           not null, primary key
#  answer                    :string
#  body                      :string
#  deleted                   :boolean          default(FALSE), not null
#  last_modified_at          :datetime
#  last_modified_by          :integer
#  question_body             :string           default("")
#  question_type             :integer
#  question_video_url        :string           default("")
#  use_data                  :boolean          default(FALSE)
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  lesson_keyword_id         :bigint
#  lesson_slide_id           :bigint
#  lesson_template_id        :bigint
#  question_fileboy_image_id :string           default("")
#
# Indexes
#
#  index_quiz_questions_on_lesson_keyword_id   (lesson_keyword_id)
#  index_quiz_questions_on_lesson_slide_id     (lesson_slide_id)
#  index_quiz_questions_on_lesson_template_id  (lesson_template_id)
#
# Foreign Keys
#
#  fk_rails_...  (lesson_keyword_id => lesson_keywords.id)
#  fk_rails_...  (lesson_slide_id => lesson_slides.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#
module QuizOld
  class Question < ApplicationRecord
    belongs_to :lesson_template, class_name: 'Lesson::Template'
    belongs_to :lesson_slide, optional: true, class_name: 'Lesson::Slide'
    belongs_to :lesson_keyword, optional: true, class_name: 'Lesson::Keyword'

    enum question_type: %i[formative summative]

    scope :anonymous, -> { where(lesson_template_id: Lesson::Template.anonymous) }

    def weight
      lesson_keyword&.weight
    end

    def v1_as_json(options = nil)
      as_json(options).merge({
        keyword_name: lesson_keyword&.name,
        slide_number: lesson_slide&.weight,
      })
    end
  end
end
