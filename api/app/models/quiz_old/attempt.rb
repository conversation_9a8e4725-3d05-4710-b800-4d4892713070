# frozen_string_literal: true

# == Schema Information
#
# Table name: quiz_attempts
#
#  id                 :bigint           not null, primary key
#  deleted            :boolean          default(FALSE), not null
#  school_year        :integer
#  school_year_start  :date
#  score              :integer          default(0), not null
#  time               :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_template_id :bigint
#  user_id            :bigint
#
# Indexes
#
#  index_quiz_attempts_on_lesson_template_id  (lesson_template_id)
#  index_quiz_attempts_on_user_id             (user_id)
#
module QuizOld
  class Attempt < ApplicationRecord
    include SchoolYear

    validates :score, presence: true
    validate { errors.add(:user, 'must be a pupil') unless user&.pupil? }

    belongs_to :user
    has_one :school, through: :user
    has_one :lesson_result, :class_name => 'Lesson::Result', dependent: :nullify, foreign_key: :last_quiz_attempt_id
    belongs_to :lesson_template, class_name: 'Lesson::Template'

    scope :ordered, -> { order(created_at: :desc) }
    scope :in_progress, -> { where(complete: false) }

    attr_accessor :lesson_id
    after_create(if: :lesson_id) do
      Lesson::Result.where(lesson_id: lesson_id, pupil_id: user_id)
                    .update_all(last_quiz_attempt_id: id)
    end

    after_create do
      user.add_points(score * 10)
      UserRankEvent.add_points(user, score * 2, "rocket_words")
    end

    after_save :update_lesson_average_marks_cache
    after_destroy :update_lesson_average_marks_cache

    def update_lesson_average_marks_cache
      return if destroyed_by_association
      user&.lessons&.find_by(template: lesson_template)&.update_average_marks_cache
      user&.forms&.map(&:update_average_marks_cache)
    end
  end
end
