# frozen_string_literal: true

# == Schema Information
#
# Table name: xls_imports
#
#  id              :bigint           not null, primary key
#  build_lessons   :boolean          default(FALSE), not null
#  complete        :boolean          default(FALSE), not null
#  data            :jsonb            not null
#  deleted         :boolean          default(FALSE), not null
#  file_name       :string
#  file_uid        :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  fileboy_file_id :string
#  school_id       :bigint
#  user_id         :bigint
#
# Indexes
#
#  index_xls_imports_on_school_id  (school_id)
#  index_xls_imports_on_user_id    (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (school_id => schools.id)
#
require 'roo'
require 'bcrypt'

class XlsImport < ApplicationRecord
  belongs_to :user, optional: true

  def self.interpolate * args
    "<{#{args.map { |arg| String(arg).tr(':<>{}', '') }.join(':')}}>"
  end

  delegate :interpolate, to: :class

  MESSAGES = {
    en: {
      _blank: 'cannot be blank.',
      _unreadable: "could not be read, check that it's an XLSX spreadsheet and that it isn't locked with a password.",
      _bad_sheets: "did not contain the expected sheets. Make sure it's based on our #{interpolate :template, "template"} and no sheets have been added, deleted or renamed.",
      _bad_headers: "did not contain the expected headers. Make sure the spreadsheet is based on our #{interpolate :template, "template"} and no sheets have been added, deleted or renamed.",
      email_matches_non_parent: 'Email matches a user who doesn\'t have a parent account',
      email_blank: 'Teacher has no email.',
      email_not_valid: 'This is not a valid email address.',
      email_not_unique: 'This email appears for multiple teachers. Emails must be unique.',
      email_in_use: -> (id, name) { "This email is already in use by #{interpolate :school, id, name}. Emails must be unique across the site." },
      email_matches_demo_account: "This email matches an email associated to an account for a demo school. Importing will update that account to be inactive.",
      email_misspelling: -> (pre_text, post_text, mistake, correction) { "This email appears to be misspelled. Consider changing: #{interpolate :spelling, pre_text, mistake, post_text} to #{interpolate :spelling, pre_text, correction, post_text}" },
      identifier_not_unique: 'This login code appears for multiple pupils. Login codes must be unique.',
      name_not_unique: 'Multiple existing pupils have this name. A Login code is required to specify which one of them this row refers to.',
      class_empty: "This class doesn't have any pupils assigned to it, it may have been misspelled.",
      class_misspelling: -> (pre_text, post_text, mistake, correction) { "This class name appears to be misspelled. Consider changing: #{interpolate :spelling, pre_text, mistake, post_text} to #{interpolate :spelling, pre_text, correction, post_text}" },
    },
    zh: {
      _blank: '不可以空白.',
      _unreadable: "不可以读取, 检查XLSX表格，还未被密码锁定 .",
      _bad_sheets: "出现不应该有的书页. 请确保这是在我们的 #{interpolate :template, "数据里"} 没有书页被添加, 删除或者重新命名.",
      _bad_headers: "没有正确的表格. 请确保电子表是在我们的  #{interpolate :template, "数据里"} 没有书页被添加, 删除或者重新命名.",
      email_matches_non_parent: '电子邮件与没有上级帐户的用户匹配',
      email_blank: '老师没有邮箱.',
      email_not_valid: '这是一个无效的邮箱地址.',
      email_not_unique: '这个邮箱地址已被使用，请使用不同的地址邮箱',
      email_in_use: -> (id, name) { "这个邮件已经被其他人使用。邮箱地址必须是独一无二的" },
      email_misspelling: -> (pre_text, post_text, mistake, correction) { "这个电子邮箱存在错误. 请更改: #{interpolate :spelling, pre_text, mistake, post_text} #{interpolate :spelling, pre_text, correction, post_text}" },
      identifier_not_unique: '这个身份重复出现，请确定身份必须是不同的。',
      name_not_unique: '已有存在的学生使用了这个姓名，身份验证要求不同的学生填写这一列。',
      class_empty: "这个班级暂时还没有学生，这可能是因为班级名称书写错误导致的。",
      class_misspelling: -> (pre_text, post_text, mistake, correction) { "这个班级的名称出现书写错误. 请更改: #{interpolate :spelling, pre_text, mistake, post_text} #{interpolate :spelling, pre_text, correction, post_text}" },
    }
  }

  def xls_errors
    -> (key, *args) do
      locale = self.locale || :en
      if MESSAGES[locale][key].is_a?(Proc)
        MESSAGES[locale][key][*args]
      else
        MESSAGES[locale][key]
      end
    end
  end

  PASSWORD = BCrypt::Password.create('Password123@')

  def sample_letter
    (('a'..'z').to_a - %w[o l z i]).sample(1).join
  end

  def sample_number
    ((3..9).to_a).sample(1).join
  end

  def gen_identifier
    [sample_letter, sample_number, sample_number, sample_letter, sample_letter, sample_number].join("")
  end

  belongs_to :school, required: false
  validates :school, :fileboy_file_id, presence: { message: proc { |record| record.xls_errors[:_blank] } }

  attr_accessor :run_import
  attr_accessor :ignore_warnings
  attr_accessor :locale

  after_save(if: :saved_change_to_fileboy_file_id?) do
    import_file
    raise ActiveRecord::RecordInvalid, self if errors.any?
  end

  after_save do
    next unless run_import
    self.run_import = false
    process_data
  end

  def build_fileboy_url
    "#{ENV['FILEBOY_URL']}/files/get/#{fileboy_file_id}"
  end

  def workbook
    @workbook ||= Roo::Spreadsheet.open(build_fileboy_url, { extension: :xlsx })
  rescue => e
    puts e
  end

  def import_file
    return errors.add(:file, xls_errors[:_blank]) unless fileboy_file_id
    return errors.add(:file, xls_errors[:_unreadable]) unless workbook

    unless workbook.sheets[0].match?(/School Staff|学校教师/i) &&
      workbook.sheets[1..-1].all? do |s|
        s.match?(/(Reception|Year\s+\d+)(\s+Pupils)?/i) || s.match?(/年级学生/i)
      end
      return errors.add(:file, xls_errors[:_bad_sheets])
    end

    self.data = { teachers: [], pupils: [] }

    workbook.sheets.each.with_index do |sheet_name, i|
      if i == 0
        read_teacher_sheet workbook.sheet(sheet_name).parse, sheet_name
      else
        read_pupil_sheet workbook.sheet(sheet_name).parse, sheet_name
      end
    end

    save!
    process_data
  end

  def read_teacher_sheet(rows, sheet_name)
    header_index = rows.index do |row|
      row.count { |cell| cell.to_s.tr(' -', '').match? /email|name|class/i } >= 3 ||
        row.count { |cell| cell.to_s.match? /全名|班级名称/i } >= 2
    end
    unless header_index
      return errors.add("Sheet #{sheet_name.inspect}", xls_errors[:_bad_headers])
    end
    rows = rows[(header_index + 1)..-1]

    data["teachers"] += rows.select { |row| row[1].present? }.map do |row|
      {
        name: { content: row[1].to_s.squish.titleize },
        email: { content: row[3].to_s.downcase.squish },
        class: { content: row[2].to_s.squish.titleize },
        admin: { content: row[0].to_s.match?(/admin|管理员/i).to_s },
      }
    end
  end

  def read_pupil_sheet(rows, sheet_name)
    header_index = rows.index do |row|
      row.count { |cell| cell.to_s.match? /name|class/i } >= 2 ||
        row.count { |cell| cell.to_s.match? /全名|班级名称/i } >= 2
    end
    unless header_index
      return errors.add("Sheet #{sheet_name.inspect}", xls_errors[:_bad_headers])
    end
    rows = rows[(header_index + 1)..-1]

    data["pupils"] += rows.select { |row| row[0..2].any?(&:present?) }.map do |row|
      {
        name: { content: (row[2].present? ? row[2] : "#{row[0]} #{row[1]}").to_s.squish.titleize },
        identifier: { content: row[4].present? ? row[4].to_s : "" },
        class: { content: row[3].to_s.squish.titleize },
        year: { content: sheet_name[/\d/].to_i.to_s },
      }
    end
  end

  def process_data
    transaction(requires_new: true) do
      @forms = {} # A map of objects (indexed by name)
      @teachers = {} # A map of objects (indexed by name)
      @pupils = [] # An array of objects
      @import_failed = false
      @identifiers = Set.new

      read_teacher_data
      read_pupil_data

      create_forms
      create_pupils
      create_teachers

      raise ActiveRecord::Rollback if import_failed?
      send_xls_complete_email if !import_failed? && Rails.env.production?
    end

    update!(data: data, complete: !import_failed?, ignore_warnings: false)
  end

  def send_xls_complete_email
    AdminMailer.xls_import_completed(self).deliver_now
  end

  def read_teacher_data
    return unless data["teachers"].present?

    data["teachers"].each do |raw_row|
      row = -> (key) {
        raw_row[key.to_s]["content"]
      }

      teacher = @teachers[row[:name]] ||= {
        name: row[:name],
        email: "",
        uid: "",
        provider: :email,
        forms: Set.new,
        type: 'Teacher',
        school_id: school_id,
        password_digest: PASSWORD,
        sources: [raw_row],
      }

      if row[:admin] == "true"
        teacher[:is_school_admin] = true
      end

      if row[:email].present?
        teacher[:email] = row[:email]
        teacher[:uid] = row[:email]
      end

      if row[:class].present?
        @forms[row[:class]] ||= { years: Set.new, sources: [] }
        @forms[row[:class]][:sources] << raw_row
        teacher[:forms] << row[:class]
      end
    end
  end

  def new_identifier
    @identifiers ||= Set.new
    loop do
      identifier = gen_identifier()
      next if @identifiers.include?(identifier)
      @identifiers.add(identifier)
      return identifier
    end
  end

  def read_pupil_data
    @pupils = []
    return unless data["pupils"].present?
    
    @pupils = data["pupils"].map do |raw_row|
      row = -> (key) { raw_row[key.to_s]["content"] }

      identifier = row[:identifier]
      has_specific_identifier = identifier.present?
      identifier = new_identifier unless has_specific_identifier

      if row[:class].present?
        @forms[row[:class]] ||= { years: Set.new, sources: [] }
        @forms[row[:class]][:years] << row[:year].to_i
        @forms[row[:class]][:sources] << raw_row
      end

      {
        name: row[:name],
        provider: :email,
        type: 'Pupil',
        school_id: school_id,
        identifier: identifier,
        has_specific_identifier: has_specific_identifier,
        email: "#{identifier}-#{school_id}-<EMAIL>".downcase,
        uid: "#{identifier}-#{school_id}-<EMAIL>".downcase,
        password_digest: PASSWORD,
        specified_year: row[:year].to_i,
        specified_class: row[:class],
        sources: [raw_row],
      }
    end
  end

  def add_error(row, field, message)
    row[:sources].map { |source| source[field.to_s]["error"] ||= message }
    @import_failed = true
  end

  def add_warning(row, field, message)
    row[:sources].map { |source| source[field.to_s]["warning"] ||= message }
    return if ignore_warnings
    @import_failed = true
  end

  def import_failed?
    !!@import_failed
  end

  def create_forms
    dictionary = []
    spell_checker = DidYouMean::SpellChecker.new(dictionary: dictionary)

    @forms.sort_by { |k, v| 0 - v[:sources].size }.each do |name, row|
      if row[:sources].all? { |source| source.has_key?("email") }
        if (correct_name = spell_checker.correct(name).first)
          add_warning(
            row,
            :class,
            xls_errors[:class_misspelling, *highlight_correction(name, correct_name)],
          )
        else
          add_warning(row, :class, xls_errors[:class_empty])
        end
      end

      dictionary << name
    end

    return if import_failed?

    existing_forms = Form.current_school_year.where(school_id: school_id).to_a

    forms_to_create = []
    @forms.each_key do |k|
      existing_row = existing_forms.find { |form| form.name == k }
      if (existing_row)
        @forms[k][:id] = existing_row.id
      else
        forms_to_create << {
          name: k,
          school_id: school_id,
          school_year: school.current_year,
          school_year_start: school.year_start
        }
      end
    end

    ids = insert_values(:forms, forms_to_create)
    forms_to_create.each.with_index { |form, i| @forms[form[:name]][:id] = ids[i] }
  end

  def create_teachers
    teachers = @teachers.values
    emails = teachers.pluck(:email).compact

    teachers.each do |teacher|
      if teacher[:email].blank?
        add_error(teacher, :email, xls_errors[:email_blank])
        next
      end

      unless teacher[:email].match(/\A[^@\s]+@[^@\s]+\.[^@\s]+\z/)
        add_error(teacher, :email, xls_errors[:email_not_valid])
      end
    end

    domains = emails.map { |email| email.split("@").last }
    correct_domain, count = domains.map { |domain| [domain, domains.count(domain)] }.max_by(&:last)

    if (emails.any? && count > 1)
      spell_checker = DidYouMean::SpellChecker.new(dictionary: [correct_domain])

      teachers.each do |teacher|
        next unless teacher[:email].present?
        name, domain = teacher[:email].split('@')
        next unless spell_checker.correct(domain).any?

        pre, post, mistake, correction = highlight_correction(domain, correct_domain, '.')

        add_warning(
          teacher,
          :email,
          xls_errors[:email_misspelling, name + '@' + pre, post, mistake, correction],
        )
      end
    end

    non_unique_elements(teachers, :email).each do |teacher|
      add_error(teacher, :email, xls_errors[:email_not_unique])
    end

    conflicts = User
                  .joins(:school)
                  .where(email: emails)
                  .where.not(school_id: school_id)
                  .pluck('lower(users.email)', 'schools.id', 'schools.name', 'schools.school_type', 'users.id')

    teacher_ids_to_update_as_inactive = []

    conflicts.each do |email, school_id, school_name, school_type, conflict_teacher_id|
      teachers.each do |teacher|
        next if teacher[:email] != email
        if school.present? && school.generic? && school_type == School.school_types["demo"]
          teacher_ids_to_update_as_inactive.push(conflict_teacher_id)
          add_warning(teacher, :email, xls_errors[:email_matches_demo_account])
        else
          add_error(teacher, :email, xls_errors[:email_in_use, school_id, school_name])
        end
      end
    end

    return if import_failed?

    teacher_ids_to_update_as_inactive.each do |teacher_id_to_update_as_inactive|
      teacher_to_update_as_inactive = Teacher.find(teacher_id_to_update_as_inactive)
      email_parts = teacher_to_update_as_inactive.email.split("@")
      email_parts[0] = "#{email_parts[0]}_inactive_#{self.id}"
      teacher_to_update_as_inactive.update!(email: email_parts.join("@"))
    end

    updated_teachers = User.where(school_id: school_id).where(email: emails).map do |teacher|
      teacher_data = teachers.find { |t| t[:email] == teacher.email }
      teachers = teachers.reject { |t| t[:email] == teacher.email }
      { id: teacher.id, forms: teacher_data[:forms] }
    end

    insert_values(
      :enrollments,
      updated_teachers.map do |t|
        t[:forms].map do |form|
          next if Enrollment.exists?(user_id: t[:id], form_id: @forms[form][:id])
          { user_id: t[:id], form_id: @forms[form][:id] }
        end
      end
    )

    ids = insert_values :users, (teachers.map { |t| t.except(:forms, :sources) })

    insert_values(
      :enrollments,
      teachers.map.with_index do |t, i|
        t[:forms].map { |form| { user_id: ids[i], form_id: @forms[form][:id] } }
      end
    )
  end

  def create_pupils
    pupils_to_create = []
    pupils_to_update = []

    pupils_with_identifier, pupils_without_identifier = @pupils.partition do |pupil|
      pupil.delete(:has_specific_identifier)
    end

    non_unique_elements(pupils_with_identifier, :identifier).each do |pupil|
      add_error(pupil, :identifier, xls_errors[:identifier_not_unique])
    end

    pupils_by_name = Pupil
                       .where(school_id: school_id, name: pupils_without_identifier.pluck(:name))
                       .pluck(:id, :identifier, :name)
                       .map { |id, identifier, name| { id: id, identifier: gen_identifier(), name: name } }

    non_unique_elements(pupils_by_name.pluck(:name)).each do |name|
      pupils_without_identifier.each do |pupil|
        next unless pupil[:name] == name
        add_error(pupil, :name, xls_errors[:name_not_unique])
      end
    end

    #################################
    #################################

    pupils_without_identifier.each do |pupil|
      existing_record = pupils_by_name.find { |p| p[:name] == pupil[:name] }
      if existing_record
        pupils_to_update << {
          **pupil,
          id: existing_record[:id],
          identifier: existing_record[:identifier]
        }
      else
        pupils_to_create << pupil
      end
    end

    pupils_by_identifier = Pupil
                             .where(school_id: school_id, identifier: pupils_with_identifier.pluck(:identifier))
                             .pluck(:id, :identifier)
                             .map { |id, identifier| { id: id, identifier: identifier } }

    pupils_with_identifier.each do |pupil|
      existing_record = pupils_by_identifier.find { |p| p[:identifier] == pupil[:identifier] }
      if existing_record
        pupils_to_update << { **pupil, id: existing_record[:id] }
      else
        pupils_to_create << pupil
      end
    end

    return if import_failed?

    insert_values(
      :enrollments,
      pupils_to_update.map do |p|
        next unless p[:specified_class].present?
        next if Enrollment.exists?(user_id: p[:id], form_id: @forms[p[:specified_class]][:id])
        { user_id: p[:id], form_id: @forms[p[:specified_class]][:id] }
      end
    )

    ids = insert_values(
      :users,
      pupils_to_create.map { |p| p.except(:has_specific_identifier, :sources) },
    )

    pupils_to_create = pupils_to_create.map.with_index do |pupil, i|
      pupil.merge({ id: ids[i] })
    end

    insert_values(
      :enrollments,
      pupils_to_create.map.with_index do |pupil, i|
        next unless pupil[:specified_class].present?
        { user_id: pupil[:id], form_id: @forms[pupil[:specified_class]][:id] }
      end
    )
  end

  # Given records as an array of hashes, inserts them into the table and returns their ids
  def insert_values(table, records = [])
    records = Array.wrap(records).flatten.compact
    return [] unless records.any?

    time = Time.current
    keys = Set.new
    records.each { |record| keys += record.keys }
    values = records.map { |record| keys.map { |k| record[k] } + [time, time] }
    columns = (keys.to_a + %i[created_at updated_at]).join(', ')
    values = values.map { |school| +'(' + school.map { |v| escape(v) }.join(', ') + ')' }.join(', ')
    self.class.connection.execute(<<-SQL.squish).values.flatten
      INSERT INTO #{table} (#{columns}) VALUES #{values} RETURNING id
    SQL
  end

  # Returns elements from a given array which appear more than once
  def non_unique_elements(array, key = nil, &transform)
    transform ||= key ? proc { |x| x[key] } : proc { |x| x }
    counts = Hash.new(0)
    array.each { |element| counts[transform[element]] += 1 }
    array.select { |element| counts[transform[element]] > 1 }.uniq
  end

  # Returns the longest subarray of two given arrays, anchored at the start
  def subarray(a, b)
    i = 0
    a.each { |c| c == b[i] ? i += 1 : break }
    a.slice(0, i)
  end

  # Returns highlight data about a spelling suggestion
  def highlight_correction(mistake, correction, seperator = ' ')
    mistake_parts = mistake.split(seperator)
    correct_parts = correction.split(seperator)

    pre = [*subarray(mistake_parts, correct_parts), nil].join(seperator)
    post = [*subarray(mistake_parts.reverse, correct_parts.reverse), nil].reverse.join(seperator)
    mistake = mistake.delete_prefix(pre).delete_suffix(post)
    correction = correction.delete_prefix(pre).delete_suffix(post)

    return [pre, post, mistake, correction]
  end
end
