# == Schema Information
#
# Table name: videos2
#
#  id                        :bigint           not null, primary key
#  ai_summary                :text
#  analyzed_at               :datetime
#  appears_in_search         :boolean          default(FALSE)
#  body                      :string
#  duration                  :float
#  fileboyVideoId            :string
#  is_assignment             :boolean          default(FALSE)
#  is_available_in_ai_search :boolean
#  is_career                 :boolean          default(FALSE)
#  is_expert                 :boolean          default(FALSE)
#  is_sponsored              :boolean          default(FALSE)
#  keywords                  :text
#  name                      :string
#  processing                :boolean          default(FALSE)
#  slug                      :string
#  source                    :string
#  subjects                  :string
#  tour_scene                :string
#  transcript                :text
#  video_url                 :string
#  created_at                :datetime         not null
#  updated_at                :datetime         not null
#  campaign_id               :bigint
#  external_id               :string
#  tour_id                   :bigint
#
# Indexes
#
#  index_videos2_on_campaign_id  (campaign_id)
#  index_videos2_on_tour_id      (tour_id)
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#  fk_rails_...  (tour_id => tours.id)
#
class Video < ApplicationRecord
  include Taggable
  extend FriendlyId
  friendly_id :name, use: :slugged

  self.table_name = "videos2"

  belongs_to :campaign, optional: true
  belongs_to :tour, optional: true

  has_one :career, foreign_key: :video_id
  has_one :de_media, foreign_key: 'videos2_id', class_name: 'DeMedia'
  has_many :live_streams, dependent: :nullify

  VALID_SUBJECTS = %w[science history geography english art].freeze

  before_validation :set_default_subject

  after_update :fetch_video_data, if: :saved_change_to_external_id?

  validates :name, :source, :external_id, presence: true
  validates_each :subjects do |record, attr, value|
    if value.present?
      subjects_array = value.split(',').map(&:strip).map(&:downcase)
      unless subjects_array.all? { |subject| VALID_SUBJECTS.include?(subject) }
        record.errors.add(attr, 'contains invalid subjects')
      end
    else
      record.errors.add(attr, 'cannot be nil or empty')
    end
  end

  has_many :articles
  has_many :video_views, foreign_key: :videos2_id, dependent: :destroy
  has_many :career_courses
  has_many :career_vacancies
  has_many :events
  has_many :campaign_units, foreign_key: :videos2_id
  has_many :campaign_lessons, foreign_key: :video_id
  has_many :lesson_slides, class_name: "Lesson::Slide", foreign_key: :video_id
  has_many :documents, as: :resource, class_name: 'NewLibrary::Document'

  has_and_belongs_to_many :career_paths, join_table: :career_paths_videos

  scope :searchable, -> { where(appears_in_search: true) }

  after_save :keep_old_and_new_formats_in_sync

  validate :validate_external_id

  def validate_external_id
    return unless source.present? && external_id.present?
    if source == 'fileboy' && !external_id.match?(/\A[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\z/i)
      errors.add(:external_id, 'Fileboy ID expects a valid UUID format')
    end
    if source == 'youtube' && external_id.length != 11
      errors.add(:external_id, 'Youtube ID expects a string of 11 characters')
    end
    if source == 'vimeo' && external_id !~ /\A\d+\z/
      errors.add(:external_id, 'Vimeo ID expects expects a string of numbers')
    end
  end

  def self.search(query)
    left_joins(:tags).where("videos2.name ILIKE :query OR tags.name ILIKE :query", query: "%#{query}%")
  end

  def attached_to
    attached_to = {}
    attached_to[:articles] = articles.count
    attached_to[:events] = events.count
    attached_to[:career_courses] = career_courses.count
    attached_to[:career_vacancies] = career_vacancies.count
    attached_to[:campaign_units] = campaign_units.count
    attached_to[:campaign_lessons] = campaign_lessons.count
    attached_to[:lesson_slides] = lesson_slides.count
    attached_to
  end

  def keep_old_and_new_formats_in_sync
    if fileboyVideoId.present? && source.blank?
      self.source = "fileboy"
      self.external_id = fileboyVideoId
      save!
    end

    if video_url.present? && source.blank?
      youtube_match = VideoUrlHelpers::YOUTUBE_REGEX.match(video_url)
      if youtube_match.present?
        self.source = "youtube"
        self.external_id = youtube_match[1]
        save!
      end

      vimeo_match = VideoUrlHelpers::VIMEO_REGEX.match(video_url)
      if vimeo_match.present?
        self.source = "vimeo"
        self.external_id = vimeo_match[1]
        save!
      end
    end

    if source.present? && source == 'fileboy' && fileboyVideoId.blank?
      self.fileboyVideoId = external_id
      save!
    end

    if source.present? && source == 'youtube' && video_url.blank?
      self.video_url = "https://www.youtube.com/watch?v=#{external_id}"
      save!
    end

    if source.present? && source == 'vimeo' && video_url.blank?
      self.video_url = "https://vimeo.com/#{external_id}"
      save!
    end
  end

  def thumbnail_url
    case source
    when 'youtube'
      "https://img.youtube.com/vi/#{external_id}/maxresdefault.jpg"
    when 'fileboy'
      "https://www.developingexperts.com/file-cdn/videos/#{external_id}/poster?transform=width:640"
    when 'vimeo'
      "https://vumbnail.com/#{external_id}.jpg"
    else
      ""
    end
  end

  def self.video_url_type(video_url)
    VideoUrlHelpers.video_url_type(video_url)
  end

  def self.video_embed_url(video_url)
    VideoUrlHelpers.video_embed_url(video_url)
  end

  def v2_as_json(options = nil)
    as_json()
      .merge({
               campaign: campaign ? { name: campaign.name, id: campaign.id, organisation: { name: campaign&.organisation&.name, id: campaign&.organisation&.id } } : nil,
               views: video_views.count,
             })
  end

  def analyze_with_ai!
    VideoTranscriptAnalysisJob.perform_later(self)
  end

  SERVICE_MAP = {
    'fileboy' => Videos::FileboyService,
    'youtube' => Videos::YoutubeService,
    'vimeo' => Videos::VimeoService
  }.freeze

  def fetch_video_data
    return unless %w[fileboy youtube vimeo].include?(source)

    service_class = SERVICE_MAP[source]
    service_class&.new(self)&.fetch_and_process
  end

  def similar_videos(limit = 8)
    candidates = Video
      .searchable
      .left_joins(:tags)
      .where.not(id: id)
      .where(
        "videos2.name ILIKE :name OR videos2.keywords ILIKE :keywords OR tags.id IN (:tag_ids)",
        name: "%#{name}%",
        keywords: "%#{keywords}%",
        tag_ids: tag_ids
      )
      .distinct

    scored = candidates.to_a.map do |video|
      [video, similarity_score(video)]
    end

    scored.sort_by { |_, score| -score }
          .first(limit)
          .map(&:first)
  end

  def appears_in_lessons
    Lesson::Template.where(id: Lesson::Slide.where(video_id: id).pluck(:template_id)).available.platform
  end

  def to_param
    if RequestStore.store[:admin_request]
      id.to_s
    else
      super  # returns slug
    end
  end

  def merge_videos_with_same_details 
    matching_videos = Video.where(source: source, external_id: external_id)
    return if matching_videos.count <= 1

    matching_videos.merge_videos
  end

  def self.merge_videos
    source = self.pluck(:source).uniq
    external_ids = self.pluck(:external_id).uniq

    # Check that all sources and external ids are the same
    if source.size > 1 || external_ids.size > 1
      raise "Cannot merge videos with different sources or external IDs"
    end

    # Get lesson slides that reference these videos
    lesson_slides = Lesson::Slide.where(video_id: self.pluck(:id))

    # Update lesson slides to reference the first video
    lesson_slides.update_all(video_id: self.first.id)

    # Delete all other videos
    self.where.not(id: self.first.id).destroy_all
    self.first
  end

  def formatted_duration
    return unless duration.present?

    minutes = (duration / 60).floor
    seconds = (duration % 60).floor

    "#{minutes}m #{seconds}s"
  end

  private

  def set_default_subject
    self.subjects = 'science' if subjects.blank?

    subjects_array = subjects.split(',').map(&:strip).map(&:downcase)
    self.subjects = subjects_array.join(',')
  end

  def similarity_score(other)
    score = 0

    score += 2 if other.name.to_s.downcase.include?(name.to_s.downcase) || name.to_s.downcase.include?(other.name.to_s.downcase)

    keywords_self = keywords.to_s.downcase.split(',').map(&:strip)
    keywords_other = other.keywords.to_s.downcase.split(',').map(&:strip)
    score += (keywords_self & keywords_other).size

    score += (tag_ids & other.tag_ids).size * 3

    score
  end
end
