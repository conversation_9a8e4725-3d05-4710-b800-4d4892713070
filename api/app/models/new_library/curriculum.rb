# frozen_string_literal: true

# == Schema Information
#
# Table name: new_library_curricula
#
#  id               :bigint           not null, primary key
#  image_cache      :jsonb            not null
#  image_name       :string
#  image_uid        :string
#  name             :string           not null
#  published        :boolean
#  slug             :string
#  weight           :integer          default(0), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  country_id       :bigint
#  fileboy_image_id :string
#
# Indexes
#
#  index_new_library_curricula_on_country_id  (country_id)
#  index_new_library_curricula_on_slug        (slug) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (country_id => countries.id)
#
module NewLibrary
  class Curriculum < ApplicationRecord
    belongs_to :country

    has_many :old_years, class_name: 'NewLibrary::Year', foreign_key: :curriculum_id

    has_many :subjects, class_name: 'NewLibrary::Subject', dependent: :nullify
    has_many :years, through: :subjects
    has_many :units, through: :years
    has_many :lesson_templates, through: :units
    has_many :documents, as: :resource, class_name: 'NewLibrary::Document'


    scope :published, -> { where(published: true) }
    scope :anon, -> { where(id: 18) }

    validates :name, presence: true

    accepts_nested_attributes_for :documents
    alias documents= documents_attributes=

    def v1_as_json(options = nil)
      as_json(options).merge(year_ids: year_ids)
    end
  end
end
