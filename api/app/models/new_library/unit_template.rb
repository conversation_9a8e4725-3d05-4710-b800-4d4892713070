# frozen_string_literal: true

# == Schema Information
#
# Table name: new_library_unit_templates
#
#  id          :bigint           not null, primary key
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  template_id :bigint           not null
#  unit_id     :bigint           not null
#
# Indexes
#
#  index_new_library_unit_templates_on_template_id              (template_id)
#  index_new_library_unit_templates_on_unit_id                  (unit_id)
#  index_new_library_unit_templates_on_unit_id_and_template_id  (unit_id,template_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#  fk_rails_...  (unit_id => new_library_units.id)
#
module NewLibrary
  class UnitTemplate < ApplicationRecord
    belongs_to :unit, class_name: "NewLibrary::Unit"
    belongs_to :template, class_name: "Lesson::Template"
  end
end
