# frozen_string_literal: true

# == Schema Information
#
# Table name: new_library_units
#
#  id                   :bigint           not null, primary key
#  details              :string
#  featured_on_homepage :boolean          default(FALSE)
#  image_cache          :jsonb            not null
#  image_name           :string
#  image_uid            :string
#  jw_body              :text
#  jw_title             :string
#  name                 :string           not null
#  new_in_2024          :boolean          default(FALSE)
#  slug                 :string
#  summary              :string
#  weight               :integer          default(0), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  fileboy_image_id     :string
#  fileboy_video_id     :string
#  jw_id                :string
#  video_id             :integer
#  year_id              :bigint
#
# Indexes
#
#  index_new_library_units_on_slug     (slug) UNIQUE
#  index_new_library_units_on_year_id  (year_id)
#
# Foreign Keys
#
#  fk_rails_...  (year_id => new_library_years.id)
#
module NewLibrary
  class Unit < ApplicationRecord
    belongs_to :year, class_name: 'NewLibrary::Year', optional: true
    has_one :subject, through: :year

    belongs_to :video, optional: true
    has_one :curriculum, through: :year
    has_many :documents, as: :resource, class_name: 'NewLibrary::Document'
    has_many :campaign_unit_units, foreign_key: :new_library_unit_id
    has_many :campaign_units, through: :campaign_unit_units
    has_many :lesson_template_libary_units, class_name: 'NewLibrary::UnitTemplate', dependent: :nullify

    has_many :lesson_templates, through: :lesson_template_libary_units, source: :template, class_name: "Lesson::Template"
    has_many :user_lesson_templates, through: :lesson_templates, source: :user_templates, class_name: 'Lesson::Template'

    has_many :tracking_lesson_template_views, class_name: "TrackingLessonTemplateViewed", through: :lesson_templates
    has_many :authors, -> { distinct }, through: :lesson_templates

    has_many :campaign_unit_units, foreign_key: :new_library_unit_id
    has_many :campaign_units, through: :campaign_unit_units
    has_many :campaign_unit_views, through: :campaign_units
    has_many :campaign_unit_external_clicks, through: :campaign_units

    has_many :campaigns, through: :campaign_units
    has_many :organisations, through: :campaigns

    has_many :lessons, through: :lesson_templates, class_name: 'Lesson::Lesson'
    has_many :forms, through: :lessons
    has_many :schools, through: :forms
    has_many :form_units, dependent: :destroy, foreign_key: :new_library_unit_id
    has_one :questionnaire, dependent: :nullify, foreign_key: :new_library_unit_id

    has_many :related_words, -> { distinct }, through: :lesson_templates

    has_many :exemplar_works, foreign_key: :new_library_unit_id
    has_many :primary_lesson_templates, foreign_key: :primary_new_library_unit_id, class_name: 'Lesson::Template'

    scope :featured_on_homepage, -> { where(featured_on_homepage: true) }

    validates :name, presence: true

    accepts_nested_attributes_for :documents
    alias documents= documents_attributes=

    # Virtual attribute for video source
    def video_source
      video&.source
    end

    # Virtual attribute for external video ID
    def external_video_id
      video&.external_id
    end

    def v2_as_json(options = nil)
      as_json(options).merge(
        documents: documents.order(name: :asc).select(:id, :name, :fileboy_file_id),
        year: year ? { id: year.id, name: year.name } : nil,
        curriculum: year && curriculum ? { id: curriculum.id, name: curriculum.name } : nil,
        views: campaign_unit_views.count,
        externalClicks: campaign_unit_external_clicks.count,
        campaignUnitIds: campaign_units.ids,
        displayCampaignUnitIds: campaign_units.where(show_on_unit_page: true).ids,
        lessonTemplateIds: lesson_templates.ids,
        exemplar_works: exemplar_works.approved,
        sponsors: campaign_units.map { |unit| unit.campaign.organisation }.compact.uniq.map { |org| { id: org.id, name: org.name, fileboy_image_id: org.fileboy_image_id } },
      )
    end

    # this is largely duped from the data_for_show function as it seems quite important
    # to keep integrity of this logic but i imagine we will deprecate data_for_show at some point?
    def templates_for_user(user)
      templates = lesson_templates.available.order(weight: :asc)

      unless user.admin?
        templates = templates
                      .joins(:available_countries)
                      .group(:id)
                      .where(countries: { id: user.country&.id || 1 })
      end

      templates = templates.select(:id, :name, :machine_name, :weight, :fileboy_image_id, :demo, :anonymous, :available)

      templates.map do |t|
        {
          id: t.id,
          name: t.name,
          machine_name: t.machine_name,
          weight: t.weight,
          fileboy_image_id: t.fileboy_image_id,
          available: !user || user.accessible_lesson_templates.ids.include?(t.id),
          demo: !!t.demo,
          anonymous: !!t.anonymous,
          rawAvailable: !!t.available,
        }
      end
    end

    def unit_templates_for_user(user)
      return lesson_templates.available.order(weight: :asc) unless user&.present?
      ids = lesson_templates.available.order(weight: :asc).includes(:source_template).map { |template| template.default_for_user(user)&.id }.compact
      # sorting by IDS order, so that any overridden templates are shown in place of the original, as the above map is ordered already
      # added accessible by to ensure that the user can see the user_generated templates
      if ids.any?
        Lesson::Template.accessible_by(Ability.new(user))
          .where(id: ids)
          .order(Arel.sql("array_position(ARRAY[#{ids.join(',')}]::integer[], id::integer)"))
      else
        lesson_templates.available.order(weight: :asc)
      end
    end

    def data_for_show(user)
      templates = lesson_templates.available.order(weight: :asc)

      unless user&.admin?
        templates = templates
                      .joins(:available_countries)
                      .group(:id)
                      .where(countries: { id: user&.country&.id || 1 })
      end
      user_accessible_curriculum = NewLibrary::Curriculum.accessible_by(Ability.new(user))
      as_json.merge(
        lesson_template_ids: templates.ids,
        lesson_templates:
          templates.map do |t|
            default_template = Lesson::Template.default_lesson_for_user(t.id, user)
            {
              id: t.id,
              name: t.name,
              machine_name: t.machine_name,
              weight: t.weight,
              fileboy_image_id: t.fileboy_image_id,
              available: !user || user&.accessible_lesson_templates&.ids&.include?(t.id),
              demo: t.demo?,
              anonymous: t.anonymous?,
              rawAvailable: t.available?,
              scientific_enquiry_types: t.scientific_enquiry_types,
              disable_viewing: t.disable_viewing,
              default_template: default_template
            }
          end,
        documents: documents.map { |x| { id: x.id, name: x.name, fileboy_file_id: x.fileboy_file_id } },
        year: year,
        curriculum: year&.curriculum&.present? ? user_accessible_curriculum.find_by(id: year.curriculum.id) : nil,
      )
    end

    def self.by_related_words(words)
      words_sql = words.is_a?(ActiveRecord::Relation) ? words.select(:name).to_sql : escape(words)

      count_related_words_sql = <<-SQL.squish
        SELECT COUNT(related_words.name)
        FROM related_words
        JOIN career_taggings ON career_taggings.career_tag_id = related_words.career_tag_id
        JOIN lesson_templates ON career_taggings.taggable_id = lesson_templates.id AND career_taggings.taggable_type = 'Lesson::Template'
        JOIN new_library_unit_templates ON new_library_unit_templates.template_id = lesson_templates.id
        WHERE new_library_unit_templates.unit_id = new_library_units.id
        AND related_words.name IN (#{words_sql})
      SQL

      result = all
      result = result.where("(#{count_related_words_sql}) > 0")
      result = result.order(Arel.sql("(#{count_related_words_sql}) DESC"))

      result
    end

    def lesson_codes
      lesson_templates.distinct.pluck(:machine_name).join(', ')
    end

    def duplicate
      duplicated_unit = self.dup

      base_name = self.name.gsub(/\s\(Copy(?: \d+)?\)$/, '')

      copy_count = NewLibrary::Unit.where("name LIKE ?", "#{base_name} (Copy%").count
      copy_suffix = copy_count == 0 ? "(Copy)" : "(Copy #{copy_count + 1})"

      duplicated_unit.name = "#{base_name} #{copy_suffix}"
      duplicated_unit.lesson_template_ids = self.lesson_template_ids

      duplicated_unit.save!

      duplicated_unit
    end
  end
end
