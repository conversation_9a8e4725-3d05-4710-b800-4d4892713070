# frozen_string_literal: true

# == Schema Information
#
# Table name: new_library_documents
#
#  id              :bigint           not null, primary key
#  file_name       :string
#  file_uid        :string
#  name            :string           not null
#  resource_type   :string           not null
#  weight          :integer          default(0), not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  fileboy_file_id :string
#  resource_id     :bigint           not null
#
# Indexes
#
#  index_new_library_documents_on_resource_type_and_resource_id  (resource_type,resource_id)
#
module NewLibrary
  class Document < ApplicationRecord
    belongs_to :resource, polymorphic: true
    has_one :document, as: :documentable, class_name: "::Document", dependent: :destroy

    validates :name, :fileboy_file_id, presence: true

    def fileboy_url
      "/document/#{fileboy_file_id}?source=new_library"
    end
  end
end
