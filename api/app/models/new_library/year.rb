# frozen_string_literal: true

# == Schema Information
#
# Table name: new_library_years
#
#  id               :bigint           not null, primary key
#  image_cache      :jsonb            not null
#  image_name       :string
#  image_uid        :string
#  max_age          :integer
#  min_age          :integer
#  name             :string           not null
#  slug             :string
#  tags             :jsonb
#  weight           :integer
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  curriculum_id    :bigint
#  fileboy_image_id :string
#  subject_id       :bigint
#
# Indexes
#
#  index_new_library_years_on_curriculum_id           (curriculum_id)
#  index_new_library_years_on_curriculum_id_and_slug  (curriculum_id,slug) UNIQUE
#  index_new_library_years_on_subject_id              (subject_id)
#  index_new_library_years_on_tags                    (tags) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (curriculum_id => new_library_curricula.id)
#  fk_rails_...  (subject_id => new_library_subjects.id)
#
module NewLibrary
  class Year < ApplicationRecord
    belongs_to :old_curriculum, class_name: 'NewLibrary::Curriculum', foreign_key: 'curriculum_id', optional: true

    belongs_to :subject, class_name: 'NewLibrary::Subject', counter_cache: true
    has_one :curriculum, through: :subject
    has_many :units, class_name: 'NewLibrary::Unit', dependent: :nullify
    has_many :lesson_templates, through: :units
    has_many :documents, as: :resource, class_name: 'NewLibrary::Document'

    delegate :name, to: :subject, prefix: true, allow_nil: true
    delegate :curriculum_id, to: :subject, allow_nil: true

    scope :ordered, -> do
      order(
        "new_library_years.name ~* 'year \\d' DESC",
        "new_library_years.name ~* 'ks\\d' DESC",
        "new_library_years.name ASC",
      )
    end

    validates :name, presence: true

    accepts_nested_attributes_for :documents
    alias documents= documents_attributes=

    def v1_as_json(options = nil)
      as_json(options).merge(unit_ids: unit_ids)
    end

    def data_for_show(user)
      as_json.merge(
        documents:
          documents
            .order(name: :asc)
            .map { |x| { id: x.id, name: x.name, fileboy_file_id: x.fileboy_file_id }},
        units:
          units
            .joins(lesson_templates: :available_countries)
            .group(:id)
            .where(countries: { id: user&.country&.id || 1 })
            .order(weight: :asc, name: :asc).map { |unit|
              unit.as_json.merge({
                sponsors: unit.campaign_units.map { |unit| unit.campaign.organisation }.compact.uniq.map { |org| { id: org.id, name: org.name, fileboy_image_id: org.fileboy_image_id } }
              })
            },
        lesson_templates:
          lesson_templates
            .available
            .joins(:available_countries)
            .group(:id)
            .where(countries: { id: user&.country&.id || 1 })
            .map do |t|
              {
                id: t.id,
                name: t.name,
                available: !user || user&.accessible_lesson_templates&.ids&.include?(t.id)
              }
            end
      )
    end

    def tags_string=(value)
      if value.is_a?(String)
        self.tags = value.split(',').map(&:strip).reject(&:blank?).uniq
      else
        self.tags = []
      end
    end

    def tags_string
      self.tags.to_a.join(', ')
    end

    def self.all_tags
      connection.select_values("SELECT DISTINCT jsonb_array_elements_text(tags) FROM new_library_years ORDER BY 1")
    end
  end
end
