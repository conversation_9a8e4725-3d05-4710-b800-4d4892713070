# == Schema Information
#
# Table name: new_library_subjects
#
#  id            :bigint           not null, primary key
#  name          :string           not null
#  years_count   :integer          default(0)
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  curriculum_id :bigint           not null
#
# Indexes
#
#  index_new_library_subjects_on_curriculum_id  (curriculum_id)
#
# Foreign Keys
#
#  fk_rails_...  (curriculum_id => new_library_curricula.id)
#
module NewLibrary
  class Subject < ApplicationRecord
    SUBJECT_NAMES = ["Science", "Geography"].freeze

    belongs_to :curriculum, class_name: 'NewLibrary::Curriculum'

    has_many :years, class_name: 'NewLibrary::Year', dependent: :nullify

	  validates :name, presence: true, inclusion: { in: SUBJECT_NAMES, message: "%{value} is not a valid subject, must be one of #{SUBJECT_NAMES.join(', ')}" }
  end
end
