# == Schema Information
#
# Table name: stripe_events
#
#  id                :bigint           not null, primary key
#  critical          :boolean          default(FALSE)
#  error             :text
#  event_data        :json
#  event_type        :string
#  last_retry_at     :datetime
#  next_retry_at     :datetime
#  processed_at      :datetime
#  retry_count       :integer          default(0)
#  webhook_signature :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  stripe_id         :string           not null
#
# Indexes
#
#  index_stripe_events_on_stripe_id  (stripe_id) UNIQUE
#
class StripeEvent < ApplicationRecord
  validates :stripe_id, presence: true, uniqueness: true
  
  scope :with_errors, -> { where.not(error: nil) }
  scope :successful, -> { where(error: nil) }
  scope :recent, -> { order(created_at: :desc).limit(100) }
  scope :by_type, ->(type) { where(event_type: type) }
  scope :pending_retry, -> { with_errors.where('next_retry_at <= ?', Time.current) }
  scope :critical, -> { where(critical: true) }

  def subscriber
    sub_id = event_data['subscription']
    sub = Subscriber.find_by(stripe_subscription_id: sub_id) if sub_id.present?
    return sub if sub.present?

    cus_id = event_data['customer']
    Subscriber.find_by(stripe_customer_id: cus_id) if cus_id.present?
  end

  # Record an event as processed
  def self.record(stripe_id, event_type, event_data = nil, error = nil, signature = nil, critical = false)
    begin
      event = create(
        stripe_id: stripe_id,
        event_type: event_type,
        event_data: event_data,
        processed_at: Time.current,
        error: error,
        webhook_signature: signature,
        critical: critical
      )
      # Schedule a retry job immediately if there was an error
      if error.present?
        begin
          StripeWebhookRetryJob.perform_later(event.id)
          Rails.logger.info "Scheduled immediate retry for event #{stripe_id}"
        rescue => job_error
          Rails.logger.error "Failed to schedule retry job: #{job_error.message}"
          Rails.logger.error job_error.backtrace.join("\n")
        end
      end
      
      event
    rescue ActiveRecord::RecordNotUnique => e
      # Handle case where event was already recorded (race condition)
      Rails.logger.warn "Duplicate event recording attempt for #{stripe_id}: #{e.message}"
      find_by(stripe_id: stripe_id)
    rescue => e
      Rails.logger.error "Error recording Stripe event #{stripe_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      # Make a last-ditch effort to record at least something
      begin
        # Create a simplified record with just the key information
        create(
          stripe_id: stripe_id,
          event_type: event_type,
          error: "Error recording full event: #{e.message}",
          critical: critical
        )
      rescue => fallback_error
        Rails.logger.error "Critical failure recording Stripe event #{stripe_id}: #{fallback_error.message}"
        nil
      end
    end
  end
  
  # Schedule the next retry
  def schedule_retry(delay = 5.minutes)
    begin
      next_retry = Time.current + delay
      update(next_retry_at: next_retry)
      
      begin
        StripeWebhookRetryJob.set(wait: delay).perform_later(id)
        Rails.logger.info "Scheduled retry for event #{stripe_id} in #{delay.to_i} seconds"
        return true
      rescue => job_error
        Rails.logger.error "Failed to schedule retry job: #{job_error.message}"
        Rails.logger.error job_error.backtrace.join("\n")
        return false
      end
    rescue => e
      Rails.logger.error "Error scheduling retry for event #{stripe_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      return false
    end
  end
  
  # Mark as successfully processed
  def mark_successful
    begin
      update(
        error: nil,
        processed_at: Time.current,
        next_retry_at: nil
      )
      Rails.logger.info "Event #{stripe_id} marked as successfully processed"
      true
    rescue => e
      Rails.logger.error "Error marking event #{stripe_id} as successful: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  # Record a failed attempt
  def record_failure(error_message, increment_count = true)
    begin
      updates = { 
        error: error_message,
        processed_at: Time.current
      }
      
      if increment_count
        updates[:retry_count] = (retry_count || 0) + 1
        updates[:last_retry_at] = Time.current
      end
      
      update(updates)
      
      Rails.logger.info "Recorded failure for event #{stripe_id}: #{error_message}"
      
      # For critical events that are failing, send alerts after multiple failures
      if critical && (retry_count || 0) >= 3
        send_critical_failure_alert
      end
      
      true
    rescue => e
      Rails.logger.error "Error recording failure for event #{stripe_id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  # Return a formatted version of the event data
  def formatted_data
    begin
      return nil unless event_data.present?
      JSON.pretty_generate(event_data)
    rescue => e
      Rails.logger.error "Error formatting event data for #{stripe_id}: #{e.message}"
      event_data.to_s
    end
  end
  
  # Get events that need retry
  def self.get_retry_candidates
    begin
      with_errors.where('(retry_count < ? OR retry_count IS NULL)', 5)
    rescue => e
      Rails.logger.error "Error getting retry candidates: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      none # Return an empty relation
    end
  end
  
  # Get events for each status (for dashboard)
  def self.status_counts
    begin
      {
        total: count,
        errors: with_errors.count,
        recent_errors: with_errors.where('created_at > ?', 24.hours.ago).count,
        pending_retry: pending_retry.count,
        critical_errors: critical.with_errors.count
      }
    rescue => e
      Rails.logger.error "Error getting status counts: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      # Return zeroed stats as fallback
      {
        total: 0,
        errors: 0,
        recent_errors: 0,
        pending_retry: 0,
        critical_errors: 0
      }
    end
  end
  
  # Send an alert for critical failures
  private
  
  def send_critical_failure_alert
    begin
      # Send alert via AdminMailer
      AdminMailer.critical_webhook_failure(self).deliver_later
      Rails.logger.info "Sent critical failure alert for event #{stripe_id}"
    rescue => e
      Rails.logger.error "Failed to send critical failure alert for #{stripe_id}: #{e.message}"
    end
  end
end
