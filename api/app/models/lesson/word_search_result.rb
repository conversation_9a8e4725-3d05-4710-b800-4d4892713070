# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_word_search_results
#
#  id                 :bigint           not null, primary key
#  deleted            :boolean          default(FALSE), not null
#  time_taken         :integer          default(0)
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  lesson_template_id :bigint
#  user_id            :bigint
#
# Indexes
#
#  index_lesson_word_search_results_on_lesson_template_id  (lesson_template_id)
#  index_lesson_word_search_results_on_user_id             (user_id)
#
module Lesson
  class WordSearchResult < ApplicationRecord

    belongs_to :user, class_name: 'User'
    belongs_to :lesson_template, class_name: 'Lesson::Template'

    validates :time_taken, presence: true

  end
end
