# == Schema Information
#
# Table name: lesson_dumps
#
#  id          :bigint           not null, primary key
#  content     :json
#  raw_data    :json
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  template_id :bigint
#
# Indexes
#
#  index_lesson_dumps_on_template_id  (template_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#
class Lesson::Dump < ApplicationRecord
  class RestoreError < StandardError; end

  belongs_to :template, class_name: 'Lesson::Template', inverse_of: :dump

  validates :template, uniqueness: true

  class << self
    def dump template
      raw_data = get_raw_data template

      content = slice raw_data, [
        "name",
        "objectives",
        plan: [
          "assessment_phrases",
          "teacher_mastery",
          "next_generation_science_standards",
          "kingdom_of_saudi_arabia",
          "chinese_compulsory_education_primary_school_science",
          learning_outcomes: ["body", "lower", "middle", "higher"],
          activities: ["name", "method", "resources"],
        ],
        keywords: ["name", "body"],
        slides: ["name", "body", "intro_text"],
        quiz_questions: ["body", "answer"],
      ]

      dump = find_or_initialize_by(template: template)
      dump.update!(raw_data: raw_data, content: content)
      dump
    end

    def slice data, fields
      fields = fields.dup
      nested_fields = fields.extract_options!

      results = Array.wrap(data).map do |data|
        result = {}
        fields.each do |field|
          result[field] = sanitize(data[field])
        end

        nested_fields.each do |k, v|
          k = k.to_s
          result[k] = slice(data[k], v) if data[k]
        end

        result
      end

      return data.is_a?(Array) ? results : results[0]
    end

    def sanitize(string)
      results = Array.wrap(string || "").map do |string|
        ActionView::Base.full_sanitizer.sanitize(
          String(string).gsub(/<\/p\s*>/, "__p__").gsub(/<br\s*\/?>/, "__br__")
        ).squish.gsub(/ ?(?:__p__) ?/, "\n\n").gsub(/ ?(?:__br__) ?/, "\n").strip.gsub("&amp;", "&")
      end
      return string.is_a?(Array) ? results : results[0]
    end

    def get_raw_data template
      template_attrs = template.attributes.except('id', 'machine_name', 'created_at', 'updated_at', 'pupils_awaiting_marks_count_cache', 'last_modified_by', 'last_modified_at', 'quip_quiz_id', 'admin_checklist', 'country_id', 'awaiting_translation', 'content_uploaded', 'group_id', 'quip_quiz_key', 'uploaded_old_quiz')
      plan_attrs = template.plan.attributes.except('id', 'template_id', 'created_at', 'updated_at')

      plan_attrs['learning_outcomes'] = template.plan.learning_outcomes.map do |outcome|
        outcome.attributes.except('id', 'plan_id', 'created_at', 'updated_at')
      end

      kw_map = {}

      keyword_attrs = template.keywords.map do |keyword|
        keyword_attrs = keyword.attributes.except('id', 'template_id', 'created_at', 'updated_at', 'last_modified_by', 'last_modified_at')
        keyword_attrs['id'] = SecureRandom.hex(4)
        kw_map[keyword.id] = keyword_attrs['id']
        keyword_attrs
      end

      slide_map = {}

      slide_attrs = template.slides.map do |slide|
        slide_attrs = slide.attributes.except('id', 'template_id', 'created_at', 'updated_at', 'last_modified_by', 'last_modified_at', 'narration_video', 'narration_url')
        slide_attrs['id'] = SecureRandom.hex(4)
        slide_map[slide.id] = slide_attrs['id']
        slide_attrs
      end

      question_attrs = template.quiz_questions.map do |question|
        question_attrs = question.attributes.except('id', 'lesson_template_id', "lesson_slide_id", "lesson_keyword_id", 'created_at', 'updated_at', 'last_modified_by', 'last_modified_at')
        question_attrs.merge(lesson_slide_id: slide_map[question.lesson_slide_id], lesson_keyword_id: kw_map[question.lesson_keyword_id])
      end

      document_attrs = template.documents.map do |document|
        document.attributes.except('id', 'template_id', 'created_at', 'updated_at', 'last_modified_by', 'last_modified_at')
      end

      assessment_attrs = template.template_assessments.map do |assessment|
        assessment.attributes.except('id', 'template_id', 'created_at', 'updated_at')
      end

      new_unit_ids = template.new_library_units.ids

      template_attrs.merge!(
        plan: plan_attrs,
        keywords: keyword_attrs,
        slides: slide_attrs,
        quiz_questions: question_attrs,
        documents: document_attrs,
        template_assessments: assessment_attrs,
        new_library_units: new_unit_ids,
      )

      return template_attrs.as_json
    end

    def symbolize x
      return x.map { |v| symbolize(v) } if x.is_a?(Array)
      return x.map { |k, v| [k.to_s.to_sym, symbolize(v)] }.to_h if x.is_a?(Hash)
      x
    end

    def restore template, data = nil, duplicate: false, lite: false
      dump = find_by!(template: template)
      new_data = symbolize(merge(dump.raw_data, data))

      template_attrs = new_data.dup
      plan_attrs = template_attrs.delete(:plan)
      outcome_attrs = plan_attrs.delete(:learning_outcomes)
      keyword_attrs = template_attrs.delete(:keywords)
      slide_attrs = template_attrs.delete(:slides)
      question_attrs = template_attrs.delete(:quiz_questions)
      document_attrs = template_attrs.delete(:documents)
      assessment_attrs = template_attrs.delete(:template_assessments)
      new_unit_ids = []
      if lite
        # lite is user for copying a template from a source -> new template by user
        template_attrs.delete(:new_library_units)
        slide_attrs = []
      else
        new_unit_ids = template_attrs.delete(:new_library_units)
      end

      transaction do
        template.update!({
          **template_attrs,
          new_library_unit_ids: new_unit_ids,
          available: false
        })

        template.plan.update!(plan_attrs)

        outcome_attrs.each do |attrs|
          template.plan.learning_outcomes.create!(attrs)
        end

        kw_map = {}

        keyword_attrs.each do |og_keyword|
          keyword = template.keywords.create!(og_keyword.except(:id))
          kw_map[og_keyword[:id]] = keyword.id
        end

        slide_map = {}

        slide_attrs.each do |og_slide|
          slide = template.slides.create!(og_slide.except(:id))
          slide_map[og_slide[:id]] = slide.id
        end

        question_attrs.each do |attrs|
          attrs[:lesson_keyword_id] = kw_map[attrs[:lesson_keyword_id]]
          attrs[:lesson_slide_id] = slide_map[attrs[:lesson_slide_id]]
          template.quiz_questions.create!(attrs)
        end

        if duplicate
          document_attrs&.each do |attrs|
            template.documents.create!(attrs)
          end
        end

        assessment_attrs.each do |attrs|
          record = template.template_assessments.new(attrs)
          record.save!(validate: false)
        end

        dump.update!(raw_data: new_data.to_json, content: data.to_json)
      end

      true
    end

    def merge a, b
      return a if b.nil?
      if a.is_a? Hash
        raise RestoreError, :badFormat unless b.is_a? Hash
        return a.map { |k, v| [k, merge(v, b[k])] }.to_h
      elsif a.is_a? Array
        raise RestoreError, :badFormat unless b.is_a? Array
        return a.map.with_index { |x, i| merge(x, b[i]) }
      else
        return b
      end
    end
  end
end
