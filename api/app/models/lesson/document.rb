# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_documents
#
#  id               :bigint           not null, primary key
#  deleted          :boolean          default(FALSE), not null
#  file_name        :string
#  file_uid         :string
#  for_pupil        :boolean
#  last_modified_at :datetime
#  last_modified_by :integer
#  name             :string
#  skip_translation :boolean          default(FALSE), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  fileboy_file_id  :string
#  template_id      :bigint
#
# Indexes
#
#  index_lesson_documents_on_template_id  (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class Document < ApplicationRecord
    scope :anonymous, -> { where(template_id: Template.anonymous) }

    has_one :document, as: :documentable, class_name: "::Document", dependent: :destroy
    belongs_to :template

    validates :name, :fileboy_file_id, presence: true

    def fileboy_url(args)
      "/document/#{fileboy_file_id}?#{args.to_param}"
    end
  end
end
