# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_template_units
#
#  id          :bigint           not null, primary key
#  deleted     :boolean          default(FALSE), not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  template_id :bigint
#  unit_id     :bigint
#
# Indexes
#
#  index_lesson_template_units_on_template_id  (template_id)
#  index_lesson_template_units_on_unit_id      (unit_id)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#  fk_rails_...  (unit_id => units.id)
#
module Lesson
  class TemplateUnit < ApplicationRecord
    belongs_to :template
    belongs_to :unit
  end
end
