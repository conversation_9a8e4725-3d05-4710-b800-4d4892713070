# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_template_frameworks
#
#  id                      :bigint           not null, primary key
#  deleted                 :boolean          default(FALSE), not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  assessment_framework_id :bigint
#  template_id             :bigint
#
# Indexes
#
#  index_lesson_template_frameworks_on_assessment_framework_id  (assessment_framework_id)
#  index_lesson_template_frameworks_on_template_id              (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (assessment_framework_id => lesson_assessment_frameworks.id)
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class TemplateFramework < ApplicationRecord
    belongs_to :template
    belongs_to :assessment_framework
  end
end
