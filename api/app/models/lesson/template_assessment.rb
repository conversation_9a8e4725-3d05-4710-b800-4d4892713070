# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_template_assessments
#
#  id                 :bigint           not null, primary key
#  deleted            :boolean          default(FALSE), not null
#  likelihood         :integer
#  severity           :integer
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  risk_assessment_id :bigint
#  template_id        :bigint
#
# Indexes
#
#  index_lesson_template_assessments_on_risk_assessment_id  (risk_assessment_id)
#  index_lesson_template_assessments_on_template_id         (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (risk_assessment_id => lesson_risk_assessments.id)
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class TemplateAssessment < ApplicationRecord
    belongs_to :template
    belongs_to :risk_assessment
    validates :likelihood, :severity, presence: true, inclusion: { in: 0..3, message: 'must be a number between 0 and 3' }

    def v1_as_json(options = nil)
      as_json(options).merge(risk_assessment_name: risk_assessment&.name)
    end
  end
end
