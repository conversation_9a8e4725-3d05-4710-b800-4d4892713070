# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_slides
#
#  id                     :bigint           not null, primary key
#  audio_name             :string
#  audio_uid              :string
#  autoFocusVideoId       :string
#  body                   :text
#  data                   :jsonb
#  deleted                :boolean          default(FALSE), not null
#  end_of_unit_assessment :boolean
#  footer                 :text
#  global                 :boolean          default(FALSE), not null
#  home_excluded          :boolean          default(FALSE), not null
#  iframe_src             :string
#  image_cache            :jsonb            not null
#  image_name             :string
#  image_style_type       :integer          default("cover")
#  image_thumbnail_url    :string
#  image_uid              :string
#  image_url              :string
#  intro_text             :string
#  last_modified_at       :datetime
#  last_modified_by       :integer
#  loop_video             :boolean
#  name                   :string
#  narration_url          :string
#  narration_video        :string
#  skip_translation       :boolean          default(FALSE), not null
#  slide_type             :integer
#  video_id__old          :bigint
#  video_url              :string
#  vtt_file_name          :string
#  vtt_file_uid           :string
#  weight                 :integer
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  fileboy_image_id       :string
#  fileboy_video_id       :string
#  fileboy_vtt_id         :string
#  homework_id            :bigint
#  homework_task_id       :bigint
#  jw_id                  :string
#  quip_question_id       :bigint
#  template_id            :bigint
#  tour_id                :bigint
#  video_id               :integer
#
# Indexes
#
#  index_lesson_slides_on_homework_id       (homework_id)
#  index_lesson_slides_on_homework_task_id  (homework_task_id)
#  index_lesson_slides_on_quip_question_id  (quip_question_id)
#  index_lesson_slides_on_template_id       (template_id)
#  index_lesson_slides_on_tour_id           (tour_id)
#  index_lesson_slides_on_video_id__old     (video_id__old)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#

module Lesson
  class Slide < ApplicationRecord
    TIMER_SLIDE_OPTIONS = [{ name: '30 seconds (red)', fileboy_id: '33ec57c0-e265-40ca-9f9a-bb907ce26fdf' }, { name: '30 seconds (green)', fileboy_id: 'e8ccd43a-a06b-404d-b3dc-72ba6d9256e2' }, { name: '1 Minute', fileboy_id: '1f448dbc-4a40-490b-be48-5ad15e06291c' }, { name: '2 Minutes', fileboy_id: 'e1854c40-49cf-4db5-a8d2-f4e038c6108a' }, { name: '3 Minutes', fileboy_id: '7406afa1-392f-4c1c-95b3-4375eee0f31f' }].freeze

    scope :ordered, -> { order(weight: :asc) }
    scope :anonymous, -> { where(template_id: Template.anonymous) }
    scope :global, -> { where(global: true) }
    scope :home_excluded, -> { where(home_excluded: true) }
    scope :home, -> { where(home_excluded: false) }

    belongs_to :template, required: false
    belongs_to :video, optional: true
    belongs_to :tour, optional: true
    belongs_to :quip_question, optional: true
    belongs_to :homework, optional: true
    belongs_to :homework_task, optional: true
    validates :template, presence: true, unless: :global

    has_many :presentation_progresses, dependent: :destroy, foreign_key: :lesson_slide_id

    # This link is used for displaying the slide when the answer is given incorrectly in the rocket word quiz
    has_many :quiz_questions,
             class_name: '::QuizOld::Question',
             foreign_key: :lesson_slide_id,
             dependent: :nullify

    enum slide_type: %i[
      text intro course_intro video keywords previous_keywords sixty_count investigation quiz outro
      progress_check song expert mission_assignment phet tour quip_question rocket_thinking
      keyword story expertVideo dynamic timer homework
    ]
    enum image_style_type: %i[cover contain]

    validates :slide_type, presence: true

    delegate :objectives, to: :template

    before_save :normalize_dynamic_slide_panels_to_array, if: -> { slide_type.to_s == 'dynamic' && data_changed? }
    before_save :process_dynamic_panel_videos, if: -> { slide_type.to_s == 'dynamic' && data_changed? }
    before_save do
      self.weight ||= (template&.slides&.ordered&.last&.weight || 0) + 1
    end

    def thumbnail
      # Hardcoded images came from presentation_controller

      if slide_type == 'dynamic'
        if data.present? && data['thumbnail'].present?
          ImageHelpers.thumbnail_url(data['thumbnail'])
        elsif data.present? && data['background_image'].present?
          ImageHelpers.thumbnail_url(data['background_image'])
        end
      elsif slide_type == 'tour'
        ImageHelpers.thumbnail_url(tour&.fileboy_image_id)
      elsif %w[video song expert mission_assignment timer].include?(slide_type)
        video_thumbnail
      elsif %w[keywords previous_keywords].include?(slide_type)
        ImageHelpers.thumbnail_url('92ff4093-f09c-4ab0-9851-cf76204f019c+rocket_word_thumbnail')
      elsif slide_type == 'phet'
        return ImageHelpers.thumbnail_url(fileboy_image_id) if fileboy_image_id.present?
        # example phet urls - replace _en.html with -420.png for the image
        # https://phet.colorado.edu/sims/html/number-pairs/latest/number-pairs-420.png
        # https://phet.colorado.edu/sims/html/number-pairs/latest/number-pairs_en.html
        iframe_src.gsub('_en.html', '-420.png') if iframe_src.present? && iframe_src.include?('phet.colorado.edu')
      elsif slide_type == 'quiz'
        ImageHelpers.thumbnail_url('92ff4093-f09c-4ab0-9851-cf76204f019c+quiz_thumbnail')
      elsif slide_type == 'quip_question'
        ImageHelpers.thumbnail_url(quip_question&.data_json&.dig('fileboyImageId'))
      elsif slide_type == 'homework'
        ImageHelpers.thumbnail_url(homework&.cover_image_fileboy_id&.presence || fileboy_image_id)
      elsif fileboy_image_id.present?
        ImageHelpers.thumbnail_url(fileboy_image_id)
      end
    end

    def video_thumbnail
      return nil unless video_type?
      return ImageHelpers.video_thumbnail(video.source, video.external_id) if video.present? && video.external_id.present?

      # handle legacy fields
      if fileboy_video_id.present?
        ImageHelpers.video_thumbnail('fileboy', fileboy_video_id)
      elsif video_url.present?
        type, match = Video.video_url_type(video_url)
        ImageHelpers.video_thumbnail(type, match)
      end
    end

    def sibling_ids(lesson=nil)
      slide_ids_from_lesson(lesson)
    end

    def slide_number(lesson=nil)
      slide_ids = slide_ids_from_lesson(lesson)
      slide_ids.index(id) + 1
    end

    def next_slide_id(lesson=nil)
      slide_ids = slide_ids_from_lesson(lesson)
      next_slide_index = slide_ids.index(id) + 1
      slide_ids[next_slide_index] if next_slide_index
    end

    def previous_slide_id(lesson=nil)
      slide_ids = slide_ids_from_lesson(lesson)
      previous_slide_index = slide_ids.index(id) - 1
      slide_ids[previous_slide_index] if previous_slide_index && previous_slide_index >= 0
    end

    # If given a lesson, returns the ids of the slides in the presentation without the previous
    # rocket words.
    #
    # TODO: expand this method to also filter out home school and library usage
    def slide_ids_from_lesson(lesson=nil)
      slides = template.slides.ordered
      if lesson && lesson.id == lesson.form.lessons.first&.id
        slides.where.not(slide_type: :previous_keywords).ids
      else
        slides.ids
      end
    end

    def lesson_from_user(user)
      lesson_in_context(user)&.previous
    end

    # returns the previous lesson in the course compared to the current lesson being looked at
    def lesson_in_context(user)
      return unless user
      template = self.template
      user.lessons.find_by(template: template)
    end

    def self.serialized_for_index_request
      all.map { |slide| slide.as_json.merge(skip_translation: slide.skip_translation) }
    end

    # Virtual attribute for video source
    def video_source
      video&.source
    end

    # Virtual attribute for external video ID
    def external_video_id
      video&.external_id
    end

    def v2_as_json(_options=nil)
      as_json
        .merge({
                 templateName: template&.name
               })
    end

    def data_for_form
      # Initial check: Is this a dynamic slide with data?
      unless slide_type.to_s == 'dynamic' && data.is_a?(Hash)
        # Rails.logger.info "[data_for_form] Not a dynamic slide or data is not a Hash. Returning original data for Slide ID: #{self.id}"
        return data
      end

      # Deep clone to avoid modifying the actual `data` attribute in memory.
      # Using JSON parse/generate is generally safe for typical JSON structures.
      begin
        form_data = JSON.parse(data.to_json)
      rescue JSON::ParserError, TypeError
        # Rails.logger.error "[data_for_form] Error deep cloning slide data with JSON, falling back. Slide ID: #{self.id}, Error: #{e.message}"
        # Fallback clone method if JSON fails (e.g., if data contains non-standard JSON types)
        # This shouldn't happen if data is always from jsonb and well-formed.
        form_data = Marshal.load(Marshal.dump(data)) rescue data.dup rescue {} # Last resort
      end

      # Ensure 'panels' exists and is an array in the form_data
      unless form_data.is_a?(Hash) && form_data['panels'].is_a?(Array)
        # Rails.logger.warn "[data_for_form] form_data['panels'] is not an array after clone or initially. Slide ID: #{self.id}. form_data['panels'] type: #{form_data['panels'].class}"
        # If 'panels' was an object {"0": {...}, "1": {...}} from db and normalize_... didn't run or was overridden by clone, handle it.
        if form_data.is_a?(Hash) && form_data['panels'].is_a?(Hash)
          # Rails.logger.info "[data_for_form] Attempting to convert panels object to array in form_data. Slide ID: #{self.id}"
          panels_hash = form_data['panels']
          form_data['panels'] = panels_hash.keys.sort_by { |k| k.to_s.to_i }.map { |k| panels_hash[k] }
        else
          form_data['panels'] = [] # Default to empty array if not present or wrong type
        end
      end

      # Rails.logger.info "[data_for_form] Processing panels for Slide ID: #{self.id}. Panel count: #{form_data['panels'].try(:count)}"

      form_data['panels'].each_with_index do |panel, _index|
        unless panel.is_a?(Hash)
          # Rails.logger.warn "[data_for_form] Panel at index #{index} is not a Hash. Skipping. Slide ID: #{self.id}"
          next
        end

        # Rails.logger.info "[data_for_form] Checking panel index #{index}: media_type=#{panel['media_type']}, video_id=#{panel['video_id']}. Slide ID: #{self.id}"

        if panel['media_type'] == 'video' && panel['video_id'].present?
          video_record = Video.find_by(id: panel['video_id'])
          if video_record
            panel['video_source'] = video_record.source # Injecting for form
            panel['external_video_id'] = video_record.external_id # Injecting for form
            # Rails.logger.info "[data_for_form] Injected video details for panel #{index}: source='#{panel['video_source']}', external_id='#{panel['external_video_id']}' from Video ID #{video_record.id}. Slide ID: #{self.id}"
          else
            # Rails.logger.warn "[data_for_form] Video record not found for panel #{index} with video_id: #{panel['video_id']}. Slide ID: #{self.id}"
            panel.delete('video_source') # Ensure no stale/incorrect data
            panel.delete('external_video_id')
          end
        else
          # If not a video panel or no video_id, ensure these temp keys are not present
          panel.delete('video_source')
          panel.delete('external_video_id')
        end
      end
      # Rails.logger.info "[data_for_form] Returning augmented form_data for Slide ID: #{self.id}: #{form_data.to_json}"
      form_data
    end

    def video_type?
      %w[video song expert mission_assignment timer].include?(slide_type)
    end

    def self.user_friendly_slide_type(type)
      return 'Keyword Question List' if type == 'quiz'
      return 'Question' if type == 'quip_question'
      return 'Embed' if type == 'phet'
      type&.titleize
    end

    def user_friendly_slide_type
      ::Lesson::Slide.user_friendly_slide_type(slide_type)
    end

    def self.filtered_slide_types
      rejected = %w[intro outro course_intro sixty_count keyword story expertVideo]
      slide_types.keys.reject { |type| rejected.include?(type) }
    end

    def self.filtered_slide_types_for_admin 
      rejected = %w[homework]
      ::Lesson::Slide.filtered_slide_types.reject { |type| rejected.include?(type) }
    end

    def self.filtered_slide_types_for_teacher
      rejected = %w[investigation progress_check rocket_thinking song expert mission_assignment rocket_word previous_keywords]
      ::Lesson::Slide.filtered_slide_types.reject { |type| rejected.include?(type) }
    end

    private

    def normalize_dynamic_slide_panels_to_array
      # ... (function as provided before, to ensure data['panels'] is an array) ...
      # Ensure this function is correctly implemented if you're using it.
      # For example:
      return unless data.is_a?(Hash) && data['panels'].is_a?(Hash)
      panels_hash = data['panels']
      return unless panels_hash.keys.all? { |k| k.to_s.match?(/\A\d+\z/) }
      data['panels'] = panels_hash.sort_by { |k, _v| k.to_s.to_i }.map { |_k, v| v }
    end

    def process_dynamic_panel_videos
      return unless data.is_a?(Hash) && data['panels'].is_a?(Array)

      data['panels'].each_with_index do |panel, index|
        next unless panel.is_a?(Hash) && panel['media_type'] == 'video'

        submitted_video_source = panel['video_source']
        submitted_external_video_id = panel['external_video_id']

        # Always remove these temporary form fields from the panel data that will be saved.
        panel.delete('video_source')
        panel.delete('external_video_id')

        if submitted_video_source.present? && submitted_external_video_id.present?
          video_name = "Panel Video for Slide #{id || 'New Slide'}, Panel #{index}: #{template&.name || ''} - #{submitted_external_video_id}"
          video = Video.find_or_initialize_by(source: submitted_video_source, external_id: submitted_external_video_id)
          video.name = video_name.truncate(255) if video.new_record? || video.name.blank?

          if video.save
            panel['video_id'] = video.id # RENAMED: Store the internal Video ID
          else
            Rails.logger.error "Failed to save video for dynamic panel (slide_id: #{id}, panel_index: #{index}): #{video.errors.full_messages.join(', ')} (Source: #{submitted_video_source}, External ID: #{submitted_external_video_id})"
            panel.delete('video_id') # RENAMED: Ensure no stale ID if save fails
          end
        elsif (panel.key?('video_source') || panel.key?('external_video_id')) && # Check if fields were part of submission
              submitted_video_source.blank? && submitted_external_video_id.blank?
          # If user explicitly cleared both source and external_id form fields, remove the video link.
          panel.delete('video_id') # RENAMED
        end
      end
    end
  end
end
