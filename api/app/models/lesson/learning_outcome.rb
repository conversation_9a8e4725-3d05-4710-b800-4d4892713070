# == Schema Information
#
# Table name: lesson_learning_outcomes
#
#  id         :bigint           not null, primary key
#  body       :string
#  higher     :string
#  lower      :string
#  middle     :string
#  weight     :integer          default(0), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  plan_id    :bigint
#
# Indexes
#
#  index_lesson_learning_outcomes_on_plan_id  (plan_id)
#
# Foreign Keys
#
#  fk_rails_...  (plan_id => lesson_plans.id)
#
module Lesson
  class LearningOutcome < ApplicationRecord
    belongs_to :plan, inverse_of: :learning_outcomes

    scope :ordered, -> { order(weight: :asc) }
  end
end
