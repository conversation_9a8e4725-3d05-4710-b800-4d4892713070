# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_assessment_frameworks
#
#  id               :bigint           not null, primary key
#  body             :text
#  key_stage        :integer
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  last_modified_by :integer
#  last_modified_at :datetime
#  deleted          :boolean          default(FALSE), not null
#
module Lesson
  class AssessmentFramework < ApplicationRecord
    has_many :template_frameworks, dependent: :destroy
    has_many :templates, through: :template_frameworks

    validates :key_stage, numericality: {greater_than_or_equal_to: 0, less_than_or_equal_to: 4, only_integer: true, message: 'must be a number between 0 and 4'}

    scope :keystage_1, -> { where(key_stage: 1) }
    scope :keystage_2, -> { where(key_stage: 2) }
  end
end
