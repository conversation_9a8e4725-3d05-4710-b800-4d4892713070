# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_ratings
#
#  id          :bigint           not null, primary key
#  body        :text
#  deleted     :boolean          default(FALSE), not null
#  rating      :integer
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  template_id :bigint
#
# Indexes
#
#  index_lesson_ratings_on_template_id  (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class Rating < ApplicationRecord
    belongs_to :template
  end
end
