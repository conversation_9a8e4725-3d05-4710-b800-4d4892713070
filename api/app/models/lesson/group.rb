# == Schema Information
#
# Table name: lesson_groups
#
#  id           :bigint           not null, primary key
#  deleted      :boolean          default(FALSE), not null
#  machine_name :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#
# Indexes
#
#  index_lesson_groups_on_machine_name  (machine_name) UNIQUE WHERE (NOT deleted)
#
class Lesson::Group < ApplicationRecord
  has_many :templates, class_name: 'Lesson::Template', inverse_of: :group

  scope :awaiting_translation, -> do
    where(id: Lesson::Template.awaiting_translation.select(:group_id))
  end

  validates :machine_name,
            presence: true,
            uniqueness: {
              unless: :deleted?, conditions: -> { where(deleted: false) }
            }

  def main_template
    Lesson::Template.default_scoped.where(group: self).order(country_id: :asc).first!
  end

  def add_template(country)
    main_template = self.main_template

    transaction do
      template =
        templates.create!(
          country: country,
          name: "#{main_template.name} - #{country.name}",
          machine_name: "#{machine_name}-#{country.machine_name}",
          available: false,
          awaiting_translation: true
        )

      Lesson::Dump.dump(main_template)

      # Quip stores huge amounts of image data, so avoid loading it into memory too many times
      sql = sql(<<-SQL)
        INSERT INTO lesson_dumps(template_id, raw_data, content, created_at, updated_at)
        SELECT :template_id, raw_data, content, :time, :time
        FROM lesson_dumps
        WHERE template_id = :main_template_id
      SQL

      sql.compact.assign(
        main_template_id: main_template.id,
        template_id: template.id,
        time: Time.current
      ).run

      template
    end
  end

  def self.serialized_for_index_request
    # includes(:templates, templates: :country)
    all.map do |x|
      x.as_json.merge(
        {
          'templates' =>
            x.templates.sort_by(&:country_id).map do |t|
              {
                id: t.id,
                name: t.name,
                machine_name: t.machine_name,
                country: t.country ? t.country.machine_name : nil,
                created_at: t.created_at,
                slide_count: t.slides.count || 0,
                use_2022_lesson_plan: t.lesson_plan_layout != 'legacy',
                views: t.tracking_lesson_template_views.count
              }
            end,
          'fileboy_image_id' =>
            x.templates.any? ? x.templates.map { |x| x.fileboy_image_id }.compact[0] : nil
        }
      )
    end
  end
end
