# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_risk_assessments
#
#  id               :bigint           not null, primary key
#  name             :string
#  hazard           :string
#  risk_level       :string
#  severity         :string
#  people           :string
#  safety_points    :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  last_modified_by :integer
#  last_modified_at :datetime
#  deleted          :boolean          default(FALSE), not null
#  response         :string
#
module Lesson
  class RiskAssessment < ApplicationRecord
    has_many :template_assessments, dependent: :destroy
    has_many :templates, through: :template_assessments

    validates :name, presence: true
  end
end
