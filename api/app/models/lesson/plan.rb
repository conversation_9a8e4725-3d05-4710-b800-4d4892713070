# == Schema Information
#
# Table name: lesson_plans
#
#  id                                                  :bigint           not null, primary key
#  activities                                          :jsonb            not null
#  assessment_marks                                    :text
#  assessment_phrases                                  :text
#  assessment_questions                                :text
#  cbse                                                :text
#  century_skills_for_life                             :text
#  chinese_compulsory_education_primary_school_science :string
#  cross_curriculum_opportunities                      :text
#  curriculum_of_excellence                            :text
#  deleted                                             :boolean          default(FALSE), not null
#  early_years_framework                               :text
#  international_baccalaureate                         :text
#  kingdom_of_saudi_arabia                             :string
#  learning_outcomes_json                              :jsonb            not null
#  national_curriculum                                 :text
#  next_generation_science_standards                   :string
#  objectives                                          :jsonb            not null
#  scientific_enquiry_type                             :text
#  teacher_mastery                                     :text
#  working_scientifically_skills                       :text
#  created_at                                          :datetime         not null
#  updated_at                                          :datetime         not null
#  template_id                                         :bigint
#
# Indexes
#
#  index_lesson_plans_on_template_id  (template_id) UNIQUE WHERE (NOT deleted)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class Plan < ApplicationRecord
    has_many :learning_outcomes, -> { ordered }, inverse_of: :plan

    accepts_nested_attributes_for :learning_outcomes, allow_destroy: true

    MAIN_FIELDS = %w[
      national_curriculum curriculum_of_excellence international_baccalaureate early_years_framework
      cbse scientific_enquiry_type working_scientifically_skills century_skills_for_life
      cross_curriculum_opportunities assessment_questions assessment_marks assessment_phrases
      teacher_mastery objectives learning_outcomes activities next_generation_science_standards kingdom_of_saudi_arabia
      chinese_compulsory_education_primary_school_science
    ].freeze

    def self.main_fields
      MAIN_FIELDS.map(&:to_sym)
    end

    def main_fields
      attributes.slice(*MAIN_FIELDS).symbolize_keys
    end

    belongs_to :template

    validates :template_id, uniqueness: true
    validate :validate_objectives_format
    validate :validate_activities_format

    def validate_objectives_format
      unless objectives.is_a?(Array)
        errors.add(:objectives, 'must be an array')
        return
      end
      self.objectives = objectives.map(&:to_s)
    end

    def validate_activities_format
      unless activities.is_a?(Array)
        errors.add(
          :activities,
          'must be an array (expected [{ name: '', method: '', resources: '' }])'
        )
        return
      end

      good_keys = %w[name method resources required_resources]

      activities.map.with_index do |activity, i|
        unless activity.is_a?(Hash)
          errors.add(
            :"activities[#{i}]",
            'must be an object (expected { name: '', method: '', resources: '', required_resources?: boolean })'
          )
          next
        end
        if (bad_keys = activity.keys - good_keys).any?
          errors.add(
            :"activities[#{i}]",
            "contains unexpected keys: #{bad_keys}, expected: ['name', 'method', 'resources', 'required_resources']"
          )
          next
        end
        good_keys.each do |k|
          if k == 'required_resources'
            activity[k] = activity[k].present? ? activity[k] : nil
          else
            activity[k] = activity[k].to_s
          end
        end
      end
    end
  end
end
