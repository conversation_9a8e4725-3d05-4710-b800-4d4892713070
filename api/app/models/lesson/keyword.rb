# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_keywords
#
#  id                      :bigint           not null, primary key
#  body                    :text
#  deleted                 :boolean          default(FALSE), not null
#  image_cache             :jsonb
#  image_name              :string
#  image_uid               :string
#  image_url               :string
#  last_modified_at        :datetime
#  last_modified_by        :integer
#  name                    :string
#  previous_keyword_weight :integer
#  weight                  :integer
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  fileboy_image_id        :string
#  template_id             :bigint
#
# Indexes
#
#  index_lesson_keywords_on_template_id  (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class Keyword < ApplicationRecord
    validates :name, presence: true

    belongs_to :template

    has_one :quiz_question, class_name: '::QuizOld::Question', foreign_key: :lesson_keyword_id, dependent: :destroy
    accepts_nested_attributes_for :quiz_question

    scope :ordered, -> { order(weight: :asc) }
    scope :previous_ordered, -> { order(previous_keyword_weight: :asc) }
    scope :anonymous, -> { where(template_id: Template.anonymous) }

    before_save { self.name = self.name.strip }

    def fileboy_image_url
      return nil unless fileboy_image_id
      "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:200x200;format:webp;quality:80"
    end

    def v1_as_json(options = nil)
      as_json(options).merge(
        quiz_question: quiz_question ? {
          use_data: quiz_question.use_data,
          id: quiz_question.id,
          body: quiz_question.body,
          answer: quiz_question.answer,
          question_body: quiz_question.question_body,
          question_video_url: quiz_question.question_video_url,
          question_fileboy_image_id: quiz_question.question_fileboy_image_id,
          lesson_slide_id: quiz_question.lesson_slide&.id,
        } : nil,
      )
    end
  end
end
