# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_results
#
#  id                     :bigint           not null, primary key
#  absent                 :boolean
#  comments               :text
#  deleted                :boolean          default(FALSE), not null
#  effort                 :string
#  end_of_unit_assessment :string
#  mark                   :integer
#  school_year            :integer
#  school_year_start      :date
#  working_scientifically :string
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#  last_quiz_attempt_id   :bigint
#  pupil_id               :bigint           not null
#  template_id            :bigint           not null
#
# Indexes
#
#  index_lesson_results_on_last_quiz_attempt_id      (last_quiz_attempt_id)
#  index_lesson_results_on_pupil_id                  (pupil_id)
#  index_lesson_results_on_template_id               (template_id)
#  index_lesson_results_on_template_id_and_pupil_id  (template_id,pupil_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (last_quiz_attempt_id => quiz_attempts.id)
#  fk_rails_...  (pupil_id => users.id)
#  fk_rails_...  (template_id => lesson_templates.id)
#
module Lesson
  class Result < ApplicationRecord
    include SchoolYear

    belongs_to :pupil
    belongs_to :template
    belongs_to :last_quiz_attempt, optional: true, class_name: 'QuizOld::Attempt'
    has_one :school, through: :pupil

    with_data_sql = <<-SQL.squish
      lesson_results.mark IS NOT NULL OR
      (trim(lesson_results.comments) = '') IS FALSE OR
      lesson_results.absent IS TRUE
    SQL

    scope :with_data, -> { where(with_data_sql) }
    scope :without_data, -> { where.not(with_data_sql) }

    validates :pupil_id, uniqueness: { scope: :template_id }

    validate :school_matches

    def school_matches
      return if pupil&.school_id == school&.id
      errors.add(:joining_table, 'may not span multiple schools')
    end

    def with_data?
      [mark, comments, absent, working_scientifically, end_of_unit_assessment, effort].any?(&:present?)
    end
  end
end
