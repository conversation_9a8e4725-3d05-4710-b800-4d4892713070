# frozen_string_literal: true

# == Schema Information
#
# Table name: lesson_lessons
#
#  id                  :bigint           not null, primary key
#  active              :boolean          default(TRUE)
#  average_marks_cache :jsonb
#  cord_cache          :jsonb            not null
#  deleted             :boolean          default(FALSE), not null
#  last_modified_at    :datetime
#  last_modified_by    :integer
#  lesson_taught       :boolean          default(FALSE)
#  school_year         :integer
#  school_year_start   :date
#  time                :datetime
#  weight              :integer
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  form_id             :bigint           not null
#  form_unit_id        :bigint
#  template_id         :bigint
#  unit_id             :bigint
#
# Indexes
#
#  index_lesson_lessons_on_cord_cache    (cord_cache) USING gin
#  index_lesson_lessons_on_form_id       (form_id)
#  index_lesson_lessons_on_form_unit_id  (form_unit_id)
#  index_lesson_lessons_on_template_id   (template_id)
#  index_lesson_lessons_on_unit_id       (unit_id)
#
# Foreign Keys
#
#  fk_rails_...  (unit_id => units.id)
#
module Lesson
  class Lesson < ApplicationRecord
    include School<PERSON>ear

    belongs_to :template
    belongs_to :form_unit, optional: true

    has_one :subject, through: :form_unit, source: :new_library_subject
    has_one :unit, through: :form_unit, source: :new_library_unit

    belongs_to :form
    has_many :pupils, through: :form
    has_many :teachers, through: :form
    has_one :school, through: :form

    has_many :tracking_rocket_words, class_name: "TrackingRocketWord", foreign_key: :lesson_id
    has_many :tracking_films, class_name: "TrackingFilm", foreign_key: :lesson_id
    has_many :tracking_lesson_template_views, class_name: "TrackingLessonTemplateViewed", foreign_key: :lesson_id
    has_many :tracking_word_searches, class_name: "TrackingWordSearch", foreign_key: :lesson_id
    has_many :tracking_summative_quizzes, class_name: "TrackingSummativeQuiz", foreign_key: :lesson_id
    has_many :tracking_link_trackings, class_name: "TrackingLinkTracking", foreign_key: :lesson_id
    has_many :tracking_documents, class_name: "TrackingDocument", foreign_key: :lesson_id
    has_many :tracking_lesson_template_favourites, class_name: "TrackingLessonTemplateFavourite", foreign_key: :lesson_id
    has_many :homeworks, class_name: "Homework", foreign_key: :lesson_lesson_id

    delegate :fileboy_image_id, :name, :objectives, to: :template

    scope :ordered, -> { order(time: :asc, weight: :asc) }
    scope :reversed, -> { order(time: :desc, weight: :desc) }
    scope :unscheduled, -> { where(time: nil) }
    scope :upcoming, -> { where('lesson_lessons.time >= ?', Time.current.end_of_week) }
    scope :past, -> { where('lesson_lessons.time < ?',  Time.current.end_of_week) }

    scope :active, -> { where(active: true) }

    done_sql = <<-SQL.squish
      EXISTS (
        SELECT FROM lesson_results
        JOIN enrollments
          ON enrollments.user_id = lesson_results.pupil_id
          AND enrollments.form_id = lesson_lessons.form_id
          AND NOT enrollments.deleted
        WHERE lesson_results.template_id = lesson_lessons.template_id
          AND (
            lesson_results.mark IS NOT NULL OR
            (trim(lesson_results.comments) = '') IS FALSE OR
            lesson_results.absent IS TRUE
          ) AND NOT lesson_results.deleted
      ) OR EXISTS (
        SELECT FROM pupil_lesson_marks
        JOIN enrollments
          ON enrollments.user_id = pupil_lesson_marks.user_id
          AND enrollments.form_id = lesson_lessons.form_id
          AND pupil_lesson_marks.lesson_id = lesson_lessons.id
          AND NOT enrollments.deleted
      )
    SQL
    # [mark, comments, absent, working_scientifically, end_of_unit_assessment, effort]
    scope :done, -> { where(done_sql) }
    scope :not_done, -> { where.not(done_sql) }
    scope :including_done_attr, -> { select("lesson_lessons.*", "#{done_sql} AS done") }

    scope :for_pupils, -> (pupil_ids) { joins(forms: :enrollments).where(forms: { enrollments: { user_id: pupil_ids } }) }

    validates :time, presence: true

    after_destroy :remove_corresponding_unit
    after_update :remove_corresponding_unit, if: -> { saved_change_to_deleted?(from: false, to: true) }

    def destroy *args
      results&.delete_all
      super(*args)
    end

    def done?
      self.class.done.exists?(id)
    end

    def results
      template&.results&.where(pupil_id: pupils)
    end

    def done
      has_attribute?(:done) ? self[:done] : results.with_data.exists?
    end

    def previous
      ids = form.lessons.ordered.ids
      index = ids.index(id) - 1
      self.class.find_by(id: index >= 0 ? ids[index] : nil)
    end

    def average_marks
      query = load_sql("form/average_marks").compact.assign(
        where_sql: sql("forms.id = ? AND templates.id = ?", form_id, template_id),
      )
      query.to_json[0]
    end

    def update_average_marks_cache
      update(average_marks_cache: average_marks)
    end

    def v1_as_json(options = nil)
      as_json(options).merge(template: template)
    end

    def v2_as_json(options = nil)
      as_json(options).merge(template: template, form: form)
    end

    # Adds a corresponding unit to the lesson if one doesn't already exist
    # @return [FormUnit, nil] The created form unit or nil if no unit could be added
    def add_corresponding_unit
      return unless template && school && form
      return unless school.new_library_curriculum_id.present?

      matching_unit = find_matching_unit_for_curriculum
      return unless matching_unit

      matching_form_unit = form.form_units.find_by(new_library_unit_id: matching_unit.id)
      if matching_form_unit
        update_columns(form_unit_id: matching_form_unit.id)
        return matching_form_unit
      end

      create_form_unit_for(matching_unit)
    end

    # Removes the corresponding form unit for the lesson if it exists and there are no more lessons using it
    def remove_corresponding_unit
      return unless form_unit.present?
      form_unit.destroy if form.lessons.where(form_unit_id: form_unit.id).empty?
    end

    private

    def find_matching_unit_for_curriculum
      template
        .new_library_units
        .joins(year: :curriculum)
        .where(new_library_curricula: {
          id: school.new_library_curriculum_id
        })
        .first
    end

    def create_form_unit_for(unit)
      transaction do
        form_unit = build_form_unit(
          new_library_unit: unit,
          start_date: time&.to_date || Date.current,
          end_date: time&.to_date || Date.current,
          form: form
        )

        form_unit.save!
        save!
        form_unit
      end
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error("Failed to create form unit: #{e.message}")
      nil
    end
  end
end
