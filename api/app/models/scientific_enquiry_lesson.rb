# == Schema Information
#
# Table name: scientific_enquiry_lessons
#
#  id                         :bigint           not null, primary key
#  created_at                 :datetime         not null
#  updated_at                 :datetime         not null
#  lesson_template_id         :bigint
#  scientific_enquiry_type_id :bigint
#
# Indexes
#
#  index_scientific_enquiry_lessons_on_lesson_template_id          (lesson_template_id)
#  index_scientific_enquiry_lessons_on_scientific_enquiry_type_id  (scientific_enquiry_type_id)
#
class ScientificEnquiryLesson < ApplicationRecord
  belongs_to :lesson_template, class_name: "Lesson::Template", optional: true
  belongs_to :scientific_enquiry_type
end
