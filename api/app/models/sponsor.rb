# frozen_string_literal: true

# == Schema Information
#
# Table name: sponsors
#
#  id               :bigint           not null, primary key
#  name             :string
#  body             :text
#  image_uid        :string
#  image_name       :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  fileboy_image_id :string
#
class Sponsor < ApplicationRecord
  has_many :school_sponsors
  has_many :schools, through: :school_sponsors

  validates :name, presence: :true

  def v1_as_json(options = nil)
    as_json(options).merge(
      school_ids: school_ids,
      schools: schools.order(name: :asc).pluck_to_hash(:id, :name),
    )
  end

  def v2_as_json(options = nil)
    as_json(options).merge(
      school_ids: school_ids,
      schools: schools.order(name: :asc).pluck_to_hash(:id, :name),
    )
  end
end
