# == Schema Information
#
# Table name: schools_affiliations
#
#  id           :bigint           not null, primary key
#  affiliate_id :bigint           not null
#  school_id    :bigint           not null
#
# Indexes
#
#  index_schools_affiliations_on_affiliate_id  (affiliate_id)
#  index_schools_affiliations_on_school_id     (school_id)
#
# Foreign Keys
#
#  fk_rails_...  (affiliate_id => affiliates.id)
#  fk_rails_...  (school_id => schools.id)
#
class SchoolsAffiliation < ApplicationRecord
    belongs_to :affiliate
    belongs_to :school
end
