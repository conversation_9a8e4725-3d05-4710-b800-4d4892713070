# == Schema Information
#
# Table name: school_invites
#
#  id              :bigint           not null, primary key
#  accepted_at     :datetime
#  email           :string           not null
#  expires_at      :datetime         not null
#  invited_at      :datetime
#  status          :integer          default("pending"), not null
#  token           :string           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  invited_user_id :bigint
#  inviter_id      :bigint           not null
#  school_id       :bigint           not null
#
# Indexes
#
#  index_school_invites_on_email                (email)
#  index_school_invites_on_invited_user_id      (invited_user_id)
#  index_school_invites_on_inviter_id           (inviter_id)
#  index_school_invites_on_school_id            (school_id)
#  index_school_invites_on_school_id_and_email  (school_id,email) UNIQUE
#  index_school_invites_on_token                (token) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (invited_user_id => users.id)
#  fk_rails_...  (inviter_id => users.id)
#  fk_rails_...  (school_id => schools.id)
#
class SchoolInvite < ApplicationRecord
  belongs_to :inviter, class_name: 'Teacher'
  belongs_to :invited_user, class_name: 'Teacher', optional: true
  belongs_to :school
  
  enum status: { pending: 0, accepted: 1, expired: 2, cancelled: 3 }
  
  validates :email, presence: true, format: { with: URI::MailTo::EMAIL_REGEXP }
  validates :token, presence: true, uniqueness: true
  validates :expires_at, presence: true
  
  before_validation :generate_token, on: :create
  before_validation :set_expiration, on: :create
  
  scope :active, -> { where(status: :pending).where('expires_at > ?', Time.current) }
  
  private
  
  def generate_token
    self.token = SecureRandom.urlsafe_base64(32)
  end
  
  def set_expiration
    self.expires_at = 7.days.from_now
  end
end
