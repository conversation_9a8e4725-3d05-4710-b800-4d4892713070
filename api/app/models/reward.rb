# frozen_string_literal: true

# == Schema Information
#
# Table name: rewards
#
#  id               :bigint           not null, primary key
#  deleted          :boolean          default(FALSE), not null
#  image_name       :string
#  image_uid        :string
#  reward_type      :integer          default("background")
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  fileboy_image_id :string
#  rank_id          :bigint
#
# Indexes
#
#  index_rewards_on_rank_id  (rank_id)
#
class Reward < ApplicationRecord
  enum reward_type: %i[background avatar]

  validates :reward_type, presence: true

  def self.ordered
    all
  end

  def points_required
    0
  end

end
