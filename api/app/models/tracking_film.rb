# frozen_string_literal: true

# == Schema Information
#
# Table name: tracking_films
#
#  id                  :bigint           not null, primary key
#  film_duration       :integer
#  film_type           :integer
#  time_viewing        :integer
#  video_resource_type :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  fileboy_video_id    :string
#  jw_id               :string
#  lesson_id           :bigint
#  lesson_template_id  :bigint
#  pupil_id            :bigint
#  video_resource_id   :bigint
#
# Foreign Keys
#
#  fk_rails_...  (lesson_id => lesson_lessons.id)
#  fk_rails_...  (lesson_template_id => lesson_templates.id)
#  fk_rails_...  (pupil_id => users.id)
#
class TrackingFilm < ApplicationRecord
  belongs_to :lesson_template, class_name: 'Lesson::Template'
  belongs_to :lesson, class_name: 'Lesson::Lesson'
  belongs_to :pupil, class_name: 'User'
  belongs_to :video_resource, polymorphic: true, optional: true

  enum film_type: %i[expert mission_assignment career video]

  validates :time_viewing, :film_duration, :film_type, presence: true
end
