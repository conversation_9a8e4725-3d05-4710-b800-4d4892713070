# == Schema Information
#
# Table name: user_achievements
#
#  id             :bigint           not null, primary key
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  achievement_id :bigint
#  user_id        :bigint
#
# Indexes
#
#  index_user_achievements_on_achievement_id              (achievement_id)
#  index_user_achievements_on_user_id                     (user_id)
#  index_user_achievements_on_user_id_and_achievement_id  (user_id,achievement_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (achievement_id => achievements.id)
#  fk_rails_...  (user_id => users.id)
#
class UserAchievement < ApplicationRecord
  belongs_to :user
  belongs_to :achievement

  validates :achievement, uniqueness: { scope: :user }
end
