# == Schema Information
#
# Table name: quiz_views
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  quiz_id    :bigint
#  user_id    :bigint
#
# Indexes
#
#  index_quiz_views_on_quiz_id  (quiz_id)
#  index_quiz_views_on_user_id  (user_id)
#
class QuizView < ApplicationRecord
  belongs_to :quiz
  belongs_to :user, optional: true

  def v2_as_json(options=nil)
    as_json()
      .merge({
        quiz: quiz
      })
  end
end
