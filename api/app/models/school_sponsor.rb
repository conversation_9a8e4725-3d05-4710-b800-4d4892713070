# frozen_string_literal: true

# == Schema Information
#
# Table name: school_sponsors
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  school_id  :bigint
#  sponsor_id :bigint
#
# Indexes
#
#  index_school_sponsors_on_school_id   (school_id)
#  index_school_sponsors_on_sponsor_id  (sponsor_id)
#
class SchoolSponsor < ApplicationRecord
  belongs_to :school
  belongs_to :sponsor
end
