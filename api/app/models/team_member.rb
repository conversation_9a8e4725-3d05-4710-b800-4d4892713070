# frozen_string_literal: true

# == Schema Information
#
# Table name: team_members
#
#  id               :bigint           not null, primary key
#  name             :string
#  title            :string
#  body             :text
#  role             :integer          default("expert")
#  image_uid        :string
#  image_name       :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  last_modified_by :integer
#  last_modified_at :datetime
#  deleted          :boolean          default(FALSE), not null
#  fileboy_image_id :string
#
class TeamMember < ApplicationRecord
  validates :name, :title, presence: true

  enum role: { expert: 0, staff: 1, board: 2 }
end
