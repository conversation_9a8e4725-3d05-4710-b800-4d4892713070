module HasOptionalAddress
  extend ActiveSupport::Concern

  included do
    has_one :address, as: :addressable, dependent: :destroy
    validate :validate_address, if: :address_present?
    after_save :save_address_changes!
  end

  def address
    super || build_address(addressable: self)
  end

  def address_fields *args
    address.main_fields(*args)
  end

  def address_present?
    address_fields.values.any?(&:present?)
  end

  def validate_address
    address.validate
    address.errors.to_h.map { |k, v| errors.add(k, v) }
  end

  def save_address_changes!
    return address.save! if address_present?
    address.destroy! if address.persisted?
  end

  Address.main_fields.each do |column|
    define_method(column) { address.send(column) }
    define_method("#{column}=") do |value|
      address.send("#{column}=", value)
    end
  end
end
