module HasCareerTags
  extend ActiveSupport::Concern

  included do
    has_many :career_taggings, as: :taggable
    has_many :career_tags, through: :career_taggings
    has_many :related_words, through: :career_tags

    # Given a collection of related_words or an array of strings, returns a collection:
    # - scoped to only rows with at least one related word
    # - ordered by the number of related words
    def self.by_related_words(words)
      words_sql = words.is_a?(ActiveRecord::Relation) ? words.select(:name).to_sql : escape(words)

      count_related_words_sql = <<-SQL.squish
        SELECT COUNT(DISTINCT related_words.name)
        FROM related_words
        JOIN career_taggings ON career_taggings.career_tag_id = related_words.career_tag_id
        WHERE career_taggings.taggable_id = #{quoted_table_name}.id
        AND career_taggings.taggable_type = #{escape name}
        AND related_words.name IN (#{words_sql})
      SQL

      result = all
      result = result.where("(#{count_related_words_sql}) > 0")
      result = result.order(Arel.sql("(#{count_related_words_sql}) DESC"))

      result
    end
  end
end
