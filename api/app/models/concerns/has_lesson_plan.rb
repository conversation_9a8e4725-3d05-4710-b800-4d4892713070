module HasLessonPlan
  extend ActiveSupport::Concern

  included do
    has_one :plan, dependent: :destroy
    validate :validate_plans
    after_save { plan.save! }
  end

  def plan
    super || build_plan(template: self)
  end

  def plan_fields(*args)
    plan.main_fields(*args)
  end

  def validate_plans
    plan.validate
    plan.errors.to_h.map { |k, v| errors.add("plan_#{k}", v) }
  end

  ::Lesson::Plan.main_fields.each do |column|
    define_method("plan_#{column}") { plan.send(column) }
    define_method("plan_#{column}=") { |v| plan.send("#{column}=", v) }
    define_method("plan_#{column}_attributes=") { |v| plan.send("#{column}_attributes=", v) }
  end
end
