# frozen_string_literal: true

module A<PERSON><PERSON><PERSON>
  extend ActiveSupport::Concern

  included do
    def as_json(*args)
      @as_json_nested_call_was = !!@as_json_nested_call
      return super(*args) if @as_json_nested_call
      @as_json_nested_call = true
      return v1_as_json(*args).merge(api_version: 1).as_json if Thread.current[:api_version] == 1
      return v2_as_json(*args).merge(api_version: 2).as_json if Thread.current[:api_version] == 2
      super(*args)
    ensure
      @as_json_nested_call = @as_json_nested_call_was
    end

    def v1_as_json(*args)
      as_json(*args)
    end

    def v2_as_json(*args)
      as_json(*args)
    end
  end
end
