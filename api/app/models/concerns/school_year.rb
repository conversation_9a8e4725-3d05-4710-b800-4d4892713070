# frozen_string_literal: true

module SchoolYear
  extend ActiveSupport::Concern

  DEFAULT_START = '15 August 2018'

  included do
    scope :school_year, -> (year) do
      any_school_year.where(school_year: year)
    end

    scope :any_school_year, -> do
      result = spawn
      predicates = result.where_clause.send(:predicates).reject do |pred|
        pred.is_a?(Arel::Nodes::Grouping) &&
          pred.expr.is_a?(Arel::Nodes::Or) &&
          pred.expr.left.is_a?(Arel::Nodes::Equality) &&
          pred.expr.left.left.name == :school_year &&
          pred.expr.right.is_a?(Arel::Nodes::Equality) &&
          pred.expr.right.left.name == :school_year
      end
      result.where_clause = ActiveRecord::Relation::WhereClause.new(predicates)
      result
    end

    before_validation do
      self.school_year ||= school&.current_year
      self.school_year_start ||= school&.year_start
    end

    def self.update_school_year
      sq = unscoped.where(id: all).joins(:school).group(:id).select(
        :id,
        'FIRST(schools.year_start) AS year_start',
        "date_part('day', FIRST(schools.year_start)) AS day",
        "date_part('month', FIRST(schools.year_start)) AS month"
      )

      before_year_start = <<-SQL
        date_part('month', created_at) < sq.month OR
        (date_part('month', created_at) = sq.month AND date_part('day', created_at) < sq.day)
      SQL

      connection.execute <<-SQL.squish
        UPDATE #{table_name}
        SET
          school_year_start = year_start,
          school_year = CASE WHEN year_start IS NULL
                        THEN null
                        ELSE
                          CASE WHEN #{before_year_start}
                          THEN date_part('year', created_at) - 1
                          ELSE date_part('year', created_at)
                          END
                        END
        FROM (#{sq.to_sql}) AS sq
        WHERE #{table_name}.id = sq.id
      SQL
    end

    def self.current_school_year
      year, month, day = Date.today.instance_eval { [year(), month(), day()] }

      current_year_sql = <<-SQL.squish
        CASE
        WHEN
          date_part('month', #{table_name}.school_year_start) > #{month} OR
          (
            date_part('month', #{table_name}.school_year_start) = #{month} AND
            date_part('day', #{table_name}.school_year_start) > #{day}
          )
        THEN
          #{year - 1}
        ELSE
          #{year}
        END
      SQL

      any_school_year.where(arel_table[:school_year].eq_any([Arel.sql(current_year_sql), nil]))
    end
  end
end
