module SubscriptionAccess
  extend ActiveSupport::Concern

  def subscription
    @subscription ||= begin
      # Check if the user has a subscription thats active
      user_subscriber = subscriber
      if user_subscriber && user_subscriber.available_products&.any?
        puts user_subscriber.inspect
        return user_subscriber
      end

      # Check if the user has a school subscription thats active
      if defined?(school) && school.present?
        school_subscriber = school.subscriber
        if school_subscriber && school_subscriber.available_products&.any?
          return school_subscriber
        end
      end

      # Otherwise, return the first available subscription
      return user_subscriber if user_subscriber
      return school_subscriber if school_subscriber

      nil
    end
  end

  def active_subscription
    # TODO update these checks to also include the free_subscription logic from the PR for free subscriptions
    @active_subscription ||= begin
      return subscriber if subscriber&.available_products&.any?

      if defined?(school) && school.present?
        school_subscriber = school.subscriber
        return school_subscriber if school_subscriber&.available_products&.any?
      end
    end
  end

  # Check if user has access to the requested product
  def requires_subscription(product_key)
    @requested_product = product_key.to_s

    unless active_subscription
      flash[:notice] = 'You need a subscription to access this content.'
      redirect_to new_subscription_path(product: product_key)
      return false
    end

    true
  end

  private

  def requested_product
    @requested_product || 'science'  # Default to science if not specified
  end
end
