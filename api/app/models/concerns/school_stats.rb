# frozen_string_literal: true

module SchoolStats
  extend ActiveSupport::Concern

  included do
    enum customer_type: %i[prospect trial_user customer lapsed cancelled]
    enum enquiry_type: %i[home school partner content_provider_talent]

    def last_lesson
      PresentationProgress.where(user_id: users)
                          .order(updated_at: :desc)
                          .first&.lesson_template&.name
    end

    def last_lesson_completed
      PresentationProgress.where(user_id: users, completed: true)
                          .order(updated_at: :desc)
                          .first&.lesson_template&.name
    end

    def lessons_started
      @lessons_started ||= PresentationProgress.where(user_id: users)
                                               .pluck('COUNT(DISTINCT lesson_template_id)')[0]
    end

    def lessons_completed
      @lessons_completed ||= PresentationProgress.where(user_id: users, completed: true)
                                                 .pluck('COUNT(DISTINCT lesson_template_id)')[0]
    end

    def percent_lessons_complete
      return 0 if lessons_started.zero?
      ((lessons_completed.to_f / lessons_started) * 100).to_i
    end

    def plan
      demo? ? 'Demo School' : 'School'
    end

    def get_stats
      {
        'Company created at' => created_at,
        'Company size' => users.where(type: "Teacher").count,
        'Trial - Lessons Started' => trial_lessons_started ? 'Yes' : 'No',
        'Trial - Handouts Browsed' => trial_handouts_viewed ? 'Yes' : 'No',
        'Trial - Expert Films' => trial_videos_watched ? 'Yes' : 'No',
        'Trial - Library Browsed' => trial_library_browsed ? 'Yes' : 'No',
        'Lessons Started' => lessons_started,
        'Lessons Completed' => lessons_completed,
        'Last Lesson' => last_lesson || 'None',
        'Last Lesson Completed' => last_lesson_completed || 'None',
        'Percent Lessons Complete' => percent_lessons_complete,
        "Subject - Maths" => 'No',
        'Subject - Science' => 'Yes',
        # lead_source: lead_source,
        'Customer Type' => customer_type,
        'Enquiry Type' => enquiry_type,
        'Onboarding - Date' => created_at,
        'Onboarding - Teacher Data' => demo? ? 'N/A' : 'Yes',
        'Onboarding Teacher Communicated' => onboarding_teacher_communicated ? 'Yes' : 'No',
        'Onboarding Pupil Data Stage' => onboarding_pupil_data_stage,
        'Onboarding Parent Data Stage' => onboarding_parent_data_stage,
        'Pupils' => pupils.any? ? 'Yes' : 'No',
        'Pupils Added' => pupils.count,
        'Plan' => plan,
        'School' => name,
        'Last Login' => users.maximum(:last_sign_in_at) || 'Never',
        'Renewal Date' => expires_at,
      }
    end
  end
end
