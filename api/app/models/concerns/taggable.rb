# The Taggable concern abstracts and encapsulates the functionality necessary for models to be associated with tags.
# It is designed to be included in any model that requires tagging functionality, such as Video or Article models.
# This concern leverages a polymorphic association via the Tagging join model, allowing a single model (Tag)
# to associate with multiple taggable models (e.g., Video, Article) in a many-to-many relationship.
#
# === Functionalities provided by this concern:
# - It sets up the necessary associations: each taggable model `has_many :taggings` and `has_many :tags` through taggings.
# - It provides a `tag_list` getter method that returns a comma-separated string of tag names, facilitating easy display and editing of tags in views and forms.
# - It provides a `tag_list=` setter method that takes a comma-separated string of tag names, splits it into individual names,
#   and associates the corresponding Tag records with the taggable model. If a tag does not exist, it is created.
#   This method ensures that tags can be easily assigned or updated through forms.
#
# === Usage:
# Include the Taggable concern in any ActiveRecord model that should be taggable. For example:
#
#   class Video < ApplicationRecord
#     include Taggable
#     # Additional model-specific logic here
#   end
#
# After including this module, the model will gain all tagging capabilities, allowing for the creation,
# retrieval, and updating of tags through a simple and unified interface.

module Taggable
  extend ActiveSupport::Concern

  included do
    has_many :taggings, as: :taggable, dependent: :destroy
    has_many :tags, through: :taggings
  end

  # Getter for tag list
  def tag_list
    tags.map(&:name).join(", ")
  end

  # Setter for tag list
  def tag_list=(names)
    self.tags = names.split(",").map do |name|
      Tag.where(name: name.strip).first_or_create!
    end
  end
end