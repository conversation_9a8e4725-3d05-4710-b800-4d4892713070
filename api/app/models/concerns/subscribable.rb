module Subscribable
  extend ActiveSupport::Concern

  included do
    has_one :subscriber, as: :subscriber, dependent: :destroy
  end

  def get_or_create_subscriber
    return subscriber if subscriber.present?

      # Get address information from the subscribable if possible
      address_attrs = {}
      if self.respond_to?(:address_line1) && self.address_line1.present?
        address_attrs = {
          address_line1: self.address_line1,
          address_line2: self.address_line2,
          city: self.city,
          state: self.state,
          postal_code: self.postal_code,
          country: self.country || 'GB' # Default to UK
        }
      end

    # Create a new subscriber with appropriate attributes
    create_subscriber
  end
end
