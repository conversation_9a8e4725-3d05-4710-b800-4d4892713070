# frozen_string_literal: true

module Undeletable
  extend ActiveSupport::Concern

  included do
    default_scope { undeletable? ? where(deleted: false) : all }

    def self.undeletable?
      defined?(@undeletable) ? @undeletable : @undeletable = column_names.include?('deleted')
    end

    def self.deleted
      unscoped.where(deleted: true)
    end

    def destroy(force: false)
      return super() if !self.class.undeletable?
      
      if force
        self.class.transaction do
          self.class.reflect_on_all_associations.each do |reflection|
            next unless reflection.options[:dependent] == :destroy

          associated = if reflection.collection?
            reflection.klass.unscoped.where(reflection.foreign_key => self.id)
          else
            [reflection.klass.unscoped.find_by(reflection.foreign_key => self.id)].compact
          end

            associated.each do |record|
              record.respond_to?(:destroy) ? record.destroy(force: true) : record.destroy
            end
          end

          super()  # This is now safe, because we're inside the original `destroy`
        end
      else
        return super() if !self.class.undeletable? || force

        if update_attribute(:deleted, true)
          self.class.reflect_on_all_associations.each do |reflection|
            if reflection.options[:dependent] == :destroy
              if reflection.macro == :has_many
                send(reflection.name).map(&:destroy)
              else
                send(reflection.name)&.destroy
              end
            end
          end
          true
        end
      end
    end
  end
end