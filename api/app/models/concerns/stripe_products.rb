# app/models/concerns/stripe_products.rb
module StripeProducts
  PRODUCTS = {
    science: { name: 'Science' },
    geography: { name: 'Geography' },
    ai: { name: 'AI' }
  }

  SIZES = {
    individual: { name: 'Individual', max_users: 1 },
    small: { name: 'Small School', max_users: 99 },
    medium: { name: 'Medium School', max_users: 250 },
    large: { name: 'Large School', max_users: 500 },
    very_large: { name: 'Very Large School', max_users: nil }
  }

  BASE_PRICES = {
    individual: 60,           # Annual price for one individual product
    individual_monthly: 6,    # Monthly price for one individual product

    small: 125,               # Annual price for one small school product
    medium: 150,              # Annual price for one medium school product
    large: 200,               # Annual price for one large school product
    very_large: 250           # Annual price for one very large school product
  }

  DISCOUNTS = {
    two_products: 0.10, # 10% discount
    three_products: 0.20 # 20% discount
  }

  def self.is_monthly_plan?(plan_key)
    plan_key.to_s.end_with?('_monthly')
  end

  def self.get_base_size_from_plan_key(plan_key)
    plan_key.to_s.chomp('_monthly').to_sym
  end

  # Add to app/models/concerns/stripe_products.rb
  def self.get_product_id(product_key, environment = nil)
    environment ||= Rails.configuration.stripe[:environment]

    product_record = StripeProductPrice.find_by(
      product: product_key.to_s,
      size: 'base'
    )

    return nil unless product_record

    environment.to_sym == :live ? product_record.live_product_id : product_record.test_product_id
  end

  def self.get_price_id(product_key, plan_key, environment = nil)
    environment ||= Rails.configuration.stripe[:environment]
    plan_key_str = plan_key.to_s # Convert symbol to string for DB lookup if needed

    # Enforce the rule: Monthly plans are only for individuals.
    # An explicit check based on the plan_key provides an additional layer of safety.
    base_size_from_key = plan_key_str.chomp('_monthly')
    if plan_key_str.end_with?('_monthly') && base_size_from_key != 'individual'
      Rails.logger.warn "Attempted to get Price ID for non-individual monthly plan: #{plan_key_str}"
      return nil # No such Price ID should exist or be queried.
    end

    price_record = StripeProductPrice.find_by(
      product: product_key.to_s,
      size: plan_key_str
    )

    return nil unless price_record

    environment.to_sym == :live ? price_record.live_price_id : price_record.test_price_id
  end

  def self.get_discount_coupon_id(product_count, environment = nil)
    environment ||= Rails.configuration.stripe[:environment]

    discount_type = case product_count
                    when 2 then 'two_products'
                    when 3 then 'three_products'
                    else return nil
                    end

    coupon_record = StripeProductPrice.find_by(
      product: 'discount',
      size: discount_type
    )

    return nil unless coupon_record

    environment.to_sym == :live ? coupon_record.live_price_id : coupon_record.test_price_id
  end
end
