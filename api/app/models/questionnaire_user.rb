# == Schema Information
#
# Table name: questionnaire_users
#
#  id               :bigint           not null, primary key
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  questionnaire_id :bigint
#  user_id          :bigint
#
# Indexes
#
#  index_questionnaire_users_on_questionnaire_id  (questionnaire_id)
#  index_questionnaire_users_on_user_id           (user_id)
#
class QuestionnaireUser < ApplicationRecord
  belongs_to :questionnaire
  belongs_to :user
end
