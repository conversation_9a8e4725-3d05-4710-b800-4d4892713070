# app/jobs/career_generation_job.rb
class CareerGenerationJob < ApplicationJob
  queue_as :default

  retry_on StandardError, wait: :exponentially_longer, attempts: 3

  def perform(career_path_id, regen: false)
    Rails.logger.info "Starting CareerGenerationJob for career_path_id: #{career_path_id}"
    
    service = CareerAIGeneratorService.new
    service.generate_career_path_by_id(career_path_id, regen, true) # nothread = true since we're already in a job
    
    Rails.logger.info "Completed CareerGenerationJob for career_path_id: #{career_path_id}"
  rescue => e
    Rails.logger.error "CareerGenerationJob failed for career_path_id: #{career_path_id}, error: #{e.message}"
    
    # Update career path status on final failure
    if executions >= self.class.retry_attempts
      career_path = CareerPath.find_by(id: career_path_id)
      if career_path
        career_path.update(status: "error", generation_key: nil)
        career_path.log "Job failed after #{executions} attempts: #{e.message}"
      end
    end
    
    raise e
  end
end