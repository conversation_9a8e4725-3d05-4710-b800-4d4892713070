class SyncTeacherHubspotJob < ApplicationJob
  queue_as :default

  def perform(teacher)
    school = teacher.school
    return if school&.is_industry_account? || (Rails.env.development? && !ENV["HUBSPOT_GOOD"])

    HubspotManager.wrap_with_error_handler({ user: teacher, school: school }) do
      if teacher.hubspot_id
        HubspotManager.update_contact_from_teacher(teacher)
      else
        hubspot_contact_id = HubspotManager.create_contact_from_teacher(teacher)
        teacher.update_column(:hubspot_id, hubspot_contact_id)
        teacher.reload
      end

      if school&.hubspot_id
        HubspotManager.associate_contact_to_company(
          teacher.hubspot_id,
          school.hubspot_id,
          school.science_leaders.first == teacher ? [:adminTeacher] : []
        )
      end
    end
  end

end
