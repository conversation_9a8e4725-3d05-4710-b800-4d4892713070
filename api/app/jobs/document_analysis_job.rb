class DocumentAnalysisJob < ApplicationJob
  def perform(document)
    begin
      return unless document.present?

      unless document.fileboy_image_id.present?
        Rails.logger.warn "Document #{document.id} does not contain an image. Skipping analysis."
        return
      end

      image_url = fileboy_image_url(document.fileboy_image_id)
      
      # Check if URL is accessible
      uri = URI(image_url)
      response = Net::HTTP.get_response(uri)
      unless response.code == '200'
        raise "Image not accessible: #{response.code}"
      end
      
      analysis = ImageAnalysisService.new(image_url).analyze
      
      document.update!(
        keywords: analysis[:keywords],
        ai_description: analysis[:description],
        processing: false,
        analyzed_at: Time.current
      )
    rescue => e
      Rails.logger.error "Failed to analyze image from document #{document.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n") if e.respond_to?(:backtrace)
      
      # Log the full error for debugging
      if e.respond_to?(:response)
        Rails.logger.error "API Response: #{e.response}"
      end
      
      document.update!(
        processing: false,
        keywords: "analysis_failed"
      )
    end
  end

  private 

  def fileboy_image_url(fileboy_image_id)
    return nil unless fileboy_image_id
    "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:1000x1000~fit:contain;format:webp;quality:80"
  end
end