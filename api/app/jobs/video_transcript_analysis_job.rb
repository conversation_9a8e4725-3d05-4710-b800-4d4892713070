class VideoTranscriptAnalysisJob < ApplicationJob
  def perform(video)
    begin
      transcript = video.transcript
      return if transcript.blank?

      analysis = VideoTranscriptAnalysisService.new(transcript).analyze
      
      video.update!(
        keywords: analysis[:keywords],
        ai_summary: analysis[:summary],
        processing: false,
        analyzed_at: Time.current
      )
    rescue => e
      Rails.logger.error "Failed to analyze video #{video.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n") if e.respond_to?(:backtrace)
      
      if e.respond_to?(:response)
        Rails.logger.error "API Response: #{e.response}"
      end
      
      video.update!(
        processing: false,
        keywords: "analysis_failed"
      )
    end
  end
end