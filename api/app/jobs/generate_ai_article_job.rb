class GenerateAiArticleJob < ApplicationJob
  queue_as :default

  def perform(title, article_type, prompt, session_id)
    Rails.logger.info "Starting AI article generation for session: #{session_id}"
    
    begin
      # Set status to processing
      Rails.cache.write("ai_generation_status_#{session_id}", 'processing', expires_in: 15.minutes)
      
      # Generate the article using our service
      generator = ArticleGenerator.new
      generated_content = generator.generate_article(title, article_type, prompt)
      
      Rails.logger.info "Article generated successfully for session: #{session_id}"
      
      # Store success result
      Rails.cache.write(
        "ai_generation_status_#{session_id}", 
        {
          status: 'completed',
          content: generated_content
        }.to_json,
        expires_in: 15.minutes
      )
      
    rescue => e
      Rails.logger.error "AI Article Generation Job Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      # Store error result
      Rails.cache.write(
        "ai_generation_status_#{session_id}",
        {
          status: 'error',
          errors: ["Failed to generate article: #{e.message}"]
        }.to_json,
        expires_in: 15.minutes
      )
    end
  end
end