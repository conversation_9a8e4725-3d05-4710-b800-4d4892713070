require 'csv'

class UkSchoolsImportJob < ApplicationJob
  queue_as :default

  def perform(file_path)
    batch_size = 500
    batch = []

    total_lines = CSV.read(file_path).length - 1
    set_progress(0, total_lines)
    CSV.foreach(file_path, headers: true, encoding: 'bom|utf-8:utf-8').with_index(1) do |row, index|
      # stop importing closed schools (some are duplicated with open and closed versions)
      next if row['EstablishmentStatus (name)'].to_s.downcase == 'closed'
      school = {
        name: row['EstablishmentName'],
        postcode: row['Postcode'],
        local_authority: row['LA (name)'],
        urn: row['URN'],
        phone: row['TelephoneNum'],
        county: row['County (name)'],
        address: [row['Street'], row['Address3'], row['Town']].compact.join(', '),
        academy_trust: row['Trusts (name)'],
        sponsors: row['SchoolSponsors (name)'],
        website: row['SchoolWebsite'].to_s,
        age_range: [row['StatutoryLowAge'], row['StatutoryHighAge']].compact.join(', '),
        number_of_pupils: row['NumberOfPupils'].presence,
        open_date: row['OpenDate'].presence && DateTime.strptime(row['OpenDate'], '%d-%m-%Y'),
        category: row['PhaseOfEducation (name)'],
        phase_of_education: row['PhaseOfEducation (name)'],
        ofsted_last_inspection: row['OfstedLastInsp'].presence && DateTime.strptime(row['OfstedLastInsp'], '%d-%m-%Y'),
        ofsted_rating: row['OfstedRating (name)'],
        region: '',
      }

      school[:category] = case school[:category].to_s.downcase
                          when /primary/ then 'primary_school'
                          when /secondary/ then 'secondary_school'
                          when /all-through/ then 'all_through'
                          else ''
                          end

      region = row['GOR (name)'].to_s.gsub(/\s/, '_').downcase
      school[:region] = region if School.regions.keys.include?(region)

      batch << school

      if batch.size >= batch_size || index == total_lines
        process_batch(batch)
        batch.clear
      end

      set_progress(index, total_lines)
    end

    set_progress(total_lines, total_lines, complete: true)
  rescue => e
    Rails.logger.error("Error processing file: #{e.message}")
    set_progress(nil, nil, success: false, complete: true, message: e.message)
  ensure
    File.delete(file_path) if File.exist?(file_path)
  end

  private

  def set_progress(current_line, total_lines, success: nil, message: nil, complete: false)
    if current_line.nil? || total_lines.nil?
      # if we dont have a line/total just abort with message
      set_cache(job_id, 0, '0/0', success, message, complete)
      return
    end

    current_line = current_line.to_f
    total_lines = total_lines.to_f
    adjusted_current_line = (current_line / 10).ceil * 10

    percent = ((current_line / total_lines) * 100).round(2)
    progress = "#{adjusted_current_line} / #{total_lines}"
    puts "#{percent} - #{progress}"
    set_cache(job_id, percent, progress, success, message, complete)
  end

  def set_cache(job_id, percent, progress, success, message, complete)
    current_cache = Rails.cache.read("uk_schools_import_progress_#{job_id}")
    Rails.cache.write("uk_schools_import_progress_#{job_id}", {
                        start_time: current_cache&.dig(:start_time) || Time.current,
                        percent: percent.presence || 0,
                        progress: progress.presence || '0/0',
                        success: success.presence || current_cache&.dig(:success).presence || true,
                        message: message.presence || current_cache&.dig(:message)&.presence || '',
                        complete: complete
                      })
  end

  def process_batch(batch)
    timestamp = Time.current

    # Transform batch for upsert
    records = batch.map do |s|
      s = s.transform_keys(&:to_sym)
      s[:created_at] = timestamp
      s[:updated_at] = timestamp
      s
    end

    # Use upsert_all to insert or update records based on 'name' and 'urn'
    UkSchool.upsert_all(
      records,
      unique_by: :index_uk_schools_on_name_and_urn # Use the composite unique index
    )
  end
end
