class CreateDocumentRecordsJob < ApplicationJob
  include Fileboy
  queue_as :default

  def perform(records)
    documents = []

    records.each do |record_hash|
      next unless valid_record_hash?(record_hash)

      documentable = find_documentable(record_hash[:id], record_hash[:class_name])
      next unless documentable
      next if documentable&.document.present?
  
      document = create_document(documentable, record_hash)
      next unless document&.fileboy_file_id.present?
      
      document.update!(processing: true)
      documents << document
    end
    
    process_documents(documents) if documents.any?
  rescue => e
    Rails.logger.error("[CreateDocumentRecordsJob] Batch failed: #{e.message}")
  end
end

private

def valid_record_hash?(record)
  record[:id].present? && record[:class_name].present? && record[:fileboy_file_id].present?
end

def find_documentable(id, type)
  type.constantize.find_by(id: id)
rescue NameError => e
  Rails.logger.error("[CreateDocumentRecordsJob] Invalid type: #{type}")
end

def create_document(documentable, record_hash)
  Document.create!(
    documentable: documentable,
    fileboy_file_id: record_hash[:fileboy_file_id],
    title: record_hash[:title] || "Untitled Document"
  )
rescue => e
  Rails.logger.error("[CreateDocumentRecordsJob] Document creation failed: #{e.message}")
end

def process_documents(documents)
  file_url_map = CloudConvertService.convert_documents_to_images(documents)

  documents.each do |document|
    file_url = file_url_map[document.id]

    unless file_url.present?
      document.update!(processing: false, keywords: "conversion_failed")
      next
    end

    fileboy_image_id = upload_image_from_url(file_url)

    if fileboy_image_id.present?
      document.update!(fileboy_image_id: fileboy_image_id)
      document.analyze_with_ai!
    else
      document.update!(processing: false, keywords: "upload_failed")
    end
  rescue => e
    Rails.logger.error("[CreateDocumentRecordsJob] Failed to process Document##{document.id}: #{e.message}")
    document.update!(processing: false, keywords: "processing_failed")
  end
end