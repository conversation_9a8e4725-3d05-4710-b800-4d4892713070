# app/jobs/mailchimp_sync_job.rb
class Mailchi<PERSON>SyncJob < ApplicationJob
  queue_as :default

  def perform(user_ids: nil, force: false, batch_size: 1000)
    Rails.logger.info "Starting Mailchimp sync job"
    
    if user_ids.present?
      # For specific users, load them normally
      users = User.where(id: user_ids)
      total_users = users.count
      Rails.logger.info "Found #{total_users} specific users to sync"
      
      users.find_in_batches(batch_size: batch_size) do |batch|
        process_batch(batch)
      end
    else
      # For bulk sync, use the optimized query and count first
      users_query = build_users_needing_sync_query
      total_users = users_query.count
      Rails.logger.info "Found #{total_users} users needing sync"
      
      return if total_users == 0
      
      # Process in batches without loading everything
      users_query.find_in_batches(batch_size: batch_size) do |batch|
        # Filter the batch for any edge cases
        valid_batch = batch.reject { |user| invalid_user?(user) }
        process_batch(valid_batch) if valid_batch.any?
      end
    end

    Rails.logger.info "Completed Mailchimp sync job"
  end


  private

  def build_users_needing_sync_query
  # Start with the basic time-based filter (this should use an index)
  User.where(type: ["Teacher", "IndividualUser"])
      .where("mailchimp_last_sync_attempt IS NULL OR mailchimp_last_sync_attempt < ?", 24.hours.ago)
      .includes(:school)
  end

  def process_batch(batch)
    begin
      # Let the sync service handle all the validation
      Mailchimp::SyncService.new.sync_users_batch(batch, trigger_type: 'scheduled', force: false)
      Rails.logger.info "Processed batch of #{batch.size} users"
    rescue => e
      Rails.logger.error "Failed to sync batch: #{e.message}"
    end
  end

  def invalid_user?(user)
    (user.type == "Teacher" && !user.school) || user.school&.is_industry_account?
  end
end