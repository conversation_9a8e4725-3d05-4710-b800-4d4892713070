# app/jobs/landing_page_generation_job.rb
class Landing<PERSON>ageGenerationJob < ApplicationJob
  queue_as :default

  def perform(landing_page_id, generation_params, session_id)
    Rails.logger.info "Starting AI landing page generation for session: #{session_id}"
    
    begin
      # Set status to processing
      Rails.cache.write("landing_page_generation_status_#{session_id}", 'processing', expires_in: 15.minutes)
      
      landing_page = LandingPage.find(landing_page_id)
      generator = LandingPageGeneratorService.new(generation_params)
      
      if generator.valid?
        generated_data = generator.send(:generate_all_content)
        
        # Update the landing page
        landing_page.update!(
          content: generated_data[:content],
          careers_data: generated_data[:careers],
          unit_links: generated_data[:unit_links],
          lesson_links: generated_data[:lesson_links],
          word_count: generated_data[:word_count],
          content_generated_at: Time.current,
          ai_model_used: generated_data[:model_used]
        )
        
        Rails.logger.info "Landing page generated successfully for session: #{session_id}"
        
        # Store success result
        Rails.cache.write(
          "landing_page_generation_status_#{session_id}", 
          {
            status: 'completed',
            landing_page_id: landing_page.id
          }.to_json,
          expires_in: 15.minutes
        )
      else
        # Store validation error
        Rails.cache.write(
          "landing_page_generation_status_#{session_id}",
          {
            status: 'error',
            errors: generator.errors.full_messages
          }.to_json,
          expires_in: 15.minutes
        )
      end
      
    rescue => e
      Rails.logger.error "Landing Page Generation Job Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      # Store error result
      Rails.cache.write(
        "landing_page_generation_status_#{session_id}",
        {
          status: 'error',
          errors: ["Failed to generate landing page: #{e.message}"]
        }.to_json,
        expires_in: 15.minutes
      )
    end
  end
end