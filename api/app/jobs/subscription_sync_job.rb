class SubscriptionSyncJob < ApplicationJob
  queue_as :default

  def perform(subscriber_id = nil, force: false)
    if subscriber_id
      # Sync a specific subscriber
      subscriber = Subscriber.find_by(id: subscriber_id)
      if subscriber
        subscriber.sync_subscription_data
      else
        Rails.logger.error "Cannot find subscriber with ID #{subscriber_id}"
      end
    else
      # Sync all active subscribers
      # Process in batches to avoid memory issues
      Subscriber.where.not(stripe_subscription_id: nil)
                .where(subscription_status: %w[active active_until_period_end past_due])
                .find_each(batch_size: 50) do |subscriber|
        begin
          # Only sync if not synced recently (within the last day)
          last_synced = subscriber.active_products['synced_at']
          subscriber.sync_subscription_data if force || last_synced.blank? || Time.parse(last_synced) < 1.day.ago
        rescue => e
          Rails.logger.error "Error processing subscriber #{subscriber.id}: #{e.message}"
        end
      end
    end
  end
end
