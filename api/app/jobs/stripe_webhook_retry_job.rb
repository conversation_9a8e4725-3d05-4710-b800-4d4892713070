class StripeWebhookRetryJob < ApplicationJob
  queue_as :default

  # Exponential backoff: retry after 1min, 5min, 15min, 1hr, 4hrs
  RETRY_DELAYS = [1.minute, 5.minutes, 15.minutes, 1.hour, 4.hours]

  def perform(event_id, attempt = 0)
    event = StripeEvent.find_by(id: event_id)
    return unless event && event.error.present?

    Rails.logger.info "Attempting to retry Stripe webhook event #{event.stripe_id}, attempt #{attempt + 1}"

    begin
      # Reconstruct the Stripe event
      stripe_event = construct_stripe_event(event)

      # Create a fresh controller instance to process the event
      controller = StripeWebhooksController.new

      # Process the event based on its type
      process_result = process_event(controller, stripe_event)

      # If successful, update the event record
      if process_result[:success]
        event.update(
          error: nil,
          processed_at: Time.current,
          retry_count: (event.retry_count || 0) + 1,
          last_retry_at: Time.current,
          next_retry_at: nil
        )

        Rails.logger.info "Successfully processed webhook event #{event.stripe_id} on retry"
      else
        # If failed, record the error and potentially schedule another retry
        record_retry_failure(event, process_result[:error], attempt)
      end
    rescue => e
      # Handle any exceptions during retry
      record_retry_failure(event, e.message, attempt)
    end
  end

  private

  def construct_stripe_event(event)
    # Create a Stripe event from the stored data
    Stripe::Event.construct_from({
                                   id: event.stripe_id,
                                   type: event.event_type,
                                   data: {
                                     object: event.event_data
                                   }
                                 })
  end

  def process_event(controller, stripe_event)
    # Process different event types
    case stripe_event.type
    when 'invoice.payment_succeeded'
      controller.send(:handle_successful_payment, stripe_event.data.object)
    when 'invoice.payment_failed'
      controller.send(:handle_failed_payment, stripe_event.data.object)
    when 'customer.subscription.updated'
      controller.send(:handle_subscription_update, stripe_event.data.object)
    when 'customer.subscription.deleted'
      controller.send(:handle_subscription_deletion, stripe_event.data.object)
    when 'customer.subscription.created'
      controller.send(:handle_subscription_creation, stripe_event.data.object)
    when 'customer.subscription.trial_will_end'
      controller.send(:handle_trial_ending, stripe_event.data.object)
    when 'invoice.upcoming'
      controller.send(:handle_upcoming_invoice, stripe_event.data.object)
    when 'payment_method.attached'
      controller.send(:handle_payment_method_attached, stripe_event.data.object)
    when 'invoice.payment_action_required'
      controller.send(:handle_payment_action_required, stripe_event.data.object)
    when 'checkout.session.completed'
      controller.send(:handle_checkout_session_completed, stripe_event.data.object)
    when 'invoice.finalized'
      controller.send(:handle_invoice_finalized, stripe_event.data.object)
    when 'invoice.sent'
      controller.send(:handle_invoice_sent, stripe_event.data.object)
    when 'customer.updated'
      controller.send(:handle_customer_updated, stripe_event.data.object)
    else
      return { success: false, error: "Unhandled event type: #{stripe_event.type}" }
    end

    # If we got here without error, consider it successful
    { success: true }
  rescue => e
    { success: false, error: "#{e.message}\n#{e.backtrace.slice(0, 4).join("\n")}" }
  end

  def record_retry_failure(event, error_message, attempt)
    next_attempt = attempt + 1

    # Update the event with the new error
    event.update(
      error: "Retry failed (attempt #{next_attempt}): #{error_message}",
      retry_count: (event.retry_count || 0) + 1,
      last_retry_at: Time.current
    )

    # Schedule another retry if we haven't exceeded the maximum attempts
    if next_attempt < RETRY_DELAYS.length
      delay = RETRY_DELAYS[next_attempt]
      Rails.logger.info "Scheduling retry #{next_attempt + 1} for event #{event.stripe_id} in #{delay} seconds"

      # Update next retry time
      event.update(next_retry_at: Time.current + delay)

      # Schedule the next retry
      StripeWebhookRetryJob.set(wait: delay).perform_later(event.id, next_attempt)
    else
      # Log that we've given up
      Rails.logger.error "Giving up on webhook event #{event.stripe_id} after #{next_attempt} failed attempts"

      # Mark that we're no longer retrying
      event.update(next_retry_at: nil)

      # Potentially trigger an alert here
      alert_critical_webhook_failure(event)
    end
  end

  def alert_critical_webhook_failure(event)
    # Send alerts for critical webhook failures
    return unless event.critical
    # This could email admins, send a Slack notification, etc.
    if defined?(AdminNotifier)
      AdminNotifier.critical_webhook_failure(event).deliver_later
    else
      Rails.logger.error "CRITICAL WEBHOOK FAILURE: #{event.stripe_id} (#{event.event_type})"
      # Could implement a direct email here as fallback
    end
  end
end
