class InvoiceReminderJob < ApplicationJob
  queue_as :default

  def perform
    # Find invoices issued 20 days ago that are still unpaid
    twenty_days_ago = 20.days.ago.beginning_of_day

    Rails.logger.info "Checking for unpaid invoices issued around #{twenty_days_ago}"

    begin
      # We need to use Stripe API to find unpaid invoices from 20 days ago
      # Since this relies on Stripe's API and doesn't have a direct filter for 20 days ago,
      # we'll get invoices created within the last month and filter them

      # Get all open (unpaid) invoices from the past month
      open_invoices = Stripe::Invoice.list({
        status: 'open',
        created: {
          # Get invoices from 20-21 days ago to cover our 20-day window with some margin
          gte: (20.days.ago.beginning_of_day).to_i,
          lte: (21.days.ago.end_of_day).to_i
        },
        limit: 100
      })

      # Filter out invoices that have already been notified
      reminder_invoices = open_invoices.data.select do |invoice|
        invoice.metadata['reminder_20_sent'] != 'true'
      end

      Rails.logger.info "Found #{reminder_invoices.size} unpaid invoices from around 20 days ago"

      # Process each invoice
      reminder_invoices.each do |invoice|
        # Find the subscriber
        subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

        if subscriber
          Rails.logger.info "Sending reminder for invoice #{invoice.id} to subscriber #{subscriber.id}"

          # Set a meta data item on the invoice we can use to track if we have sent a reminder
          Stripe::Invoice.update(
            invoice.id,
            metadata: { reminder_20_sent: true }
          )

          # Send the reminder email
          SubscriptionMailer.invoice_reminder(subscriber, invoice.id).deliver_later

          # Log the reminder
          Rails.logger.info "Reminder sent for invoice #{invoice.id} to #{subscriber.email}"
        else
          Rails.logger.warn "Could not find subscriber for customer #{invoice.customer}, invoice #{invoice.id}"
        end
      end

    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe API error in invoice reminder job: #{e.message}"
    rescue => e
      Rails.logger.error "Error in invoice reminder job: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end
end
