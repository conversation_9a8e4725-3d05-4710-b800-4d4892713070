class LessonTemplateProofJob < ApplicationJob
  queue_as :default

  # Arguments:
  #   lesson_template_id: ID of the Lesson::Template to proof 
  #   prompt: The AI prompt to use for proofing
  #   session_id: Unique session or job identifier for status tracking
  def perform(lesson_template)
    Rails.logger.info "Starting lesson template proof for template: #{lesson_template.id}"

    begin
      # Set status to processing
      Rails.cache.write("lesson_template_proof_status_#{lesson_template.id}", { status: 'processing' }, expires_in: 15.minutes)
      LessonTemplateProofService.new.generate_proof(lesson_template)

      Rails.logger.info "Lesson template proof completed for template: #{lesson_template.id}"
      Rails.cache.write("lesson_template_proof_status_#{lesson_template.id}", { status: 'completed' }, expires_in: 15.minutes)
    rescue => e
      Rails.logger.error "LessonTemplateProofJob Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # Store error result
      Rails.cache.write(
        "lesson_template_proof_status_#{lesson_template.id}",
        {
          status: 'error',
          errors: ['Error generating proof for lesson template'],
          message: e.message,
        },
        expires_in: 15.minutes
      )
    end
  end
end
