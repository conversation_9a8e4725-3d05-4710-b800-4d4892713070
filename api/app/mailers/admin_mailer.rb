# app/mailers/admin_mailer.rb
class AdminMailer < ApplicationMailer
  default from: '<EMAIL>'
  
  # Send a daily webhook report
  def webhook_daily_report(report_content)
    @report = report_content
    @report_lines = report_content.split("\n")
    @date = Date.today.strftime("%Y-%m-%d")
    
    mail(
      to: admin_emails,
      subject: "Stripe Webhook Daily Report - #{@date}"
    )
  end
  
  # Send alerts for critical webhook failures
  def critical_webhook_failure(event)
    @event = event
    @error = event.error
    @event_type = event.event_type
    @event_id = event.stripe_id
    @retry_count = event.retry_count
    @created_at = event.created_at
    
    mail(
      to: admin_emails,
      subject: "ALERT: Critical Stripe Webhook Failure - #{@event_type}"
    )
  end
  
  # Send a summary of multiple critical failures
  def critical_webhook_failure_summary(critical_events)
    @critical_events = critical_events
    @count = critical_events.count
    
    mail(
      to: admin_emails,
      subject: "ALERT: #{@count} Critical Stripe Webhook Failures Detected"
    )
  end
  
  # Send a notification about webhook cleanup
  def webhook_cleanup_notification(count)
    @count = count
    @date = Date.today.strftime("%Y-%m-%d")
    
    mail(
      to: admin_emails,
      subject: "Stripe Webhook Cleanup - #{@count} Events Removed"
    )
  end

  # Send error log notifications
  def error_log_notification(error_log)
    @error_log = error_log
    @error_message = error_log.error_message
    @pretty_error = error_log.pretty_error
    @user = error_log.user
    @school = error_log.school
    @user_link = error_log.user_link
    @created_at = error_log.created_at
    
    mail(
      to: admin_emails,
      subject: "DEVEX ERROR - #{@created_at.strftime('%Y-%m-%d %H:%M:%S')}"
    )
  end

  # Send alert when account closure fails
  def failed_account_closure(user, error_message)
    @user = user
    @error_message = error_message
    @customer_id = user&.stripe_customer_id
    
    mail(
      to: admin_emails,
      subject: "ALERT: Failed Account Closure - #{user.email}"
    )
  end

  # Send notification when new school is activated
  def new_school_activation(school)
    @school = school
    @school_id = school.id
    @school_name = school.name
    
    mail(
      to: school_activation_emails,
      subject: "New School Activation - #{@school_name}"
    )
  end

  # Send notification when XLS import is completed
  def xls_import_completed(xls_import)
    @xls_import = xls_import
    @school = xls_import.school
    @user_email = xls_import.user&.email || "-"
    @upload_date = Date.today
    @forms_count = xls_import.instance_variable_get(:@forms)&.count || 0
    @teachers_count = xls_import.instance_variable_get(:@teachers)&.count || 0
    @pupils_count = xls_import.instance_variable_get(:@pupils)&.count || 0
    
    mail(
      to: school_import_xls_emails,
      subject: "#{@school.name} has uploaded a new XLS"
    )
  end
  
  private
  
  def admin_emails
    # Replace with your actual admin email addresses or fetch from configuration
    tech_email = ENV['TECH_EMAIL'] || '<EMAIL>'
    
    [tech_email]
  end

  def school_activation_emails
    [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ]
  end

  def school_import_xls_emails
    [
      '<EMAIL>',
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ]
  end
end