# app/mailers/application_mailer.rb
require 'mandrill'

class ApplicationMailer < ActionMailer::Base
  default from: '<EMAIL>'
  layout 'mailer'

  # Define MandrillDeliveryMethod class
  class MandrillDeliveryMethod
    attr_accessor :settings

    def initialize(settings)
      @settings = settings || {}
    end

    def deliver!(mail)
      Rails.logger.info "Sending email via Mandrill delivery method"

      # Extract API key from settings
      api_key = settings[:api_key]
      if api_key.blank?
        Rails.logger.error "Mandrill API key is missing!"
        return false
      end

      # Build message parameters
      message = {
        subject: mail.subject,
        from_email: mail.from[0],
        from_name: get_name_from_email(mail.from[0]) || "Developing Experts",
        to: mail.to.map { |email| { email: email, type: "to" } },
        headers: {},
        important: false,
        track_opens: true,
        track_clicks: true,
        auto_text: true,
        inline_css: true,
        url_strip_qs: false,
        preserve_recipients: false
      }

      # Handle CC and BCC
      if mail.cc && !mail.cc.empty?
        message[:to] += mail.cc.map { |email| { email: email, type: "cc" } }
      end

      if mail.bcc && !mail.bcc.empty?
        message[:to] += mail.bcc.map { |email| { email: email, type: "bcc" } }
      end

      # Add Reply-To header if present
      if mail.reply_to && mail.reply_to.first
        message[:headers]['Reply-To'] = mail.reply_to.first
      end

      # Get message content
      if mail.multipart?
        if mail.html_part
          message[:html] = mail.html_part.body.decoded
        end

        if mail.text_part
          message[:text] = mail.text_part.body.decoded
        end
      else
        if mail.content_type =~ /text\/html/
          message[:html] = mail.body.decoded
        else
          message[:text] = mail.body.decoded
        end
      end

      # Handle test mode filtering
      original_recipients = message[:to].dup
      
      if mail.header['X-Test-Mode']&.value == 'true'
        allowed_recipient_domains = %w[@cd2solutions.co.uk @cd2.uk @developingexperts.com]

        message[:subject] = "[TEST] #{message[:subject]}"
        message[:to] = message[:to].select { |r| allowed_recipient_domains.any? { |domain| r[:email].downcase.end_with?(domain) } }

        if message[:to].empty?
          Rails.logger.error 'No allowed recipient domains found for test mode email.'
          raise 'No allowed recipient domains found for test mode email.'
        end
      elsif !Rails.env.production? && !ENV['ALLOW_CUSTOM_TEST_EMAILS']
        # Override recipients in development
        if settings[:test_email]
          original_recipients_text = message[:to].map { |r| r[:email] }.join(', ')
          message[:subject] = "[DEV] #{message[:subject]} (to: #{original_recipients_text})"
          message[:to] = [{ email: settings[:test_email], type: 'to' }]
        end
      end

      # Log the email details
      Rails.logger.info "Mandrill email:"
      Rails.logger.info "  From: #{message[:from_email]}"
      Rails.logger.info "  To: #{message[:to].map { |r| r[:email] }.join(', ')}"
      Rails.logger.info "  Subject: #{message[:subject]}"

      # Track email in PostHog BEFORE sending
      track_email_attempt(mail, message, original_recipients)

      # Send the email through Mandrill's API or simulate in development
      begin
        # Only send if we have an API key and either in production or configured to send in development
        if api_key && (Rails.env.production? || settings[:send_in_development])
          mandrill = Mandrill::API.new(api_key)
          result = mandrill.messages.send(message)

          # Log the success
          Rails.logger.info "Email sent via Mandrill. Message ID: #{result.first['_id']} Status: #{result.first['status']}"

          # Track successful email delivery in PostHog
          track_email_delivery(mail, message, result, original_recipients)

          return result
        else
          # Track simulated email in development
          track_email_simulation(mail, message, original_recipients)
          
          # Log instead of send in development
          Rails.logger.info "Development mode - email not actually sent:"
          Rails.logger.info "Would have sent email with subject: #{message[:subject]}"
        end
      rescue Mandrill::Error => e
        # Track email failure in PostHog
        track_email_failure(mail, message, e, original_recipients)
        
        Rails.logger.error "Mandrill API error: #{e.message}"
        raise e
      rescue => e
        # Track email failure in PostHog
        track_email_failure(mail, message, e, original_recipients)
        
        Rails.logger.error "Error sending via Mandrill: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        raise e
      end
    end

    private

    def get_name_from_email(email)
      if email =~ /(.*)<(.*)>/
        $1.strip
      else
        nil
      end
    end

    # Track email attempt (before sending)
    def track_email_attempt(mail, message, original_recipients)
      return unless defined?($posthog) && $posthog

      # Extract user information from mail headers if available
      user_data = extract_user_data_from_mail(mail)
      
      properties = build_email_properties(mail, message, original_recipients).merge({
        email_status: 'attempted',
        email_stage: 'pre_send'
      }).merge(user_data[:properties])

      $posthog.capture(
        distinct_id: user_data[:distinct_id] || 'system',
        event: 'email attempted',
        properties: properties
      )
    rescue => e
      Rails.logger.error "Error tracking email attempt in PostHog: #{e.message}"
    end

    # Track successful email delivery
    def track_email_delivery(mail, message, mandrill_result, original_recipients)
      return unless defined?($posthog) && $posthog

      user_data = extract_user_data_from_mail(mail)
      
      properties = build_email_properties(mail, message, original_recipients).merge({
        email_status: 'sent',
        email_stage: 'delivered',
        mandrill_message_id: mandrill_result&.first&.dig('_id'),
        mandrill_status: mandrill_result&.first&.dig('status'),
        mandrill_reject_reason: mandrill_result&.first&.dig('reject_reason')
      }).merge(user_data[:properties])

      $posthog.capture(
        distinct_id: user_data[:distinct_id] || 'system',
        event: 'email sent',
        properties: properties
      )
    rescue => e
      Rails.logger.error "Error tracking email delivery in PostHog: #{e.message}"
    end

    # Track email simulation (development mode)
    def track_email_simulation(mail, message, original_recipients)
      return unless defined?($posthog) && $posthog

      user_data = extract_user_data_from_mail(mail)
      
      properties = build_email_properties(mail, message, original_recipients).merge({
        email_status: 'simulated',
        email_stage: 'development',
        environment: Rails.env
      }).merge(user_data[:properties])

      $posthog.capture(
        distinct_id: user_data[:distinct_id] || 'system',
        event: 'email simulated',
        properties: properties
      )
    rescue => e
      Rails.logger.error "Error tracking email simulation in PostHog: #{e.message}"
    end

    # Track email failure
    def track_email_failure(mail, message, error, original_recipients)
      return unless defined?($posthog) && $posthog

      user_data = extract_user_data_from_mail(mail)
      
      properties = build_email_properties(mail, message, original_recipients).merge({
        email_status: 'failed',
        email_stage: 'error',
        error_type: error.class.name,
        error_message: error.message,
        environment: Rails.env
      }).merge(user_data[:properties])

      $posthog.capture(
        distinct_id: user_data[:distinct_id] || 'system',
        event: 'email failed',
        properties: properties
      )
    rescue => e
      Rails.logger.error "Error tracking email failure in PostHog: #{e.message}"
    end

    # Build common email properties for PostHog tracking
    def build_email_properties(mail, message, original_recipients)
      {
        email_subject: message[:subject],
        email_from: message[:from_email],
        email_from_name: message[:from_name],
        email_to: message[:to].map { |r| r[:email] },
        email_to_count: message[:to].length,
        email_cc: mail.cc&.join(', '),
        email_bcc: mail.bcc&.join(', '),
        email_reply_to: mail.reply_to&.first,
        email_has_html: message[:html].present?,
        email_has_text: message[:text].present?,
        email_is_multipart: mail.multipart?,
        email_content_type: mail.content_type,
        email_test_mode: mail.header['X-Test-Mode']&.value == 'true',
        email_original_recipients: original_recipients.map { |r| r[:email] },
        email_original_recipient_count: original_recipients.length,
        email_mailer_class: extract_mailer_class_from_mail(mail),
        email_mailer_action: extract_mailer_action_from_mail(mail),
        environment: Rails.env,
        timestamp: Time.current.iso8601
      }
    end

    # Extract user data from mail headers or other mail metadata
    # Returns both distinct_id for PostHog and properties for tracking
    def extract_user_data_from_mail(mail)
      triggered_by_user = nil
      recipient_user = nil
      
      # 1. Try to get triggered_by user from custom headers (highest priority)
      if mail.header['X-Triggered-By-User-ID']
        triggered_by_user = find_user_by_id(mail.header['X-Triggered-By-User-ID'].value)
      end

      # 2. Try to get recipient user from custom headers
      if mail.header['X-User-ID']
        recipient_user = find_user_by_id(mail.header['X-User-ID'].value)
      end

      # 3. Try to extract from arguments if available (Rails mailer context)
      if mail.instance_variable_get(:@_mail_was_called) && 
         mail.respond_to?(:params) && 
         mail.params.is_a?(Hash)
        
        # Look for triggered_by user first
        triggered_by_user ||= mail.params[:triggered_by] || mail.params[:triggered_by_user] || mail.params[:current_user]
        
        # Look for recipient user
        recipient_user ||= mail.params[:user] || mail.params[:recipient] || mail.params[:to_user]
      end

      # 4. Fallback: try to extract recipient from the first recipient email if it looks like a user
      if !recipient_user && mail.to&.first && defined?(User)
        recipient_user = User.find_by(email: mail.to.first)
      end

      # Determine which user to use as distinct_id (prioritize triggered_by)
      primary_user = triggered_by_user || recipient_user
      
      # Build user properties
      properties = {}
      
      if triggered_by_user
        properties.merge!({
          triggered_by_user_id: triggered_by_user.id,
          triggered_by_user_email: triggered_by_user.respond_to?(:email) ? triggered_by_user.email : nil,
          triggered_by_user_type: triggered_by_user.respond_to?(:type) ? triggered_by_user.type : nil,
          triggered_by_user_is_admin: triggered_by_user.respond_to?(:is_school_admin?) ? triggered_by_user.is_school_admin? : nil
        })
        
        # Add school data for triggered_by user using your existing helper pattern
        if triggered_by_user.respond_to?(:school) && triggered_by_user.school
          properties.merge!({
            triggered_by_school_id: triggered_by_user.school.id,
            triggered_by_school_name: triggered_by_user.school.name,
            triggered_by_school_postcode: triggered_by_user.school.postcode,
            triggered_by_school_region: triggered_by_user.school.region
          })
        end
      end
      
      if recipient_user && recipient_user != triggered_by_user
        properties.merge!({
          recipient_user_id: recipient_user.id,
          recipient_user_email: recipient_user.respond_to?(:email) ? recipient_user.email : nil,
          recipient_user_type: recipient_user.respond_to?(:type) ? recipient_user.type : nil,
          recipient_user_is_admin: recipient_user.respond_to?(:is_school_admin?) ? recipient_user.is_school_admin? : nil
        })
        
        # Add school data for recipient user
        if recipient_user.respond_to?(:school) && recipient_user.school
          properties.merge!({
            recipient_school_id: recipient_user.school.id,
            recipient_school_name: recipient_user.school.name,
            recipient_school_postcode: recipient_user.school.postcode,
            recipient_school_region: recipient_user.school.region
          })
        end
      end

      # If triggered_by and recipient are the same, mark it
      if triggered_by_user && recipient_user && triggered_by_user.id == recipient_user.id
        properties[:is_self_triggered_email] = true
      end

      {
        distinct_id: primary_user&.id,
        triggered_by_user: triggered_by_user,
        recipient_user: recipient_user,
        properties: properties
      }
    end

    # Helper method to safely find user by ID
    def find_user_by_id(user_id)
      return nil unless user_id && defined?(User)
      User.find_by(id: user_id.to_i)
    rescue
      nil
    end

    # Extract mailer class name from mail object
    def extract_mailer_class_from_mail(mail)
      # Try to get the mailer class from the delivery handler
      if mail.delivery_handler.respond_to?(:name)
        return mail.delivery_handler.name
      end

      # Fallback to checking the mail object's class hierarchy
      mail.class.name if mail.class != Mail::Message
    end

    # Extract mailer action/method name from mail object
    def extract_mailer_action_from_mail(mail)
      # This is tricky to extract reliably, but we can try a few approaches
      
      # Check if there's an action name in the mail headers
      return mail.header['X-Mailer-Action']&.value if mail.header['X-Mailer-Action']

      # Try to extract from the mail's instance variables if available
      action = mail.instance_variable_get(:@_action_name)
      return action if action

      # Return nil if we can't determine the action
      nil
    end
  end
end