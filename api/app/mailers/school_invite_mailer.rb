# app/mailers/school_invite_mailer.rb
class SchoolInviteMailer < ApplicationMailer
  def invitation_email(school_invite)
    @school_invite = school_invite
    @inviter = school_invite.inviter
    @school = school_invite.school
    @accept_url = accept_school_invite_url(token: school_invite.token)
    
    mail(
      to: school_invite.email,
      subject: "#{@inviter.name} has invited you to join #{@school.name} on Developing Experts"
    )
  end

  def invite_accepted_email(school_invite)
    @inviter = school_invite.inviter
    @invited_user = school_invite.invited_user
    @school = school_invite.school
    @view_invites_url = school_invites_url

    mail(
      to: @inviter.email,
      subject: "#{@invited_user.name} has joined #{@school.name} on Developing Experts"
    )
  end
end
