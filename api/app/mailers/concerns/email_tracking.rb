# app/mailers/concerns/email_tracking.rb
module EmailTracking
  extend ActiveSupport::Concern

  included do
    # Add a before_action to set tracking headers if available
    before_action :set_email_tracking_headers
  end

  private

  # Set tracking headers that the delivery method can use
  def set_email_tracking_headers
    safe_params = respond_to?(:params) && params ? params : {}

    # Set triggered_by user ID header (the user who initiated the email)
    if safe_params[:triggered_by]&.respond_to?(:id)
      headers['X-Triggered-By-User-ID'] = safe_params[:triggered_by].id.to_s
    elsif safe_params[:triggered_by_user]&.respond_to?(:id)
      headers['X-Triggered-By-User-ID'] = safe_params[:triggered_by_user].id.to_s
    elsif safe_params[:current_user]&.respond_to?(:id)
      headers['X-Triggered-By-User-ID'] = safe_params[:current_user].id.to_s
    end

    # Set recipient user ID header (who the email is being sent to)
    if safe_params[:user]&.respond_to?(:id)
      headers['X-User-ID'] = safe_params[:user].id.to_s
    elsif safe_params[:recipient]&.respond_to?(:id)
      headers['X-User-ID'] = safe_params[:recipient].id.to_s
    elsif safe_params[:to_user]&.respond_to?(:id)
      headers['X-User-ID'] = safe_params[:to_user].id.to_s
    end

    # Set mailer action header
    headers['X-Mailer-Action'] = action_name if respond_to?(:action_name)
    
    # Set mailer class header
    headers['X-Mailer-Class'] = self.class.name
    
    # Add any custom tracking data
    add_custom_tracking_headers if respond_to?(:add_custom_tracking_headers, true)
  end

  # Class methods for tracking specific email types
  class_methods do
    # Track email sending for specific mailer actions
    def track_email_action(action_name, triggered_by: nil, recipient: nil, user: nil, additional_properties: {})
      return unless defined?($posthog) && $posthog

      # Handle backward compatibility with user parameter
      triggered_by ||= user
      recipient ||= user

      properties = {
        mailer_class: self.name,
        mailer_action: action_name,
        email_type: 'mailer_action'
      }.merge(additional_properties)

      # Add triggered_by user data if available
      if triggered_by
        properties.merge!({
          triggered_by_user_id: triggered_by.id,
          triggered_by_user_email: triggered_by.email,
          triggered_by_user_type: triggered_by.respond_to?(:type) ? triggered_by.type : nil
        })
      end

      # Add recipient user data if available and different from triggered_by
      if recipient && (triggered_by.nil? || recipient.id != triggered_by.id)
        properties.merge!({
          recipient_user_id: recipient.id,
          recipient_user_email: recipient.email,
          recipient_user_type: recipient.respond_to?(:type) ? recipient.type : nil
        })
      end

      # Use triggered_by user as distinct_id, fallback to recipient
      distinct_id = (recipient || triggered_by)&.id || 'system'

      $posthog.capture(
        distinct_id: distinct_id,
        event: 'email action triggered',
        properties: properties
      )
    rescue => e
      Rails.logger.error "Error tracking email action in PostHog: #{e.message}"
    end
  end

  # Instance method to track email context
  def track_email_context(additional_properties = {})
    return unless defined?($posthog) && $posthog

    triggered_by = params[:triggered_by] || params[:triggered_by_user] || params[:current_user]
    recipient = params[:user] || params[:recipient] || params[:to_user]
    
    properties = {
      mailer_class: self.class.name,
      mailer_action: action_name,
      email_type: 'context_tracking'
    }.merge(additional_properties)

    # Add triggered_by user data if available
    if triggered_by && triggered_by.respond_to?(:id)
      properties.merge!({
        triggered_by_user_id: triggered_by.id,
        triggered_by_user_email: triggered_by.respond_to?(:email) ? triggered_by.email : nil,
        triggered_by_user_type: triggered_by.respond_to?(:type) ? triggered_by.type : nil
      })
    end

    # Add recipient user data if available and different from triggered_by
    if recipient && recipient.respond_to?(:id) && (triggered_by.nil? || recipient.id != triggered_by.id)
      properties.merge!({
        recipient_user_id: recipient.id,
        recipient_user_email: recipient.respond_to?(:email) ? recipient.email : nil,
        recipient_user_type: recipient.respond_to?(:type) ? recipient.type : nil
      })
    end

    # Use triggered_by user as distinct_id, fallback to recipient
    primary_user = triggered_by || recipient
    distinct_id = primary_user&.id || 'system'

    $posthog.capture(
      distinct_id: distinct_id,
      event: 'email context tracked',
      properties: properties
    )
  rescue => e
    Rails.logger.error "Error tracking email context in PostHog: #{e.message}"
  end
end