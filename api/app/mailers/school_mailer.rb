# app/mailers/school_mailer.rb
class SchoolMailer < ApplicationMailer
  include EmailTracking
  
  default from: '<EMAIL>'

  # teacher#send_welcome_email
  def welcome_email(teacher, new_password, test = false)
    @teacher = teacher
    @new_password = new_password

    if @teacher.wonde_id.present?
      @auth_url = "https://edu.wonde.com/oauth/authorize?#{URI.encode_www_form({
                                                                                client_id: ENV['WONDE_CLIENT_ID'],
                                                                                redirect_uri: ENV['WONDE_REDIRECT_URI'],
                                                                                response_type: 'code',
                                                                                state: 'login',
                                                                              })}"
    end

    recipient = test ? [@teacher.email] : [@teacher.email, '<EMAIL>']
    mail(
      to: recipient,
      subject: 'Welcome to Developing Experts',
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  # user#send_password_reset_email
  def password_reset(user, token)
    @user = user
    @token = token

    mail(
      to: [@user.email],
      subject: 'Password Reset',
    )
  end

  # Send new password notification to teacher
  def password_changed(teacher, new_password)
    @teacher = teacher
    @new_password = new_password

    mail(
      to: [@teacher.email],
      subject: 'Password Changed - Developing Experts',
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  # Send school invite request notification to school admin
  def school_invite_request(requesting_user_name, requesting_user_email, school)
    @requesting_user_name = requesting_user_name
    @requesting_user_email = requesting_user_email
    @school = school
    @admin_teacher = school.main_teacher

    mail(
      to: [@admin_teacher.email],
      subject: "#{@requesting_user_name} wants to join your school on Developing Experts",
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  def exemplar_work_submitted(teacher, exemplar_work)
    @teacher = teacher
    @exemplar_work = exemplar_work

    mail(
      to: [@teacher.email],
      subject: "Your Exemplar Work Has Been Submitted - Developing Experts",
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  def exemplar_work_approved(teacher, exemplar_work)
    @teacher = teacher
    @exemplar_work = exemplar_work

    mail(
      to: [@teacher.email],
      subject: "Your Exemplar Work Has Been Approved - Developing Experts",
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  def lesson_feedback_submitted(teacher, lesson_feedback)
    @teacher = teacher
    @lesson_feedback = lesson_feedback

    mail(
      to: [@teacher.email],
      subject: "Lesson Feedback Received - Thank You from Developing Experts",
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  def lesson_feedback_received_comment(teacher, lesson_feedback, feedback_comment)
    @teacher = teacher
    @lesson_feedback = lesson_feedback
    @feedback_comment = feedback_comment

    mail(
      to: [@teacher.email],
      subject: "New Comment on Your Lesson Feedback - Developing Experts",
      from: 'Developing Experts <<EMAIL>>'
    )
  end

  def homework_submission(homework, submission_stage = "first")
    @homework = homework
    @teacher = @homework.created_by
    @homework_submission = submission_stage == "first" ? @homework.submissions.first : @homework.submissions.last
    @pupil = @homework_submission.user
    @submission_stage = submission_stage

    mail(
      to: [@teacher.email],
      subject: "Homework Submission - Developing Experts",
      from: 'Developing Experts <<EMAIL>>'
    )
  end
end