# app/mailers/user_referral_mailer.rb
class UserReferralMailer < ApplicationMailer
  def invitation_email(user_referral)
    @user_referral = user_referral
    @referrer = user_referral.referrer
    @reward_amount = user_referral.reward_amount
    @accept_url = accept_user_referral_url(token: user_referral.token)
    
    mail(
      to: user_referral.email,
      subject: "#{@referrer.name} has invited you to join Developing Experts"
    )
  end

  def invite_accepted_email(user_referral)
    @referrer = user_referral.referrer
    @referred_user = user_referral.referred_user
    @reward_amount = user_referral.reward_amount
    @referrals_path = user_referrals_url

    return unless @referrer.allows_notification?(:referral_invite_accepted)

    mail(
      to: @referrer.email,
      subject: "#{@referred_user.name} has accepted your invite to join Developing Experts"
    )
  end

  def referral_code_used_email(user_referral)
    @user_referral = user_referral
    @referrer = user_referral.referrer # The person who OWNS the code
    @referred_user = user_referral.referred_user # The person who USED the code
    @referrals_path = user_referrals_url

    return unless @referrer.allows_notification?(:referral_code_used)

    mail(
      to: @referrer.email, # The person who OWNS the code
      subject: "#{@referred_user.name} has used your referral code to join Developing Experts"
    )
  end
end
