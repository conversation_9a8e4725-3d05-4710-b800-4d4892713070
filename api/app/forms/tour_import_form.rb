# app/forms/tour_import_form.rb
class TourImportForm
  include Fileboy
  include ActiveModel::Model

  attr_accessor :tour_name, :organisation_name, :campaign_name, :fileboy_image_id, :data, :fileboy_image, :tour_data

  validates :tour_name, presence: true
  validates :organisation_name, presence: true
  validates :campaign_name, presence: true
  validates :fileboy_image, presence: true
  validates :data, presence: true

  def to_seed_attributes
    {
      tour_name: tour_name,
      org_name: organisation_name,
      campaign_name: campaign_name,
      preview_image_fb_id: fileboy_image_id
    }
  end

  def upload_fileboy_image
    return unless fileboy_image.present?
    self.fileboy_image_id = upload_image(fileboy_image)
    errors.add(:fileboy_image, 'errored uploading') unless fileboy_image_id.present?
  end

  def parse_data
    parsed = JSON.parse(data || '{}')
    unless parsed.is_a?(Hash)
      errors.add(:data, 'must be a JSON object')
      return nil
    end
    self.tour_data = parsed
  rescue JSON::ParserError
    errors.add(:data, 'is not valid JSON')
    nil
  end
end
