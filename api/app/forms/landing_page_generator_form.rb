# app/forms/landing_page_generator_form.rb
class LandingPageGeneratorForm
  include ActiveModel::Model
  include ActiveModel::Attributes
  
  attribute :key_stage, :string
  attribute :subject, :string
  attribute :topic, :string
  attribute :resource_type, :string
  attribute :career_theme, :string
  attribute :curriculum_alignment, :string
  attribute :geographic_focus, :string
  
  validates :key_stage, presence: true, inclusion: { in: LandingPage::KEY_STAGES }
  validates :subject, presence: true, inclusion: { in: LandingPage::SUBJECTS }
  validates :topic, presence: true, length: { minimum: 3 }
  
  def resource_type_options
    LandingPage::RESOURCE_TYPES.map { |type| [type.humanize, type] }
  end
  
  def key_stage_options
    LandingPage::KEY_STAGES.map { |stage| [stage, stage] }
  end
  
  def subject_options
    LandingPage::SUBJECTS.map { |subject| [subject, subject] }
  end
end