<div class="flex flex-wrap text-center gap-x-2 font-semibold text text-lg tracking-tight">
    <%- @tabs&.each do |tab| %>
        <% name = tab[:name] || tab['name'] || tab.try(:name) || 'Untitled' %>
        <% path = tab[:path] || tab['path'] || tab.try(:path) || '#' %>
        <% active = tab[:active] %>
        <div class="relative group w-fit">
            <%= link_to name, path, 
                class: "tab-link inline-block px-4 py-4 transition-opacity duration-300 #{active ? 'opacity-100' : 'opacity-50 hover:opacity-75'}" %>
            <div class="absolute bottom-0 left-0 w-full h-1 bg-yellow-400 rounded-full transition-opacity duration-300 
                <%= active ? 'opacity-100' : 'opacity-0 group-hover:opacity-50' %>">
            </div>
        </div>
    <%- end %>
</div>

<script>
    document.addEventListener("DOMContentLoaded", () => {
        let hasUnsavedChanges = false;

        document.querySelectorAll("input, textarea, select").forEach(el => {
            el.addEventListener("change", () => {
                hasUnsavedChanges = true;
            });
        });

        document.querySelectorAll(".tab-link").forEach((link) => {
            link.addEventListener("click", e => {
                if (hasUnsavedChanges) {
                    const confirmLeave = confirm("You have unsaved changes. Are you sure you want to leave this page?");
                    if (!confirmLeave) {
                        e.preventDefault();
                    }
                }
            });
        });
    });
</script>