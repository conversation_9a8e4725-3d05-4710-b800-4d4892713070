
module ButtonComponent
  class Base < ViewComponent::Base
    include ActionView::Helpers::UrlHelper
    include ActionView::Helpers::TagHelper

    renders_one :left_content

    VARIANTS = {
      primary: "btn-cyan",
      secondary: "btn-flat-white",
      white: "btn-white",
      purple: "btn-purple",
      cyan: "btn-cyan",
      flat_cyan: "btn-flat-cyan",
      flat_red: "btn-flat-red",
      transparent: "btn-transparent",
      white_outline: "btn-white-outline",
    }

    SIZES = {
      sm: "btn-sm",
      md: "btn-base",
      lg: "btn-lg"
    }

    def initialize(text: nil, url: nil, variant: :primary, size: :md, method: nil, data: {}, onclick: nil, left_icon: nil, right_icon: nil, classes: nil, id: nil, target: nil, disabled: false, type: "button")
      @text = text
      @url = url
      @variant = variant
      @size = size
      @method = method
      @data = data
      @onclick = onclick
      @left_icon = left_icon
      @right_icon = right_icon
      @custom_classes = classes
      @id = id
      @target = target
      @type = type
      @disabled = disabled

      raise ArgumentError, "Cannot use both content and text" if content? && text.present?
      raise ArgumentError, "Cannot use both left_icon and with_left_content" if @left_icon && left_content?
    end

    private

    def classes
      ["btn", VARIANTS[@variant], SIZES[@size], @custom_classes].compact.join(" ")
    end
  end
end
