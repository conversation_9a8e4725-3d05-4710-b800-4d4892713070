<%- text = @text ? "Back to: #{@text.truncate(40)}" : "Back"%>
<%= render ButtonComponent::Base.new(
  url: @link,
  variant: :transparent,
  size: :md,
  classes: "-mx-5 mb-2"
) do |button| %>
  <% button.with_left_content do %>
    <div class="bg-de-brand w-6 h-6 rounded-full flex items-center justify-center text-white">
      <i class="fa-solid fa-arrow-left"></i>
    </div>
  <% end %>

  Back  
  <% if @text.present? %>
    to: 
    <span class="font-semibold">
      <span class="hidden md:inline"><%= @text %></span>
      <span class="inline md:hidden"><%= @text.truncate(30) %></span>
    </span>
  <% end %>
<% end %>
