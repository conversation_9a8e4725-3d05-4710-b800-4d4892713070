<% button_content = capture do %>
  <button
    <%= "disabled" if @disabled %>
    id="<%= @id %>"
    class="<%= classes %>"
    <%= "onclick=#{@onclick}" if @onclick %>
    <% @data.each do |key, value| %>
      data-<%= key %>="<%= value %>"
    <% end if @data.present? %>
    type="<%= @type %>"
  >
    <% if left_content? %>
      <%= left_content %>
      <span class="ml-3"><%= @text || content %></span>
    <% elsif @left_icon %>
      <i class="<%= @left_icon %> mr-3"></i>
      <%= @text || content %>
    <% else %>
      <%= @text || content %>
    <% end %>
    <% if @right_icon %>
      <i class="<%= @right_icon %> ml-3"></i>
    <%end%>
  </button>
<% end %>

<% if @url %>
  <%= link_to @url, class: "inline-block", method: @method, data: @data, target: @target do %>
    <%= button_content %>
  <% end %>
<% else %>
  <%= button_content %>
<% end %>
