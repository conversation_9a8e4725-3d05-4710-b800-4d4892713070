<!-- FORM DATA: each select will be returned as questionId_selectName  -->
<div id="free-text-<%= @id %>" data-question-wrapper>
  <%= render partial: 'shared/question_prompt', locals: { prompt: @prompt, fileboyImageId: @fileboyImageId, subtitle: get_free_text_description() } %>
  <textarea id="<%= @id %>" data-input-value name="<%= @id %>" rows="auto" class="w-full" data-submit-disabled></textarea>
  <div id="char-limit" class="font-bold"></div>
</div>
<script>
  (function () {
    const input = document.getElementById("<%= @id %>");
    const question = document.getElementById("free-text-<%= @id %>");
    const minLength = Number("<%= @minLength %>")
    const maxLength = Number("<%= @maxLength %>") ?? null

    function checkLimit(event) {
      const value = event?.target?.value ?? ""
      let isValid = false
      if(!minLength && !maxLength) {
        isValid = true
      }
      if(!isValid) {
        isValid = value.length >= minLength && (maxLength === null || value.length <= maxLength)
      }
      const question = document.getElementById("free-text-<%= @id %>");
      const charLimit = question.querySelector("#char-limit");

      const red = "text-red-500";
      const green = "text-green-500";

      if (isValid) {
        charLimit.classList.add(green);
        charLimit.classList.remove(red);
      } else {
        charLimit.classList.add(red);
        charLimit.classList.remove(green);
      }
      const charCount = value.length;
      let text = charCount
      if(maxLength) {
        text = `${charCount}/${maxLength}`
      }
      charLimit.innerText = text
    }

    input.addEventListener("input", checkLimit);

    checkLimit()
  })();
</script>
