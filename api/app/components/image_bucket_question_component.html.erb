<%= javascript_include_tag 'scripts/dnd-mobile-polyfill.js' %>

<div id="image-bucket-<%= @id %>" data-question-wrapper class="flex-grow flex flex-col nova-headings">
  <%= render partial: 'shared/question_prompt', locals: { prompt: @prompt, fileboyImageId: @fileboyImageId, subtitle: "Drag the images into the correct place." } %>

  <div class="flex flex-col flex-grow">
    <div class="flex flex-col md:flex-row flex-wrap gap-6 flex-grow">
      <% @buckets.each do |bucket| %>
        <div
          id="<%= bucket['name'] %>"
          name="bucket"
          data-bucket-target
          data-option-key="<%= bucket["key"] %>"
          class="flex-1 min-w-[280px] flex flex-col text-center bg-slate-50 rounded-xl border-2 border-dashed border-slate-300 p-4 transition-all duration-300"
        >
          <input type="hidden" data-bucket-input data-bucket="<%= bucket['name'] %>" name="<%= "#{@id}_#{bucket['name']}" %>" />
          <h2 class="text-2xl font-bold text-slate-700 mb-4"><%= bucket["name"]&.titleize %></h2>
          <div class="image-answers-holder gap-4 flex flex-grow flex-wrap justify-center items-center min-h-[150px] bg-white/50 rounded-lg p-2"></div>
        </div>
      <% end %>
    </div>

    <div class="mt-8 gap-4 flex flex-wrap flex-grow justify-center p-4 bg-slate-100 rounded-lg" data-holster>
      <% @options.each do |option| %>
        <div
          id="option_<%= @id %>_<%= option['value'] %>"
          name="option" 
          data-option-id="<%= option['value'] %>"
          data-draggable-option
          class="flex flex-col items-center gap-2 cursor-grab active:cursor-grabbing rounded-lg bg-white p-2 shadow-md transition-all duration-200 w-32 sm:w-40 opacity-0"
          draggable="true"
        >
          <div
            class="w-full h-24 sm:h-32 rounded-md bg-contain bg-center bg-no-repeat pointer-events-none"
            style="background-image: url(<%= fileboy_image_path(option['fileboyId'].presence || '5ae3fca6-77d6-4a47-b721-7cd8981dcc5b') %>)"
          ></div>
          <p class="text-lg font-semibold text-slate-800 text-center select-none leading-tight"><%= option['label'] %></p>
        </div>
      <% end %>
    </div>
  </div>
</div>
<script>
  (function() {
    const questionContainer = document.getElementById("image-bucket-<%= @id %>");
    if (!questionContainer) return;

    // --- Configuration & State ---
    const options = questionContainer.querySelectorAll("[data-draggable-option]");
    const buckets = questionContainer.querySelectorAll("[data-bucket-target]");
    const holster = questionContainer.querySelector("[data-holster]");
    const DRAGGING_CLASS = 'is-dragging';
    const DRAG_OVER_CLASS = 'is-drag-over';

    function isDisabled() {
      return questionContainer.hasAttribute("data-disabled");
    }

    function manageHolsterVisibility() {
      if (!holster) return;
      const itemsInHolster = holster.querySelectorAll("[data-draggable-option]").length;
      holster.classList.toggle('hidden', itemsInHolster === 0);
    }

    function updateBucketValues() {
      buckets.forEach(bucket => {
        const input = bucket.querySelector("[data-bucket-input]");
        const droppedOptions = bucket.querySelectorAll("[data-draggable-option]");
        const value = Array.from(droppedOptions)
          .map(option => option.getAttribute("data-option-id"))
          .filter(Boolean)
          .join(",");
        if (input) {
          input.value = value;
        }
      });
    }

    // --- Draggable Options Logic ---
    options.forEach(option => {
      option.addEventListener("dragstart", (event) => {
        if (isDisabled()) { event.preventDefault(); return; }
        option.classList.add(DRAGGING_CLASS);
        event.dataTransfer.setData("text/plain", event.target.id);
        event.dataTransfer.effectAllowed = 'move';
      });

      option.addEventListener("dragend", (event) => {
        option.classList.remove(DRAGGING_CLASS);
      });
    });

    // --- Drop Zone (Buckets & Holster) Logic ---
    const dropZones = [ ...buckets, holster ];

    dropZones.forEach(zone => {
      zone.addEventListener("dragenter", event => {
        if (isDisabled()) return;
        event.preventDefault();
        if (zone.hasAttribute('data-bucket-target')) {
            zone.classList.add(DRAG_OVER_CLASS);
        }
      });

      zone.addEventListener("dragover", event => {
        if (isDisabled()) return;
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
      });

      zone.addEventListener("dragleave", event => {
        if (isDisabled() || zone.contains(event.relatedTarget)) return;
        if (zone.hasAttribute('data-bucket-target')) {
            zone.classList.remove(DRAG_OVER_CLASS);
        }
      });

      zone.addEventListener("drop", event => {
        if (isDisabled()) return;
        event.preventDefault();
        if (zone.hasAttribute('data-bucket-target')) {
            zone.classList.remove(DRAG_OVER_CLASS);
        }

        const optionId = event.dataTransfer.getData("text/plain");
        const droppedOption = document.getElementById(optionId);

        if (droppedOption) {
          const answersHolder = zone.hasAttribute('data-bucket-target') ? zone.querySelector(".image-answers-holder") : zone;
          if (answersHolder) {
              answersHolder.appendChild(droppedOption);
          }
          updateBucketValues();
          manageHolsterVisibility();
        }
      });
    });

    // --- Dynamic Styles & Animations ---
    const style = document.createElement('style');
    style.innerHTML = `
      @keyframes fade-in-up {
        from { opacity: 0; transform: translateY(15px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .animate-fade-in-up {
        animation: fade-in-up 0.4s ease-out forwards;
      }

      [data-draggable-option].${DRAGGING_CLASS} {
        opacity: 0.5;
        transform: scale(1.05) rotate(3deg);
      }

      [data-bucket-target].${DRAG_OVER_CLASS} {
        border-color: #3b82f6; /* blue-500 */
        background-color: #eff6ff; /* blue-50 */
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
        transform: scale(1.02);
      }
    `;
    document.head.appendChild(style);

    // --- Fun Extra: Staggered Entrance Animation ---
    if(holster) {
      const initialOptions = holster.querySelectorAll("[data-draggable-option]");
      initialOptions.forEach((option, index) => {
        option.classList.add('animate-fade-in-up');
        option.style.animationDelay = `${index * 80}ms`;

        // **THE BUGFIX**: When the animation ends, remove both the animation class
        // AND the initial opacity-0 class to ensure the item remains visible.
        option.addEventListener('animationend', (e) => {
          e.stopPropagation();
          option.classList.remove('animate-fade-in-up', 'opacity-0');
          option.style.animationDelay = ''; // Also clean up the inline style
        }, { once: true });
      });
    }
  })();
</script>