class FullWidthPanelComponent < ViewComponent::Base
  def initialize(image_url: nil, video_id: nil, fileboy_video_id: nil, title:, fileboy_image_id: nil, pre_title: nil, feature_side: "right", background_color: 'bg-secondary-dark-blue', primary_action: nil, secondary_action: nil, media_credit: nil, media_frame: nil, image_scale: nil, alt_text: nil)
    @feature_side = feature_side
    @fileboy_image_id = fileboy_image_id
    @title = title
    @background_color = background_color
    @pre_title = pre_title
    @primary_action = primary_action
    @secondary_action = secondary_action
    @video_id = video_id
    @media_credit = media_credit
    @media_frame = media_frame
    @fileboy_video_id = fileboy_video_id
    @image_scale = image_scale
    @alt_text = alt_text.nil? ? title : alt_text
    @image_url = image_url
  end
end
