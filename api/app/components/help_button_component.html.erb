<div id="view-help-button" class="btn btn-sm btn-mid-blue flex gap-3 min-w-[92px] items-center cursor-pointer whitespace-nowrap !min-w-0">
  <i class="fas fa-question-circle"></i>
  <span>View Help</span>
</div>
<dialog id="help-dialog" class="bg-mid-blue text-white p-4 md:p-8 rounded-xl">
  <div class="flex flex-wrap gap-4 mb-4 max-w-4xl">
    <div class="flex-[1_0_200px] min-w-[200px]">
      <h2 class="text-2xl mb-2"><%= @title %></h2>
      <p><%= @body %></p>
    </div>
    <div class="min-w-[280px] flex-[3_0_280px]">
      <div id="video-container" class="video-player">
        <% if @video.present? && @video.source == "youtube" %>
          <iframe
            data-video-type="youtube"
            height="100%"
            width="100%"
            src="https://www.youtube.com/embed/<%= @video.external_id %>?autoplay=0&enablejsapi=1"
            title="<%= @video.name %>"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
          ></iframe>
        <% elsif @fileboy_video_id %>
          <div data-video-type="fileboy" data-fileboy-video="height=100%" id="<%= @fileboy_video_id %>"></div>
        <% end %>
      </div>
    </div>
  </div>
  <button id="close-help-dialog" class="btn btn-base btn-white">Close</button>
</dialog>

<script type="module">
    const helpButton = document.getElementById('view-help-button');
    const helpDialog = document.getElementById('help-dialog');
    const videoContainer = document.getElementById('video-container');
    const closeButton = document.getElementById('close-help-dialog');

    // Video elements to add/remove/stop
    const youtubePlayer = helpDialog.querySelector('iframe[data-video-type="youtube"]');
    const fileboyPlayer = helpDialog.querySelector('div[data-video-type="fileboy"]');

    // Open dialog and reload video
    helpButton.addEventListener('click', () => {
      if (fileboyPlayer) {
        window.fb.reloadVideoPlayers();
      }
      helpDialog.showModal();
    });

    closeButton.addEventListener('click', () => {
      helpDialog.close();
    });

    // Close dialog and stop video
    helpDialog.addEventListener('close', () => {
      if (youtubePlayer) {
        youtubePlayer.contentWindow.postMessage(
          '{"event":"command","func":"stopVideo","args":""}',
          '*'
        );
      }
      if (fileboyPlayer) {
        const fvPlayerContainer = document.querySelector('.fv-player-container');
        if (fvPlayerContainer) {
            fvPlayerContainer.remove();
        }
        const videoContainer = fileboyPlayer.parentElement;
        if (videoContainer) {
          videoContainer.innerHTML = '';
          videoContainer.appendChild(fileboyPlayer);
        }
      }
    });
</script>