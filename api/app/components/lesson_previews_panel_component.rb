# frozen_string_literal: true

class LessonPreviewsPanelComponent < ViewComponent::Base

  def initialize(curriculum_id: 18, subject: nil)
    @curriculum = NewLibrary::Curriculum.find(curriculum_id)
    @subject = subject
  
    @templates = base_templates
    @years = base_years
  end
  
  private
  
  def base_templates
    scope = @curriculum.lesson_templates.anonymous
    scope = scope.joins(new_library_unit_templates: { unit: :subject })
                 .where(new_library_subjects: { name: @subject })
                 .distinct if @subject.present?
    scope.order(weight: :asc).limit(9)
  end
  
  def base_years
    scope = @curriculum.years.includes(:subject).where.not(id: [84, 82])
    scope = scope.joins(:subject)
                 .where(new_library_subjects: { name: @subject }) if @subject.present?
    scope.order(weight: :asc)
  end

end
