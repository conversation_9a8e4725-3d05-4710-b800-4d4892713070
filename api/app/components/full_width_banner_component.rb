# frozen_string_literal: true

class FullWidthBannerComponent < ViewComponent::Base
  def initialize(title:, subtitle: nil, body: nil, body_2: nil, image: nil, alt_text: nil, background_color: 'bg-secondary-dark-blue', text_color: 'text-white', primary_action: nil, secondary_action: nil, gradient_to_color: nil, max_image_width: 500, image_inline: false)
    @title = title
    @subtitle = subtitle
    @body = body
    @body_2 = body_2
    @image = image
    @alt_text = alt_text.nil? ? title : alt_text
    @background_color = background_color
    @gradient_to_color = gradient_to_color
    @text_color = text_color
    @primary_action = primary_action
    @secondary_action = secondary_action
    @max_image_width = max_image_width
    @image_inline = image_inline == true
  end
end
