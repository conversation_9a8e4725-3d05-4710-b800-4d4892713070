<%# app/components/drawer_component.html.erb %>
<!--
# To trigger a callback on draw open, use this listener
# document.addEventListener('drawer:opened', (event))
-->
<div <%= "data-controller=drawer" unless @external_control %>>
  <%# Only show trigger if not externally controlled %>
  <% unless @external_control %>
    <div data-action="click->drawer#open" class="cursor-pointer">
      <%= trigger %>
    </div>
  <% end %>

  <div
    <%= "id=#{@id}" if @id %>
    <%= "data-drawer-target=panel" unless @external_control %>
    class="fixed inset-y-0 <%= @position == 'right' ? 'right-0 translate-x-full' : 'left-0 -translate-x-full' %> 
           bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 flex flex-col h-full
            w-screen sm:w-[600px]
          "
  >
    <div class="relative flex flex-col h-full">
      <!-- Header -->
      <div class="p-8 border-b flex justify-between items-center">
        <h2 class="text-xl font-bold pr-4 nova"><%= title %></h2>
        
        <!-- Close Button -->
        <div
          class="text-gray-500 hover:text-gray-700 cursor-pointer"
          <%= "data-action=click->drawer#close" unless @external_control %>
          onclick="<%= @external_control ? "document.querySelector('##{@id}').classList.remove('translate-x-0')" : nil %>"
        >
          <i class="fa-solid fa-xmark text-xl"></i>
        </div>
      </div>

      <!-- Content -->
      <div class="flex-1 overflow-y-auto p-8">
        <%= content %>
      </div>

      <% if footer? %>
        <div class="border-t p-8 mt-auto">
          <%= footer %>
        </div>
      <% end %>
    </div>
  </div>
  
  <%# Backdrop overlay when drawer is open %>
  <div 
    <%= "data-drawer-target=backdrop" unless @external_control %>
    class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"
    <%= "data-action=click->drawer#close" unless @external_control %>
  ></div>
</div>
