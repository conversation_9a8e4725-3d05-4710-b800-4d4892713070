<td class="w-1 whitespace-nowrap">
  <div class="flex gap-2">
    <%- @links.each do |link| %>
      <%if link[:path]%>
        <%= link_to link[:text], link[:path], class: "admin-btn-small-white", target: link[:target], onClick: link[:confirm] ? "onClickConfirm(event, '#{link[:confirm]}')" : "" %>
      <%else%>
        <button id="<% link[:id] %>" <%= action_attributes(link) %> class="admin-btn-small-white"><%= link[:text] %></button>
      <%end%>
    <%- end %>
  </div>
</td>

<script>
  function onClickConfirm(event, message) {
    if (!confirm(message)) {
      event.preventDefault();
    }
  }
</script>
