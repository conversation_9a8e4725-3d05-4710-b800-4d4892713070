<div class="relative w-full max-w-lg">
  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
    <svg aria-hidden="true" class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewbox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
    </svg>
  </div>
  <%= search_field_tag :query, params[:query], placeholder: "Search...", class: "bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary-500 focus:border-primary-500 block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500" %>
  <div class="absolute right-1 top-1 flex gap-1">
    <button type="submit" class="admin-btn-small-white">
      Search
    </button>
    <% if params[:query].present? %>
      <%= link_to params: request.query_parameters.except(:query) do %>
        <button type="button" class="admin-btn-small-white">
          Clear
        </button>
      <% end %>
    <% end %>
  </div>
</div>
