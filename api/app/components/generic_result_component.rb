# frozen_string_literal: true

class GenericResultComponent < ViewComponent::Base
  @comp_type = 'Unknown'

  def fileboy_image_path(id)
    return '' if id.blank?
    "https://www.developingexperts.com/file-cdn/images/get/#{id}?transform=resize:300x_;format:webp;quality:75"
  end

  def question_type
    case question['type']
    when 'multi-choice'
      'Multiple choice'
    when 'fill-in-blanks'
      'Fill in the blanks'
    when 'text-bucket'
      'Text bucket'
    when 'image-bucket'
      'Image bucket'
    when 'sort-list'
      'Sort list'
    when 'free-text'
      'Free Text'
    else
      'Unknown'
    end
  end

  def prompt
    question.dig('dataJson', 'prompt')
  end

  def result
    @data['result']
  end

  def question
    @data['question']
  end

  def answer
    result['answer']
  end

  def score
    result['score'].to_i
  end

  def total
    result['total'].to_i
  end

  def feedback
    question.dig('dataJson', 'feedback')
  end

  def initialize(data)
    @data = data
  end
end
