# frozen_string_literal: true
class SortListQuestionComponent < GenericQuestionComponent
  # {
  #   "id": "0b40ee75-29cf-45a1-8efd-7bce8b70c667",
  #   "type": "sort-list",
  #   "weight": 4,
  #   "dataJson": {
  #     "key": 3,
  #     "prompt": "<p>This is a sortable list.</p><p>&nbsp;</p><p>The answer is 1,2,3,4,5</p>",
  #     "options": [
  #       {
  #         "label": "One",
  #         "value": "One"
  #       },
  #       {
  #         "key": "0.7120514125722116",
  #         "label": "Two",
  #         "value": "Two"
  #       },
  #       {
  #         "key": "0.3274747040622089",
  #         "label": "Three",
  #         "value": "Three"
  #       },
  #       {
  #         "key": "0.4812380281010228",
  #         "label": "Four",
  #         "value": "Four"
  #       },
  #       {
  #         "key": "0.42698738079437815",
  #         "label": "Five",
  #         "value": "Five"
  #       }
  #     ],
  #     "feedback": "Custom feedback node",
  #     "validResponse": [
  #       "One",
  #       "Two",
  #       "Three",
  #       "Four",
  #       "Five"
  #     ],
  #     "fileboyImageId": "",
  #     "shuffleOptions": true
  #   }
  # }

  def initialize(qd)
    super(qd)

    @options = @data['options'].shuffle!
    @valid_responses = @data['validResponse']
  end
end
