<!-- FORM DATA: input called questionId will have comma joined array values  -->

<div id="multi-choice-<%= @id %>" data-question-wrapper>
  <%= render partial: 'shared/question_prompt', locals: { prompt: @prompt, fileboyImageId: @fileboyImageId, subtitle: choose_answers_text() } %>
  <input type="hidden" id="multi-choice-<%= @id %>-value" name="<%= @id %>" data-input-value>
  <% @options.map do |option| %>
    <div>
      <input id="<%= "#{option["value"]}-#{@id}" %>" type="checkbox" data-option-value="<%= option["value"] %>" class="hidden peer">
      <label
        for="<%= "#{option["value"]}-#{@id}" %>"
        data-validate="<%= option["value"] %>"
        class="bg-white rounded p-3 border-4 flex gap-4 items-center cursor-pointer text-black"
      >
        <div name="option_icon_container" class="relative border-2 border-gray-200 rounded-full w-8 h-8 flex items-center justify-center aspect-square">
          <div name="option_icon">
          </div>
        </div>
        <p class="text-xl">
          <%= option["label"] %>
        </p>
        <% if fileboy_image_path(option["fileboyId"]).present? %>
            <img src="<%= fileboy_image_path(option["fileboyId"]) %>" class="w-16 ml-auto rounded hidden md:block">
        <% end %>
      </label>
    </div>
  <% end %>
</div>

<script>
    (function () {
        function isDisabled() {
            const container = document.getElementById("multi-choice-<%= @id %>")
            return container.hasAttribute("data-disabled")
        }

        const name = "<%= "multi-choice-#{@id}" %>"
        const maxChoices = Number("<%= @max_choices.presence || @options.length %>")
        const iconClassSelected = "fas fa-check text-de-brand"
        const iconClassUnselected = "fas text-gray-400"

        const id = "multi-choice-<%= @id %>"
        const container = document.getElementById(id)
        const options = container.querySelectorAll(`[data-option-value]`);

        function setValue() {
            const id = "multi-choice-<%= @id %>"
            const container = document.getElementById(id)
            const options = container.querySelectorAll(`[data-option-value]:checked`);
            const value = Array.from(options).map(option => option.getAttribute("data-option-value")).filter(Boolean).join(":|:");
            container.querySelector(`[data-input-value]`).value = value;
        }

        function setOptionIcon(group) {
            const input = group.querySelector('input');
            const iconEl = group.querySelector('[name="option_icon"]')

            if (input.checked) {
                iconEl.innerHTML = `<i class="${iconClassSelected}"></i>`
                iconEl.parentElement.classList.add("border-de-brand")
                iconEl.parentElement.classList.remove("border-gray-200")
            } else {
                iconEl.innerHTML = `<i class="${iconClassUnselected}"></i>`
                iconEl.parentElement.classList.remove("border-de-brand")
                iconEl.parentElement.classList.add("border-gray-200")
            }
        }

        function selectedCount() {
            return container.querySelectorAll(`[data-option-value]:checked`).length
        }

        for (const opt of options) {
            opt.addEventListener("change", event => {
                if (isDisabled()) {
                    event.target.checked = !event.target.checked
                    return
                }
                if (maxChoices == 1) {
                    container.querySelectorAll(`[data-option-value]:checked`).forEach(opt => {
                        if (opt !== event.target) {
                            opt.checked = false
                            setOptionIcon(opt.parentElement)
                        }
                    })
                }
                if (event.target.checked && selectedCount() > maxChoices) {
                    event.target.checked = false
                    return
                }
                setOptionIcon(event.target.parentElement)
                setValue()
            })
        }
        container.querySelectorAll(`[data-option-value]:checked`).forEach(opt => setOptionIcon(opt.parentElement))
    })();
</script>
