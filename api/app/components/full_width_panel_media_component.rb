class FullWidthPanelMediaComponent < ViewComponent::Base
  def initialize(image_url: nil, video_id: nil, fileboy_video_id: nil, fileboy_image_id: nil, media_credit: nil, media_frame: nil, image_scale: 'cover', alt_text: "")
    fileboy_image = image_url ? image_url : fileboy_image_id ? "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:800x600~fit:#{image_scale};format:webp;quality:75" : nil
    @image_url = fileboy_image
    @video_id = video_id
    @media_credit = media_credit
    @media_frame = media_frame
    @fileboy_video_id = fileboy_video_id
    @alt_text = alt_text
  end
end
