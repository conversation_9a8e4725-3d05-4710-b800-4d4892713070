# app/components/drawer_component.rb
class DrawerComponent < ViewComponent::Base
  attr_reader :title, :id, :position

  renders_one :trigger
  renders_one :footer

  def initialize(title:, id: nil, external_control: false, position: "right")
    @title = title
    @id = id
    @external_control = external_control
    @position = position # can be "right" or "left"
  end

  # To trigger a callback on draw open, use this listener
  # document.addEventListener('drawer:opened', (event))

  def before_render
    raise ArgumentError, "A trigger block is required for DrawerComponent" unless trigger? || @external_control
  end
end
