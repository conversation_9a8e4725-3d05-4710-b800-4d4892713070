<div class="<%= @background_color %> pt-16 pb-8 px-4 sm:pb-16 lg:px-6">
  <div class="gap-8 items-center mx-auto max-w-screen-xl xl:gap-16 grid md:grid-cols-2">
    <% if @feature_side == 'right' %>
      <!-- Text on the left, image on the right -->
      <div class="text-white data-observe opacity-0 transition-opacity duration-1000 ease-in-out">
        <div class="shape-gen"></div>
        <%- if @pre_title %>
          <p class="mb-2 uppercase opacity-50"><%= @pre_title %></p>
        <% end %>
        <h3 class="text-3xl sm:text-4xl mb-6"><%= @title %></h3>
        <div class="full-width-panel-component-body prose prose-invert">
          <%= content %>
        </div>
        <div class="block md:hidden pb-4 pt-8 max-w-lg mx-auto">
          <%= render FullWidthPanelMediaComponent.new(
            video_id: @video_id,
            media_credit: @media_credit,
            media_frame: @media_frame,
            fileboy_video_id: @fileboy_video_id,
            fileboy_image_id: @fileboy_image_id,
            image_scale: @image_scale,
            image_url: @image_url
          ) %>
        </div>
        <%- if @primary_action %>
          <div class="flex gap-2 pt-4 justify-center md:justify-start">
            <%= link_to @primary_action[:path] do %>
              <button class="btn btn-lg btn-lime">
                <%= @primary_action[:text] %>
              </button>
            <% end %>
            <%- if @secondary_action %>
              <%= link_to @secondary_action[:path] do %>
                <button class="btn btn-lg btn-<%= @secondary_action[:colour] || "white" %>">
                  <%= @secondary_action[:text] %>
                </button>
              <% end %>
            <%- end %>
          </div>
        <% end %>
      </div>
      <div class="hidden md:block">
        <%= render FullWidthPanelMediaComponent.new(
          video_id: @video_id,
          media_credit: @media_credit,
          media_frame: @media_frame,
          fileboy_video_id: @fileboy_video_id,
          fileboy_image_id: @fileboy_image_id,
          image_scale: @image_scale,
          alt_text: @alt_text,
          image_url: @image_url
        ) %>
      </div>
    <% else %>
      <!-- Image on the left, text on the right -->
      <div class="hidden md:block">
        <%= render FullWidthPanelMediaComponent.new(
          video_id: @video_id,
          media_credit: @media_credit,
          media_frame: @media_frame,
          fileboy_video_id: @fileboy_video_id,
          fileboy_image_id: @fileboy_image_id,
          alt_text: @alt_text,
          image_url: @image_url
        ) %>
      </div>
      <div class="text-white data-observe opacity-0 transition-opacity duration-1000 ease-in-out">
        <div class="shape-gen"></div>
        <%- if @pre_title %>
          <p class="mb-2 uppercase opacity-50"><%= @pre_title %></p>
        <% end %>
        <h3 class="text-4xl mb-6"><%= @title %></h3>
        <div class="full-width-panel-component-body prose prose-invert">
          <%= content %>
        </div>
        <div class="block md:hidden pb-4 pt-8 max-w-lg mx-auto">
          <%= render FullWidthPanelMediaComponent.new(
            video_id: @video_id,
            media_credit: @media_credit,
            media_frame: @media_frame,
            fileboy_video_id: @fileboy_video_id,
            fileboy_image_id: @fileboy_image_id,
            alt_text: @alt_text,
            image_url: @image_url
          ) %>
        </div>
        <%- if @primary_action %>
          <div class="flex gap-2 pt-4 justify-center md:justify-start">
            <%= link_to @primary_action[:path] do %>
              <button class="btn btn-lg btn-lime">
                <%= @primary_action[:text] %>
              </button>
            <% end %>
            <%- if @secondary_action %>
              <%= link_to @secondary_action[:path] do %>
                <button class="btn btn-lg btn-<%= @secondary_action[:colour] || "white" %>">
                  <%= @secondary_action[:text] %>
                </button>
              <% end %>
            <%- end %>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>