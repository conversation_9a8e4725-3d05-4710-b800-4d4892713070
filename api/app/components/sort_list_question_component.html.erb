<!-- FORM DATA: input called questionId will have comma joined array values, ordered  -->

<%= javascript_include_tag 'scripts/dnd-mobile-polyfill.js' %>
<div id="sort-list-<%= @id %>" data-question-wrapper>
  <%= render partial: 'shared/question_prompt', locals: { prompt: @prompt, fileboyImageId: @fileboyImageId, subtitle: "Sort the options into the correct order." } %>

  <input type="hidden" id="sort-list-<%= @id %>-value" data-input-value />
  <div id="sort-list-<%= @id %>-container" class="space-y-2">
    <% @options.map.with_index do |option, index| %>
      <div
        id="<%= "#{option["value"]}-#{@id}" %>"
        data-option-value="<%= option["value"] %>"
        name="option"
        draggable="true"
        class="bg-white rounded p-3 flex gap-4 items-center cursor-pointer border-4 text-black"
        data-validate
      >
        <i class="fas fa-bars"></i>
        <p class="text-xl">
          <%= option["label"] %>
        </p>
        <% if fileboy_image_path(option["fileboyId"]).present? %>
          <img src="<%= fileboy_image_path(option["fileboyId"]) %>" class="w-16 ml-auto rounded hidden md:block">
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<script>
  (function() {
    function isDisabled() {
      const container = document.getElementById("sort-list-<%= @id %>")
      return container.hasAttribute("data-disabled")
    }
    const internalDNDType = 'text/x-draggable-id';


    function getContainer() {
      const container = document.getElementById("sort-list-<%= @id %>-container");
      return container
    }
    function getInput() {
      const input = document.getElementById("sort-list-<%= @id %>").querySelector("[data-input-value]");
      return input
    }
    const input = getInput();
    const container = getContainer();


    const options = container.querySelectorAll("[name='option']");

    function setInputValue() {
      const options = document.getElementById("sort-list-<%= @id %>").querySelectorAll("[name='option']");
      const value = Array.from(options).map(option => option.getAttribute("data-option-value")).filter(Boolean).join(",");
      getInput().value = value;
    }

    function triggerContainerDragging() {}
    function triggerContainerDraggingEnd() {}

    options.forEach(option => {
      option.addEventListener("dragstart", (event) => {
        if(isDisabled()) {
          event.preventDefault()
          return
        }
        triggerContainerDragging()
        event.target.setAttribute("data-is-dragging", "true")

        option.classList.add("opacity-50");
        // use the element's data-value="" attribute as the value to be moving:
        event.dataTransfer.setData(internalDNDType, event.target.id);
        event.dataTransfer.effectAllowed = 'move';
      });

      option.addEventListener("dragend", (event) => {
        if(isDisabled()) {
          event.preventDefault()
          return
        }
        triggerContainerDraggingEnd()
        event.target.removeAttribute("data-is-dragging", "true")
        option.classList.remove("opacity-50");
      });
    });

    container.addEventListener("dragover", (event) => {
      if(isDisabled()) {
        event.preventDefault()
        return
      }
      event.preventDefault()

      const optionId = event.dataTransfer.getData(internalDNDType)
      const container = getContainer();
      const draggedEl = container.querySelector("[data-is-dragging]");
      let clone = container.querySelector("[data-drag-clone]");
      if (!clone) {
        clone = draggedEl.cloneNode(true);
        clone.setAttribute("data-drag-clone", "true");
        clone.classList.add("opacity-50");
      }
      clone.removeAttribute("id")


      const options = Array.from(container.querySelectorAll("[name='option']")).filter(x => x.id !== optionId).filter(y => !y.getAttribute("data-drag-clone"))
      let insertBefore = null;

      for (const option of options) {
        const rect = option.getBoundingClientRect();
        const optionTop = rect.top;
        const optionBottom = rect.bottom;
        const halfHeight = (optionBottom - optionTop) / 2;
        if (event.clientY < optionTop + halfHeight) {
          insertBefore = option;
          break;
        }
      }

      if (insertBefore) {
        container.insertBefore(clone, insertBefore);
      } else {
        container.appendChild(clone);
      }
    });

    container.addEventListener("dragend", (event) => {
      if(isDisabled()) {
        event.preventDefault()
        return
      }
      const container = getContainer();
      const clone = container.querySelector("[data-drag-clone]");
      if (clone) {
        clone.remove();
      }
    });

    container.addEventListener("dragover", (event) => {
      if(isDisabled()) {
        event.preventDefault()
        return
      }
      event.dataTransfer.dropEffect = 'move';
    });

    container.addEventListener("drop", (event) => {
      if(isDisabled()) {
        event.preventDefault()
        return
      }
      event.preventDefault()
      const container = getContainer();
      const optionId = event.dataTransfer.getData(internalDNDType)

      // get options except the moving element
      const options = Array.from(container.querySelectorAll("[name='option']")).filter(x => x.id !== optionId).filter(y => !y.getAttribute("data-drag-clone"))
      const optNode = document.getElementById(optionId)

      let insertBefore = null;

      for (const option of options) {
        const rect = option.getBoundingClientRect();
        const optionTop = rect.top;
        const optionBottom = rect.bottom;
        const halfHeight = (optionBottom - optionTop) / 2;
        if (event.clientY < optionTop + halfHeight) {
          insertBefore = option;
          break;
        }
      }

      if (insertBefore) {
        // there are no options above the placement
        console.log("inserting at top")
        container.insertBefore(optNode, insertBefore);
      } else {
        // there are no options below the placement (this is done to prevent it picking up on the clone)
        console.log("inserting at bottom")
        container.appendChild(optNode);
      }
      optNode.style.display = 'flex';
      const clone = container.querySelector("[data-drag-clone]");
      if (clone) {
        clone.remove();
      }
      setInputValue()
    });
    setInputValue()
  })();
</script>
