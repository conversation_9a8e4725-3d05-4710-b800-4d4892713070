<style>
    .letter {
        font-family: "aktiv-grotesk-extended", sans-serif;
        height: 30px;
        width: 30px;
        display: flex;
        align-items:center;
        justify-content: center;
        border-radius: 3px;
        transition: 0.3s;
        color: white;
    }

    .letter:hover, .selectedLetter {
        background-color: rgb(0, 184, 178);
    }
</style>

<div class="grid grid-cols-[repeat(auto-fit,_minmax(30px,_1fr))] mb-4">
    <%- @alphabet.each do |e| -%>
        <%= link_to "#{e}", "/glossary/letter/#{e}", class: "letter #{e == @letter ? "selectedLetter" : ""}" %>
    <%- end -%>
</div>
