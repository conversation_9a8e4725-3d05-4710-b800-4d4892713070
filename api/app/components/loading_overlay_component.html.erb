<style>
    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    .overlay-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 36px;
        text-align: center;
    }
</style>

<div id="<%= @id %>" class="overlay">
  <div class="overlay-content bg-black/80 p-8 rounded-lg">
    <div>
      <i class="fa-solid fa-circle-notch animate-spin"></i>
    </div>
    <div>
      <h3><%= @text %></h3>
      <p id="<%= @id %>-text" class="text-lg"></p>
    </div>
  </div>
</div>