<div class="relative mb-6 text-black" data-glossary-words-value=''>
  <div class="relative">
    <input class="rounded-md text-lg w-full" type="text" placeholder="Search for a word..." id="glossary-search-input">
    <i class="fas fa-search absolute right-4 top-4 text-de-brand"></i>
  </div>
  <div class="bg-white max-h-48 overflow-y-auto font-semibold rounded-md absolute top-14 w-full z-[999]" id="glossary-search-results">
    <!-- Search results will appear here -->
  </div>
</div>
<script>
    const glossaryElement = document.querySelector('[data-glossary-words-value]');
    if (glossaryElement) {

        // Place your JavaScript code here that you want to initialize
        // after the HTMX swap has occurred. This is where you would
        // initialize event listeners and other necessary setups.
        // For example:
        const inputElement = document.getElementById('glossary-search-input');
        const resultsElement = document.getElementById("glossary-search-results");

        let index = 0;
        let hideTimeout;

        async function waitForGlossaryData() {
            return new Promise((resolve) => {
                const observer = new MutationObserver((mutationsList, observer) => {
                    for (const mutation of mutationsList) {
                        if (mutation.attributeName === 'data-glossary-words-value' && glossaryElement.getAttribute('data-glossary-words-value') !== '') {
                            observer.disconnect();
                            resolve();
                        }
                    }
                });
                observer.observe(glossaryElement, {attributes: true});
            });
        }

        async function search() {
            let query = inputElement.value.toLowerCase();

            if (query.length < 2) {
                resultsElement.innerHTML = "";
                return;
            }

            if (glossaryElement.getAttribute('data-glossary-words-value') === '') {
                resultsElement.innerHTML = `
                    <div class="block p-2 flex gap-4">
                        <div class="w-8 h-8 flex-none">
                            <i class="p-2 fa-solid fa-circle-notch animate-spin"></i>
                        </div>
                        <div class="flex-1 leading-8 text-lg">
                            Loading results...
                        </div>
                    </div>
                `
                await waitForGlossaryData();
            }

            let wordsData;
            try {
                wordsData = JSON.parse(glossaryElement.dataset.glossaryWordsValue);
            } catch (e) {
                console.error('Error parsing JSON:', e);
                return;
            }
            let filteredWords = wordsData.filter(word => word.name.toLowerCase().includes(query));

            if (filteredWords.length > 0) {
                resultsElement.innerHTML = filteredWords.map(word => `
                    <a href="/glossary/${word.slug}" class="block p-2 hover:bg-gray-100 flex gap-4">
                        <div class="w-8 h-8 flex-none bg-cyan-600 rounded">
                            ${word.fileboy_image_id
                    ? `<img src="https://www.developingexperts.com/file-cdn/images/get/${word.fileboy_image_id}?transform=resize:150x150~fit:cover" alt="${word.fileboy_image_id}" class="w-8 h-8 inline-block mr-2"/>`
                    : `<div class="text-center text-white leading-8">${word.name[0].toUpperCase()}</div>`}
                        </div>
                        <div class="flex-1 leading-8 text-lg">
                            ${highlightWord(word.name, query)}
                        </div>
                    </a>
                `).join("");
            } else {
                resultsElement.innerHTML = `
                    <div class="block p-2 flex gap-4">
                        <div class="w-8 h-8 flex-none">
                            <i class="p-2 fa-solid fa-image-slash"></i>
                        </div>
                        <div class="flex-1 leading-8 text-lg">
                            No results found
                        </div>
                    </div>
                `
            }

            index = -1;
        }

        function highlightWord(word, query) {
            let startIndex = word.toLowerCase().indexOf(query);
            let endIndex = startIndex + query.length;
            return `${word.substring(0, startIndex)}<span class="bg-yellow-300">${word.substring(startIndex, endIndex)}</span>${word.substring(endIndex)}`;
        }

        function navigate(event) {
            console.log(event.key);
            let items = resultsElement.children;

            switch (event.key) {
                case "ArrowDown":
                    index = Math.min(index + 1, items.length - 1);
                    scrollToVisible(items[index]);
                    break;
                case "ArrowUp":
                    index = Math.max(index - 1, 0);
                    scrollToVisible(items[index]);
                    break;
                case "Enter":
                    items[index]?.click();
                    return;
                case "Escape":
                    inputElement.value = "";
                    resultsElement.innerHTML = "";
                    return;
            }

            Array.from(items).forEach((item, i) => {
                item.classList.toggle("bg-gray-100", i === index);
            });
        }

        function scrollToVisible(item) {
            if (item.offsetTop < resultsElement.scrollTop) {
                resultsElement.scrollTop = item.offsetTop;
            } else if (item.offsetTop + item.offsetHeight > resultsElement.scrollTop + resultsElement.offsetHeight) {
                resultsElement.scrollTop = item.offsetTop + item.offsetHeight - resultsElement.offsetHeight;
            }
        }

        function showResults() {
            clearTimeout(hideTimeout);
            resultsElement.classList.remove('hidden');
        }

        function hideResults() {
            hideTimeout = setTimeout(() => {
                resultsElement.classList.add('hidden');
                inputElement.value = "";
                resultsElement.innerHTML = "";
            }, 150);
        }

        function loadGlossary() {
            fetch(`/glossary/all`, {
                method: 'GET'
            })
                .then(response => response.json())
                .then(data => {
                    glossaryElement.dataset.glossaryWordsValue = JSON.stringify(data);
                })
                .catch(error => {
                    console.error('Error: ', error);
                });
        }

        if (!glossaryElement.dataset.glossaryWordsValue) {
            inputElement.addEventListener('focus', loadGlossary);
        }
        inputElement.addEventListener('input', search);
        document.body.addEventListener('keydown', navigate);

        // Bind showResults and hideResults to appropriate events
        inputElement.addEventListener('focus', showResults);
        inputElement.addEventListener('blur', hideResults);
    }
</script>
