<% if @years.present? && @templates.present? %>
<div class="px-4 mb-16">
  <div class="px-4 py-8 sm:px-8 border-2 border-white rounded-2xl container mx-auto">
    <div class="flex flex-wrap mb-8 gap-4">
      <% if @subject.nil? %>
        <div class="flex-1 min-w-[200px]">
          <h3 class="text-2xl text-white mb-2">Take a look at our science lessons</h3>
          <p class="text-xl text-white mb-1 pl-2">What to expect from our science curriculum?</p>
          <ul class="text-white list-disc pl-8">
            <li>Sequenced and mapped against the National Curriculum and AQA, with a focus on prior learning</li>
            <li>Lessons plans, handouts, unit tests, unit knowledge organisers, before and after tests and presentations
            </li>
            <li>Fully resourced, editable lessons and assessment tools</li>
            <li>A focus on ‘working scientifically skills’ through investigations and practical activities</li>
            <li>Quizzes to assess understanding for effective retrieval practice</li>
            <li>Key scientific vocabulary, with words from tiers 1 - 3</li>
            <li>Activities to encourage higher order thinking and link scientific concepts to the real world</li>
            <li>Support for science leaders through progression documents, curriculum maps, an OFSTED pack and free CPD
              sessions
            </li>
            <li>Links to the world of STEM and careers, with personalised career guidance</li>
          </ul>
        </div>
      <% else %>
        <div class="flex-1 min-w-[200px]">
          <h3 class="text-2xl text-white mb-2">Take a look at our <%= @subject.downcase %> lessons</h3>
        </div>
      <% end %>
      <div class="flex-0 text-center min-w-max">
        <%= link_to "Start your free trial", "/accounts/new", class: "py-4 px-6 bg-white rounded-full text-slate-700 block w-60 font-bold text-size-lg mx-auto hover:bg-cyan-100 duration-300" %>
      </div>
    </div>
    <div data-controller="tabs">
      <div class="flex mb-8">
        <div class="active-tab-heading active tab-heading" data-tab="years">Years</div>
        <div class="tab-heading" data-tab="lessons">Lessons</div>
      </div>
      <div data-tab="years" class="tab-content active grid sm:grid-cols-fluid-18 gap-4">
        <%- @years.each do |year| %>
          <%= render CardComponent.new(
            title: year.name,
            image_background_fileboy_id: year.fileboy_image_id,
            path: "/unit-library/curriculum/#{year.curriculum_id}/years/#{year.id}")
          %>
        <% end %>
      </div>
      <div data-tab="lessons" class="tab-content grid sm:grid-cols-fluid-18 gap-4">
        <%- @templates.each do |template| %>
          <%= render CardComponent.new(
            title: template.name,
            image_background_fileboy_id: template.fileboy_image_id,
            path: "/missions/#{template.id}")
          %>
        <% end %>
      </div>
    </div>
  </div>
</div>
<% end %>