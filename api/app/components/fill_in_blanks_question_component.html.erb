<!-- Add this CSS to your application.css or in a <style> tag -->
<style>
  .fill-blanks-select {
    display: inline!important;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    color: white;
    font-size: inherit;
    line-height: 1.2;
    height: auto;
    padding: 2px 6px;
    margin: 0 2px;
    vertical-align: baseline;
    min-width: 80px;
    max-width: 150px;
  }
  
  .fill-blanks-select:focus {
    outline: 2px solid #3b82f6;
    background: rgba(255, 255, 255, 0.15);
  }
  
  .fill-blanks-select option {
    background: #1f2937;
    color: white;
  }
  
  .fill-blanks-container {
    line-height: 1.8;
    word-spacing: normal;
  }
  
  .fill-blanks-container > * {
    vertical-align: baseline;
  }
</style>

<!-- FORM DATA: each select will be returned as questionId_selectName -->
<div id="fill-blanks-<%= @id %>" 
     data-question-wrapper 
     data-id="<%= @id %>" 
     data-template="<%= @template %>" 
     data-opts-data="<%= @options.to_json %>">
  
  <%= render partial: 'shared/question_prompt', 
             locals: { 
               prompt: @prompt, 
               fileboyImageId: @fileboyImageId, 
               subtitle: "Select the missing words in the text." 
             } %>
  
  <div id="template-container-<%= @id %>" 
       class="text-xl text-white fill-blanks-container"></div>
</div>

<script>
  (function () {
    // Get the wrapper element
    const wrapper = document.getElementById("fill-blanks-<%= @id %>");
    const id = wrapper.dataset.id;
    const template = wrapper.dataset.template;
    const options = JSON.parse(wrapper.dataset.optsData);
    const container = wrapper.querySelector(`#template-container-${id}`);
    
    // Initialize
    init();
    
    function init() {
      clearContainer();
      renderTemplate();
      attachEventListeners();
    }
    
    function clearContainer() {
      container.innerHTML = "";
    }
    
    function renderTemplate() {
      const fieldRegex = /{{(.*?)}}/g;
      const fields = [...template.matchAll(fieldRegex)];
      
      let currentPosition = 0;
      
      fields.forEach((match, index) => {
        const fieldStart = match.index;
        const fieldEnd = fieldStart + match[0].length;
        const fieldName = match[1];
        
        // Add text before the field
        if (fieldStart > currentPosition) {
          const textBefore = template.slice(currentPosition, fieldStart);
          appendTextSpan(textBefore);
        }
        
        // Add the select element
        appendSelectElement(fieldName);
        
        currentPosition = fieldEnd;
        
        // Add remaining text after the last field
        if (index === fields.length - 1 && currentPosition < template.length) {
          const textAfter = template.slice(currentPosition);
          appendTextSpan(textAfter);
        }
      });
    }
    
    function appendTextSpan(text) {
      if (!text) return;
      
      const span = document.createElement("span");
      span.textContent = text;
      container.appendChild(span);
    }
    
    function appendSelectElement(fieldName) {
      const select = document.createElement("select");
      select.className = "fill-blanks-select";
      select.setAttribute("data-field-name", fieldName);
      select.setAttribute("name", `<%= @id %>_${fieldName}`);
      
      // Add placeholder option
      const placeholderOption = createOption("", "Choose...", true, true);
      select.appendChild(placeholderOption);
      
      // Add field options
      const fieldOptions = options[fieldName] || [];
      fieldOptions.forEach(option => {
        const optionElement = createOption(option.value, option.label);
        select.appendChild(optionElement);
      });
      
      container.appendChild(select);
    }
    
    function createOption(value, label, disabled = false, selected = false) {
      const option = document.createElement("option");
      option.value = value;
      option.textContent = label;
      option.disabled = disabled;
      option.selected = selected;
      if (disabled) {
        option.style.display = "none";
      }
      return option;
    }
    
    function attachEventListeners() {
      const selects = container.querySelectorAll("select");
      selects.forEach(select => {
        select.addEventListener("change", () => {
          validateForm();
        });
      });
    }
    
    function getSelectedAnswers() {
      const selects = container.querySelectorAll("select");
      const answers = {};
      
      selects.forEach(select => {
        const fieldName = select.getAttribute("data-field-name");
        answers[fieldName] = select.value;
      });
      
      return answers;
    }
    
    function validateForm() {
      const answers = getSelectedAnswers();
      const allFieldsFilled = Object.values(answers).every(value => value);
      
      // Enable/disable submit button based on completion
      const submitButton = document.querySelector(`[data-question-id="<%= @id %>"] button[type="submit"]`);
      if (submitButton) {
        submitButton.disabled = !allFieldsFilled;
      }
      
      return allFieldsFilled;
    }
    
    function reset() {
      const selects = container.querySelectorAll("select");
      selects.forEach(select => {
        select.selectedIndex = 0;
      });
    }
    
    // Expose reset function globally if needed
    window[`resetFillBlanks${id}`] = reset;
  })();
</script>
