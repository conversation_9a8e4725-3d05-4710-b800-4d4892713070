class TeacherStandardTitleAreaComponent < ViewComponent::Base
  def initialize(title:, count: nil, subtitle: nil, tour: nil, help_button: nil, icon: "fad fa-chalkboard-teacher", layout:, initials: nil)
    if layout != :float && layout != :joined
      raise ArgumentError, "Invalid layout: #{layout}. Must be :float or :joined."
    end

    @title = title
    @count = count
    @subtitle = subtitle
    @tour = tour
    @help_button = help_button
    @icon = icon
    @layout = layout
    @initials = initials
  end
end