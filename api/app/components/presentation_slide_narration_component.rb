# frozen_string_literal: true

class PresentationSlideNarrationComponent < ViewComponent::Base
  def initialize(presentation:, slide: nil, intro_slide: false, outro_slide: false, auto_play: false)
    super
    @text = slide_narration(presentation, slide, intro_slide, outro_slide)
    @auto_play = auto_play
  end

  private

  def slide_narration(presentation, slide, intro_slide, outro_slide)
    text_elements = []

    # IF YOU NEED DATA FROM PRESENTATION - ADD IT ON THE FRONT END AS IT ONLY SENDS THE REQUIRED VALUES TO REDUCE DATA SIZE

    # handle supplied type of intro separately so that we can narrate the title and objectives
    # instead without expecting a slide to be present in data.
    if !slide || intro_slide || outro_slide
      text_elements += build_intro_slide_text(presentation) if intro_slide
      if outro_slide
        text_elements << 'End of presentation'
        text_elements << presentation[:title]
        text_elements << 'Please leave any feedback below (optional):'
        text_elements << 'Your feedback will help us improve our presentations.'
      end
    else
      text_elements << "#{slide[:top_text]}." if slide[:top_text].present?
      text_elements += build_intro_slide_text(presentation) if slide.dig(:content, :type) == 'intro'
      text_elements << "#{slide[:bottom_text]}." if slide[:bottom_text].present?
    end

    # make sure we actually have some text so we don't waste an API call on nothing
    text_elements.reject! { |text| text == '' || !text.present? }
    if text_elements.empty?
      print "SLIDE HAS NO TEXT TO NARRATE #{slide&.dig(:content, :type)}\n"
      return nil
    end

    text_elements.join("\n")
  end

  def build_intro_slide_text(presentation) # rubocop:disable Metrics/CyclomaticComplexity
    text_elements = []
    text_elements << "#{presentation[:title]}."
    text_elements << 'Mission Objectives:'
    text_elements << "#{presentation[:objectives].join(', ')}."
    text_elements << 'Keywords:'
    keywords = presentation[:slides]&.detect { |s| s.dig(:content, :keywords)&.any? }&.dig(:content, :keywords) || []
    text_elements << keywords&.pluck(:name)&.join(', ')
    text_elements
  end
end
