<% aria_label = raw %(aria-label="#{ERB::Util.html_escape(@alt_text)}") %>
<div class="relative">
  <div class="text-left gap-4 <%= @background_color.present? ? @background_color : "from-white to-slate-100 bg-gradient-to-r" %> border border-gray-200 rounded-xl shadow p-5 flex relative">
    <div class="min-w-[140px] h-[140px]">
      <%- if @path %>
        <a href="<%= @path %>" style="background-image: url(<%= @background %>)" class="block rounded-lg h-full w-full overflow-hidden bg-cover bg-center <%= @background_color %>" <%= aria_label unless @image.present? %>>
          <%- if @image %>
            <img class="rounded-t-lg object-contain h-full w-full" src="<%= @image %>" alt="<%= @alt_text %>"/>
          <%- end %>
        </a>
      <%- else %>
        <div style="background-image: url(<%= @background %>)" class="block rounded-lg overflow-hidden bg-cover bg-center w-full h-full <%= @background_color %>" <%= aria_label unless @image.present? %>>
          <%- if @image %>
            <img class="rounded-t-lg object-contain h-full w-full" src="<%= @image %>" alt="<%= @alt_text %>"/>
          <%- end %>
        </div>
      <% end %>
    </div>
    <div class="flex-1 flex flex-col justify-between">
      <div class="w-full">
        <%- if @tags %>
          <div class="mb-1">
            <%- @tags.each do |tag| %>
              <span class="inline-block px-2 py-1 text-xs font-semibold bg-cyan-100 text-cyan-600 rounded mr-1"><%= tag %></span>
            <% end %>
          </div>
        <% end %>
        <%- if @path %>
          <a href="<%= @path %>" class="block">
            <p class="mb-4 text-2xl font-bold tracking-tight <%= @color %>"><%= @title %></p>
          </a>
        <%- else %>
          <p class="mb-4 text-2xl font-bold tracking-tight <%= @color %>"><%= @title %></p>
        <% end %>
        <p class="mb-3 font-normal <%= @color %>"><%= @body %></p>
      </div>
      <%- if @path %>
        <div class="text-right">
          <a href="<%= @path %>" class="btn btn-text-base inline-flex items-center mt-auto ml-auto">
            <%= @path_text %>
            <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
              <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
            </svg>
          </a>
        </div>
      <% end %>
    </div>
  </div>
  <%= content %>
</div>