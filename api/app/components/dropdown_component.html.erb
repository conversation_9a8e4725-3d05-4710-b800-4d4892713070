<div class="relative" data-is-drop-down>
  <button
    type="button"
    class="cursor-pointer"
    aria-expanded="false">
    <i class="fa-solid fa-ellipsis font-bold text-xl text-black"></i>
  </button>
  <div class="hidden z-10 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 focus:outline-none">
    <% @dropdown_items.each do |item| %>
      <% if item[:hidden].present? && item[:hidden] == true %>
      <%else%>
        <% if item[:url].present? %>
          <%= link_to item[:label], item[:url], method: item[:method].present? ? item[:method] : :get, data: item[:options].present? ? item[:options] : {}, class: "block px-4 py-2 text-gray-700 min-w-[140px] hover:bg-slate-100" %>
        <% elsif item[:onclick].present? %>
          <%= button_tag item[:label], onclick: item[:onclick], type: "button", class: "text-left block px-4 py-2 text-gray-700 min-w-[140px] hover:bg-slate-100" %>
        <% end %>
      <%end%>
    <% end %>
  </div>
</div>
