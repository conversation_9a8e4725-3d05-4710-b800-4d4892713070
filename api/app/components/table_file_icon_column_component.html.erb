<td id="icon-<%= @fileboy_id %>" class="admin-table-thumbnail-column w-12 !p-1"></td>

<script>
  document.addEventListener('DOMContentLoaded', function () {
      var fileboyId = '<%= @fileboy_id %>';
      var iconDiv = document.getElementById(`icon-${fileboyId}`);
      var icon = document.createElement('img');
      const assetBasePath = '/assets/svg/';

      const filetypes = {
        'pdf': 'pdf',
        'vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
        'vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx'
      }

      fetch('<%= @file_url %>')
          .then(response => response.json())
          .then(data => {
              const mimeType = data.data.mime.split('/')[1];
              filetype = filetypes[mimeType]

              icon.src = filetype ? `/assets/svg/${filetype}.svg` : `/assets/svg/bin.svg`;
              iconDiv.appendChild(icon);
          })
          .catch(error => {
              console.error(error);
          })
  });
</script>