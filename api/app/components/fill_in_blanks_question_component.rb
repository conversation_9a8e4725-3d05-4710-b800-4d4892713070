# frozen_string_literal: true

class FillInBlanksQuestionComponent < GenericQuestionComponent
  # {
  #   "id": "65e43242-f831-4608-a0cc-fed19c0062ba",
  #   "type": "fill-in-blanks",
  #   "weight": 3,
  #   "dataJson": {
  #     "key": 2,
  #     "prompt": "<p>Fill in the blanks!</p>",
  #     "options": {
  #       "count": [
  #         {
  #           "label": "1",
  #           "value": "1"
  #         },
  #         {
  #           "label": "3",
  #           "value": "3"
  #         },
  #         {
  #           "label": "5",
  #           "value": "5"
  #         }
  #       ],
  #       "water": [
  #         {
  #           "label": "wet",
  #           "value": "wet"
  #         },
  #         {
  #           "label": "dry",
  #           "value": "dry"
  #         }
  #       ],
  #       "sky_color": [
  #         {
  #           "label": "blue",
  #           "value": "blue"
  #         },
  #         {
  #           "label": "green",
  #           "value": "green"
  #         }
  #       ]
  #     },
  #     "feedback": "Custom feedback",
  #     "template": "Water is {{water}}, the sky is {{sky_color}}, a person gets {{count}} years older each year.",
  #     "validResponse": {
  #       "count": "1",
  #       "water": "wet",
  #       "sky_color": "blue"
  #     },
  #     "fileboyImageId": ""
  #   }
  # }

  def initialize(qd)
    super(qd)
    @template = @data['template']
    @options = @data['options'].transform_values(&:shuffle)

    @valid_options = @data['validOptions']
    @valid_response = @data['validResponse']

    @max_choices = 1
  end
end
