<style>
    .audio-container {
        position: relative;
        display: inline-block;
    }

    .play-button {
        height: 54px;
        font-size: 1.5rem;
        border: none;
        cursor: pointer;
    }

    .audio-player {
        display: none;
    }

    .expanded {
        display: block;
    }
</style>

<% if @url || @add_src_later %>
    <div class="<%= @collapsed ? 'audio-container' : '' %>">
        <% if @collapsed %>
            <button class="play-button" type="button" onclick="toggleAudioControls(this)">
                <i class="fa-solid fa-play"></i>
            </button>
            <audio class="audio-player" controls preload="auto">
                <source src="<%= @url %>" type="audio/mp3">
                Your browser does not support the audio element.
            </audio>
        <% else %>
            <audio controls preload="auto">
                <source src="<%= @url %>" type="audio/mp3">
                Your browser does not support the audio element.
            </audio>
        <% end %>
    </div>
<% end %>

<script>
    function toggleAudioControls(clickedButton) {
        var allAudioContainers = document.querySelectorAll('.audio-container');

        allAudioContainers.forEach(function(audioContainer) {
            var playButton = audioContainer.querySelector('.play-button');
            var audioPlayer = audioContainer.querySelector('.audio-player');

            if (audioPlayer !== clickedButton.nextElementSibling) {
            audioPlayer.classList.remove('expanded');
            playButton.style.display = 'inline-block';
            }
        });

        var clickedAudioPlayer = clickedButton.nextElementSibling;
        clickedAudioPlayer.classList.toggle('expanded');
        clickedButton.style.display = clickedAudioPlayer.classList.contains('expanded') ? 'none' : 'inline-block';

        if (clickedAudioPlayer.classList.contains('expanded')) {
            allAudioContainers.forEach(function(audioContainer) {
                var otherAudioPlayer = audioContainer.querySelector('.audio-player');
                if (otherAudioPlayer !== clickedAudioPlayer) {
                    otherAudioPlayer.pause();
                }
            });
            clickedAudioPlayer.play();
        }
    }
</script>
