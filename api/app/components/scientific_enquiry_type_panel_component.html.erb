<!-- Icons -->
<div class="flex gap-2">
  <% @enquiry_types.map do |enq_type| %>
    <div class="w-8 h-8 bg-white rounded-lg p-1" data-enquiry-type-tooltip="<%= enq_type[:uid] %>">
      <div
        class="bg-no-repeat bg-center bg-contain w-full h-full"
        style="background-image: url(<%= enq_type[:fileboy_url] %>)"
      ></div>
    </div>
    <div id="<%= enq_type[:uid] %>" class="fixed bg-white rounded-lg p-4 z-50 shadow-lg border border-gray-300 max-w-md" style="visibility: hidden;">
      <p class="text-lg text-black font-bold leading-6 mb-1">
        <%= enq_type['title'] %>
      </h4>
      <p class="whitespace-pre-wrap text-black"><%= enq_type['body'] %></p>
    </div>
  <% end %>
</div>

<script type="module">
  import {
    computePosition,
    offset,
    shift,
  } from "https://cdn.jsdelivr.net/npm/@floating-ui/dom@latest/+esm"

  function initializeTooltips() {
    // build the popout elements
    const nodeId = "<%= @portal_id %>"
    const portal = document.getElementById(nodeId)

    const enquiryTypes = <%= @enquiry_types.to_json.html_safe %>
    for(const el of enquiryTypes) {
      console.log(el)
      // find the node with the id of the enquiry type.uid
      const node = document.getElementById(el.uid)
      if(!node) {
        console.error(`Could not find node with id ${el.uid}`)
        continue
      }
      // copy the node
      const clone = node.cloneNode(true)
      // delete the original
      node.remove()
      // append the copy to the portal
      portal.appendChild(clone)
    }

    // attach the tooltips
    document.querySelectorAll("[data-enquiry-type-tooltip]").forEach((button) => {
      let isOpen = false
      const tooltip = document.getElementById(button.getAttribute("data-enquiry-type-tooltip"))
      console.log("TOOLTIP", tooltip)
      tooltip.style.visibility = "hidden" // Ensure tooltip starts hidden
      tooltip.style.opacity = "0" // Ensure tooltip starts fully transparent

      function handleOpen() {
        computePosition(button, tooltip, {
          placement: "bottom",
          middleware: [offset(10), shift({ padding: 5 })],
          strategy: "fixed",
        }).then(({ x, y }) => {
          tooltip.style.left = `${x}px`
          tooltip.style.top = `${y}px`
          tooltip.style.visibility = "visible"
          tooltip.style.opacity = "1" // Fade in
        })
        isOpen = true
      }
      function handleClose() {
        tooltip.style.visibility = "hidden"
        tooltip.style.opacity = "0" // Fade out
        isOpen = false
      }

      // mobile touch click - open/close clicking icon
      button.addEventListener("touchstart", (event) => {
        event.preventDefault()
        if(isOpen) {
          handleClose()
        } else {
          handleOpen()
        }
      })

      // click outside to close for mobile as they don't get enter/leave events
      document.addEventListener("touchstart", (event) => {
        if (isOpen && !button.contains(event.target)) {
          handleClose()
        }
      })

      // Mouse over / leave controller for desktop
      button.addEventListener("mouseenter", (event) => {
        handleOpen()
      })

      button.addEventListener("mouseleave", function (event) {
        handleClose()
      })
    })
  }
  document.addEventListener("DOMContentLoaded", initializeTooltips)
</script>
