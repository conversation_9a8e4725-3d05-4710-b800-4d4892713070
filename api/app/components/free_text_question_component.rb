# frozen_string_literal: true
class FreeTextQuestionComponent < GenericQuestionComponent
  # {
  #   "id": "3ff5c176-67af-4ba5-be9f-01e1930490e2",
  #   "type": "free-text",
  #   "weight": 6,
  #   "dataJson": {
  #     "key": 5,
  #     "prompt": "<p>Free text question!</p><p>&nbsp;</p><p>Type whatever you want I guess.</p>",
  #     "feedback": "Custom feedback node",
  #     "maxLength": "280",
  #     "minLength": "10",
  #     "fileboyImageId": ""
  #   }
  # }

  def get_free_text_description
    has_min = @minLength > 0
    has_max = @maxLength.present?
    if has_min && has_max
      "Type a response between #{@minLength} and #{@maxLength} characters."
    elsif has_min
      "Type a response at least #{@minLength} characters long."
    elsif has_max
      "Type a response up to #{@maxLength} characters long."
    else
      ''
    end
  end

  def initialize(qd)
    super(qd)

    @minLength = @data['minLength'].present? ? @data['minLength'].to_i : 0
    @maxLength = @data['maxLength'].present? ? @data['maxLength'].to_i : nil
  end
end
