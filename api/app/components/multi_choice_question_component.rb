# frozen_string_literal: true

class MultiChoiceQuestionComponent < GenericQuestionComponent
  # {
  #   "type": "rocket-word",
  #   "slide_id": 2459,
  #   "key": 576,
  #   "prompt": "<p>What is lodestone?</p>",
  #   "validResponse": [
  #     576
  #   ],
  #   "use_data": false,
  #   "question_body": "",
  #   "question_video_url": "",
  #   "question_fileboy_image_id": "",
  #   "options": [
  #     {
  #       "label": "A tiny iron based magnetic line on a type of card which stores data.",
  #       "value": 580,
  #       "fileboyId": "8dba6fff-086b-4752-b59d-9cade9625cd8"
  #     },
  #     {
  #       "label": "A heavy type of metal that is very common.",
  #       "value": 577,
  #       "fileboyId": "6d5f0174-239b-44e1-a05b-254e61a9e139"
  #     },
  #     {
  #       "label": "A naturally magnetised mineral.",
  #       "value": 576,
  #       "fileboyId": "536dd30e-0675-406e-875e-f8ca14ed1f7c"
  #     },
  #     {
  #       "label": "A metal-bearing mineral or rock, or a metal that can be mined at a profit.",
  #       "value": 578,
  #       "fileboyId": "3b1c3066-2b47-4a8b-bbbd-21be210c70b5"
  #     },
  #     {
  #       "label": "To pull towards. Metals such as iron, nickel and cobalt are attracted to magnets.",
  #       "value": 579,
  #       "fileboyId": "1616e8e6-9ba0-4a1e-9046-375de82da9c0"
  #     }
  #   ]
  # }

  def parse_rocket_word_question(qd)
    @id = "#{qd['slide_id']}-#{rand}"
    @prompt = qd['prompt']
    @body = qd['question_body']
    @video_url = qd['question_video_url']
    @image_id = qd['question_fileboy_image_id']
    @options = qd['options']
    @valid_responses = qd['validResponse']
    @min_choices = 1
    @max_choices = 1
  end

  # {
  #   "id": "49ade537-3736-4779-bc88-5afbc71753f1",
  #   "type": "multi-choice",
  #   "weight": 2,
  #   "dataJson": {
  #     "key": 1,
  #     "prompt": "<p>Multiple choice example prompt</p><p>&nbsp;</p><p>John and Jane are twins.</p><p>Johns birthday is in april.</p><p>Bens birthday is in december.</p><p>Bills birthday is in june.</p><p>Select those who have birthdays in april.</p>",
  #     "options": [
  #       {
  #         "label": "John",
  #         "value": "John"
  #       },
  #       {
  #         "label": "Ben",
  #         "value": "Ben"
  #       },
  #       {
  #         "label": "Jane",
  #         "value": "Jane"
  #       },
  #       {
  #         "label": "Bill",
  #         "value": "Bill"
  #       }
  #     ],
  #     "feedback": "Custom feedback node\n",
  #     "limitChoices": {
  #       "max": "2",
  #       "min": "1"
  #     },
  #     "validResponse": [
  #       "John",
  #       "Jane"
  #     ]
  #   }
  # }

  def parse_quip_question(qd)
    @options = @data['options']
    @valid_responses = @data['validResponse']

    # use valid answer count if setting or no max specified
    use_valid_answer_limit_max = !!@data.dig('limitChoices', 'sameNumberAsCorrectAnswers') || @data.dig('limitChoices', 'max').nil?
    @max_choices = use_valid_answer_limit_max ? @data['validResponse'].length : @data.dig('limitChoices', 'max')

    use_valid_answer_limit_min = !!@data.dig('limitChoices', 'sameNumberAsCorrectAnswers')
    @min_choices = use_valid_answer_limit_min ? @data['validResponse'].length : @data.dig('limitChoices', 'min').presence || 1
  end

  def choose_answers_text
    return "Choose #{@max_choices} #{'answer'.pluralize(@max_choices)}" if @min_choices == @max_choices || @max_choices == 1
    "Choose #{@min_choices} to #{@max_choices} answers"
  end

  def initialize(qd)
    if qd['slide_id'].present?
      parse_rocket_word_question(qd)
      return
    end
    super(qd)
    parse_quip_question(qd)
  end
end
