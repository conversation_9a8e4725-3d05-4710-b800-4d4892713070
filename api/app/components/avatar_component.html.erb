<div class="inline-flex gap-3 items-center">
  <div class="w-[46px] h-[46px] min-w-[46px] rounded-full bg-de-brand font-semibold text-center leading-[46px] text-lg text-white overflow-hidden bg-center bg-no-repeat bg-cover" style='<%= @image_url.present? ? "background-image: url('#{@image_url}')" : "" %>'>
    <%- if @image_url.nil? %>
      <div><%= @text.strip.split.map { |part| part[0].upcase }.join %></div>
    <% end %>
  </div>
  <%- if @include_text %>
    <div class="font-semibold text-base flex flex-col">
      <div class="flex gap-4 items-center">
      <%- if @link %>
        <%= link_to @text, @link, class: "underline underline-offset-2 decoration-2" %>
      <% else %>
        <%= @text %>
      <% end %>
      <%- if @tag %>
        <span class="admin-badge bg-purple-100 text-purple-800"><%= @tag %></span>
      <% end %>
      </div>

      <%- if @sub_text %>
        <span class="text-sm w-fit font-light">
          <%= @sub_text %>
        </span>
      <% end %>
      <%- if @copyable_text %>
        <span class="text-sm w-fit font-light cursor-pointer rounded-lg hover:bg-blue-100" onclick="copyContent(event, this, '<%= @copyable_text %>')">
          <i class="fa-solid fa-copy"></i>
          <%= @copyable_text %>
        </span>
      <% end %>
    </div>
  <% end %>
</div>
