<div class="data-observe opacity-0 transition-opacity duration-1000 ease-in-out <%= @media_frame ? "#{@media_frame} p-4 rounded-xl" : "" %>">
  <%- if @image_url.present? %>
    <%= image_tag @image_url, class: "w-full rounded-lg overflow-hidden", alt: @alt_text %>
  <% end %>
  <%- if @video_id.present? %>
    <div class="video-player">
      <div hx-trigger="load" hx-get="/video/<%= @video_id %>" hx-swap="outerHtml" hx-target="this"></div>
    </div>
  <% end %>
  <%- if @fileboy_video_id.present? %>
    <div class="video-player">
      <div data-fileboy-video="height=100%" id="<%= @fileboy_video_id %>"></div>
    </div>
  <% end %>
  <%- if @media_credit %>
    <div class="text-right pt-2">
      <%= image_tag @media_credit[:image], class: "w-24 h-24 rounded-full ml-auto", alt: @alt_text %>
      <p class="text-white font-bold"><%= @media_credit[:text] %></p>
    </div>
  <% end %>
</div>