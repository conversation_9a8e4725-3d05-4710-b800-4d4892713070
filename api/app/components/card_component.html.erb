<% aria_label = raw %(aria-label="#{ERB::Util.html_escape(@alt_text)}") %>
<div class="text-left w-full max-w-md <%= @background_color.present? ? @background_color : "from-white to-slate-100 bg-gradient-to-r" %> border border-gray-200 rounded-xl shadow p-5 flex flex-col justify-between h-full relative">
  <%- if @overlay_image %>
    <img class="absolute top-[26px] right-[26px] bg-white rounded overflow-hidden max-w-24 opacity-80" src="<%= @overlay_image %>"/>
  <% end %>
  <%- if @path %>
    <a href="<%= @path %>" style="background-image: url(<%= @background %>)" class="block rounded-lg overflow-hidden mb-5 aspect-teaser bg-cover bg-center <%= @image_background_color %>" <%= aria_label unless @image.present? %>>
      <%- if @image %>
        <img class="rounded-t-lg object-contain h-full w-full" src="<%= @image %>" alt="<%= @alt_text %>"/>
      <%- end %>
    </a>
  <%- elsif @onclick %>
    <div onclick="<%= @onclick %>" style="background-image: url(<%= @background %>)" class="block rounded-lg overflow-hidden mb-5 aspect-teaser bg-cover bg-center cursor-pointer <%= @image_background_color %>" <%= aria_label unless @image.present? %>>
      <%- if @image %>
        <img class="rounded-t-lg object-contain h-full w-full" src="<%= @image %>" alt="<%= @alt_text %>"/>
      <%- end %>
    </div>
  <%- else %>
    <div style="background-image: url(<%= @background %>)" class="block rounded-lg overflow-hidden mb-5 aspect-teaser bg-cover bg-center <%= @image_background_color %>" <%= aria_label unless @image.present? %>>
      <%- if @image %>
        <img class="rounded-t-lg object-contain h-full w-full" src="<%= @image %>" alt="<%= @alt_text %>"/>
      <%- end %>
    </div>
  <% end %>
  <div class="flex-grow">
    <%- if @tags %>
      <div class="mb-3 -mt-1">
        <%- @tags.each do |tag| %>
          <span class="inline-block px-2 py-1 text-sm font-semibold bg-cyan-100 text-cyan-600 rounded mr-1"><%= tag %></span>
        <% end %>
      </div>
    <% end %>
    <%- if @path %>
      <%= link_to @path, class: "block" do %>
        <p class="mb-4 text-2xl font-bold tracking-tight <%= @color %>"><%= @title %></p>
      <% end %>
    <% elsif @onclick %>
      <a onclick="<%= @onclick %>" class="block cursor-pointer">
        <p class="mb-4 text-2xl font-bold tracking-tight <%= @color %>"><%= @title %></p>
      </a>
    <%- else %>
      <p class="mb-4 text-2xl font-bold tracking-tight <%= @color %>"><%= @title %></p>
    <% end %>
    <p class="mb-3 font-normal -mt-2 <%= @color %>"><%= @body %></p>
  </div>
  <%- if @path %>
    <div>
      <%= link_to @path, class: "btn btn-text-base inline-flex items-center mt-auto" do %>
        View
        <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
        </svg>
      <% end %>
    </div>
  <% elsif @onclick %>
    <div>
      <a onclick="<%= @onclick %>" class="btn btn-text-base inline-flex items-center mt-auto cursor-pointer">
        View
        <svg class="rtl:rotate-180 w-3.5 h-3.5 ms-2" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
        </svg>
      </a>
    </div>
  <% end %>
  <% if footer? %>
    <div>
      <%= footer %>
    </div>
  <% end %>
</div>
