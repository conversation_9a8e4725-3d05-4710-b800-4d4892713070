# frozen_string_literal: true

class ReportMediaButtonComponent < ViewComponent::Base

  # <% opts = report_media_options(source: 'CareerPath', id: @result.id, reference: @result.career_path['image']) %>
  def initialize(source:, id:, reference:, type:, class_name:)
    @type = type
    @source = source
    @id = id
    @reference = reference
    @class_name = class_name
  end

  def report_media_options(source:, id:, reference:, type:)
    {
      type: type,
      source: source,
      id: id,
      reference: reference
    }
  end
end
