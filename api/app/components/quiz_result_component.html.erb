<% if @compact %>
  <div class="quiz-result bg-gray-900 rounded-lg shadow-xl p-4 text-white border border-gray-700">
    <!-- Compact Header -->
    <div class="flex items-center justify-between mb-3 pb-2 border-b border-gray-700">
      <div class="flex items-center gap-2">
        <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-md flex items-center justify-center">
          <i class="fas fa-question-circle text-white text-sm"></i>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-gray-100"><%= question_type.humanize %></h3>
        </div>
      </div>
      <div class="flex items-center gap-1 bg-gray-800 rounded px-2 py-1">
        <span class="text-lg font-bold text-purple-400"><%= score %></span>
        <span class="text-gray-500 text-sm">/</span>
        <span class="text-sm font-medium text-gray-300"><%= total %></span>
      </div>
    </div>

    <!-- Compact Question -->
    <div class="mb-3 p-2 bg-gray-800 rounded border-l-2 border-purple-500">
      <div class="text-sm text-gray-200"><%= prompt&.html_safe %></div>
    </div>

    <!-- Compact Results -->
    <div class="result-content">
      <% case question['type'] %>
      <% when 'multi-choice' %>
        <div class="space-y-1">
          <% question_data['options'].each do |option| %>
            <% user_selected = answer.any? { |ans| ans.dig('option', 'value') == option['value'] } %>
            <% is_correct = valid_response.include?(option['value']) %>
            <% 
              if is_correct && user_selected
                bg_class = "bg-green-900/50 border-green-600"
                icon_class = "fas fa-check text-green-400"
              elsif is_correct && !user_selected
                bg_class = "bg-green-900/30 border-green-700"
                icon_class = "fas fa-check text-green-500"
              elsif !is_correct && user_selected
                bg_class = "bg-red-900/50 border-red-600"
                icon_class = "fas fa-times text-red-400"
              else
                bg_class = "bg-gray-800 border-gray-600"
                icon_class = "fas fa-circle text-gray-500"
              end
            %>
            <div class="flex items-center gap-2 p-2 rounded border <%= bg_class %>">
              <i class="<%= icon_class %> text-xs"></i>
              <span class="text-sm text-gray-200"><%= option['label'] %></span>
            </div>
          <% end %>
        </div>

      <% when 'free-text' %>
        <div class="bg-gray-800 rounded p-3 border-l-2 border-blue-500">
          <div class="text-sm text-gray-300"><%= answer.html_safe %></div>
        </div>

      <% when 'sort-list' %>
        <div class="space-y-1">
          <% valid_response.each_with_index do |value, index| %>
            <div class="flex items-center gap-2 p-2 rounded <%= answer[index] == value ? 'bg-green-900/50 border border-green-600' : 'bg-red-900/50 border border-red-600' %>">
              <div class="w-5 h-5 rounded-full <%= answer[index] == value ? 'bg-green-600' : 'bg-red-600' %> text-white flex items-center justify-center text-xs font-bold">
                <%= index + 1 %>
              </div>
              <i class="fas <%= answer[index] == value ? 'fa-check text-green-400' : 'fa-times text-red-400' %> text-xs"></i>
              <span class="text-sm text-gray-200">
                <%= question_data['options'].detect { |x| x['value'] == value }['label'] %>
              </span>
            </div>
          <% end %>
        </div>

      <% when 'fill-in-blanks' %>
        <div class="bg-gray-800 rounded p-3">
          <div id="fill-blanks-result-data-<%= question['id'] %>" class="data text-sm leading-relaxed text-gray-200"></div>
        </div>
        <script>
          (function() {
            const messageEl = document.getElementById("fill-blanks-result-data-<%= question['id'] %>");
            const template = `<%= question.dig('dataJson', 'template') %>`;
            const fieldRegex = /({{.*?}})/;
            const optsData = <%= question.dig('dataJson', 'options').to_json.html_safe %>;
            const orderedFields = template.match(new RegExp(fieldRegex.source, "g"));
            let partialTemplate = template;

            function appendEl(el) {
              el.style['white-space'] = "pre-wrap";
              messageEl.appendChild(el);
            }

            const validRes = <%= question.dig('dataJson', 'validResponse').to_json.html_safe %>;
            const answers = <%= answer.to_json.html_safe %>;
            
            orderedFields.forEach((field, index) => {
              const [before, after] = partialTemplate.split(field);
              const span = document.createElement("span");
              span.innerHTML = before;
              appendEl(span);

              const fieldName = field.match(/{{(.*?)}}/)[1];
              const options = optsData[fieldName];
              const answer = answers.find(y => y.field === fieldName);

              if(answer.value != validRes[fieldName]) {
                const wrongValueSpan = document.createElement("span");
                const wrongValue = answer.value;
                const opt = options.find(x => x.value == wrongValue);
                wrongValueSpan.innerHTML = opt.label;
                wrongValueSpan.classList.add("text-red-400", "line-through", "bg-red-900", "px-1", "rounded", "mr-1", "text-sm");
                appendEl(wrongValueSpan);
              }

              const correctValueSpan = document.createElement("span");
              const correctValue = validRes[fieldName];
              const opt = options.find(x => x.value == correctValue);
              correctValueSpan.innerHTML = opt.label;
              correctValueSpan.classList.add("text-green-400", "bg-green-900", "px-1", "rounded", "font-medium", "text-sm");
              appendEl(correctValueSpan);

              partialTemplate = after;

              if(index === orderedFields.length - 1) {
                const span = document.createElement("span");
                span.innerHTML = after;
                appendEl(span);
              }
            });
          })();
        </script>

      <% when 'text-bucket' %>
        <div class="space-y-2">
          <% bucket_data.each do |item| %>
            <div class="bg-gray-800 rounded p-2">
              <h5 class="text-sm font-medium text-gray-200 mb-1 pb-1 border-b border-gray-700">
                <%= item[:bucket_name] %>
              </h5>
              <% if item[:correct_values].any? || item[:incorrect_values].any? %>
                <div class="space-y-1">
                  <% item[:correct_values].each do |val| %>
                    <div class="flex items-center gap-2 text-xs text-green-400">
                      <i class="fas fa-check"></i>
                      <span><%= val['label'] %></span>
                    </div>
                  <% end %>
                  <% item[:incorrect_values].each do |val| %>
                    <div class="flex items-center gap-2 text-xs text-red-400">
                      <i class="fas fa-times"></i>
                      <span><%= val['label'] %></span>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-2 text-gray-500 text-xs italic">
                  No items
                </div>
              <% end %>
            </div>
          <% end %>
        </div>

      <% when 'image-bucket' %>
        <div class="space-y-2">
          <% bucket_data.each do |item| %>
            <div class="bg-gray-800 rounded p-2">
              <h5 class="text-sm font-medium text-gray-200 mb-2 pb-1 border-b border-gray-700">
                <%= item[:bucket_name] %>
              </h5>
              <% if item[:correct_values].any? || item[:incorrect_values].any? %>
                <div class="grid gap-2 grid-cols-2">
                  <% item[:correct_values].each do |val| %>
                    <div class="border border-green-600 bg-green-900/30 rounded p-2">
                      <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded bg-gray-700 flex-shrink-0">
                          <div class="w-full h-full bg-no-repeat bg-center bg-contain" 
                              style="background-image: url('<%= fileboy_image_url(val['fileboyId']) %>')"></div>
                        </div>
                        <div class="min-w-0">
                          <i class="fas fa-check text-green-400 text-xs"></i>
                          <% if val['label'].present? %>
                            <p class="text-xs text-gray-300 truncate"><%= val['label'] %></p>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                  <% item[:incorrect_values].each do |val| %>
                    <div class="border border-red-600 bg-red-900/30 rounded p-2">
                      <div class="flex items-center gap-2">
                        <div class="w-8 h-8 rounded bg-gray-700 flex-shrink-0">
                          <div class="w-full h-full bg-no-repeat bg-center bg-contain" 
                              style="background-image: url('<%= fileboy_image_url(val['fileboyId']) %>')"></div>
                        </div>
                        <div class="min-w-0">
                          <i class="fas fa-times text-red-400 text-xs"></i>
                          <% if val['label'].present? %>
                            <p class="text-xs text-gray-300 truncate"><%= val['label'] %></p>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-2 text-gray-500 text-xs italic">
                  No items
                </div>
              <% end %>
            </div>
          <% end %>
        </div>

      <% else %>
        <div class="bg-red-900/50 border border-red-600 rounded p-2">
          <div class="flex items-center gap-2">
            <i class="fas fa-exclamation-triangle text-red-400 text-sm"></i>
            <span class="text-red-300 text-sm">Unknown: <%= question['type'] %></span>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% else %>
  <div class="quiz-result bg-white rounded-xl shadow-lg p-6 text-black border border-gray-200">
    <!-- Header Section -->
    <div class="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
      <div class="flex items-center gap-3">
        <div class="w-12 h-12 bg-gradient-to-r from-purple-600 to-indigo-700 rounded-lg flex items-center justify-center">
          <i class="fas fa-question-circle text-white text-xl"></i>
        </div>
        <div>
          <h3 class="text-xl font-bold text-gray-900"><%= question_type.humanize %> Question</h3>
          <p class="text-sm text-gray-500">Quiz Results</p>
        </div>
      </div>
      <div class="text-right">
        <div class="inline-flex items-center gap-2 bg-gray-50 rounded-lg px-4 py-2">
          <span class="text-2xl font-bold text-purple-600"><%= score %></span>
          <span class="text-gray-400">/</span>
          <span class="text-lg font-semibold text-gray-600"><%= total %></span>
        </div>
        <p class="text-xs text-gray-500 mt-1">Your Score</p>
      </div>
    </div>

    <!-- Question Prompt -->
    <div class="mb-6 p-4 bg-gray-50 rounded-lg border-l-4 border-purple-500">
      <div class="font-semibold text-lg text-gray-900">
        <%= prompt&.html_safe %>
      </div>
    </div>

    <!-- Results Content -->
    <div class="result-content space-y-6">
      <% case question['type'] %>
      <% when 'multi-choice' %>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h4 class="font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <i class="fas fa-list text-purple-500"></i>
            Answer Options
          </h4>
          <div class="space-y-3">
            <% question_data['options'].each do |option| %>
              <% user_selected = answer.any? { |ans| ans.dig('option', 'value') == option['value'] } %>
              <% is_correct = valid_response.include?(option['value']) %>
              <% 
                if is_correct && user_selected
                  bg_class = "bg-green-50 border-green-300"
                  text_class = "text-green-700"
                  icon_class = "fas fa-check-circle text-green-500"
                  status_text = "Your answer - Correct"
                elsif is_correct && !user_selected
                  bg_class = "bg-green-50 border-green-200"
                  text_class = "text-green-600"
                  icon_class = "fas fa-check text-green-500"
                  status_text = "Correct answer"
                elsif !is_correct && user_selected
                  bg_class = "bg-red-50 border-red-300"
                  text_class = "text-red-700"
                  icon_class = "fas fa-times-circle text-red-500"
                  status_text = "Your answer - Incorrect"
                else
                  bg_class = "bg-gray-50 border-gray-200"
                  text_class = "text-gray-600"
                  icon_class = "fas fa-circle text-gray-400"
                  status_text = ""
                end
              %>
              <div class="flex items-center justify-between p-3 rounded-md border-2 <%= bg_class %>">
                <div class="flex items-center gap-3">
                  <i class="<%= icon_class %>"></i>
                  <span class="<%= text_class %> font-medium"><%= option['label'] %></span>
                </div>
                <% if status_text.present? %>
                  <span class="text-xs <%= text_class %> font-medium bg-white bg-opacity-50 px-2 py-1 rounded">
                    <%= status_text %>
                  </span>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>

      <% when 'free-text' %>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <i class="fas fa-edit text-blue-500"></i>
            Your Answer
          </h4>
          <div class="bg-gray-50 rounded-lg p-4 border-l-4 border-blue-500">
            <div class="prose prose-sm max-w-none text-gray-700">
              <%= answer.html_safe %>
            </div>
          </div>
        </div>

      <% when 'sort-list' %>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <i class="fas fa-sort text-purple-500"></i>
            Sorting Results
          </h4>
          <div class="space-y-2">
            <% valid_response.each_with_index do |value, index| %>
              <div class="flex items-center gap-3 p-3 rounded-md <%= answer[index] == value ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200' %>">
                <div class="w-8 h-8 rounded-full <%= answer[index] == value ? 'bg-green-500' : 'bg-red-500' %> text-white flex items-center justify-center font-bold text-sm">
                  <%= index + 1 %>
                </div>
                <i class="fas <%= answer[index] == value ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500' %>"></i>
                <span class="<%= answer[index] == value ? 'text-green-700' : 'text-red-700' %>">
                  <%= question_data['options'].detect { |x| x['value'] == value }['label'] %>
                </span>
              </div>
            <% end %>
          </div>
        </div>

      <% when 'fill-in-blanks' %>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <i class="fas fa-fill-drip text-orange-500"></i>
            Fill in the Blanks Results
          </h4>
          <div id="fill-blanks-result-data-<%= question['id'] %>" class="data text-lg leading-relaxed"></div>
        </div>
        <script>
          (function() {
            const messageEl = document.getElementById("fill-blanks-result-data-<%= question['id'] %>");
            const template = `<%= question.dig('dataJson', 'template') %>`;
            const fieldRegex = /({{.*?}})/;
            const optsData = <%= question.dig('dataJson', 'options').to_json.html_safe %>;
            const orderedFields = template.match(new RegExp(fieldRegex.source, "g"));
            let partialTemplate = template;

            function appendEl(el) {
              el.style['white-space'] = "pre-wrap";
              messageEl.appendChild(el);
            }

            const validRes = <%= question.dig('dataJson', 'validResponse').to_json.html_safe %>;
            const answers = <%= answer.to_json.html_safe %>;
            
            orderedFields.forEach((field, index) => {
              const [before, after] = partialTemplate.split(field);
              const span = document.createElement("span");
              span.innerHTML = before;
              appendEl(span);

              const fieldName = field.match(/{{(.*?)}}/)[1];
              const options = optsData[fieldName];
              const answer = answers.find(y => y.field === fieldName);

              if(answer.value != validRes[fieldName]) {
                const wrongValueSpan = document.createElement("span");
                const wrongValue = answer.value;
                const opt = options.find(x => x.value == wrongValue);
                wrongValueSpan.innerHTML = opt.label;
                wrongValueSpan.classList.add("text-red-600", "line-through", "bg-red-100", "px-2", "py-1", "rounded", "mr-1");
                appendEl(wrongValueSpan);
              }

              const correctValueSpan = document.createElement("span");
              const correctValue = validRes[fieldName];
              const opt = options.find(x => x.value == correctValue);
              correctValueSpan.innerHTML = opt.label;
              correctValueSpan.classList.add("text-green-600", "bg-green-100", "px-2", "py-1", "rounded", "font-semibold");
              appendEl(correctValueSpan);

              partialTemplate = after;

              if(index === orderedFields.length - 1) {
                const span = document.createElement("span");
                span.innerHTML = after;
                appendEl(span);
              }
            });
          })();
        </script>

      <% when 'text-bucket' %>
        <div class="space-y-4">
          <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <i class="fas fa-layer-group text-indigo-500"></i>
            Text Categorization Results
          </h4>
          <% bucket_data.each do |item| %>
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h5 class="font-semibold text-lg text-gray-900 mb-3 pb-2 border-b border-gray-200">
                <%= item[:bucket_name] %>
              </h5>
              <% if item[:correct_values].any? || item[:incorrect_values].any? %>
                <div class="space-y-2">
                  <% item[:correct_values].each do |val| %>
                    <div class="flex items-center gap-3 p-2 bg-green-50 rounded-md border border-green-200">
                      <i class="fas fa-check-circle text-green-500"></i>
                      <span class="text-green-700"><%= val['label'] %></span>
                    </div>
                  <% end %>
                  <% item[:incorrect_values].each do |val| %>
                    <div class="flex items-center gap-3 p-2 bg-red-50 rounded-md border border-red-200">
                      <i class="fas fa-times-circle text-red-500"></i>
                      <span class="text-red-700"><%= val['label'] %></span>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-6 text-gray-500">
                  <i class="fas fa-inbox text-2xl mb-2 opacity-50"></i>
                  <p class="italic">No items were placed in this category</p>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>

      <% when 'image-bucket' %>
        <div class="space-y-6">
          <h4 class="font-semibold text-gray-900 mb-3 flex items-center gap-2">
            <i class="fas fa-images text-pink-500"></i>
            Image Categorization Results
          </h4>
          <% bucket_data.each do |item| %>
            <div class="bg-white border border-gray-200 rounded-lg p-4">
              <h5 class="font-semibold text-lg text-gray-900 mb-4 pb-2 border-b border-gray-200">
                <%= item[:bucket_name] %>
              </h5>
              <% if item[:correct_values].any? || item[:incorrect_values].any? %>
                <div class="grid gap-4 md:grid-cols-2">
                  <% item[:correct_values].each do |val| %>
                    <div class="border-2 border-green-300 bg-green-50 rounded-lg overflow-hidden shadow-sm">
                      <div class="flex items-center gap-4 p-4">
                        <div class="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                          <div class="w-full h-full bg-no-repeat bg-center bg-contain" 
                              style="background-image: url('<%= fileboy_image_url(val['fileboyId']) %>')"></div>
                        </div>
                        <div class="flex-grow">
                          <div class="flex items-center gap-2 mb-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span class="text-sm font-medium text-green-700">Correct</span>
                          </div>
                          <% if val['label'].present? %>
                            <p class="text-gray-700 font-medium"><%= val['label'] %></p>
                          <% else %>
                            <p class="text-gray-500 italic text-sm">No label provided</p>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                  <% item[:incorrect_values].each do |val| %>
                    <div class="border-2 border-red-300 bg-red-50 rounded-lg overflow-hidden shadow-sm">
                      <div class="flex items-center gap-4 p-4">
                        <div class="w-20 h-20 rounded-lg overflow-hidden bg-gray-100 flex-shrink-0">
                          <div class="w-full h-full bg-no-repeat bg-center bg-contain" 
                              style="background-image: url('<%= fileboy_image_url(val['fileboyId']) %>')"></div>
                        </div>
                        <div class="flex-grow">
                          <div class="flex items-center gap-2 mb-2">
                            <i class="fas fa-times-circle text-red-500"></i>
                            <span class="text-sm font-medium text-red-700">Incorrect</span>
                          </div>
                          <% if val['label'].present? %>
                            <p class="text-gray-700 font-medium"><%= val['label'] %></p>
                          <% else %>
                            <p class="text-gray-500 italic text-sm">No label provided</p>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="text-center py-8 text-gray-500">
                  <i class="fas fa-inbox text-3xl mb-3 opacity-50"></i>
                  <p class="italic">No items were placed in this category</p>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>

      <% else %>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="flex items-center gap-3">
            <i class="fas fa-exclamation-triangle text-red-500"></i>
            <span class="text-red-700 font-medium">Unknown question type: <%= question['type'] %></span>
          </div>
        </div>
      <% end %>
    </div>
  </div>
<% end %>