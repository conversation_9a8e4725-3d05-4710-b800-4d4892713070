# frozen_string_literal: true

class ImageBucketQuestionComponent < GenericQuestionComponent
  # {
  #   "id": "7fdfa571-88d9-4d07-97c4-f0d4ac79d40b",
  #   "type": "image-bucket",
  #   "weight": 5,
  #   "dataJson": {
  #     "key": 4,
  #     "prompt": "<p>Image Bucket!</p>",
  #     "buckets": [ // THIS CAN ALSO BE A STRING[]
  #       {
  #         "key": "0",
  #         "name": "Cats"
  #       },
  #       {
  #         "key": "0.24342286683388636",
  #         "name": "Dogs"
  #       },
  #       {
  #         "key": "0.10449482879004324",
  #         "name": "Ferrets"
  #       }
  #     ],
  #     "options": [
  #       {
  #         "label": "Cat",
  #         "value": "Cat",
  #         "bucket": "0",
  #         "fileboyId": "ea1b4930-c4e3-4cb0-8408-24b89d7bb1d5"
  #       },
  #       {
  #         "key": "0.7856111365033835",
  #         "label": "Dog",
  #         "value": "Dog",
  #         "bucket": "0.24342286683388636",
  #         "fileboyId": "73a8c7c2-d696-498f-8b55-c403f19d0fd5"
  #       },
  #       {
  #         "key": "0.9541505852344291",
  #         "label": "Ferret",
  #         "value": "Ferret",
  #         "bucket": "0.10449482879004324",
  #         "fileboyId": "9a7cbd3b-70c0-4d0a-8db5-adeb9d767133"
  #       }
  #     ],
  #     "feedback": "Custom feedback node"
  #   }
  # }

  def initialize(qd)
    super(qd)
    @buckets = @data['buckets'].map do |bucket|
      if bucket.is_a?(Hash)
        bucket.with_indifferent_access
      else
        { name: bucket, key: bucket, value: bucket }.with_indifferent_access
      end
    end
    # need to map the valid response buckets into the options
    @options = @data['options']

    if @data['buckets'][0].is_a?(String)
      @data['validResponse'].each_key do |key|
        answers = @data['validResponse'][key]
        answers.each do |answer|
          option = @options.find { |o| o['value'] == answer }
          option['bucket'] = key
        end
      end
    end

    # overwriting buckets data becuase it can be a string[] for some reason
    # so formatting to match the newer format
    @data['buckets'] = @buckets
  end
end
