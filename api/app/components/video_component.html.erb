<%- if @video.present? %>
  <div
    class="video-player"
    data-video-id="<%= @video.id %>"
    data-video-view-tracked="false"
    onmouseenter="handleVideoHover(this);bindVideoViewTimer(this);"
    data-properties="<%= @tracking_data.to_json %>"
    data-should-track="<%= @should_track %>"
  >
    <%- if @video.source == "fileboy" %>
      <div data-video-element data-fileboy-video="height=100% <%= @autoPlay ? "autoPlay=true" : "" %>" id="<%= @video.external_id %>"></div>
    <%- elsif @video.source == "youtube" %>
      <iframe
        height="100%"
        width="100%"
        src="https://www.youtube.com/embed/<%= @video.external_id %>?autoplay=<%= @autoPlay ? '1' : '0' %>"
        title="<%= @video.name %>"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowfullscreen
      ></iframe>
    <%- elsif @video.source == "vimeo" %>
      <iframe
        title="<%= @video.name %>"
        src="https://player.vimeo.com/video/<%= @video.external_id %>?autopause=0&autoplay=<%= @autoPlay ? '1' : '0' %>&muted=<%= @autoPlay ? '1' : '0' %>"
        width="100%"
        height="100%"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowfullscreen
      ></iframe>
    <%- end %>
  </div>
<% elsif @fileboy_video_id %>
  <div class="video-player"
        data-video-fileboy-id="<%= @fileboy_video_id %>"
        data-video-view-tracked="false"
        onmouseenter="handleVideoHover(this);bindVideoViewTimer(this);"
        data-properties="<%= @tracking_data.to_json %>"
        data-should-track="<%= @should_track %>"
      >
    <div data-video-element data-fileboy-video="height=100% <%= @autoPlay ? "autoPlay=true" : "" %>" id="<%= @fileboy_video_id %>"></div>
  </div>
<% elsif @video_url %>
  <div class="video-player"
        data-video-url="<%= @video_url %>"
        data-video-view-tracked="false"
        onmouseenter="handleVideoHover(this);bindVideoViewTimer(this);"
        data-properties="<%= @tracking_data.to_json %>"
        data-should-track="<%= @should_track %>"
      >
    <iframe
        src="<%= @video_url %>"
        width="100%"
        height="100%"
        frameborder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowfullscreen
    ></iframe>
  </div>
<% end %>
