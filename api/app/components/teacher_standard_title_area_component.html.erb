<div class="relative overflow-hidden <%= @layout == :float ? 'mb-4' : '-mb-6 md:border border-white/20 md:rounded-xl' %>">

  <!-- Floating orbs animation -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute w-32 h-32 bg-blue-500/5 rounded-full blur-xl animate-float-1" style="top: 10%; left: 20%;"></div>
    <div class="absolute w-24 h-24 bg-purple-500/5 rounded-full blur-xl animate-float-2" style="top: 60%; right: 15%;"></div>
    <div class="absolute w-20 h-20 bg-pink-500/5 rounded-full blur-xl animate-float-3" style="bottom: 20%; left: 70%;"></div>
  </div>
  
  <!-- Gradient overlay for depth -->
  <% if @layout == :joined %>
    <div class="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-blue-900/10 to-purple-900/20"></div>
  <% end %>
  
  <div class="relative <%= @layout == :float ? '' : 'pt-6 pb-14 px-8' %>">
    <!-- Breadcrumbs integrated at top -->
    <div class="mb-4 opacity-80">
      <%= helpers.render_breadcrumbs(dark_text: false) %>
    </div>
    
    <div class="flex flex-wrap items-start justify-between gap-4 flex-col md:flex-row">
      <div class="flex items-start gap-4 flex-1 min-w-0">
        <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg ring-2 ring-white/20 backdrop-blur-sm">
          <% if @initials.present? %>
            <span class="text-white text-2xl font-bold">
              <%= @initials %>
            </span>
          <% else %>
            <i class="<%= @icon %> text-white text-2xl opacity-80"></i>
          <% end %>
        </div>
        
        <div class="flex-1 min-w-0 self-center">
          <h1 class="text-3xl font-bold text-white leading-tight">
            <%= @title %>
            <% if @count.present? %>
              <span class="opacity-50 ml-4"><%= @count %></span>
            <% end %>
          </h1>

          <% if @subtitle.present? %>
            <p class="text-base text-gray-300 leading-relaxed max-w-3xl">
              <%= @subtitle %>
            </p>
          <% end %>
        </div>
      </div>

      <div class="flex items-center gap-3 flex-shrink-0">
        <% if @tour.present? && @tour[:show] %>
          <%= tag.div(
            content_tag(:span, "Guided Tour", class: "flex items-center gap-2") +
            content_tag(:svg, content_tag(:path, "", d: "M9 5l7 7-7 7"), 
                       class: "w-4 h-4 transition-transform group-hover:translate-x-0.5", 
                       fill: "none", stroke: "currentColor", viewBox: "0 0 24 24"),
            id: "start-tour",
            data: { tour_page: @tour[:page] }.merge(@tour[:extra_data] || {}),
            class: "btn btn-sm btn-purple group hover:bg-purple-600 hover:shadow-lg transition-all duration-200 backdrop-blur-sm ring-1 ring-white/20 shepherd-start",
          ) %>
        <% end %>

        <% if @help_button.present? %>
          <div class="backdrop-blur-sm">
            <%= render HelpButtonComponent.new(
              title: @help_button[:title],
              body: @help_button[:body],
              fileboy_video_id: @help_button[:fileboy_video_id],
              id: @help_button[:video],
            ) %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>