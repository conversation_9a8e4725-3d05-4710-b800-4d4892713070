# frozen_string_literal: true

class TableThumbnailColumnComponent < ViewComponent::Base
  def initialize(image_url: nil, fileboy_id: nil, background_size: nil, class_name: "")
    @image_url = image_url

    @background_size = background_size || "cover"

    if (fileboy_id)
      @image_url = "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_id}?transform=resize:40x40~fit:#{@background_size}~background:transparent;format:png"
    end
    @class_name = class_name
  end
end
