# api/app/components/modal_component.rb
class ModalComponent < ViewComponent::Base
  attr_reader :title, :id, :max_width

  renders_one :trigger
  renders_one :footer

  def initialize(title:, id: nil, external_control: false, max_width: nil)
    @title = title
    @id = id
    @max_width = max_width
    @external_control = external_control
  end

  def before_render
    raise ArgumentError, "A trigger block is required for ModalComponent" unless trigger? || @external_control
  end
end
