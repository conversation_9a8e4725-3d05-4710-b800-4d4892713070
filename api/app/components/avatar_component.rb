# frozen_string_literal: true

class AvatarComponent < ViewComponent::Base
  def initialize(text:, image_url: nil, fileboy_image_id: nil, include_text: false, copyable_text: nil, link: nil, sub_text: nil, tag: nil)
    fileboy_image_url = fileboy_image_id ? "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:125x125~fit:cover;format:webp;quality:75" : nil

    @text = text
    @copyable_text = copyable_text
    @image_url = fileboy_image_url || image_url
    @include_text = include_text
    @link = link
    @sub_text = sub_text
    @tag = tag

  end
end
