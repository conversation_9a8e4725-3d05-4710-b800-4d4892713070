<%# api/app/components/modal_component.html.erb %>
<div <%= "data-controller=modal" unless @external_control %>>
  <%# Only show trigger if not externally controlled %>
  <% unless @external_control %>
    <div data-action="click->modal#open" class="cursor-pointer">
      <%= trigger %>
    </div>
  <% end %>

  <dialog
    <%= "id=#{@id}" if @id %>
    <%= "data-modal-target=dialog" unless @external_control %>
    class="<%= @max_width.presence || "max-w-md md:max-w-xl" %> w-full p-6 rounded-lg shadow-lg bg-white"
    role="dialog"
    aria-modal="true"
    aria-labelledby="<%= @id %>_title"
  >
    <div class="relative">
      <!-- Close Button -->
      <div
        class="absolute top-0 right-0 text-gray-500 hover:text-gray-700 cursor-pointer"
        <%= "data-action=click->modal#close keydown->modal#closeOnKey" unless @external_control %>
        onclick="<%= @external_control ? "this.closest('dialog').close()" : nil %>"
        onkeypress="<%= @external_control ? "if(event.key === 'Enter') this.closest('dialog').close()" : nil %>"
        aria-label="Close"
        tabindex="0"
      >
        <i class="fa-solid fa-xmark text-2xl"></i>
      </div>

      <h2 class="text-2xl mb-2 font-bold pr-4 nova" id="<%= @id %>_title"><%= title %></h2>

      <div class="mb-6 text-base font-normal text-gray-600">
        <%= content %>
      </div>

      <% if footer? %>
        <div class="border-t pt-4 mt-4 flex gap-2">
          <%= footer %>
        </div>
      <% end %>
    </div>
  </dialog>
</div>
