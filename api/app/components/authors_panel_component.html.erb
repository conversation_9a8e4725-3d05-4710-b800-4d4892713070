<div class="space-y-4">
  <%- @authors.pluck(:name, :body, :fileboy_image_id).each do |name, body, fileboy_image_id| %>
    <%- background_image_url = "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:300x_~fit:cover;format:webp;quality:75" %>
    <div class="text-black from-white to-slate-100 bg-gradient-to-r border border-gray-200 rounded-xl shadow p-5 flex gap-6">
      <div class="flex-[0_0_100px]">
        <div style="background-image: url(<%= background_image_url %>)" class="block rounded-lg overflow-hidden mb-5 bg-cover bg-center bg-slate-100 h-[100px]" aria-label="<%= name %>"></div>
      </div>
      <div class="prose prose-slate max-w-none">
        <h3 class="leading-[20px]"><%= name %></h3>
        <div class="text-sm h-[44px] overflow-hidden transition-all duration-300" data-more-content>
          <%= body&.html_safe %>
        </div>
        <button class="text-de-brand text-sm font-bold mt-1" onclick="toggleAuthorReadMore(this)" data-more-button>
          Read more
        </button>
      </div>
    </div>
  <% end %>
</div>

<script>
  // Author panels read more
  function toggleAuthorReadMore(button) {
    const content = button.previousElementSibling;
    const isExpanded = content.classList.contains('h-[44px]');

    if (isExpanded) {
      content.classList.remove('h-[44px]');
      button.textContent = 'Show less';
    } else {
      content.classList.add('h-[44px]');
      button.textContent = 'Read more';
    }
  }
</script>
