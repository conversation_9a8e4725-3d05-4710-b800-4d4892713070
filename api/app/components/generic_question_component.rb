# frozen_string_literal: true

class GenericQuestionComponent < ViewComponent::Base
  def fileboy_image_path(id)
    return '' if id.blank?
    "https://www.developingexperts.com/file-cdn/images/get/#{id}?transform=resize:300x_;format:webp;quality:75"
  end

  def initialize(qd)
    @id = qd['id']
    @data = qd['dataJson']
    @prompt = @data['prompt']
    @fileboyImageId = @data['fileboyImageId']
  end
end
