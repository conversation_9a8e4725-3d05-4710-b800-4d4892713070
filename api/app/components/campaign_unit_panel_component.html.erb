<% if @campaign_units.any? %>
  <div class="flex flex-col gap-2">
    <% @campaign_units.map do |campaign_unit| %>
      <div class="rounded-xl p-4 flex flex-wrap gap-4 items-center justify-between bg-white">
        <div class="min-w-48 flex-1">
          <p class="text-de-blue text-lg font-bold">Sponsored by</h4>
          <h2 class="text-black text-2xl mb-2"><%= campaign_unit.campaign.organisation.name %></h2>
          <%if campaign_unit.campaign.organisation.pupil_text.present? %>
            <span class="prose whitespace-pre-wrap mb-2"><%== campaign_unit.campaign.organisation.pupil_text %></span>
          <%end%>
          <%if campaign_unit.page_link.present? && !@hide_links%>
            <div class=pb-2></div>
            <%= render ButtonComponent::Base.new(
              url: campaign_unit.page_link,
              text: "Read more",
              left_icon: "fa-solid fa-arrow-up-right-from-square",
              onclick: "window.handleCampaignLinkFollow(event,'#{campaign_unit.campaign.id}','#{@current_user&.id}')"
            ) %>
          <%end%>
        </div>
        <img src="<%= campaign_unit.campaign&.organisation&.organisation_logo %>" class="w-48 ml-auto" alt="<%= campaign_unit.campaign.organisation.name %>">
      </div>
    <%end%>
  </div>
<% end %>
