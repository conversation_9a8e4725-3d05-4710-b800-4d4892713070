# frozen_string_literal: true

class CardComponent < ViewComponent::Base
  renders_one :footer

  def initialize(tags: nil, title:, pre_title: nil, image_background_img: nil, image_background_fileboy_id: nil, image: nil, image_fileboy_id: nil, overlay_image: nil, overlay_image_fileboy_id: nil, image_background_color: nil, body: nil, path: nil, background_color: "bg-white", color: "text-gray-900", onclick: nil, alt_text: nil)
    fileboy_background_image = image_background_fileboy_id ? "https://www.developingexperts.com/file-cdn/images/get/#{image_background_fileboy_id}?transform=resize:600x300~fit:cover;format:webp;quality:75" : nil
    fileboy_image = image_fileboy_id ? "https://www.developingexperts.com/file-cdn/images/get/#{image_fileboy_id}?transform=resize:600x300~fit:contain;format:webp;quality:75" : nil
    overlay_fileboy_image = overlay_image_fileboy_id ? "https://www.developingexperts.com/file-cdn/images/get/#{overlay_image_fileboy_id}?transform=resize:600x300~fit:contain;format:webp;quality:75" : nil

    if !fileboy_background_image && !fileboy_image && !overlay_fileboy_image && !image
      svg = <<~SVG
        <svg xmlns='http://www.w3.org/2000/svg' width='800' height='600'>
          <defs>
            <linearGradient id='grad' x1='0%' y1='0%' x2='100%' y2='100%'>
              <stop offset='0%' style='stop-color:#a18cd1; stop-opacity:1' />
              <stop offset='100%' style='stop-color:#fbc2eb; stop-opacity:1' />
            </linearGradient>
          </defs>
          <rect width='100%' height='100%' fill='url(#grad)' />
        </svg>
      SVG
      default_image = "data:image/svg+xml;base64,#{Base64.strict_encode64(svg)}"
    end

    @tags = tags
    @title = title
    @pre_title = pre_title
    @background = fileboy_background_image || image_background_img || default_image
    @image = fileboy_image || image
    @overlay_image = overlay_fileboy_image || overlay_image
    @alt_text = alt_text.nil? ? title : alt_text

    @image_background_color = image_background_color
    @body = body
    @path = path
    @background_color = background_color
    @color = color

    @onclick = onclick
  end
end
