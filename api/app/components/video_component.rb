# frozen_string_literal: true

class VideoComponent < ViewComponent::Base
  def initialize(id: nil, fileboy_video_id: nil, video_url: nil, autoplay: false, tracking_data: {}, should_track: true)
    @should_track = should_track
    @video = Video.find_by(id: id)

    @fileboy_video_id = fileboy_video_id unless fileboy_video_id.blank?
    unless video_url.blank?
      url, _type = Video.video_embed_url(video_url)
      @video_url = if url.present?
                     url
                   else
                     ''
                   end
    end
    @autoplay = autoplay

    @tracking_data = tracking_data
  end
end
