<div class="<%= @background_color %> <%= @gradient_to_color %> bg-gradient-to-r pt-16 pb-8 px-4 sm:pb-16 lg:px-6 mb-16">
  <div class="gap-8 <%= @image_inline ? "items-center" : "items-end" %> mx-auto max-w-screen-xl xl:gap-16 grid md:grid-cols-2">
    <div class="<%= @text_color %>">
      <% if helpers.breadcrumbs_any? %>
        <div class="mb-1">
          <%= helpers.render_breadcrumbs(omit_text_color: true) %>
        </div>
      <% end %>
      <h1 class="text-3xl sm:text-5xl mb-4"><%= @title %></h1>
      <%- if @subtitle.present? %>
        <p class="font-semibold text-xl mb-4"><%= @subtitle %></p>
      <%- end %>
      <%- if @body.present? %>
        <%- if @body_2.present? %>
          <p class="sm:text-lg mb-2"><%= @body %></p>
          <p class="sm:text-lg"><%= @body_2 %></p>
        <%- else %>
          <p class="sm:text-lg"><%= @body %></p>
        <%- end %>
      <%- end %>
      <%- if @primary_action %>
        <div class="flex gap-2 pt-4">
          <%= link_to @primary_action[:path] do %>
            <button class="btn btn-lg btn-lime">
              <%= @primary_action[:text] %>
            </button>
          <% end %>
          <%- if @secondary_action %>
            <%= link_to @secondary_action[:path] do %>
              <button class="btn btn-lg btn-white">
                <%= @secondary_action[:text] %>
              </button>
            <% end %>
          <%- end %>
        </div>
      <% end %>
    </div>
    <div class="<%= @image_inline ? "" : "-mb-24" %> hidden md:block">
      <%- if @image %>
        <%= image_tag @image, class: "w-full", style: @max_image_width ? "max-width: #{@max_image_width}px" : nil, alt: @alt_text %>
      <%- end %>
    </div>
  </div>
</div>
