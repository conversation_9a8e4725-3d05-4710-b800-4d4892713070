<div>
  <%= label_tag :fileboy_image, "Image of Exemplar Work", required: true %>
  <%- if @fileboy_image_id.present? %>
    <div class="flex justify-between p-2">
      <div class="hidden" id="img-preview">
        <p>New</p>
        <div class="flex overflow-hidden border w-[200px] h-[200px]">
          <img class="object-cover" id="img"/>
        </div>
      </div>
      <div>
        <p>Current</p>
        <div class="overflow-hidden border w-[200px] h-[200px]">
          <img src="https://www.developingexperts.com/file-cdn/images/get/<%= @fileboy_image_id %>?transform=resize:200x200"/>
        </div>
      </div>
    </div>
  <% else %>
    <div class="hidden p-2" id="img-preview">
      <p>Preview</p>
      <div class="flex overflow-hidden border w-[200px] h-[200px]">
        <img class="object-cover" id="img"/>
      </div>
    </div>
  <% end %>
  <%= file_field_tag :fileboy_image, accept:'image/*' %>
</div>

<script>
  document.getElementById('fileboy_image').addEventListener('change', function () {
    if (this.files && this.files[0]) {
      var reader = new FileReader();
      reader.onload = function (e) {
        document.getElementById("img").setAttribute('src', e.target.result);
      }
      reader.readAsDataURL(this.files[0]);
      document.getElementById('img-preview').style.display = "block";
    } else {
      document.getElementById('img-preview').style.display = "none";
    }
  });
</script>