# frozen_string_literal: true

class QuizResultComponent < ViewComponent::Base
  def initialize(result_data, compact: false)
    @result_data = result_data
    @question = result_data['question']
    @result = result_data['result']
    @compact = compact
  end

  private

  attr_reader :result_data, :question, :result

  def question_type
    question['type'].humanize
  end

  def prompt
    question.dig('dataJson', 'prompt')
  end

  def score
    result['score']
  end

  def total
    result['total']
  end

  def answer
    result['answer']
  end

  def question_id
    question['id']
  end

  def question_data
    question['dataJson']
  end

  def valid_response
    question_data['validResponse']
  end

  # For bucket questions (text-bucket, image-bucket)
  def bucket_data
    return [] unless question_data['buckets']
    
    question_data['buckets'].map do |bucket|
      ans_data = answer.detect { |ans| ans['bucket']['key'] == bucket['key'] }
      next unless ans_data

      {
        bucket_name: bucket['name'],
        correct_values: question_data['options'].select { |opt| 
          ans_data['values'].include?(opt['value']) && opt['bucket'] == bucket['key'] 
        },
        incorrect_values: question_data['options'].select { |opt| 
          ans_data['values'].include?(opt['value']) && opt['bucket'] != bucket['key'] 
        }
      }
    end.compact
  end

  def fileboy_image_url(fileboy_id)
    "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_id}?transform=resize:1000x_;format:webp;quality:75"
  end
end