import {Controller} from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["tab", "content"];

    connect() {
        this.showTab(0); // Show the first tab initially
    }

    switchTab(event) {
        const index = this.tabTargets.indexOf(event.currentTarget);
        this.showTab(index);
    }


    showTab(index) {
        this.tabTargets.forEach((tab, i) => {
            tab.classList.toggle("active-tab-heading", i === index);
        });
        this.contentTargets.forEach((content, i) => {
            content.classList.toggle("hidden", i !== index);
        });
    }
}
