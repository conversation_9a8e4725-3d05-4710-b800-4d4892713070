// app/javascript/controllers/drawer_controller.js
import { Controller } from "@hotwired/stimulus"

// To trigger a callback on draw open, use this listener
// document.addEventListener('drawer:opened', (event))

export default class extends Controller {
  static targets = ["panel", "backdrop"]
  static values = { closeOnBackdropClick: Boolean }

  connect() {
    this.closeOnBackdropClickValue = this.hasCloseOnBackdropClickValue ? this.closeOnBackdropClickValue : true;
    
    // Add escape key listener
    document.addEventListener("keydown", this.handleKeydown.bind(this))
  }

  disconnect() {
    // Remove escape key listener when controller disconnects
    document.removeEventListener("keydown", this.handleKeydown.bind(this))
  }

  handleKeydown(event) {
    if (event.key === "Escape" && this.isOpen) {
      this.close()
    }
  }

  open() {
    this.panelTarget.classList.add("translate-x-0")
    this.panelTarget.classList.remove("translate-x-full", "-translate-x-full")
    this.backdropTarget.classList.remove("hidden")
    
    // Add a small delay before showing backdrop for smoother transition
    setTimeout(() => {
      this.backdropTarget.classList.add("opacity-100")
    }, 50)
    
    this.isOpen = true
    
    // Prevent body scrolling when drawer is open
    document.body.classList.add("overflow-hidden")

    // Fire custom event when drawer is opened
    const event = new CustomEvent('drawer:opened', { detail: { controller: this, target: this.panelTarget } })
    document.dispatchEvent(event)
  }

  close() {
    const rightSide = this.panelTarget.classList.contains("right-0")
    
    this.panelTarget.classList.remove("translate-x-0")
    this.panelTarget.classList.add(rightSide ? "translate-x-full" : "-translate-x-full")
    this.backdropTarget.classList.remove("opacity-100")
    
    // Add a small delay before hiding backdrop for smoother transition
    setTimeout(() => {
      this.backdropTarget.classList.add("hidden")
    }, 300)
    
    this.isOpen = false
    
    // Re-enable body scrolling
    document.body.classList.remove("overflow-hidden")
  }
}
