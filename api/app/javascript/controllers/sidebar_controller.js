import {Controller} from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["section"]

    connect() {
        this.sectionTargets.forEach((section, index) => {
            const selectedSection = section.querySelector(`a[href="${window.location.pathname}"]`);

            if (selectedSection) {
                selectedSection.classList.add('selected');
            }
        });
    }
}
