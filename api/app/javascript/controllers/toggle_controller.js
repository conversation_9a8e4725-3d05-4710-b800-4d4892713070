import {Controller} from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["section", "content", "icon"]

    connect() {
        this.sectionTargets.forEach((section, index) => {
            const content = this.contentTargets[index];
            const icon = this.iconTargets[index];
            const shouldExpand = content.querySelector(`a[href="${window.location.pathname}"]`);

            // Remove the 'hidden-on-load' class and set the display style accordingly
            content.classList.remove('hidden');
            if (shouldExpand) {
                content.style.display = 'block';
                icon.classList.add('fa-chevron-up');
                icon.classList.remove('fa-chevron-down');
            } else {
                content.style.display = 'none';
            }
        });
    }


    toggleSection(event) {
        const content = event.currentTarget.nextElementSibling;
        const icon = event.currentTarget.querySelector('i');
        const isVisible = content.style.display === 'block';
        content.style.display = isVisible ? 'none' : 'block';
        icon.classList.toggle('fa-chevron-down', isVisible);
        icon.classList.toggle('fa-chevron-up', !isVisible);
    }
}
