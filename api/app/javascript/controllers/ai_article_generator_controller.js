import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.selectedTitle = null
    this.articleData = {}
  }

  generateTitles() {
    const articleType = document.getElementById('article_type').value
    const prompt = document.getElementById('prompt').value
    
    if (!articleType || !prompt) {
      alert('Please select an article type and enter a prompt')
      return
    }

    // Disable the button and show loading
    const button = document.getElementById('generate-titles-btn')
    const originalText = button.textContent
    
    button.disabled = true
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...'
    this.showLoading('titles')
    
    fetch('/admin/articles/generate_titles', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: new URLSearchParams({
        article_type: articleType,
        prompt: prompt
      })
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok')
      }
      return response.json()
    })
    .then(data => {
      if (data.error) {
        throw new Error(data.error)
      }
      this.displayTitles(data.titles)
      this.showStep(2)
    })
    .catch(error => {
      console.error('Error:', error)
      alert('Error generating titles: ' + error.message)
    })
    .finally(() => {
      // Re-enable button
      button.disabled = false
      button.innerHTML = originalText
      this.hideLoading('titles')
    })
  }

  generateArticle() {
    if (!this.selectedTitle) {
      alert('Please select a title')
      return
    }

    // Disable the button and show loading
    const button = document.getElementById('generate-article-btn')
    const originalText = button.textContent
    
    button.disabled = true
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating...'
    this.showLoading('article')
    
    const articleType = document.getElementById('article_type').value
    const prompt = document.getElementById('prompt').value
    
    fetch('/admin/articles/generate_article', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: new URLSearchParams({
        title: this.selectedTitle,
        article_type: articleType,
        prompt: prompt
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Start polling for generation status
        this.startGenerationPolling(data.session_id, button, originalText)
      } else {
        throw new Error(data.errors ? data.errors.join(', ') : 'Unknown error')
      }
    })
    .catch(error => {
      console.error('Error:', error)
      alert('Error generating article: ' + error.message)
      
      // Reset button state on error
      button.disabled = false
      button.innerHTML = originalText
      this.hideLoading('article')
    })
  }

  startGenerationPolling(sessionId, button, originalText) {
    let pollCount = 0
    const maxPolls = 120 // 10 minutes max (5 second intervals)
    
    const poll = () => {
      pollCount++
      
      // Update button text to show we're still working
      const dots = '.'.repeat((pollCount % 3) + 1)
      button.innerHTML = `<i class="fas fa-spinner fa-spin mr-2"></i>Generating${dots}`
      
      fetch(`/admin/articles/check_generation_status/${sessionId}`)
        .then(response => response.json())
        .then(data => {
          switch(data.status) {
            case 'processing':
              if (pollCount < maxPolls) {
                setTimeout(poll, 5000) // Poll every 5 seconds
              } else {
                // Timeout
                alert('Article generation is taking longer than expected. Please try again.')
                button.disabled = false
                button.innerHTML = originalText
                this.hideLoading('article')
              }
              break
              
            case 'completed':
              // Store the generated content and show preview
              this.articleData = data.content
              this.displayArticlePreview(data.content)
              this.showStep(3)
              
              // Reset button
              button.disabled = false
              button.innerHTML = originalText
              this.hideLoading('article')
              break
              
            case 'error':
              throw new Error(data.errors ? data.errors.join(', ') : 'Article generation failed')
              
            case 'not_found':
              throw new Error('Session expired. Please try again.')
              
            default:
              throw new Error('Unknown status received')
          }
        })
        .catch(error => {
          console.error('Polling error:', error)
          alert('Error generating article: ' + error.message)
          
          // Reset button state
          button.disabled = false
          button.innerHTML = originalText
          this.hideLoading('article')
        })
    }
    
    // Start polling immediately
    poll()
  }

  // Revert createArticle back to synchronous (since it's fast)
  createArticle() {
    if (!this.articleData) {
      alert('No article data available')
      return
    }

    // Get the button and add loading state
    const button = document.getElementById('create-article-btn')
    const originalText = button.textContent
    
    button.disabled = true
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Article...'

    // Generate slug from name
    const slug = this.articleData.name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    fetch('/admin/articles/create_from_ai', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: new URLSearchParams({
        'article[name]': this.articleData.name,
        'article[body]': this.articleData.body,
        'article[summary]': this.articleData.summary,
        'article[title]': this.articleData.title,
        'article[meta_description]': this.articleData.meta_description,
        'article[slug]': slug
      })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Show success state briefly before redirect
        button.innerHTML = '<i class="fas fa-check mr-2"></i>Article Created!'
        button.className = 'btn btn-base btn-green'
        
        // Redirect after a brief delay
        setTimeout(() => {
          window.location.href = data.redirect_url
        }, 500)
      } else {
        throw new Error(data.errors ? data.errors.join(', ') : 'Unknown error')
      }
    })
    .catch(error => {
      console.error('Error:', error)
      alert('Error creating article: ' + error.message)
      
      // Reset button state on error
      button.disabled = false
      button.innerHTML = originalText
      button.className = 'btn btn-base btn-cyan'
    })
  }

  displayTitles(titles) {
    const container = document.getElementById('title-options')
    container.innerHTML = ''
    
    titles.forEach((title, index) => {
      const div = document.createElement('div')
      const label = document.createElement('label')
      label.className = 'flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50'
      
      const input = document.createElement('input')
      input.type = 'radio'
      input.name = 'selected_title'
      input.value = title
      input.className = 'mr-3'
      input.addEventListener('change', () => this.selectTitle(title))
      
      const span = document.createElement('span')
      span.textContent = title
      
      label.appendChild(input)
      label.appendChild(span)
      div.appendChild(label)
      container.appendChild(div)
    })
    
    this.hideLoading('titles')
  }

  selectTitle(title) {
    this.selectedTitle = title
    document.getElementById('generate-article-btn').disabled = false
  }

  displayArticlePreview(data) {
    document.getElementById('preview-name').textContent = data.name
    document.getElementById('preview-summary').textContent = data.summary
    document.getElementById('preview-body').innerHTML = data.body
    
    document.getElementById('article-preview').classList.remove('hidden')
    this.hideLoading('article')
  }

  startOver() {
    // Reset all form fields
    document.getElementById('article_type').value = ''
    document.getElementById('prompt').value = ''
    
    // Clear title options
    document.getElementById('title-options').innerHTML = ''
    
    // Reset state
    this.selectedTitle = null
    this.articleData = {}
    
    // Reset all buttons to original state
    const generateTitlesBtn = document.getElementById('generate-titles-btn')
    generateTitlesBtn.disabled = false
    generateTitlesBtn.innerHTML = 'Generate Title Options'
    generateTitlesBtn.className = 'btn btn-base btn-cyan'
    
    const generateArticleBtn = document.getElementById('generate-article-btn')
    generateArticleBtn.disabled = true
    generateArticleBtn.innerHTML = 'Generate Article'
    generateArticleBtn.className = 'btn btn-base btn-cyan'
    
    const createArticleBtn = document.getElementById('create-article-btn')
    createArticleBtn.disabled = false
    createArticleBtn.innerHTML = 'Create Article (Draft)'
    createArticleBtn.className = 'btn btn-base btn-cyan'
    
    // Hide preview
    document.getElementById('article-preview').classList.add('hidden')
    
    // Show first step
    this.showStep(1)
  }
  showStep(stepNumber) {
    document.querySelectorAll('.ai-step').forEach(step => step.classList.add('hidden'))
    document.getElementById(`step-${stepNumber}`).classList.remove('hidden')
  }

  showLoading(type) {
    document.getElementById(`loading-${type}`).classList.remove('hidden')
  }

  hideLoading(type) {
    document.getElementById(`loading-${type}`).classList.add('hidden')
  }
}