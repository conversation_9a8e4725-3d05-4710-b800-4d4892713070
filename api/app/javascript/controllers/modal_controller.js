// api/app/javascript/controllers/modal_controller.js
import {Controller} from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["dialog"]
    static values = {closeOnBackgroundClick: Boolean}

    connect() {
        this.closeOnBackgroundClickValue = this.hasCloseOnBackgroundClickValue ? this.closeOnBackgroundClickValue : true;

        if (this.closeOnBackgroundClickValue) {
            this.dialogTarget.addEventListener("click", this.backgroundClick.bind(this))
        }
    }

    backgroundClick(event) {
        if (event.target === this.dialogTarget) {
            this.close()
        }
    }

    closeOnKey(event) {
        if (event.key === "Enter" || event.key === " " || event.key === "Spacebar") {
            event.preventDefault();
            this.close();
        }
    }

    open() {
        this.dialogTarget.showModal()
    }

    close() {
        this.dialogTarget.close()
    }
}
