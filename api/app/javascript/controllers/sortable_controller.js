import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["sortableItem"];

  connect() {
    this.sortableItems = Array.from(this.sortableItemTargets);

    this.sortableItems.forEach(item => {
      item.draggable = true;
      item.addEventListener("dragstart", this.dragStart.bind(this));
      item.addEventListener("dragover", this.dragOver.bind(this));
      item.addEventListener("drop", this.drop.bind(this));
    });
  }

  dragStart(event) {
    event.dataTransfer.setData("text/plain", "");
    event.dataTransfer.effectAllowed = "move";
    this.draggedElement = event.currentTarget;
    this.draggedElement.classList.add("dragging");
  }

  dragOver(event) {
    event.preventDefault();
  }

  drop(event) {
    event.preventDefault();
    const targetElement = event.currentTarget;

    if (this.draggedElement && targetElement) {
        const draggedIndex = this.sortableItems.indexOf(this.draggedElement);
        const targetIndex = this.sortableItems.indexOf(targetElement);
        
        if (draggedIndex !== -1 && targetIndex !== -1) {
            const parent = targetElement.parentNode;
            parent.insertBefore(this.draggedElement, targetElement);

            this.updateOrder();
        }
    }

    this.draggedElement.classList.remove("dragging");
  }

  updateOrder() {
    this.sortableItems = Array.from(this.sortableItemTargets);
    const updatedOrder = this.sortableItems.map(item => Number(item.id));
    this.element.querySelector('[data-target="sortable.container"]').value = JSON.stringify(updatedOrder);
  }
}