const hubspotIntercom = { booted: false, userId: null };
function shutdownHubspotSession() {
  if (hubspotIntercom.booted) {
    window.HubSpotConversations?.clear();
    window.HubSpotConversations?.widget?.remove();
    hubspotIntercom.booted = false;
  }
}

async function bootHubspotSession() {
  const hubspotDontLoadOnRegexRoute = [/\/a\/presentations\/\d+\/view/];
  try {
    const csrfTokenMeta = document.querySelector('meta[name="csrf-token"]');
    const csrfToken = csrfTokenMeta ? csrfTokenMeta.content : null;
    if (!csrfToken) {
      console.error("CSRF token not found");
      return;
    }
    const result = await fetch("/sessions/hubspot_visitor_verification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRF-Token": csrfToken,
      },
    });
    if(!result.ok) {
      throw new Error(result.statusText);
    }
    // empty object if no user
    const data = await result.json();
    if (data?.error?.message) {
      throw new Error(data.error.message);
    }
    shutdownHubspotSession(); // close any existing sessions
    if (data?.id && data.type !== "Pupil") {
      if ("token" in data && data.token && data.email) {
        window.hsConversationsSettings = {
          identificationEmail: data.email,
          identificationToken: data.token,
        };
      } else {
        console.error("Hubspot chat failed to verify visitor", data);
        window.hsConversationsSettings = { loadImmediately: false };
      }
      // Bypass loading on certain routes as it's alot easier to do here than to have to check after
      const skipMount = hubspotDontLoadOnRegexRoute.some((regex) => regex.test(window.location.pathname));
      if (!skipMount) {
        await waitForHubSpotConversationsReady()
        window.HubSpotConversations?.widget?.load();
      }
      hubspotIntercom.userId = data.id;
      hubspotIntercom.booted = true;
    }
  } catch (e) {
    console.error("Failed to boot hubspot session", e);
    // if we can't auth, just load the widget anyway without user data
    window.HubSpotConversations?.widget?.load();
  }
}

function waitForHubSpotConversationsReady() {
  return new Promise((resolve, reject) => {
    let retries = 0;
    const maxRetries = 30;
    const checkIntercom = () => {
      if (window.HubSpotConversations) {
        resolve();
      } else if (retries < maxRetries) {
        retries++;
        setTimeout(checkIntercom, 1000);
      } else {
        reject(new Error("Intercom failed to load after 30 retries"));
      }
    };
    checkIntercom();
  });
}

const HubspotIntercomManager = {
  bootHubspotSession,
  shutdownHubspotSession,
};

window.HubspotIntercomManager = Object.freeze({
  bootHubspotSession,
  shutdownHubspotSession,
});

window.addEventListener("DOMContentLoaded", HubspotIntercomManager.bootHubspotSession);
