import <PERSON> from 'https://cdn.jsdelivr.net/npm/shepherd.js@14.5.0/dist/esm/shepherd.mjs';
import { buildStepButtons, resolveShowOn, setKeyboardHandler } from './helpers';

export function startTour(name, steps, data = {}, startAtIndex = 0) {
  const tour = new Shepherd.Tour({
    tourName: name,
    useModalOverlay: true,
    keyboardNavigation: false,
    defaultStepOptions: {
      cancelIcon: { enabled: true },
      scrollTo: { behavior: 'smooth', block: 'center' },
    }
  });

  const btnClasses = 'btn btn-base btn-purple';

  steps.forEach((step, i) => {
    tour.addStep({
      ...step,
      showOn: resolveShowOn(step),
      buttons: buildStepButtons(i, steps.length, step.buttons, btnClasses)
    });
  });

  ['complete', 'cancel'].forEach((event) => {
    tour.on(event, () => {
      if (event === 'complete') { window.scrollTo({ top: 0, behavior: 'smooth' }); }
      sessionStorage.removeItem("shepherd_tour");
    })
  });

  setKeyboardHandler();

  tour.start();
  for (let i = 0; i < startAtIndex; i++) {
    const step = tour.steps[i];
    const showOn = step.options.showOn;
    // if showOn is false for step, it gets skipped - no need to skip twice
    if (typeof showOn === "function" && !showOn()) {
      continue;
    }
  
    tour.next();
  }

  const currentTourId = Shepherd.activeTour.id;
  tourStorage(currentTourId, Shepherd.activeTour?.steps.indexOf(Shepherd.activeTour?.getCurrentStep()), data);

  tour.on("show", () => {
    tourStorage(currentTourId, Shepherd.activeTour?.steps.indexOf(Shepherd.activeTour?.getCurrentStep()), data);
  });
}

function tourStorage(tourId, stepIndex, data = {}) {
  const tour = JSON.parse(sessionStorage.getItem("shepherd_tour"));
  sessionStorage.setItem("shepherd_tour", JSON.stringify({ ...tour, id: tourId, step: stepIndex, data, autoplay: true }));
}