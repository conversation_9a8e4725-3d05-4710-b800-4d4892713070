export const presentationSteps = (previous) => [
  {
    title: "🖥️ Slide Management",
    text: "This is your central hub for all the slides in this presentation. You can add new slides, edit existing ones, and reorder them to structure your presentation's flow 🎞️.",
    attachTo: { element: '#slides-container', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    buttons: [
      {  text: "🔙 Back to Lesson Plan", action: previous, classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" },
    ]
  },
  {
    title: "➕ Add a New Slide",
    text: "Ready to build out your presentation? Click here or use the 'Next' button to open the form and create a new slide! ✍️",
    attachTo: { element: '#add-slide-btn', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    advanceOn: { selector: '#add-slide-btn', event: 'click' },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      {
        action() { document.querySelector('#add-slide-btn').click(); },
        text: 'Next',
        classes: "shepherd-next"
      }
    ]
  },
  {
    title: "✨ Create Your First Slide!",
    text: "This presentation is looking a little empty! Now that you know how, click the 'Add Slide' button to start building your slide deck. 🎬",
    attachTo: { element: '#add-slide-btn', on: 'left' },
    showOn: () => {
      return !document.querySelector('#slides-container tbody tr');
    },
    buttons: [
      { text: "Back", action: "back", classes: "shepherd-back" },
      { text: "Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📝 Slide Content Form",
    text: "Fill in the content for your new slide here. When you’re finished, just hit save. You can close this form anytime using the 'X' or the cancel button. 💾",
    attachTo: { element: '#new-slide-drawer', on: 'left' },
    scrollTo: true,
    buttons: [
      { 
        text: "🔙 Back",
        classes: "shepherd-back",
        action() {
          document.querySelector('[data-action="click->drawer#close"]').click();
          setTimeout(() => this.back(), 150);
        }
      },
      { 
        text: "Next ➡️",
        classes: "shepherd-next",
        action() {
          document.querySelector('[data-action="click->drawer#close"]').click();
          setTimeout(() => this.next(), 150);
        }
      },
    ]
  },
  {
    title: "📖 Intro & Outro Slides",
    text: "Every presentation starts with a fixed intro and ends with a fixed outro slide. These bookend your content and are not editable or reorderable.",
    attachTo: { element: '#slides-container tbody tr:first-child', on: 'top' },
    extraHighlights: ['#slides-container tbody tr:last-child'],
    buttons: [
      {
        action() { 
          document.querySelector('#add-slide-btn').click();
          setTimeout(() => this.back(), 150);
        },
        text: 'Back',
        classes: "shepherd-back"
      },
      { text: "Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🚥 Slide Status",
    text: "Each row provides a quick summary of a slide.",
    attachTo: { element: '#slides-container .regular-slide', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
  },
  {
    title: "🛠️ Edit or Delete",
    text: "Need to make a change? Use these buttons to edit a slide using the same form as before, or to remove it from the presentation entirely. 🗑️",
    attachTo: { element: '#slides-container .regular-slide td:last-child', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
  },
  {
    title: "↕️ Change the Order",
    text: "Click and drag this handle to reorder your slides. It's a perfect way to arrange the flow of your presentation!",
    attachTo: { element: '#slides-container .regular-slide td:first-child', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
  },
]