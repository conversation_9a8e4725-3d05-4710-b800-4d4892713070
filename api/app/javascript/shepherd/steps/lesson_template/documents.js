export const documentsSteps = (previous) => [
  {
    title: "📚 Document Management",
    text: "This is your central library for all lesson resources. Here you can upload files for pupils or add helpful materials for teachers, keeping everything organized in one place. 🗂️",
    attachTo: { element: '#documents-header', on: 'top' },
    extraHighlights: ['#documents-table'],
    scrollTo: { behavior: 'smooth', block: 'center' },
    buttons: [
      {  text: "🔙 Back to Keywords", action: previous, classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" },
    ]
  },
  {
    title: "➕ Add a Document",
    text: "Ready to add a new resource? Click here or use the 'Next' button to open the form and upload a new document! ✍️",
    attachTo: { element: '#add-document-btn', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    advanceOn: { selector: '#add-document-btn', event: 'click' },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      {
        action() { document.querySelector('#add-document-btn').click(); },
        text: 'Next',
        classes: "shepherd-next"
      }
    ]
  },
  {
    title: "📝 Document Details Form",
    text: "Upload your file and fill in its details here. When you’re finished, just hit save. You can close this form anytime using the 'X' or the cancel button. 💾",
    attachTo: { element: '#new-document-drawer', on: 'left' },
    scrollTo: true,
    buttons: [
      { 
        text: "🔙 Back",
        classes: "shepherd-back",
        action() {
          document.querySelector('[data-action="click->drawer#close"]').click();
          setTimeout(() => this.back(), 150);
        }
      },
      { 
        text: "Next ➡️",
        classes: "shepherd-next",
        action() {
          document.querySelector('[data-action="click->drawer#close"]').click();
          setTimeout(() => this.next(), 150);
        }
      },
    ]
  },
  {
    title: "Ready to Build? 🏗️",
    text: "Once you upload your resources, they'll appear right here. You're all set to start building your document library!",
    attachTo: { element: '#empty-documents-body', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
    buttons: [
      {
        action() { 
          document.querySelector('#add-document-btn').click();
          setTimeout(() => this.back(), 150);
        },
        text: 'Back',
        classes: "shepherd-back"
      },
      { text: "Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📄 Document Overview",
    text: "Each row provides a quick summary of a document.",
    attachTo: { element: '#documents-table #document-table-body tr', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
    buttons: [
      {
        action() { 
          document.querySelector('#add-document-btn').click();
          setTimeout(() => this.back(), 150);
        },
        text: 'Back',
        classes: "shepherd-back"
      },
      { text: "Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🛠️ Edit or Delete",
    text: "Need to make a change? Use these buttons to edit a document's details using the same form as before, or to remove it from the lesson entirely. 🗑️",
    attachTo: { element: '#documents-table #document-table-body tr td:last-child', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
  },
  {
    title: "📥 Download a File",
    text: "To preview or save a file to your computer, simply click on its filename in this column. It's a great way to double-check your resources!",
    attachTo: { element: '#documents-table #document-table-body tr td:nth-child(3)', on: 'top' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
  },
]