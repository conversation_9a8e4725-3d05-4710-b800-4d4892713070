const isTableEmpty = () => {
  return !!document.querySelector('#keywords-container tbody td[colspan="5"]');
};

export const keywordsSteps = (previous) => [
  {
    title: "🔑 Keyword Management",
    text: "This is your hub for all the keywords in this lesson. You can add new terms, edit existing ones, and reorder them to match the lesson flow 🧠.",
    attachTo: { element: '#keywords-container', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    buttons: [
      {  text: "🔙 Back to Quiz", action: previous, classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" },
    ]
  },
  {
    title: "➕ Add a New Keyword",
    text: "Ready to expand your lesson's vocabulary? Click here or use the 'Next' button to open the form and add a new key term! ✍️",
    attachTo: { element: '#add-keyword-btn', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    advanceOn: { selector: '#add-keyword-btn', event: 'click' },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      {
        action() { document.querySelector('#add-keyword-btn').click(); },
        text: 'Next',
        classes: "shepherd-next"
      }
    ]
  },
  {
    title: "📝 Keyword Details Form",
    text: "Fill in the details for your new keyword here. When you’re finished, just hit save. You can close this form anytime using the 'X' or the cancel button. 💾",
    attachTo: { element: '#new-keyword-drawer', on: 'left' },
    scrollTo: true,
    buttons: [
      { 
        text: "🔙 Back",
        classes: "shepherd-back",
        action() {
          document.querySelector('[data-action="click->drawer#close"]').click();
          setTimeout(() => this.back(), 150);
        }
      },
      { 
        text: "Next ➡️",
        classes: "shepherd-next",
        action() {
          document.querySelector('[data-action="click->drawer#close"]').click();
          setTimeout(() => this.next(), 150);
        }
      },
    ]
  },
  {
    title: '📋 Your Keyword List',
    text: "This is where your keywords will appear once you start adding them.",
    attachTo: { element: '#keywords-container tbody tr', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showOn: () => isTableEmpty(),
  },
  {
    title: "🚥 Keyword Status",
    text: "Each row provides a quick summary of a keyword. We'll highlight any issues here, like a missing slide, so you can fix them in a snap! ⚡",
    attachTo: { element: '#keywords-container tbody tr', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
    showOn: () => !isTableEmpty(),
    buttons: [
      {
        action() { 
          document.querySelector('#add-keyword-btn').click();
          setTimeout(() => this.back(), 150);
        },
        text: 'Back',
        classes: "shepherd-back"
      },
      { text: "Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🛠️ Edit or Delete",
    text: "Need to make a change? Use these buttons to edit a keyword using the same form as before, or to remove it from the lesson entirely. 🗑️",
    attachTo: { element: '#keywords-container tbody tr td:last-child', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
    showOn: () => !isTableEmpty(),
  },
  {
    title: "↕️ Change the Order",
    text: "Click and drag this handle to reorder your keywords. It's a great way to structure the lesson exactly how you want it!",
    attachTo: { element: '#keywords-container tbody tr td:first-child', on: 'left' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
    showOn: () => !isTableEmpty(),
  },
]