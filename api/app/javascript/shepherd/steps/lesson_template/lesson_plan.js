export const lessonPlanSteps = (previous) => [
  {
    title: "📝 Choose Your Lesson Plan Type",
    text: "This is the most important setting on the page. It lets you switch between a standard layout (KS1-3) and an advanced layout (KS4), which includes extra fields.",
    attachTo: { element: '#lesson-plan-type-select', on: 'bottom' },
    scrollTo: { behavior: 'smooth', block: 'center' },
    buttons: [
      { text: "🔙 Back to Details", action: previous, classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" },
    ]
  },
  {
    title: "🧱 Core Lesson Details",
    text: "These fields are the foundation of your lesson plan. They are included in both the KS1-3 and KS4 layouts, covering the core intent and implementation.",
    attachTo: { element: '#common-fields', on: 'top' },
    scrollTo: { behavior: 'smooth', block: 'center' },
  },
  {
    title: "🔄 Switch to the Advanced Layout",
    text: "Now, let's see the advanced fields. Please select 'KS4' from the dropdown menu to continue the tour. <br/><br/><strong>Note:</strong> A pop-up will ask if you want to add default content. You can choose either option!",
    attachTo: { element: '#lesson-plan-type-select', on: 'bottom' },
    scrollTo: true,
    advanceOn: { selector: '#lesson-plan-type-select', event: 'change' },
  },
  {
    title: "🔬 Advanced KS4 Fields",
    text: "Great! As you can see, selecting the KS4 layout reveals these extra fields, which are designed for more detailed planning for older students.",
    attachTo: { element: '#ks4-only-fields', on: 'top' },
    scrollTo: true,
    showIfAttachToExists: true,
  },
  {
    title: "💾 Save Your Masterpiece",
    text: "Once you've filled everything out, click this button to save your lesson plan. Don't worry, you can always come back and edit it later!",
    attachTo: { element: 'button[type="submit"]', on: 'top' },
    scrollTo: true,
  }
];