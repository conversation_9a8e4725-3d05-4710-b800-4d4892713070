export const quizSteps = (previous) => [
  {
    title: "🚀 Welcome to the Quiz Builder!",
    text: "This is where you can create and manage all the questions for your quiz.",
    scrollTo: { behavior: 'smooth', block: 'center' },
    buttons: [
      { text: "🔙 Back to Presentation", action: previous, classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" },
    ]
  },
  {
    title: "🏷️ Give It a Name",
    text: "First, make sure your quiz has a memorable title. Without a title, you will not be able to add any questions!",
    scrollTo: { behavior: 'smooth', block: 'center' },
    attachTo: { element: '#quiz-title', on: 'top' },
  },
  {
    title: "🧩 The Two-Column Layout",
    text: "The builder is split into two main areas. On the left is your Questions Sidebar for navigation, and on the right is the Editor Area where you'll build each question.",
    attachTo: { element: '.grid.grid-cols-1', on: 'top' },
    when: {
      'before-show': function() {
        const titleInput = document.getElementById("quiz-title");
        if (titleInput && titleInput.value.trim() === '') {
          titleInput.value = 'New Quiz';
          const event = new Event('input', { bubbles: true });
          titleInput.dispatchEvent(event);
        }
      }
    }
  },
  {
    title: "🕹️ Your Command Center",
    text: "This sidebar is your main control panel. Your questions will appear in a list here. You can click to jump between them or drag to reorder.",
    attachTo: { element: '.sticky.top-4', on: 'right' },
  },
  {
    title: "☝️ Your First Question",
    text: "Ready to begin? Click the 'Add Question' button. A dialog will pop up, letting you choose from several powerful question types. Let's do that now!",
    attachTo: { element: '#add-question-btn', on: 'right' },
    advanceOn: { selector: '#add-question-btn', event: 'click' },
    showOn: function() {
      const editorList = document.querySelector('#question-editor-list');
      return editorList && editorList.childElementCount == 0;
    }
  },
  {
    title: "➕ Add Another Question",
    text: "You can add as many questions as you need. Simply click the 'Add Question' button to open the type selection dialog.",
    attachTo: { element: '#add-question-btn', on: 'right' },
    advanceOn: { selector: '#add-question-btn', event: 'click' },
    showOn: function() {
      const editorList = document.querySelector('#question-editor-list');
      return editorList && editorList.childElementCount > 0;
    }
  },
  {
    title: "📝 The Question Editor",
    text: "This is your main workspace. In this area, you'll write your question prompt, add answer options, and set the correct answers.",
    attachTo: { element: '#question-editor-list', on: 'left' },
  },
  {
    title: "✅ Setting Up Answers",
    text: "Use the 'Add Option' button to create answers. You can add as many as you need. Don't forget to check the box for each correct answer!",
    attachTo: { element: '#question-editor-list [name="add-option"]', on: 'bottom' },
    scrollTo: true,
    showIfAttachToExists: true
  },
  {
    title: "📋 It's in the List!",
    text: "Your new question has automatically appeared in the sidebar list. As you add more, you can click these items to quickly navigate your quiz.",
    attachTo: { element: '#sortable-question-list > div[data-question-id]', on: 'right' },
    scrollTo: true,
    showIfAttachToExists: true
  },
  {
    title: "💾 Don't Forget to Save!",
    text: "This is the most important button! Click 'Save Quiz' frequently to make sure all your hard work is saved. You'll see a confirmation message appear below it.",
    attachTo: { element: '#save-button', on: 'right' },
  },

  {
    title: "🎉 You're a Quiz Master!",
    text: "You've learned the basics of the Quiz Builder. Now you can add more questions, reorder them, and build an amazing quiz for your students!",
  }
]