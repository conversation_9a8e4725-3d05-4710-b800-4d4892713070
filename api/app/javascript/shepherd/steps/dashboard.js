// Steps for <PERSON>.js tour
      //  steps should have:
        //  id (optional) - id used internally for <PERSON> steps
        //  title - string that appears in the header of tour card
        //  text - string that appear in body of tour card
        //  attachTo: element - css selector of element to attach tour card to
        //             on      - where the card is placed relative to the element

export const dashboardSteps = [
    {
      id: "step-details",
      title: "Your School Hub!",
      text: "Welcome to your school's command centre! Here you'll find all the important details about your school's subscription and contacts. Keep everything up-to-date for a smooth experience!",
      attachTo: { element: "#details-container", on: "bottom" },
    },
    {
      id: "step-quick-links",
      title: "Speedy Shortcuts!",
      text: "Zoom around your dashboard with these handy Quick Links! You can even customise them to put your most-used features right at your fingertips. How cool is that?",
      attachTo: { element: "#quick-links-container", on: "bottom" },
    },
    {
      id: "step-pupil-login",
      title: "Pupil Power-Up!",
      text: "Want to see what your pupils see? Select a pupil here to jump into their dashboard and explore their learning world. It's like having a secret spyglass!",
      attachTo: { element: "#pupil-login-container", on: "left" },
    },
    {
      id: "step-ai-tools",
      title: "Unleash the AI Magic! ✨",
      text: "Ready to be amazed? Our award-winning AI tools are here to help you build incredible lessons and explore exciting career paths. Give them a whirl!",
      attachTo: { element: "#ai-tools-container", on: "right" },
    },
    {
      id: "step-lessons",
      title: "Lesson Central! 📚",
      text: "This is your mission control for all things lessons! See what's coming up, plan new adventures, and keep your teaching organised and awesome.",
      attachTo: { element: "#lessons-container", on: "top" },
    },
    {
      id: "step-pupil-leaderboards",
      title: "Climb the Ranks! 🏆",
      text: "Check out the Pupil Leaderboards to see who's acing their learning! A little friendly competition can be super motivating. See how your school and pupils stack up globally!",
      attachTo: { element: "#pupil-leaderboards-container", on: "top" },
    },
    {
      id: "step-de-updates",
      title: "Hot off the Press! 📰",
      text: "Stay in the loop with the latest news, tips, and updates directly from the Developing Experts team. We're always working to make things better for you!",
      attachTo: { element: "#de-updates-container", on: "left" },
    },
    {
      id: "step-de-socials",
      title: "Get Social with DE! 💬",
      text: "Connect with us on social media! See what we're up to, share your successes, and be part of our amazing community. Don't miss out on the fun!",
      attachTo: { element: "#de-socials-container", on: "right" },
      scrollTo: { behavior: 'smooth', block: 'start' },
    }
  ];