import { tourPreviousPage, tourNextPage } from "../helpers";

export const manageTeachersSteps = (data) => [
  {
    title: "👩‍🏫 Teacher List",
    text: "Here's where you'll find all the teachers currently linked to your school. It’s your staffroom overview! 🗂️",
    attachTo: { element: "#table-container", on: "top" },
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
  {
    title: "🔎 Find a Teacher",
    text: "Looking for someone specific? Just type their name here to find them quickly and easily 👀",
    attachTo: { element: "#search-container", on: "bottom" },
  },
  {
    title: "➕ Add a New Teacher",
    text: "Click here to add a new teacher to your school. It just takes a few quick steps to get them set up! ✍️",
    attachTo: { element: "#add-teacher-btn", on: "bottom" },
  },
  {
    title: "📭 No Teachers Yet?",
    text: "It looks like there aren’t any teachers listed just yet. Use the button above to add your first! 🚀",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: "complete", classes: "shepherd-next" }
    ],
    showOn: () => {
      const element = document.querySelector('#table-container tbody tr:first-child');
      return !element;
    },
  },
  {
    title: "👀 Teacher Info at a Glance",
    text: "Each row shows key info like their name, email, and when they last logged in. Nice and simple! 📋",
    attachTo: { element: "tbody tr:first-child", on: "bottom" },
  },
  {
    title: "🛠️ Edit a Teacher",
    text: "Click on a teacher’s name to update their profile, classes, or account settings. Easy peasy! 🧑‍💻",
    attachTo: { element: "tbody tr:first-child td:first-child", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✏️ Go to Edit Page", action: (() => tourNextPage(`/school/teachers/${data.teacherId}/edit`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "📝 Edit Basic Details",
    text: "Here you can update the teacher’s name, email and other key details. Keep things tidy and up to date! 📮",
    attachTo: { element: "#details-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Teachers", action: (() => tourPreviousPage('/school/teachers')), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🧭 Navigate the Profile",
    text: "Use these handy tabs to switch between different parts of the teacher’s profile – details, classes, and more 📑",
    attachTo: { element: "#tabs-container", on: "bottom" }
  },
  {
    title: "🛠️ Edit Tab",
    text: "You're in the 'Edit' tab right now – perfect for updating their name, email, and basic account info 🧾",
    attachTo: { element: "#tabs-container .group:first-child", on: "bottom" }
  },
  {
    title: "🏫 View Assigned Classes",
    text: "Click the 'Classes' tab to see and update which classes this teacher is linked to 🧑‍🏫",
    attachTo: { element: "#tabs-container .group:nth-child(2)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "📘 Go to Classes", action: (() => tourNextPage(`/school/teachers/${data.teacherId}/classes`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "📚 Assigned Classes",
    text: "This table shows every class assigned to the teacher. Use the buttons to update any class links 🔁",
    attachTo: { element: "#table-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Edit", action: (() => tourPreviousPage(`/school/teachers/${data.teacherId}/edit`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },{
    title: "🧐 No Classes Yet?",
    text: "It looks like this teacher doesn’t have any classes assigned yet. You can do this from the 'Manage Classes' page 🗂️",
    showOn: () => {
      const element = document.querySelector('#table-container tbody tr:first-child');
      return !element;
    },
  },
  {
    title: "✏️ Edit Classes",
    text: "Use the action buttons beside each class to update the class info. Simple and flexible! 🔄",
    attachTo: { element: "#table-container tbody tr:first-child td:last-child", on: "left" },
    showIfAttachToExists: true
  },
  {
    title: "🔐 Password Tab",
    text: "Switch to the 'Password' tab if you need to update or reset a teacher’s login credentials 🔑",
    attachTo: { element: "#tabs-container .group:nth-child(3)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "🔐 Go to Password", action: (() => tourNextPage(`/school/teachers/${data.teacherId}/password`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "🛠️ Manage Password",
    text: "You can reset a password manually or send a reset link via email – whichever works best 📧",
    attachTo: { element: "#details-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Classes", action: (() => tourPreviousPage(`/school/teachers/${data.teacherId}/classes`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },  
  {
    title: "✍️ Manual Password Reset",
    text: "Type in a new password directly here. Perfect for quick updates during staff training days 👨‍🏫",
    attachTo: { element: "#password-first-option", on: "bottom" },
  },
  {
    title: "📩 Send Reset Email",
    text: "Prefer to let the teacher handle it? Send them a password reset link instead 📬",
    attachTo: { element: "#password-second-option", on: "bottom" },
  },
  {
    title: "⚠️ Account Actions",
    text: "Use the 'Actions' tab if you need to permanently delete a teacher’s account (only do this if you're sure!) 🗑️",
    attachTo: { element: "#tabs-container .group:nth-child(4)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "🚨 Go to Actions", action: (() => tourNextPage(`/school/teachers/${data.teacherId}/actions`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "🧨 Delete Teacher",
    text: "This page lets you permanently delete a teacher's profile. Heads up: this can’t be undone! ⚠️",
    attachTo: { element: "#details-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Password", action: (() => tourPreviousPage(`/school/teachers/${data.teacherId}/password`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🎉 Tour Complete!",
    text: "That’s everything! You’ve completed the Manage Teachers tour. Come back anytime if you need a refresher 😊",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: (() => {
        sessionStorage.removeItem("shepherd_tour");
        tourNextPage('/school/teachers')
      }), classes: "shepherd-next" }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
];