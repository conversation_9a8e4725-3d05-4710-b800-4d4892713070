import { tourPreviousPage, tourNextPage } from "../helpers";

export const importXlsSteps = (data) => [
  {
    title: "📥 Importing School Data",
    text: "Welcome! This page is where you can import school data using a handy Excel spreadsheet 🧑‍🏫",
    attachTo: { element: "#data-container", on: "top" },
  },
  {
    title: "🔍 Search Imported Records",
    text: "Use this search bar to quickly find a specific upload or entry – no scrolling needed! 🔎",
    attachTo: { element: "#search-container", on: "top" },
  },
  {
    title: "📄 Download Template",
    text: "Click here to download the spreadsheet template. Make sure to follow the format so everything uploads smoothly ✅",
    attachTo: { element: "#download-template-btn", on: "top" },
  },
  {
    title: "📤 Upload Spreadsheet",
    text: "When your spreadsheet is ready, click here to upload it. Or continue the tour to learn how the process works!",
    attachTo: { element: "#upload-spreadsheet-btn", on: "top" },
    showIfAttachToExists: true
  },
  {
    title: "📝 Upload Form",
    text: "This is the upload form. Once you select and upload your file, we’ll start processing your data automatically 📊",
    attachTo: { element: "#upload-container", on: "top" },
    buttons: [
      { text: "🔙 Back to Index Page", action: (() => tourPreviousPage(`/school/data-imports/xls`)), classes: "shepherd-back" },
    ],
    showIfAttachToExists: true
  },
  {
    title: "📋 Imported Data Table",
    text: "Here you’ll see a list of all your uploaded spreadsheets. Each row shows the status and summary 📚",
    attachTo: { element: "#data-container table", on: "top" },
    showIfAttachToExists: true    
  },
  {
    title: "🔍 View Import Details",
    text: "Click a row or use the three-dot menu to see full details or make changes to that specific import 📂",
    attachTo: { element: "#data-container table tbody", on: "top" },
    extraHighlights: ['.z-10'],
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" }
    ],
    showIfAttachToExists: true
  },
  {
    title: "📊 Import Overview",
    text: "This page shows a full breakdown of your uploaded spreadsheet – including any validation checks and student info 🧾",
    attachTo: { element: "#import-container", on: "top" },
    buttons: [
      { text: "🔙 Back to Index Page", action: (() => tourPreviousPage(`/school/data-imports/xls`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📘 Year Group Tabs",
    text: "Switch between year groups using these tabs to focus on one cohort at a time 🎓",
    attachTo: { element: ".group-tabs", on: "top" },
  },
  {
    title: "🔁 Reupload Spreadsheet",
    text: "Need to fix something? Use this section to upload a corrected version of your spreadsheet ✏️",
    attachTo: { element: "#reupload-container", on: "top" },
    showIfAttachToExists: true
  },
  {
    title: "✅ Validated Data Table",
    text: "This table shows the pupil data that passed all checks and was successfully imported 🎉",
    attachTo: { element: "#complete-container table", on: "top" },
    showIfAttachToExists: true
  },
  {
    title: "⚠️ Incomplete or Invalid Data",
    text: "Here you’ll find entries that need fixing. Fields that need attention are highlighted, and you can edit them directly 🧩",
    attachTo: { element: "#incomplete-container table", on: "top" },
    extraHighlights: ['.grid', '.admin-btn'],
    showIfAttachToExists: true
  },
  {
    title: "🎉 Tour Complete!",
    text: "That’s it! You’ve completed the XLS Import tour. You can always restart it from the imports page if you need a refresher 📂",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { 
        text: "✅ Finish Tour", 
        action: (() => {
          sessionStorage.removeItem("shepherd_tour");
          tourNextPage(`/school/data-imports/xls`);
        }), 
        classes: "shepherd-next" 
      }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
];