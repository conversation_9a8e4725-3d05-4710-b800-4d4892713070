import { tourPreviousPage, tourNextPage } from "../helpers";

const view_all = "?all=true"
export const managePupilsSteps = (data) => [
  {
    title: "👩‍🎓 Pupil Overview",
    text: "Here you’ll find all the pupils you have access to. Use this table to keep track of their info and learning progress 📈",
    attachTo: { element: "#table-container", on: "bottom" },
  },
  {
    title: "🔍 Search Pupils",
    text: "Looking for someone? Start typing a pupil's name to find them in a flash ⚡",
    attachTo: { element: "#search-container", on: "bottom" },
  },
  {
    title: "🛠️ View Options & Login Codes",
    text: "Switch between your classes or the whole school view. You can also download login codes to make pupil access a breeze 🧾",
    attachTo: { element: "#pupil-buttons-container", on: "bottom" },
  },
  {
    title: "📭 No Pupils Yet?",
    text: "It looks like there aren’t any pupils added yet. Ask your school administrator to get things set up for you 🧑‍💼",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ End Tour", action: "complete", classes: "shepherd-next" }
    ],
    showOn: () => {
      const element = document.querySelector('#table-container tbody tr:first-child');
      return !element;
    },
  },
  {
    title: "👁️ Pupil Details",
    text: "Each row gives you a snapshot of the pupil: name, login code, and activity status – all at a glance 👓",
    attachTo: { element: "tbody tr:first-child", on: "bottom" },
  },
  {
    title: "✏️ Edit a Pupil",
    text: "Click on a pupil’s name to view their profile and make changes to their info or learning setup 🛠️",
    attachTo: { element: "tbody tr:first-child td:first-child", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "👤 To Pupil Edit Page", action: (() => tourNextPage(`/school/pupils/${data.pupilId}/edit`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "📋 Edit Pupil Info",
    text: "Change the pupil’s name, assign them to a class, and keep their details up to date 🧒👧",
    attachTo: { element: "#details-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Pupils Page", action: (() => tourPreviousPage(`/school/pupils${data?.adminView ? view_all : ''}`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🧭 Profile Tabs",
    text: "Use these tabs to explore different sections of the pupil’s profile – including classes, welcome letter, and more 📑",
    attachTo: { element: "#tabs-container", on: "bottom" }
  },
  {
    title: "✍️ Current Tab",
    text: "You're in the 'Edit' tab right now, where you can change the pupil’s basic details 🧾",
    attachTo: { element: "#tabs-container .group:first-child", on: "bottom" }
  },
  {
    title: "🏫 Go to Classes",
    text: "Hop over to the 'Classes' tab to view or update which classes this pupil is in 🎒",
    attachTo: { element: "#tabs-container .group:nth-child(2)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "➡️ To Classes Page", action: (() => tourNextPage(`/school/pupils/${data.pupilId}/classes`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "📚 Class Management",
    text: "This table lists all the classes this pupil is part of. Use it to make changes or tidy things up 🔧",
    attachTo: { element: "#table-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Edit Page", action: (() => tourPreviousPage(`/school/pupils/${data.pupilId}/edit`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "➕ Add or Edit Classes",
    text: "Use the buttons next to each class to make changes, assign new ones, or remove links 🎛️",
    attachTo: { element: "#table-container tbody tr:first-child td:last-child", on: "left" },
    showIfAttachToExists: true
  },
  {
    title: "📄 Go to Letter",
    text: "Switch to the 'Letter' tab to view the welcome letter for this pupil – it includes everything they need to get started ✉️",
    attachTo: { element: "#tabs-container .group:nth-child(3)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "📨 To Letter Page", action: (() => tourNextPage(`/school/pupils/${data.pupilId}/letter`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "💌 Pupil Welcome Letter",
    text: "This is the welcome letter sent to pupils – it includes login info and helpful instructions to get them learning quickly 📘",
    attachTo: { element: "#letter-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Classes Page", action: (() => tourPreviousPage(`/school/pupils/${data.pupilId}/classes`)), classes: "shepherd-back" },
      { text: "✅ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🎉 Tour Complete!",
    text: "That’s the end of your pupil management tour! You can restart it anytime from the pupil dashboard 🧭",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: (() => {
        sessionStorage.removeItem("shepherd_tour");
        tourNextPage(`/school/pupils${data?.adminView ? view_all : ''}`)
      }), classes: "shepherd-next" }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
];