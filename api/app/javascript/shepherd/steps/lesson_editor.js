import { tourPreviousPage, tourNextPage } from "../helpers";
import { lessonPlanSteps } from "./lesson_template/lesson_plan";
import { presentationSteps } from "./lesson_template/presentation";
import { quizSteps } from "./lesson_template/quiz";
import { keywordsSteps } from "./lesson_template/keywords";
import { documentsSteps } from "./lesson_template/documents";
import { deleteSteps } from "./lesson_template/delete";

export const lessonEditorSteps = (data, tour) => {
  const indexSteps = [
    {
      title: "📥 Lesson Editor",
      text: "Welcome! This page is where you can create and manage custom lessons 🧑‍🏫",
    },
    {
      title: "☝️ Your First Lesson",
      text: "Click the 'Create New Lesson' button to begin." + 
            "<br><br>Please note that creating a new lesson will end this tour. Once you are finished, you can return to this page to see and edit your newly created lesson.",
      attachTo: { element: "#create-lesson-btn", on: "left" },
      showOn: () => {
        return !document.querySelector('#recently-edited-container div.bg-white');
      },
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      ]
    },
    {
      id: "add-or-edit-lesson",
      title: "🚀 Continue to the Lesson Editor",
      text: "To continue with the editor tour, please click on any existing lesson card below." + 
            "<br><br>Alternatively, you can create a brand new lesson, but please note: this will end the current tour. You can also use the 'Skip' button to learn about folders instead.",
      attachTo: { element: "#recently-edited-container", on: "top" },
      extraHighlights: ["#create-lesson-btn"],
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        {
          text: 'Skip to Folder Organization',
          classes: 'shepherd-button-secondary',
          action: function() {
            this.lastStepBeforeSkip = this.getCurrentStep().id;
            this.show('start-folder-tour');
          }
        },
      ]
    }
  ];

  const headerSteps = [
    {
      title: "🎛️ Lesson Editor Hub",
      text: "This is your main dashboard for editing the lesson. From here, you can manage publishing, and navigate to different sections.",
      scrollTo: { behavior: 'smooth', block: 'center' },
      buttons: [
        { 
          text: "🔙 Back to Lessons",
          action: () => tourPreviousPage(`/school/lesson-editing`), 
          classes: "shepherd-back"
        },
        { text: "➡️ Next", action: "next", classes: "shepherd-next" },
      ]
    },
    {
      title: "🙋 Need Help?",
      text: "If you get stuck or want more information on editing lessons, this button provides quick access to relevant help articles.",
      attachTo: { element: 'button[data-help-id="5346"]', on: 'left' },
      showIfAttachToExists: true,
    },
    {
      title: "🚀 Ready to Publish?",
      text: "This section is your pre-flight checklist for making the lesson available to other teachers in your school's community library.",
      attachTo: { element: 'div.bg-secondary-dark-blue', on: 'bottom' },
      scrollTo: { behavior: 'smooth', block: 'center' },
    },
    {
      title: "🚦 Publishing Checklist",
      text: "Before you can publish, any required tasks (❌) must be completed. A green check (✅) means a task is done. The exclamation mark (❗) is for optional tasks.",
      attachTo: { element: '#publishable-tasks-container', on: 'bottom' },
      showIfAttachToExists: true,
    },
    {
      title: "🚀 Ready to Go Live?",
      text: "Once all required tasks on your checklist are complete, the 'Publish Lesson' button here will unlock. After publishing, a 'View Lesson' button will appear so you can see it just like a student would.",
      attachTo: { element: 'div.bg-secondary-dark-blue > div:last-child', on: 'left' },
      showIfAttachToExists: true,
    },
  ];

  const detailsSteps = [
    {
      title: "📝 Edit Lesson Details",
      text: "Welcome to the lesson editor! On this page, you can customize everything about this lesson, from its name and objectives to the skills it covers.",
      attachTo: { element: 'div.bg-white.rounded-xl', on: 'top' },
      scrollTo: { behavior: 'smooth', block: 'center' },
    },
    {
      title: "✏️ Lesson Name",
      text: "This is the main title for the lesson. Keep it clear and descriptive for teachers and pupils.",
      attachTo: { element: 'input#lesson_template_name', on: 'bottom' },
      showIfAttachToExists: true,
    },
    {
      title: "🎯 Learning Objectives",
      text: "Here you can define the key learning goals for this lesson. What should pupils know or be able to do by the end?",
      attachTo: { element: '#objectives-container', on: 'bottom' },
      showIfAttachToExists: true,
    },
    {
      title: "🧪 Scientific Enquiry Types",
      text: "Tag this lesson with the scientific skills it covers. Hover over any title to see a full description before you select it.",
      attachTo: { element: '.grid.grid-cols-1.md\\:grid-cols-2', on: 'bottom' },
      showIfAttachToExists: true,
    },
    {
      title: "🖼️ Update the Lesson Image",
      text: "A great image makes a lesson more engaging! Use the image uploader to select a new cover image for this lesson.",
      attachTo: { element: '.image-field-container', on: 'right' },
      showIfAttachToExists: true,
    },
    {
      title: "✅ Save Your Changes",
      text: "Once you're happy with your edits, click this button to save everything.",
      attachTo: { element: 'button[type="submit"]', on: 'top' },
      showIfAttachToExists: true,
    },
  ];

  const finalStep = {
    title: "🎉 Tour Complete!",
    text: "That’s it! You’ve completed the Lesson Editor tour. You can always restart it from the Lesson Editing page if you need a refresher 📂",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { 
        text: "✅ Finish Tour", 
        action: (() => {
          sessionStorage.removeItem("shepherd_tour");
          tourNextPage(`/school/lesson-editing`);
        }), 
        classes: "shepherd-next" 
      }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  }

  const tabsSteps = (data) => [
    {
      title: "🧭 Navigate the Editor",
      text: "Use these tabs to edit different parts of your lesson, like the details, presentation, and quizzes. Let's take a look at a few.",
      attachTo: { element: 'div.flex.flex-wrap.text-center', on: 'bottom' },
      scrollTo: { behavior: 'smooth', block: 'center' },
      showIfAttachToExists: true,
    },
    {
      title: "📝 You Are Here: Details",
      text: "Currently, you're on the 'Details' tab, where you can edit the lesson's name, objectives, and skills.",
      attachTo: { element: 'div.flex > div.group:nth-child(1) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
    },
    {
      title: "📜 Go to Lesson Plan",
      text: "The next tab is 'Lesson Plan'. Click the button below to go there and continue the tour.",
      attachTo: { element: 'div.flex > div.group:nth-child(2) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        { 
          text: "➡️ To Lesson Plan", 
          action: () => tourNextPage(`/school/lesson-editing/${data.templateId}/show_lesson_plan`), 
          classes: "shepherd-next" 
        },
      ]
    },
    {
      title: "🖥️ Go to Presentation",
      text: "Next up is the 'Presentation' tab, where you can edit the slides for this lesson.",
      attachTo: { element: 'div.flex > div.group:nth-child(3) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        { 
          text: "➡️ To Presentation", 
          action: () => tourNextPage(`/school/lesson-editing/${data.templateId}/show_presentation`), 
          classes: "shepherd-next" 
        },
      ]
    },
    {
      title: "❓ Go to Quiz",
      text: "Let's check out the 'Quiz' tab next. Here you can create and manage questions to assess pupils' understanding.",
      attachTo: { element: 'div.flex > div.group:nth-child(4) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        { 
          text: "➡️ To Quiz", 
          action: () => tourNextPage(`/school/lesson-editing/${data.templateId}/show_quiz`), 
          classes: "shepherd-next" 
        },
      ]
    },
    {
      title: "❓ Go to Keywords",
      text: "Let's check out the 'Keywords' tab next.",
      attachTo: { element: 'div.flex > div.group:nth-child(5) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        { 
          text: "➡️ To Keywords", 
          action: () => tourNextPage(`/school/lesson-editing/${data.templateId}/show_keywords`), 
          classes: "shepherd-next" 
        },
      ]
    },
    {
      title: "❓ Go to Documents",
      text: "Let's check out the 'Documents' tab next.",
      attachTo: { element: 'div.flex > div.group:nth-child(6) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        { 
          text: "➡️ To Documents", 
          action: () => tourNextPage(`/school/lesson-editing/${data.templateId}/show_documents`), 
          classes: "shepherd-next" 
        },
      ]
    },
    {
      title: "❓ Go to Delete",
      text: "Let's check out the 'Delete' tab next.",
      attachTo: { element: 'div.flex > div.group:nth-child(7) a.tab-link', on: 'bottom' },
      showIfAttachToExists: true,
      buttons: [
        { text: "🔙 Back", action: "back", classes: "shepherd-back" },
        { 
          text: "➡️ To Delete", 
          action: () => tourNextPage(`/school/lesson-editing/${data.templateId}/show_delete`), 
          classes: "shepherd-next" 
        },
      ]
    },
  ]

  const tabs = tabsSteps(data);

  const backToIndexStep = {
    title: "🚀 Ready to Organize?",
    text: "Great work on the editor! Click below to return to your main lesson library, where you can see all your lessons and manage them in folders.",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { 
        text: "➡️ To Folders", 
        action: () => tourNextPage(`/school/lesson-editing`), 
        classes: "shepherd-next" 
      },
    ]
  }

  const folderSteps = [
    {
    id: 'start-folder-tour',
    title: '📚 Your Lesson Library',
    text: "Welcome! This is where you can organize all of your lessons into folders. Let's take a quick look around.",
    attachTo: { element: '#folder-index', on: 'bottom' },
    buttons: [
      {
        text: '🔙 Back',
        classes: 'shepherd-back',
        action: function() {
          const tour = this; 

          if (tour.lastStepBeforeSkip) {
            const previousStepId = tour.lastStepBeforeSkip;
            delete tour.lastStepBeforeSkip; 
            tour.show(previousStepId);
          } else {
            tour.back();
          }
        }
      },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" },
    ]
  },
  {
    title: '🗂️ Stay Organized with Folders',
    text: 'Your lessons can be grouped into folders. You can click on a folder to expand or collapse it and see the lessons inside.',
    attachTo: { element: '.folder-drop-zone[data-folder-id]:not([data-folder-id=""])', on: 'bottom' },
    showIfAttachToExists: true
  },
  {
    title: '📁 Create a New Folder',
    text: "Click this button anytime you want to create a new folder to better organize your lessons.",
    attachTo: { element: '[name="new-folder-btn"]', on: 'left' },
  },
  {
    title: '✅ Select Your Lessons',
    text: "Each lesson is listed in a row like this. Use the checkbox on the left to select one or more lessons to perform actions on them.",
    attachTo: { element: '.folder-row', on: 'bottom' },
    showIfAttachToExists: true
  },
    {
    title: '🚀 Take Action on Selected Lessons',
    text: "After you select lessons with the checkbox, new buttons will appear here, allowing you to move them to a folder or remove them from their current folder.",
    attachTo: { element: '#folder-index .flex.gap-4.flex-wrap', on: 'left' },
  },
  {
    title: '✨ Drag & Drop Power',
    text: "Pro-tip: You can also organize your lessons by clicking and dragging a row directly into a different folder!",
    attachTo: { element: '.folder-row', on: 'right' },
    showIfAttachToExists: true
  },
  {
    title: '📥 The "Loose" Lessons Area',
    text: "Any lessons that aren't in a folder will appear here. You can drag lessons from a folder and drop them here to 'un-file' them.",
    attachTo: { element: '#loose-folder-container', on: 'top' },
    showIfAttachToExists: true
  }
  ]

  return [
    ...indexSteps,
    ...headerSteps,
    ...detailsSteps,
    tabs[0],
    tabs[1],
    tabs[2],
    ...lessonPlanSteps(() => tourPreviousPage(`/school/lesson-editing/${data.templateId}/show_details`)),
    tabs[3],
    ...presentationSteps(() => tourPreviousPage(`/school/lesson-editing/${data.templateId}/show_lesson_plan`)),
    tabs[4],
    ...quizSteps(() => tourPreviousPage(`/school/lesson-editing/${data.templateId}/show_presentation`)),
    tabs[5],
    ...keywordsSteps(() => tourPreviousPage(`/school/lesson-editing/${data.templateId}/show_quiz`)),
    tabs[6],
    ...documentsSteps(() => tourPreviousPage(`/school/lesson-editing/${data.templateId}/show_keywords`)),
    tabs[7],
    ...deleteSteps(() => tourPreviousPage(`/school/lesson-editing/${data.templateId}/show_documents`)),
    backToIndexStep,
    ...folderSteps,
    finalStep
  ]
}