import { tourPreviousPage, tourNextPage } from "../helpers";

const view_all = "?all=true"
export const manageClassesSteps = (data) => [
  {
    title: "🏫 Your Class Overview",
    text: "This table shows all the classes at your school. Use it to view, open or manage each one with ease ✨",
    attachTo: { element: "#table-container", on: "bottom" },
    scrollTo: { behavior: 'smooth', block: 'top' },
  },
  {
    title: "🔍 Find a Class",
    text: "Use this search bar to quickly find a class by name – super handy when your list gets long 📚",
    attachTo: { element: "#search-container", on: "bottom" },
  },
  {
    title: "🛠️ Manage Class Options",
    text: "Use these buttons to add new classes or switch views between just your classes or the whole school. Tailor things to suit your role 🎛️",
    attachTo: { element: "#class-buttons-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ],
    showOn: () => {
      const element = document.querySelector('#class-buttons-container');
      if (!element) { return false };
      return element.childNodes.length > 1;
    },
  },  
  {
    title: "📭 No Classes Yet?",
    text: "Looks like there aren’t any classes set up yet. Please contact your school admin to get things started 🧑‍💼",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ End Tour", action: "complete", classes: "shepherd-next" }
    ],
    showOn: () => {
      const element = document.querySelector('#table-container tbody tr:first-child');
      return !element;
    },
  },
  {
    title: "📝 Class Summary",
    text: "Each row gives you a quick overview – class name, assigned teachers, and how many pupils are enrolled 👩‍🏫👨‍🎓",
    attachTo: { element: "#table-container tbody tr:first-child", on: "bottom" },
  },
  {
    title: "🔍 View Class Details",
    text: "Click on the class name to dive into its details – see pupils, lessons and more 🎒",
    attachTo: { element: "#forms-table", on: "bottom" },
    extraHighlights: ['.z-10'],
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "➡️ To Class Page", action: (() => tourNextPage(`/school/classes/${data.formId}/pupils`)), classes: "shepherd-next" }
    ],
    showIfAttachToExists: true
  },
  {
    title: "👩‍🎓 Pupils in This Class",
    text: "This table shows all the pupils currently assigned to this class. You’re in the right place to manage them 🎯",
    attachTo: { element: "#table-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Classes Page", action: (() => tourPreviousPage(`/school/classes${data?.adminView ? view_all : ''}`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📭 No Pupils Yet?",
    text: "This class doesn’t have any pupils yet. Your school admin can help get them added 👥",
    showOn: () => {
      const element = document.querySelector('#table-container tbody tr:first-child');
      return !element;
    },
  },
  {
    title: "👁️ Pupil Overview",
    text: "Each row here gives you the key info about a pupil in this class – quick and clear ✅",
    attachTo: { element: "#table-container tbody tr:first-child", on: "bottom" },
    showIfAttachToExists: true
  },
  {
    title: "✏️ Edit Pupil Details",
    text: "Click a pupil’s name to update their details or see their learning info 🧒📘",
    attachTo: { element: "#table-container tbody tr:first-child td:first-child", on: "bottom" },
    showIfAttachToExists: true
  },
  {
    title: "⚙️ Pupil Actions Menu",
    text: "Use this menu to edit, remove, or update pupils in the class – simple and flexible 🎛️",
    attachTo: { element: "#table-container tbody tr:first-child td:nth-child(6)", on: "bottom" },
    showIfAttachToExists: true
  },
  {
    title: "🧭 Navigating Tabs",
    text: "Use the tabs to move between class views – Pupils, Lessons, and Edit Details. Easy to switch between! 🔄",
    attachTo: { element: "#tabs-container div:first-child", on: "bottom" },
    showIfAttachToExists: true
  },
  {
    title: "👨‍🏫 Current View: Pupils",
    text: "You’re in the Pupils tab right now. Use the other tabs to explore lessons or make changes to the class 📝",
    attachTo: { element: "#tabs-container .group:first-child", on: "bottom" },
  },
  {
    title: "📚 Go to Lessons",
    text: "Switch to the Lessons tab to view and manage all the lessons assigned to this class 📆",
    attachTo: { element: "#tabs-container .group:nth-child(2)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "➡️ To Lessons Page", action: (() => tourNextPage(`/school/classes/${data.formId}/lessons`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "📖 Class Lessons",
    text: "This section shows every lesson assigned to this class. You can view, change or add more with just a few clicks 🧠",
    attachTo: { element: "#table-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Pupils Page", action: (() => tourPreviousPage(`/school/classes/${data.formId}/pupils`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "➕ Add More Lessons",
    text: "Want to add even more lessons to this class? Use this button to open the lesson adder and find content from any curriculum.",
    attachTo: { element: "#addLessonsBtn", on: "left" },
    showIfAttachToExists: true,
    showOn: () => {
      return document.querySelector('table tbody tr');
    },
  },
  {
    title: "🆕 No Lessons Yet?",
    text: "This class doesn’t have any lessons yet. Click the 'Add lessons to class' button to get started ➕",
    attachTo: { element: "#addLessonsBtn", on: "left" },
    showIfAttachToExists: true,
    showOn: () => {
      return !document.querySelector('table tbody tr');
    },
  },
  {
    title: "📚 Add Lessons to a Class",
    text: "You're on the lesson adder page. From here, you can browse entire curriculums and add lessons to your class in just a few clicks.",
    attachTo: { element: "#add-to-form-container", on: "bottom" },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showIfAttachToExists: true,
  },
  {
    title: "1️⃣ Select a Class",
    text: "First, choose the class you want to add lessons to. If you came from a class page, this might already be selected for you.",
    attachTo: { element: "#forms-select", on: "bottom" },
    showIfAttachToExists: true,
  },
  {
    title: "2️⃣ Choose a Curriculum",
    text: "Next, pick a curriculum. This will filter the subjects available in the next dropdown.",
    attachTo: { element: "#curriculum-select", on: "bottom" },
    showIfAttachToExists: true,
  },
  {
    title: "3️⃣ Pick a Subject",
    text: "Now select a subject to see the relevant year groups.",
    attachTo: { element: "#subjects-select", on: "bottom" },
    showIfAttachToExists: true,
  },
  {
    title: "4️⃣ Select a Year Group",
    text: "Finally, choose a year group. This will load all the available lessons for that year below.",
    attachTo: { element: "#years-select", on: "bottom" },
    showIfAttachToExists: true,
  },
  {
    title: "🔎 Let's Find Some Lessons",
    text: "Use the dropdowns you've just seen to select a class, curriculum, subject, and year group. Once you've made a valid selection, the available lessons will appear below.",
    attachTo: { element: "#add-to-form-container", on: "bottom" },
    scrollTo: { behavior: 'smooth', block: 'center' },
    showOn: () => {
      return !document.querySelector('table[data-unit-table]');
    },
    showIfAttachToExists: true,
    buttons: [{ text: "🔙 Back", action: "back", classes: "shepherd-back" }]
  },
  {
    title: "✅ Select Your Lessons",
    text: "Here are all the lessons for the year group you selected, grouped into units. You can now choose which ones to add.",
    attachTo: { element: "table[data-unit-table]", on: "top" },
    showIfAttachToExists: true,
    scrollTo: { behavior: 'smooth', block: 'center' },
  },
  {
    title: "🖱️ How to Select",
    text: "You can select a whole unit by clicking the checkbox in the header, or pick individual lessons by clicking their checkboxes or the entire row.",
    attachTo: { element: 'table[data-unit-table]', on: 'bottom' },
    showIfAttachToExists: true,
  },
  {
    title: "🗓️ Schedule and Save",
    text: "This section at the bottom shows how many lessons you've selected. You can also set a start date to schedule them automatically.",
    attachTo: { element: 'div.sticky.bottom-0', on: 'top' },
    showIfAttachToExists: true,
  },
  {
    title: "💾 Add to Class",
    text: "When you're ready, click here to add the selected lessons to your class!",
    attachTo: { element: 'input[type="submit"][value="Save and add lessons"]', on: 'top' },
    showIfAttachToExists: true,
  },
  {
    title: "✅ Page Complete! What's Next?",
    text: "You've seen how to add lessons! You can now save them or click the button to return to the lessons list for this class.",
    attachTo: { element: '#add-to-form-container', on: 'top' },
    showIfAttachToExists: true,
    buttons: [
      {
        text: "🔙 Back",
        action: "back",
        classes: "shepherd-back"
      },
      {
        text: "➡️ To Lessons Page",
        action: () => tourNextPage(`/school/classes/${data.formId}/lessons`),
        classes: "shepherd-next"
      }
    ]
  },
  {
    title: "📋 Lesson Overview",
    text: "Each row shows lesson details – subject, schedule, and more. Everything you need at a glance 📚",
    attachTo: { element: "#table-container tbody tr.items-center", on: "bottom" },
    showIfAttachToExists: true
  },
  {
    title: "📤 Share with Google Classroom",
    text: "Use this button to share a lesson directly with pupils via Google Classroom – quick and connected 💻",
    attachTo: { element: "#table-container tbody tr.items-center td:last-child div[name='share-to-classroom']", on: "left" },
    showIfAttachToExists: true
  },
  {
    title: "🎯 Set Independent Learning",
    text: "Assign independent learning to a lesson using this button – great for extra practice or homework ✨",
    attachTo: { element: "#table-container tbody tr.items-center td:last-child .independent-learning-btn", on: "left" },
    showIfAttachToExists: true
  },
  {
    title: "🗑️ Remove Lesson",
    text: "Click here to remove a lesson from the class. You can always reassign it later if needed 🔁",
    attachTo: { element: "#table-container tbody tr.items-center td:last-child .remove-lesson-btn", on: "left" },
    showIfAttachToExists: true
  },
  {
    title: "🛠️ Edit Class",
    text: "Use this tab to change basic class info – name, assigned teachers, and more 📋",
    attachTo: { element: "#tabs-container .group:nth-child(3)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✏️ To Edit Page", action: (() => tourNextPage(`/school/classes/${data.formId}/edit`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "📑 Edit Class Details",
    text: "Use this form to update the class’s name and who’s teaching it – quick and clear 👩‍🏫",
    attachTo: { element: "#edit-container", on: "bottom" },
    buttons: [
      { text: "🔙 Back to Lessons Page", action: (() => tourPreviousPage(`/school/classes/${data.formId}/lessons`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📝 Update Basic Info",
    text: "Use this section to tweak class details like name or code. Keep everything up to date 🗃️",
    attachTo: { element: "#details-container", on: "bottom" },
  },
  {
    title: "👩‍🏫 Manage Teachers",
    text: "Add, remove or update the teachers assigned to this class. Great for keeping everything organised 👥",
    attachTo: { element: "#teachers-container", on: "bottom" },
  },
  {
    title: "🎉 Tour Complete!",
    text: "All done! You've completed the Class Management tour 🎓 You can revisit it anytime from the Classes page.",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: (() => {
        sessionStorage.removeItem("shepherd_tour");
        tourNextPage(`/school/classes${data?.adminView ? view_all : ''}`)
      }), classes: "shepherd-next" }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
];