import { tourPreviousPage, tourNextPage } from "../helpers";

export const markBookSteps = (data) => [
  {
    title: "📘 Welcome to the Mark Book!",
    text: "Let’s take a quick tour! Here you can track how your pupils are doing across all your lessons and classes – easy and stress-free 🎯",
  },
  {
    title: "🚫 No Classes Yet?",
    text: "Looks like you haven’t added any classes yet. No worries! Just pop over to the ‘My Classes’ page to get started 🧑‍🏫",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: "complete", classes: "shepherd-next" }
    ],
    showOn: () => {
      const element = document.querySelector('#my-classes .class-container');
      return !element;
    },
  },
  {
    title: "👩‍🏫 My Classes",
    text: "Here’s where you’ll see all the classes assigned to you. Click on a class card to dive into its Mark Book 📋",
    attachTo: { element: "#my-classes", on: "top" },
    showIfAttachToExists: true
  },
  {
    title: "🏫 All School Classes",
    text: "Want to view Mark Books across the whole school? Use this section to check on or support other classes too 👥",
    attachTo: { element: "#all-classes", on: "top" },
    scrollTo: { behavior: 'smooth', block: 'start' },
    showIfAttachToExists: true
  },
  {
    title: "🗂️ Class Cards",
    text: "Each card gives a snapshot of a class. Click one to open that class’s Mark Book and see the details 🔍",
    attachTo: { element: "#my-classes .class-container", on: "bottom" },
    scrollTo: { behavior: 'smooth', block: 'end' },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "📖 Open Mark Book", action: (() => tourNextPage(`/school/mark-book/${data.formId}`)), classes: "shepherd-next" }
    ],
    showIfAttachToExists: true
  },
  {
    title: "📊 Inside the Mark Book",
    text: "Welcome in! This is where you’ll see your class’s assessments, pupil scores, and how they’re getting on 🌟",
    buttons: [
      { text: "🔙 Back to Class List", action: (() => tourPreviousPage('/school/mark-book')), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🏷️ Assessment Labels",
    text: "Each column is a different task or activity. The labels help you quickly see what kind of assessment it is ✏️",
    attachTo: { element: "#labels-container", on: "bottom" }
  },
  {
    title: "📈 Mark Book Table",
    text: "This is the big picture! Each row is a pupil, and columns show their progress across the curriculum. Scroll across to see more ➡️",
    attachTo: { element: "#mark-book-container", on: "bottom" }
  },
  {
    title: "🔍 Expand & Add Notes",
    text: "Click a row to peek into unit-level details. You can expand lessons and even leave notes to support each pupil’s journey 📝",
    attachTo: { element: "#mark-book-container", on: "bottom" }
  },  
  {
    title: "📥 Export Your Data",
    text: "Need to work offline or share with colleagues? Download your Mark Book as a handy spreadsheet 📊",
    attachTo: { element: "#spreadsheet-export-btn", on: "top" }
  },
  {
    title: "🎉 That’s a Wrap!",
    text: "You’ve completed the Mark Book tour! Come back anytime – the tour’s always available from the Mark Book page 🚀",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: (() => {
        sessionStorage.removeItem("shepherd_tour");
        tourNextPage('/school/mark-book')
      }), classes: "shepherd-next" }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
];