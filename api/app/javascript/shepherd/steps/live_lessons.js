import { tourPreviousPage, tourNextPage } from "../helpers";

export const liveLessonsSteps = (data) => [
  {
    title: "🎬 Welcome to DE Live",
    text: "This is where you’ll find everything to do with live learning – streams that are happening now, coming up soon, or already finished 🎓",
  },
  {
    title: "📡 Current Live Stream",
    text: "If there’s a live stream happening right now, you’ll see it here. Just click to jump straight in! 🔴",
    attachTo: { element: "#stream-container", on: "top" },
    showIfAttachToExists: true
  },
  {
    title: "⏸️ No Active Stream",
    text: "Nothing’s streaming live at the moment. Pop back soon to catch the next one! ⏰",
    attachTo: { element: "#no-stream-container", on: "top" },
    showIfAttachToExists: true
  },
  {
    title: "🧭 Navigation Tabs",
    text: "Use these tabs to switch between what's Live, what's Upcoming, and what's already been streamed 🔄",
    attachTo: { element: "#tabs-container", on: "bottom" }
  },
  {
    title: "📺 Currently Viewing: Live",
    text: "You're on the Live tab right now. Any lesson currently streaming will show up here 🎥",
    attachTo: { element: "#tabs-container .group:first-child", on: "bottom" }
  },
  {
    title: "📅 View Upcoming Lessons",
    text: "Click here to see which lessons are scheduled to go live soon – great for planning ahead! 🗓️",
    attachTo: { element: "#tabs-container .group:nth-child(2)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "➡️ Go to Upcoming", action: (() => tourNextPage(`/school/youtube/upcoming`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "🚀 Upcoming Live Lessons",
    text: "All the upcoming live lessons are listed here. You’ll see the date, time and topic – get ready to join in! ⏳",
    attachTo: { element: "#upcoming-container", on: "top" },
    buttons: [
      { text: "🔙 Back to Live", action: (() => tourPreviousPage(`/school/youtube/live`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📚 View Past Lessons",
    text: "Click this tab to view recordings of lessons that were previously streamed – perfect for catch-up learning! 🧠",
    attachTo: { element: "#tabs-container .group:nth-child(3)", on: "bottom" },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "📼 Go to History", action: (() => tourNextPage(`/school/youtube/history?category=youtube`)), classes: "shepherd-next" }
    ]
  },
  {
    title: "🕰️ Past Live Lessons",
    text: "Here you’ll find all the previously streamed live lessons, ready to rewatch at any time 🎞️",
    attachTo: { element: "#history-container", on: "top" },
    buttons: [
      { text: "🔙 Back to Upcoming", action: (() => tourPreviousPage(`/school/youtube/upcoming`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "📭 No Past Streams",
    text: "It looks like there aren’t any past lessons just yet. As streams are added, they’ll appear here 👀",
    attachTo: { element: "#no-streams-container", on: "top" },
    showIfAttachToExists: true,
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: "complete", classes: "shepherd-next" }
    ]
  },
  {
    title: "🗂️ History Categories",
    text: "Past content is organized into two categories: Live Lessons and Webinars.",
    attachTo: { element: "#history-container > div:first-child", on: "bottom" },
  },
  {
    title: "🎥 View Webinars",
    text: "Now, let's check out the webinars. Click this tab to see a list of our past webinar sessions.",
    attachTo: { element: 'a[href*="category=webinar"]', on: 'bottom' },
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "➡️ Go to Webinars", action: (() => tourNextPage(`/school/youtube/history?category=webinar`)), classes: "shepherd-next" }
    ]
  },
    {
    title: "🎓 Past Webinars",
    text: "Here you'll find all our previously recorded webinars. Feel free to rewatch them anytime.",
    attachTo: { element: "#history-container", on: "top" },
    buttons: [
      { text: "🔙 Back to Live Lessons", action: (() => tourPreviousPage(`/school/youtube/history?category=youtube`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "▶️ Watch a Past Lesson",
    text: "Click on any of the cards here to open and watch a recorded live lesson 🎬",
    attachTo: { element: "#streams-container .bg-gray-100:first-child", on: "top" },
    showIfAttachToExists: true,
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
    ]
  },
  {
    title: "📺 Past Lesson Stream",
    text: "This is where you can rewatch a previous live lesson in full – pause, rewind and revisit anytime 🎧",
    attachTo: { element: "#stream-container", on: "bottom" },
    showIfAttachToExists: true,
    buttons: [
      { text: "🔙 Back to History", action: (() => tourPreviousPage(`/school/youtube/history`)), classes: "shepherd-back" },
      { text: "➡️ Next", action: "next", classes: "shepherd-next" }
    ]
  },
  {
    title: "🎉 Tour Complete!",
    text: "That’s the end of the DE Live tour! You can return to it anytime from the DE Live pages 🧑‍🏫",
    buttons: [
      { text: "🔙 Back", action: "back", classes: "shepherd-back" },
      { text: "✅ Finish Tour", action: (() => {
        sessionStorage.removeItem("shepherd_tour");
        tourNextPage(`/school/youtube`)
      }), classes: "shepherd-next" }
    ],
    scrollTo: { behavior: 'smooth', block: 'top' }
  },
];