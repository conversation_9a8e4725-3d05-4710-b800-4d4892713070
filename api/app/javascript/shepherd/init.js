import { tourMap } from "./tour_map";
import { startTour } from "./start_tour";

document.addEventListener("DOMContentLoaded", () => {
  const startButton = document.querySelector(".shepherd-start");
  if (!startButton) {
    return;
  }

  let tourStore = JSON.parse(sessionStorage.getItem("shepherd_tour"));

  const autoplayPageKey = tourStore?.id?.split('--')[0] || null;

  function runTour(pageKey, data, startAtIndex = 0, route = null) {
    const tourConfig = tourMap[pageKey]

    if (!tourConfig || typeof tourConfig.steps !== 'function') {
      console.warn("No tour defined for page: ", pageKey);
      return;
    }

    const { steps: stepsFn, startRoute } = tourConfig;
    const steps = stepsFn(data);

    if (startRoute && window.location.pathname !== startRoute && startAtIndex === 0) {
      sessionStorage.setItem("shepherd_tour", JSON.stringify({ id: pageKey, step: 0, data, autoplay: true }));
  
      window.location.href = startRoute;
      return;
    }

    if (steps.length > 0) {
      if (route && window.location.href !== route) {
        window.history.pushState({}, '', route);
      }
      startTour(pageKey, steps, data, startAtIndex);
    }
  }

  if (autoplayPageKey === startButton.dataset.tourPage) {
    startButton.innerHTML = "Continue Tour";
  }

  if (tourStore?.autoplay && autoplayPageKey) {
    if (autoplayPageKey === startButton.dataset.tourPage) {
      const tourConfig = tourMap[autoplayPageKey];

      if (tourConfig && typeof tourConfig.prepareTourData === 'function') {
        const updatedData = tourConfig.prepareTourData(tourStore.data);

        tourStore = {
          ...tourStore,
          data: updatedData,
          route: window.location.pathname
        }

        sessionStorage.setItem("shepherd_tour", JSON.stringify(tourStore));
      }

      runTour(autoplayPageKey, tourStore.data, tourStore.step, tourStore.route)
    } else {
      sessionStorage.setItem("shepherd_tour", JSON.stringify({ ...tourStore, autoplay: false }));
    }
  }
  
  startButton.addEventListener("click", () => {
    const pageKey = startButton.dataset.tourPage;
    if (pageKey === "data_imports") { setImportTour(); }
    if (autoplayPageKey && autoplayPageKey === startButton.dataset.tourPage) {
      runTour(autoplayPageKey, tourStore.data, tourStore.step, tourStore.route);
    } else {
      runTour(pageKey, startButton.dataset)
    }
  });

  function setImportTour() {
    const importCards = document.querySelectorAll(".import-card");
    
    importCards.forEach(card => {
      card.addEventListener('click', function() {
        runTour(card.dataset.tour)
      })
    })
  }
});