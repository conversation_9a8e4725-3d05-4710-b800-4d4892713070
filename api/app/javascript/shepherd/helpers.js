import Shepherd from 'https://cdn.jsdelivr.net/npm/shepherd.js@14.5.0/dist/esm/shepherd.mjs';

export function buildStepButtons(stepIndex, totalSteps, customButtons = null, btnClasses = '') {
  if (customButtons) {
    return customButtons.map(btn => ({
      ...btn,
      classes: [btnClasses, btn.classes].join(' '),
      action: typeof btn.action === 'string'
        ? Shepherd.Tour.prototype[btn.action]
        : btn.action
    }));
  }

  let buttons;
  if (stepIndex === 0) {
    buttons = [{ text: "Next", action: "next", classes: "shepherd-next" }];
  } else if (stepIndex === totalSteps - 1) {
    buttons = [{ text: "Finish", action: "complete", classes: "shepherd-next" }];
  } else {
    buttons = [
      { text: "Back", action: "back", classes: "shepherd-back" },
      { text: "Next", action: "next", classes: "shepherd-next"  }
    ];
  }

  return buttons.map(btn => ({
    ...btn,
    classes: [btnClasses, btn.classes].join(' '),
    action: typeof btn.action === 'string'
      ? Shepherd.Tour.prototype[btn.action]
      : btn.action
  }));
}

function buildUrl(route) {
  const { protocol, host } = window.location;
  return `${protocol}//${host}${route}`;
}

function updateTourProgress(delta, route, otherData) {
  const tourStore = JSON.parse(sessionStorage.getItem("shepherd_tour")) || {};
  const step = (tourStore.step ?? 0) + delta;
  sessionStorage.setItem("shepherd_tour", JSON.stringify({
    id: tourStore.id || null,
    step: Math.max(0, step),
    data: tourStore.data || {},
    route,
    autoplay: true,
    ...otherData
  }));
}

export function tourNextPage(route, otherData = {}) {
  updateTourProgress(1, route, otherData);
  window.location.replace(buildUrl(route));
}

export function tourPreviousPage(route, otherData = {}) {
  updateTourProgress(-1, route, otherData);
  window.location.replace(buildUrl(route));
}

// handle custom actions for keyboard
export function setKeyboardHandler() {
  document.onkeydown = function (e) {
    const currentElement = Shepherd.activeTour?.getCurrentStep().el;
    if (Shepherd.activeTour != null) {
      switch(e.key) {
        case 'ArrowLeft':
          currentElement.querySelector(".shepherd-back")?.click();
          break;
        case 'ArrowRight':
          currentElement.querySelector(".shepherd-next")?.click();
          break;
      }
    }
  }
}

// check if attached element exists for a step
function elementExists(step) {
  if (!step?.attachTo?.element) {
    return false;
  }

  const element = document.querySelector(step.attachTo.element);
  if (!element) {
    return false;
  }

  if (element.offsetWidth === 0 && element.offsetHeight === 0) {
    return false;
  }

  const style = window.getComputedStyle(element);
  if (style.visibility === 'hidden' || style.opacity === '0') {
    return false;
  }

  return true;
}

export function resolveShowOn(step) {
  const attachToCondition = step.showIfAttachToExists ? elementExists(step) : true;
  const customShowOnCondition = typeof step.showOn === "function" ? step.showOn() : true;

  return () => attachToCondition && customShowOnCondition;
}

export function extractDataFromUrl(currentData, regex, dataKey) {
  const path = window.location.pathname;
  const match = path.match(regex);

  if (match && match[1]) {
    const extractedValue = match[1];
    console.log(`Shepherd preparing data with ${dataKey}: ${extractedValue}`);
    return {
      ...currentData,
      [dataKey]: extractedValue
    };
  }

  return currentData;
}