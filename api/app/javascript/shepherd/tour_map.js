import { dashboardSteps } from './steps/dashboard';
import { manageClassesSteps } from './steps/manage_classes';
import { managePupilsSteps } from './steps/manage_pupils';
import { manageTeachersSteps } from './steps/manage_teachers';
import { markBookSteps } from './steps/mark_book';
import { liveLessonsSteps } from './steps/live_lessons';
import { importXlsSteps } from './steps/import_xls';
import { lessonEditorSteps } from './steps/lesson_editor';

import { extractDataFromUrl } from './helpers';

// new tours must be added here
export const tourMap = {
  dashboard: {
    steps: () => dashboardSteps,
    startRoute: "/school/dashboard"
  },
  manage_classes: {
    steps: (data) => manageClassesSteps(data),
    startRoute: "/school/classes",
    prepareTourData: (data) => { return extractDataFromUrl(data, /\/classes\/(\d+)\//, 'formId'); }
  },
  manage_pupils: {
    steps: (data) => managePupilsSteps(data),
    startRoute: "/school/pupils"
  },
  manage_teachers: {
    steps: (data) => manageTeachersSteps(data),
    startRoute: "/school/teachers"
  },
  mark_book: {
    steps: (data) => markBookSteps(data),
    startRoute: "/school/mark-book"
  },
  live_lessons: {
    steps: (data) => liveLessonsSteps(data),
    startRoute: "/school/youtube/live"
  },
  xls_imports: {
    steps: (data) => importXlsSteps(data),
    startRoute: "/school/data-imports/xls"
  },
  lesson_editor: {
    steps: (data) => lessonEditorSteps(data),
    startRoute: "/school/lesson-editing",
    prepareTourData: (data) => { return extractDataFromUrl(data, /\/lesson-editing\/(\d+)\//, 'templateId'); }
  }
};