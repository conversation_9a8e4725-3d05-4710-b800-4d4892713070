// @ts-check

// MARK: data-args
// "data-nice-select": "Enhance the select element"
// "data-default-options": "When not searching, still show options"
// "data-is-clearable": "The prompt text for 'unselect option' option"
// "data-filter-options": (selectList, option) => boolean function to filter option values on render. Occurs before search filtering"
//=================

const log = console.log.bind(console, "[nice-select]")
const error = console.error.bind(console, "[nice-select]")

/**
 * @typedef {Object} PopperOptions
 * @property {string} placement - The placement option (e.g., "bottom-start").
 * @property {Array<{ name: string, options: Record<string, any> }>} modifiers - An array of modifiers.
 *
 * @typedef {Object} PopperInstance
 * @property {() => Promise<void>} update - Recalculates the popper's position.
 *
 * @typedef {Object} Popper
 * @property {(reference: HTMLElement, popper: HTMLElement, options: PopperOptions) => PopperInstance} createPopper
 */

/** @type {Popper} */
// @ts-ignore
const Popper = window.Popper

/** * @type {WeakSet<HTMLElement>} */
const enhanced = new WeakSet()

// MARK: Enhance Select

/** * @param {HTMLSelectElement} $el */
function enhanceSelect($el) {
	log("Enhancing", $el)

	if (enhanced.has($el)) {
		log("Element already enhanced", $el)
		return
	}

	insertGlobalStyles()

	const state = { isOpen: false, query: "" }
	const scrollLocker = new ScrollLocker()
	let placeholder = $el.getAttribute("placeholder") || ""

	/** @typedef {{ value: string, main: string, sub?: string, raw: string }} Option */
	/** @type {Option[]} */
	const options = []
	Array.from($el.options)
		.forEach($option => {
			if (!$option.value) {
				if (!placeholder && $option.textContent) placeholder = $option.textContent
				return
			}
			if (!$option.textContent) return
			const raw = $option.textContent
			if ($option.dataset.main) {
				const { main, sub } = $option.dataset
				options.push({ value: $option.value, main, sub, raw })
			} else{
				options.push({ value: $option.value, main: $option.textContent, raw })
			}
		})
	options.sort((a, b) => a.main.localeCompare(b.main))

	const elements = buildDom(placeholder || "Select")
	const popperInstance = Popper.createPopper(elements.$head, elements.$dropdown, {
		placement: 'bottom-start',
		modifiers: [{ name: 'offset', options: { offset: [0, 4] } }],
	})

	Array.from($el.options).forEach($option => {
		if ($option.selected) setSelected($option.value)
	})


	// MARK: Listeners


	// allows for watching of changes on the select elements so we can toggle different attributes
	// like disabled etc. need to manually add the handling for attributes required.
	const attributesObserver = new MutationObserver(function(mutations) {
		mutations.forEach(function(mutation) {
			if (mutation.type === "attributes") {
				if($el.hasAttribute("disabled")) {
					elements.$wrapper.setAttribute("disabled", $el.getAttribute("disabled") || "")
				} else {
					elements.$wrapper.removeAttribute("disabled")
				}
				// watch the attribute "value" (note. i don't think works for .value, only if you do 'setAttribute("value", "...")')
				// The nice select creates a wrapper around the existing select list - setting the value of the existing select was not
				// propagated to the nice-select once rendered. This change allows for using the setAttribute to set the value of the select
				// and have it propagate to the nice select.
				if($el.hasAttribute("value")) {
					setSelected($el.getAttribute("value"))
				}
			}
		});
	});
	attributesObserver.observe($el, { attributes: true });


	elements.$head.addEventListener("click", () => {
		if (state.isOpen) {
			close()
		} else {
			open()
		}
	})
	elements.$wrapper.addEventListener("keydown", (e) => {
		if ((e.key === "Enter" || e.key === " ") && !state.isOpen) {
			e.preventDefault();
			open();
		}
	});
	elements.$wrapper.addEventListener("focusout", (e) => {
		if (!(e.relatedTarget instanceof Node) || !elements.$wrapper.contains(e.relatedTarget)) {
			close()
		}
	})
	document.addEventListener("click", (e) => {
		if (!(e.target instanceof Node) || !elements.$wrapper.contains(e.target)) {
			close()
		}
	})
	elements.$searchInput.addEventListener("input", () => {
		state.query = elements.$searchInput.value
		renderOptions()
	})
	elements.$searchInput.addEventListener("keydown", (e) => {
		if (!state.isOpen) return;

		switch (e.key) {
			case "Escape":
				e.preventDefault();
				close();
				break;
			case "ArrowDown":
				e.preventDefault();
				moveSelection("next");
				break;
			case "ArrowUp":
				e.preventDefault();
				moveSelection("prev");
				break;
			case "Enter":
				e.preventDefault();
				e.stopPropagation();
				/** @type {HTMLElement | null} */
				const selected = elements.$options.querySelector("[data-keyboard-selected]");
				const value = selected?.dataset.value;
				onChange(value);

				break;
		}
	});
	// stop propagation of scroll events from the dropdown
	elements.$options.addEventListener("wheel", (e) => {
		e.stopPropagation()
	})
	// stop propagation of touchmove events from the dropdown
	elements.$options.addEventListener("touchmove", (e) => {
		e.stopPropagation()
	})
	elements.$options.addEventListener("click", (e) => {
		if (!(e.target instanceof HTMLElement)) return
		const $option = e.target.closest("[data-value]")
		if (!($option instanceof HTMLElement)) return

		const value = $option.dataset.value
		onChange(value);
	})

	// MARK: FINAL SWAP

	// Swap out the select element with the wrapper
	$el.insertAdjacentElement("beforebegin", elements.$wrapper)
	// move the $el into the wrapper
	elements.$wrapper.appendChild($el)
	$el.classList.add("enhanced")
	enhanced.add($el)


	// MARK: Functions

	function isDisabled() {
		return !!elements.$wrapper.hasAttribute("disabled")
	}

	function open() {
		if (isDisabled()) return
		if (state.isOpen) return
		state.isOpen = true
		scrollLocker.lock()
		elements.$dropdown.classList.add("open")
		popperInstance.update()
		elements.$searchInput.focus()
		elements.$searchInput.select()
		renderOptions()
	}

	function close() {
		if (!state.isOpen) return
		state.isOpen = false
		scrollLocker.unlock()
		elements.$dropdown.classList.remove("open")
	}

	/** @param {string | null | undefined} value */
	function onChange(value) {
		if (isDisabled()) return
		$el.value = value || ""
		$el.dispatchEvent(new Event("change", { bubbles: true }))
		setSelected(value)
		close()
		elements.$wrapper.focus()
	}

	/** @param {string | null | undefined} value */
	function setSelected(value) {
		if (!value) {
			elements.$selected.classList.add("hidden")
			elements.$placeholder.classList.remove("hidden")
			return
		}
		const option = options.find(opt => opt.value === value)
		if (!option) return
		const $opt = div({}, option.raw)
		elements.$selected.innerHTML = ""
		elements.$selected.appendChild($opt)
		elements.$selected.classList.remove("hidden")
		elements.$placeholder.classList.add("hidden")
	}

	/**
	 * Moves the keyboard selection in the specified direction.
	 *
	 * @param {"next" | "prev"} direction - Direction to move the selection.
	 */
	function moveSelection(direction) {
		let selected =  elements.$options.querySelector("[data-keyboard-selected]");

		if (!selected) {
			// If nothing is selected, choose the first or last element
			selected = direction === "next" ?  elements.$options.firstElementChild :  elements.$options.lastElementChild;
			if (selected instanceof HTMLElement) {
				selected.setAttribute("data-keyboard-selected", "");
			}
		} else {
			// Remove current selection and choose the appropriate sibling,
			// wrapping around if necessary.
			selected.removeAttribute("data-keyboard-selected");
			let newSelection =
				direction === "next"
					? selected.nextElementSibling ||  elements.$options.firstElementChild
					: selected.previousElementSibling ||  elements.$options.lastElementChild;
			if (newSelection instanceof HTMLElement) {
				newSelection.setAttribute("data-keyboard-selected", "");
				selected = newSelection;
			}
		}

		// Scroll the new selection into view
		const currentSelected =  elements.$options.querySelector("[data-keyboard-selected]");
		currentSelected?.scrollIntoView({ block: "nearest" });
	}

	// MARK: Rendering

	function renderOptions() {
		const minSearchLength = 2
		const maxNumberOfOptions = 30

		const defaultOptions = !!elements.$wrapper.querySelector('select')?.hasAttribute("data-default-options")

		elements.$options.classList.remove("hidden")
		elements.$tooManyOptionsMessage.classList.add("hidden")
		elements.$message.classList.add("hidden")

		if (state.query.length < minSearchLength && !defaultOptions) {
			return renderMessage("Type to search...")
		}
		// rebuild options list so we don't mutate the original options values
		let filteredOptions = [...options]

		// if we have set a data-filter-options, pre-filter the options by the named function first.
		const filterFunctionName = $el.getAttribute("data-filter-options")
		if(filterFunctionName && window[filterFunctionName]) {
			const filterFn = window[filterFunctionName]
			filteredOptions = options.filter(opt => filterFn($el, opt))
		}

		filteredOptions = filteredOptions.filter(opt => fuzzyMatch(opt.raw, state.query))

		if (filteredOptions.length === 0) {
			return renderMessage(`No results found`)
		}

		const tooManyOptions = filteredOptions.length > maxNumberOfOptions
		filteredOptions = filteredOptions.slice(0, maxNumberOfOptions)

		if ($el.hasAttribute("data-is-clearable")) {
			filteredOptions.unshift({ value: "", main: $el.getAttribute("data-is-clearable") || "Clear selection", raw: "" })
		}
		const optionsEls = filteredOptions.map(opt => buildOption(opt))
		elements.$options.innerHTML = ""
		optionsEls.forEach($opt => {
			elements.$options.appendChild($opt)
		})

		if (tooManyOptions) {
			elements.$tooManyOptionsMessage.classList.remove("hidden")
		} else {
			elements.$tooManyOptionsMessage.classList.add("hidden")
		}
	}

	/** @param {Option} opt */
	function buildOption(opt) {
		return div(
			{ class: "option", data: { value: opt.value } },
			`${opt.main}`,
			opt.sub ? div({ class: "option-sub" }, opt.sub) : ''
		)
	}

	/** @param {string} message */
	function renderMessage(message) {
		elements.$options.classList.add("hidden")
		elements.$message.classList.remove("hidden")
		elements.$message.innerHTML = message
	}
}

// MARK: Insert Global Styles
/**
 * Inserts the necessary styles for the nice select.
 */
function insertGlobalStyles() {
	// check if there is a style tag with the id "nice-select-styles"
	if (document.getElementById("nice-select-styles")) return


	const style = document.createElement("style")
	style.id = "nice-select-styles"

	style.textContent = `
[data-nice-select].enhanced {
	opacity: 0;
    position: absolute;
    pointer-events: none;
    top: 0;
	left: 0;
}

[data-nice-select-wrapper][disabled] {
	opacity: 0.5;
	& * {
		cursor: not-allowed !important;
	}
}

[data-nice-select-wrapper] {
    position: relative;
}

[data-nice-select-wrapper]:focus {
	outline: 0;
}
[data-nice-select-wrapper]:focus-within [data-head] {
    border: 2px solid #2563eb;
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
	outline: 0;
}

[data-head] {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 12px;
	padding-top: 10px;
	padding-bottom: 10px;
	border: 2px solid #d1d5db;
	border-radius: 8px;
	cursor: pointer;
	background-color: #ffffff;
	color: #1f2937;
}

[data-placeholder] {
	color: #9ca3af;
}

.nice-select [data-dropdown] {
    visibility: hidden;
    background-color: #ffffff;
    width: 100%;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 10;
		color: #1f2937;
}

.nice-select [data-dropdown].open {
    visibility: visible;
}

[data-search] {
	position: relative;
	display: flex;
	align-items: center;
    border-bottom: 1px solid #e5e7eb;
}

[data-search]:focus-within {
	border: 2px solid #2563eb;
}

[data-dropdown] i {
    color: #9ca3af;
	display: block;
	width: 32px;
	text-align: center;
}

[data-dropdown] input[type="search"] {
    flex: 1;
    padding: 12px;
    box-sizing: border-box;
    border: none;
}
[data-dropdown] input[type="search"]:focus {
	outline: none;
	box-shadow: none;
}

[data-options] {
	max-height: 240px;
	overflow-y: auto;
}

[data-options] .option {
    padding: 8px 12px;
    cursor: pointer;
}

[data-options] .option:hover {
    background-color: #f3f4f6;
}

[data-options] .option-sub {
    color: #9ca3af;
    font-size: 0.8rem;
}

[data-options] .option[data-keyboard-selected] {
	background-color:rgba(176, 202, 255, 0.49);
}

[data-too-many-options] {
	padding: 12px;
	color: #9ca3af;
	text-align: center;
}

[data-message] {
	padding: 12px;
	color: #9ca3af;
}
	`

	document.head.appendChild(style)
}

// MARK: Element Helpers
/**
 * Creates an element with the given attributes and children.
 * @param {string} name
 * @param {Record<string, any>} attrs
 * @param {HTMLElement[]} children
 * @returns {HTMLElement}
 */
const el = (name, attrs = {}, ...children) => {
	const $el = document.createElement(name)

	for (const [key, value] of Object.entries(attrs)) {
		if (key === "data") {
			for (const [dataKey, dataValue] of Object.entries(value)) {
				$el.dataset[dataKey] = dataValue
			}
		} else {
			$el.setAttribute(key, value)
		}
	}

	for (const child of children) {
		if (child instanceof Node) {
			$el.appendChild(child)
		} else {
			$el.appendChild(document.createTextNode(String(child)))
		}
	}

	return $el
}

/**
 * Creates a div element with the given attributes and children.
 * @param {Record<string, string>} attrs
 * @param {HTMLElement[]} children
 * @returns {HTMLElement}
 */
const div = el.bind(null, "div")

// MARK: Build DOM

/**
 * Builds the DOM structure for the nice select.
 * @param {string} placeholder
 * @returns {{
 * 	$wrapper: HTMLElement,
 * 	$head: HTMLElement,
 * 	$placeholder: HTMLElement,
 * 	$selected: HTMLElement,
 * 	$dropdown: HTMLElement,
 * 	$searchInput: HTMLInputElement
 * 	$options: HTMLElement
 * 	$tooManyOptionsMessage: HTMLElement
 * 	$message: HTMLElement
 * }}
 */
function buildDom(placeholder = "Select") {
	const $wrapper = div(
		{ class: "nice-select", data: { niceSelectWrapper: true }, tabindex: "0" },
		div(
			{ data: { head: true } },
			div({ data: { placeholder: true } }, placeholder),
			div({ data: { selected: true }, class: "hidden" }),
			el("i", { class: "caret" }, div({ class: "fas fa-caret-down" }))
		),
		div(
			{ data: { dropdown: true } },
			div(
				{ data: { search: true } },
				el("i", { class: "fas fa-search" }),
				el("input", { type: "search", placeholder: "Search..." })
			),
			div({ data: { options: true } }, "OPTIONS GO HERE"),
			div({ data: { tooManyOptions: true }, class: "hidden" }, "Showing the first 30 results"),
			div({ data: { message: true } }, "Type to search...")
		)
	)

	/** @param {string} selector */
	const query = (selector) => {
		const $el = $wrapper.querySelector(selector)
		if (!$el) throw new Error(`[nice-select] Element not found: ${selector}`)
		if (!($el instanceof HTMLElement)) throw new Error(`[nice-select] Element is not an HTMLElement: ${selector}`)
		return $el
	}

	/**
	 * @param {string} selector
	 */
	const queryInput = (selector) => {
		const $el = query(selector)
		if (!($el instanceof HTMLInputElement)) throw new Error(`[nice-select] Element is not an HTMLInputElement: ${selector}`)
		return $el
	}

	return {
		$wrapper,
		$head: query("[data-head]"),
		$placeholder: query("[data-placeholder]"),
		$selected: query("[data-selected]"),
		$dropdown: query("[data-dropdown]"),
		$searchInput: queryInput("[data-search] input[type='search']"),
		$options: query("[data-options]"),
		$tooManyOptionsMessage: query("[data-too-many-options]"),
		$message: query("[data-message]"),
	}
}

// MARK: Scroll Locker
class ScrollLocker {
	locked = false

	constructor() {
		document.body.addEventListener("touchmove", (e) => this.#onTouchMove(e), { passive: false })
		document.body.addEventListener("wheel", (e) => this.#onWheel(e), { passive: false })
	}

	lock() {
		this.locked = true;
	}

	unlock() {
		this.locked = false;
	}

	/**
	 * @param {TouchEvent} event
	 * @returns
	 */
	#onTouchMove(event) {
		if (this.locked) {
			event.preventDefault();
		}
	}

	/**
	 * @param {WheelEvent} event
	 */
	#onWheel(event) {
		if (this.locked) {
			event.preventDefault();
		}
	}
}

// MARK: Fuzzy Match
/**
 * Checks if the given string matches the query.
 * @param {string} str
 * @param {string} query
 */
function fuzzyMatch(str, query) {
	return query
		.toLowerCase()
		.split(" ")
		.every(part => str.toLowerCase().includes(part))
}

// MARK: Enhance All Selects
document.querySelectorAll("[data-nice-select]").forEach($el => {
	if ($el instanceof HTMLSelectElement) {
		enhanceSelect($el)
	} else {
		error("Element is not a select", $el)
	}
})

function enhanceWithAjax() {
  document.querySelectorAll("[data-nice-select][data-ajax-url]").forEach($el => {
    if (!($el instanceof HTMLSelectElement)) return;
    
    const ajaxUrl = $el.dataset.ajaxUrl;
    const minSearchLength = parseInt($el.dataset.minSearchLength || "2", 10);
    
    if (!ajaxUrl) return;
    
    // Cache for previously loaded results
    const cache = new Map();
    
    // Find the nice-select wrapper
    const $wrapper = $el.closest("[data-nice-select-wrapper]");
    if (!$wrapper) return;
    
    // Get the existing elements
    const $searchInput = $wrapper.querySelector('[data-search] input[type="search"]');
    const $options = $wrapper.querySelector("[data-options]");
    const $message = $wrapper.querySelector("[data-message]");
    const $placeholder = $wrapper.querySelector("[data-placeholder]");
    const $selected = $wrapper.querySelector("[data-selected]");
    
    if (!$searchInput || !$options || !$message || !($searchInput instanceof HTMLInputElement)) return;
    
    // Add loading state
    const $loading = div({ class: "p-4 text-center text-gray-500" }, "Loading...");
    $wrapper.appendChild($loading);
    $loading.style.display = "none";
    
    // Keep original options if any
    const originalOptions = Array.from($el.options)
      .filter($option => $option.value)
      .map($option => {
        const main = $option.dataset.main || $option.textContent;
        const sub = $option.dataset.sub;
        return { 
          value: $option.value, 
          main, 
          sub, 
          raw: $option.textContent 
        };
      });
    
    // Add debounce for search
    let searchTimeout;
    
    // Override the original event listener to handle AJAX search
		$searchInput.addEventListener("input", (e) => {
			const query = $searchInput.value.trim();
			
			// Clear previous timeout
			if (searchTimeout) clearTimeout(searchTimeout);
			
			// Show message if query is too short
			if (query.length < minSearchLength) {
				if ($options) $options.classList.add("hidden");
				if ($message) {
					$message.classList.remove("hidden");
					$message.textContent = `Type at least ${minSearchLength} characters to search...`;
				}
				if ($loading) $loading.style.display = "none";
				return;
			}
			
			// Check cache first
			if (cache.has(query)) {
				renderResults(cache.get(query), query);
				return;
			}
			
			// Show searching message immediately
			if ($options) {
				// Keep options visible but show a loading indicator
				$options.classList.remove("hidden");
				$options.innerHTML = "";
				const $searchingIndicator = div({ class: "loading-indicator p-3 text-center" }, "Searching...");
				$options.appendChild($searchingIndicator);
			}
			if ($message) $message.classList.add("hidden");
			
			// Set timeout for debounce
			searchTimeout = setTimeout(() => {
				loadResults(query);
			}, 300);
			
			// Stop the event from bubbling up to the original handler
			e.stopPropagation();
		});
    
    /**
		 * Load results from the server
		 * @param {string} query 
		 */
		function loadResults(query) {
			// Don't hide the options during loading - just show loading state
			if ($message) $message.classList.add("hidden");
			
			// Add loading indicator to options area
			if ($options) {
				// Keep options visible but show a loading indicator
				$options.classList.remove("hidden");
				
				// Create loading indicator if it doesn't exist
				if (!$options.querySelector(".loading-indicator")) {
					const $loadingIndicator = div({ class: "loading-indicator p-3 text-center" }, "Loading results...");
					$options.innerHTML = "";
					$options.appendChild($loadingIndicator);
				}
			}
			
			fetch(`${ajaxUrl}?query=${encodeURIComponent(query)}`)
				.then(response => {
					if (!response.ok) throw new Error("Network response was not ok");
					return response.json();
				})
				.then(data => {
					try {
						// Process the data into the format expected by nice-select
						const processedData = processResponseData(data);
						
						// Cache the results
						cache.set(query, processedData);
						
						// Render the results
						renderResults(processedData, query);
					} catch (err) {
						console.error(err.message);
						if ($message) {
							$message.classList.remove("hidden");
							$message.textContent = "Invalid data format received. Check console for details.";
						}
						if ($options) $options.classList.add("hidden");
					}
				})
				.catch(error => {
					console.error("Error fetching data:", error);
					if ($options) $options.classList.add("hidden");
					if ($message) {
						$message.classList.remove("hidden");
						$message.textContent = "Error loading results. Please try again.";
					}
				});
}
    
		/**
		 * Process response data into the format expected by nice-select
		 * @param {any} data - The data returned from the server
		 * @returns {Array<{ value: string, main: string, sub?: string, raw: string }>}
		 */
		function processResponseData(data) {
			// Check if data is an array
			if (!Array.isArray(data)) {
				console.error("Expected array, got:", data);
				throw new Error("Expected response to be an array");
			}
			
			// Handle different response formats
			if (data.length > 0) {
				const firstItem = data[0];
				
				// Standard format with sub label: [{value: string, label: string, sub: string}, ...]
				if (firstItem && typeof firstItem === 'object' && 'value' in firstItem && 'label' in firstItem) {
					return data.map(item => {
						// Construct the raw text differently based on whether sub is present
						const hasSub = 'sub' in item && item.sub;
						const rawText = hasSub ? `${item.label} (${item.sub})` : String(item.label);
						
						return {
							value: String(item.value),
							main: String(item.label),
							sub: hasSub ? String(item.sub) : undefined,
							raw: rawText
						};
					});
				} 
				// Other formats not supported
				else {
					console.error("Unsupported data format. Expected array of objects with 'value' and 'label' properties, got:", data);
					throw new Error("Unsupported data format. Expected [{value: string, label: string, sub?: string}, ...]");
				}
			}
			
			return [];
		}
    
    /**
     * Render the results in the dropdown
     * @param {Array} results 
     * @param {string} query
     */
    function renderResults(results, query) {
      if ($loading) $loading.style.display = "none";
      
      if (results.length === 0) {
        if ($options) $options.classList.add("hidden");
        if ($message) {
          $message.classList.remove("hidden");
          $message.textContent = "No results found matching your search.";
        }
        return;
      }
      
      // Render options
      if (!$options) return;
      $options.innerHTML = "";
      $options.classList.remove("hidden");
      if ($message) $message.classList.add("hidden");
      
      // Combine with original options if present
      const combinedResults = [...originalOptions, ...results];
      
      combinedResults.forEach(option => {
        const $option = div(
          { class: "option", data: { value: option.value } },
          option.main,
          option.sub ? div({ class: "option-sub" }, option.sub) : ''
        );
        
        // Add click handler directly to the option
        $option.addEventListener("click", (e) => {
          selectOption(option);
          e.stopPropagation();
        });
        
        $options.appendChild($option);
      });
    }

		// Add keyboard event handler specifically for AJAX search
		$searchInput.addEventListener("keydown", (e) => {
			// Only handle when the dropdown is open
			const $dropdown = $wrapper.querySelector("[data-dropdown]");
			if (!$dropdown || !$dropdown.classList.contains("open")) return;
			
			if (e.key === "Enter") {
				console.log("Enter key pressed");
				// Immediately stop propagation to prevent the original handler from running
				e.stopPropagation();
				e.preventDefault();
				
				// Find the selected option
				const $selectedOption = $options.querySelector("[data-keyboard-selected]");
				if ($selectedOption&& $selectedOption instanceof HTMLElement && $selectedOption.dataset.value) {
					// Find the option data that matches this value
					const optionValue = $selectedOption.dataset.value;
					const allOptions = [...originalOptions, ...(cache.get($searchInput.value.trim()) || [])];
					const selectedOption = allOptions.find(opt => opt.value === optionValue);
					
					if (selectedOption) {
						selectOption(selectedOption);
					}
				}
			}
		}, true);
    
    /**
		 * Select an option and update the UI
		 * @param {{ value: string, main: string, sub?: string, raw: string }} option 
		 */
		function selectOption(option) {
			/** @type {HTMLSelectElement} */
			const selectEl = /** @type {HTMLSelectElement} */ ($el);
			
			// First, ensure any existing options are removed or updated
			while (selectEl.options.length > 0) {
				selectEl.options[0].remove();
			}
			
			// Add the placeholder option if needed
			if ($placeholder && $placeholder.textContent) {
				const placeholderOption = document.createElement('option');
				placeholderOption.value = "";
				placeholderOption.text = $placeholder.textContent;
				selectEl.add(placeholderOption);
			}
			
			// Add the selected option to the select element
			const selectedOption = document.createElement('option');
			selectedOption.value = option.value;
			selectedOption.text = option.raw;
			selectedOption.selected = true;
			selectEl.add(selectedOption);
			
			// Explicitly set the value
			selectEl.value = option.value;
			
			// Force the select element to validate
			selectEl.setAttribute('selected-value', option.value);
			
			// Dispatch input event first (for validation)
			selectEl.dispatchEvent(new Event("input", { bubbles: true }));
			
			// Then dispatch change event
			selectEl.dispatchEvent(new Event("change", { bubbles: true }));
			
			// Update the display
			if (!option.value) {
				if ($selected) $selected.classList.add("hidden");
				if ($placeholder) $placeholder.classList.remove("hidden");
			} else if ($selected) {
				const $opt = div({}, option.raw);
				$selected.innerHTML = "";
				$selected.appendChild($opt);
				$selected.classList.remove("hidden");
				if ($placeholder) $placeholder.classList.add("hidden");
			}
			
			// Force blur on any active elements
			if (document.activeElement instanceof HTMLElement) {
				document.activeElement.blur();
			}
			
			// Close the dropdown
			if ($wrapper) {
				const $dropdown = $wrapper.querySelector("[data-dropdown]");
				if ($dropdown) $dropdown.classList.remove("open");
				
				// Clear the search input to reset for next time
				if ($searchInput && $searchInput instanceof HTMLInputElement) {
					$searchInput.value = "";
					// Force blur on the search input
					$searchInput.blur();
				}
				
				// Remove all active element focus
				if (document.activeElement instanceof HTMLElement) {
					document.activeElement.blur();
				}
				
				// Use a more substantial delay to ensure all focus events have completed
				setTimeout(() => {
					// Focus the wrapper
					if ($wrapper instanceof HTMLElement) {
						$wrapper.focus();
						
						// Set a flag on the wrapper to prevent immediate reopening
						$wrapper.dataset.justClosed = "true";
						
						// Clear the flag after a short delay to allow normal interactions again
						setTimeout(() => {
							delete $wrapper.dataset.justClosed;
						}, 300);
					}
				}, 50);
			}
		}
  });
}

// Call after document is ready
document.addEventListener("DOMContentLoaded", () => {
  enhanceWithAjax();
});
