// Print functions
// create or return the print portal, appended to the document body
function printableGetPortal() {
  const portal = document.getElementById("print-portal");
  if (portal) {
    return portal;
  }
  const printPortal = document.createElement("div");
  printPortal.id = "print-portal";
  printPortal.style.display = "none";
  printPortal.style.width = "100vw";
  document.body.appendChild(printPortal);
  return printPortal;
}

// show the portal, hide the rest
function printableShowPortal() {
  const children = document.body.children;
  // hide all the children of the body
  Array.from(children).forEach((child) => {
    if (child.tagName.toLowerCase() !== "script") {
      child.style.display = "none";
    }
  });
  // show the portal
  printableGetPortal().style.display = "block";
}

// show the body, hide the portal & clean up any children added to the portal
function printableShowBody() {
  // show all the children of the body
  const children = document.body.children;
  Array.from(children).forEach((child) => {
    child.style.display = "";
  });
  // hide the portal
  const portal = printableGetPortal();
  portal.style.display = "none";
  // remove all the children of the portal
  Array.from(portal.children).forEach((child) => {
    portal.removeChild(child);
  });
}

function handlePrintElementById(id) {
  const portal = printableGetPortal();
  const original = document.getElementById(id);
  const printable = original.cloneNode(true);

  // Copy computed styles from the original element to the cloned element
  copyComputedStyle(original, printable);

  printable.style.display = "block";
  printable.classList.add("print");
  // Append what we want to print into the portal on the index.html
  portal.appendChild(printable);
  setTimeout(() => printableShowPortal(), 100);
  setTimeout(() => window.print(), 100);
  setTimeout(() => printableShowBody(), 100);
}

function copyComputedStyle(source, target) {
  const computedStyle = window.getComputedStyle(source);
  for (let key of computedStyle) {
    target.style[key] = computedStyle.getPropertyValue(key);
  }

  // Recursively copy styles for child elements
  Array.from(source.children).forEach((sourceChild, index) => {
    const targetChild = target.children[index];
    if (targetChild) {
      copyComputedStyle(sourceChild, targetChild);
    }
  });
}
