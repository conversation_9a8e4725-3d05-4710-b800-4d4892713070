// Unified Dashboard Management System
class DashboardManager {
  constructor() {
    this.charts = new Map();
    this.data = new Map();
    this.sections = new Map();
    
    // Chart color palette
    this.chartColors = {
      primary: '#667eea',
      secondary: '#764ba2',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
      purple: '#8b5cf6',
      pink: '#ec4899',
      teal: '#14b8a6',
      orange: '#f97316'
    };

    this.colorPalette = [
      this.chartColors.primary,
      this.chartColors.secondary,
      this.chartColors.success,
      this.chartColors.warning,
      this.chartColors.info,
      this.chartColors.purple,
      this.chartColors.pink,
      this.chartColors.teal,
      this.chartColors.orange,
      this.chartColors.error
    ];

    this.init();
  }

  init() {
    // Get campaign ID from a data attribute or global variable
    const campaignId = document.body.dataset.campaignId || window.campaignId;
    
    if (!campaignId) {
      console.error('Campaign ID not found. Please set data-campaign-id on body or window.campaignId');
      return;
    }

    // Set Mapbox access token if available
    const mapboxToken = document.body.dataset.mapboxToken || window.mapboxToken;
    if (mapboxToken && typeof mapboxgl !== 'undefined') {
      mapboxgl.accessToken = mapboxToken;
    }

    // Initialize all dashboard sections
    this.initSection('careers', {
      endpoint: `/admin/campaigns/${campaignId}/impressions/top_careers`,
      container: '[data-career-stats]'
    });

    this.initSection('lessons', {
      endpoint: `/admin/campaigns/${campaignId}/impressions/lessons`,
      container: '[data-lesson-stats]'
    });

    this.initSection('units', {
      endpoint: `/admin/campaigns/${campaignId}/impressions/units`,
      container: '[data-unit-stats]'
    });

    this.initSection('schools', {
      endpoint: `/admin/campaigns/${campaignId}/impressions/schools`,
      container: '[data-school-stats]'
    });

    // Add more sections as needed
    // this.initSection('section3', { ... });
    // this.initSection('section4', { ... });

    // Global resize handler
    this.setupResizeHandler();
  }

  initSection(sectionId, config) {
    const container = document.querySelector(config.container);
    if (!container) return;

    const section = {
      id: sectionId,
      container,
      endpoint: config.endpoint,
      form: container.querySelector('form'),
      spinner: container.querySelector('[data-spinner]'),
      clearBtn: container.querySelector('[data-clear]'),
      data: null,
    };

    this.sections.set(sectionId, section);

    // Setup event listeners
    this.setupSectionListeners(section);

    // Initial data load
    this.loadSectionData(sectionId);
  }

  setupSectionListeners(section) {
    const { form, clearBtn } = section;

    if (form) {
      form.addEventListener('input', () => this.loadSectionData(section.id));
    }

    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        form.reset();
        this.loadSectionData(section.id);
      });
    }
  }

  setupResizeHandler() {
    let resizeTimeout;
    window.addEventListener('resize', () => {
      if (resizeTimeout) clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Re-render all sections
        this.sections.forEach((section, sectionId) => {
          if (section.data) {
            this.renderSection(sectionId);
          }
        });
      }, 200);
    });
  }

  renderSchoolsTable(section) {
    const table = section.container.querySelector('[data-schools-table] table tbody');
    if (!table || !section.data.schools) return;

    table.innerHTML = '';
    section.data.schools.forEach(school => {
      const tr = document.createElement('tr');
      tr.classList.add('hover:bg-gray-50', 'transition-colors');
      tr.innerHTML = `
        <td class="px-4 py-3 w-16">
          ${school.fileboy_id ? 
            `<div class="w-12 h-12 bg-gray-200 rounded overflow-hidden">
              <img src="https://api.fileboy.io/images/get/${school.fileboy_id}?transform=resize:100x_" class="w-full h-full object-cover" alt="${school.name}" />
            </div>` : 
            `<div class="w-12 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span class="text-gray-400 text-xs">No Logo</span>
            </div>`
          }
        </td>
        <td class="px-4 py-3 font-medium text-gray-900">${school.name}</td>
        <td class="px-4 py-3 text-gray-600">${school.postcode || 'N/A'}</td>
        <td class="px-4 py-3 text-gray-600">${school.views.toLocaleString()}</td>
      `;
      table.appendChild(tr);
    });
  }

  renderRegionalTable(section) {
    const table = section.container.querySelector('#schools-regional-table tbody');
    if (!table || !section.data.regionalViews) return;

    const values = section.data.regionalViews.map(region => region.views);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);

    table.innerHTML = '';
    section.data.regionalViews.forEach(region => {
      const tr = document.createElement('tr');
      tr.classList.add('hover:bg-gray-50', 'transition-colors', 'cursor-pointer');
      tr.dataset.regionName = region.name;
      
      // Calculate heat level
      const normalizedValue = maxValue > minValue ? (region.views - minValue) / (maxValue - minValue) : 0;
      const heatLevel = Math.round(normalizedValue * 5);
      const heatBars = '█'.repeat(heatLevel) + '░'.repeat(5 - heatLevel);
      
      tr.innerHTML = `
        <td class="px-4 py-3 font-medium text-gray-900">${this.titleize(region.name)}</td>
        <td class="px-4 py-3 text-gray-600">${region.views.toLocaleString()}</td>
        <td class="px-4 py-3 text-sm font-mono text-orange-600">${heatBars}</td>
      `;
      table.appendChild(tr);
    });
  }

  renderSchoolsMap(section) {
    if (typeof mapboxgl === 'undefined' || !section.data.schools) {
      console.error('Mapbox GL JS not loaded or no schools data');
      return;
    }

    // Clear any existing map
    const mapContainer = document.getElementById("schools-map");
    mapContainer.innerHTML = '';

    try {
      // You'll need to set your Mapbox access token
      // mapboxgl.accessToken = 'your-mapbox-token-here';
      
      const map = new mapboxgl.Map({
        container: 'schools-map',
        style: 'mapbox://styles/mapbox/streets-v12', // You can change this style
        center: [-2.5555, 54.3555], // UK center [lng, lat]
        zoom: 5
      });

      // Add navigation controls
      map.addControl(new mapboxgl.NavigationControl());

      const schools = section.data.schools.filter(school => 
        school.latitude && school.longitude
      );

      // Create markers for schools
      schools.forEach(school => {
        // Create a popup
        const popup = new mapboxgl.Popup({ offset: 25 })
          .setHTML(`
            <div class="p-3">
              <h3 class="font-semibold text-gray-900">${school.name}</h3>
              ${school.postcode ? `<p class="text-sm text-gray-600">${school.postcode}</p>` : ''}
              <p class="text-sm mt-1"><strong>Engagement:</strong> ${school.views.toLocaleString()}</p>
            </div>
          `);

        // Create a marker
        const marker = new mapboxgl.Marker({
          color: '#ea580c' // Orange color to match theme
        })
          .setLngLat([parseFloat(school.longitude), parseFloat(school.latitude)])
          .setPopup(popup)
          .addTo(map);
      });

      // If there are schools, fit map to show all markers
      if (schools.length > 0) {
        const bounds = new mapboxgl.LngLatBounds();
        schools.forEach(school => {
          bounds.extend([parseFloat(school.longitude), parseFloat(school.latitude)]);
        });
        
        map.on('load', () => {
          map.fitBounds(bounds, {
            padding: 50,
            maxZoom: 10
          });
        });
      }

    } catch (error) {
      console.error('Error rendering Mapbox map:', error);
      // Show fallback message
      mapContainer.innerHTML = `
        <div class="flex items-center justify-center h-full bg-gray-100 text-gray-500">
          <div class="text-center">
            <p class="mb-2">Map could not be loaded</p>
            <p class="text-sm">Please check Mapbox configuration</p>
          </div>
        </div>
      `;
    }
  }

  renderRegionalHeatmap(section) {
    if (!section.data.regionalViews) return;

    const svg = section.container.querySelector("#schools-heatmap-container svg");
    if (!svg) return;

    const regionMappings = {
      east_midlands: "#east-midlands",
      east_of_england: "#east-england", 
      greater_london: "#london",
      london: "#london",
      north_east: "#north-east",
      north_west: "#north-west",
      south_east: "#south-east", 
      south_west: "#south-west",
      west_midlands: "#west-midlands",
      yorkshire_and_the_humber: "#yorkshire-and-humber",
      wales: "#wales",
      scotland: "#scotland",
      northern_ireland: "#northern-ireland"
    };

    // Reset all regions
    Object.values(regionMappings).forEach(selector => {
      const element = svg.querySelector(selector);
      if (element) {
        element.style.fill = "rgb(203, 213, 225)"; // gray-300
        element.style.filter = "brightness(1)";
      }
    });

    const values = section.data.regionalViews.map(region => region.views);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);

    // Color regions based on data
    section.data.regionalViews.forEach(region => {
      const selector = regionMappings[region.name];
      const element = selector ? svg.querySelector(selector) : null;
      
      if (element) {
        const color = this.getColorForValue(region.views, minValue, maxValue);
        element.style.fill = color;
        
        // Add hover effects
        element.addEventListener("mouseover", () => {
          element.style.filter = "brightness(1.2)";
          // Highlight corresponding table row
          const tableRow = section.container.querySelector(`[data-region-name="${region.name}"]`);
          if (tableRow) {
            tableRow.style.backgroundColor = "rgba(251, 146, 60, 0.1)";
          }
        });
        
        element.addEventListener("mouseout", () => {
          element.style.filter = "brightness(1)";
          // Remove table row highlight
          const tableRow = section.container.querySelector(`[data-region-name="${region.name}"]`);
          if (tableRow) {
            tableRow.style.backgroundColor = "";
          }
        });
      }
    });

    // Add hover effects to table rows
    const tableRows = section.container.querySelectorAll('#schools-regional-table tbody tr');
    tableRows.forEach(row => {
      const regionName = row.dataset.regionName;
      row.addEventListener("mouseover", () => {
        const selector = regionMappings[regionName];
        const element = selector ? svg.querySelector(selector) : null;
        if (element) {
          element.style.filter = "brightness(1.2)";
        }
        row.style.backgroundColor = "rgba(251, 146, 60, 0.1)";
      });
      
      row.addEventListener("mouseout", () => {
        const selector = regionMappings[regionName];
        const element = selector ? svg.querySelector(selector) : null;
        if (element) {
          element.style.filter = "brightness(1)";
        }
        row.style.backgroundColor = "";
      });
    });
  }

  getColorForValue(value, minValue, maxValue) {
    // Normalize the value to a range between 0 and 1
    const normalizedValue = maxValue > minValue ? (value - minValue) / (maxValue - minValue) : 0;
    
    // Clamp the value to the range [0, 1]
    const clampedValue = Math.max(0, Math.min(1, normalizedValue));
    
    // Interpolate between yellow and red (orange theme)
    const startColor = { r: 255, g: 228, b: 119 }; // Yellow
    const endColor = { r: 160, g: 0, b: 0 };       // Dark Red
    
    const r = Math.round(startColor.r + clampedValue * (endColor.r - startColor.r));
    const g = Math.round(startColor.g + clampedValue * (endColor.g - startColor.g));
    const b = Math.round(startColor.b + clampedValue * (endColor.b - startColor.b));
    
    return `rgb(${r}, ${g}, ${b})`;
  }

  renderUnitsTable(section) {
    const table = section.container.querySelector('[data-units-table] table tbody');
    if (!table || !section.data.units) return;

    table.innerHTML = '';
    section.data.units.forEach(unit => {
      const tr = document.createElement('tr');
      tr.classList.add('hover:bg-gray-50', 'transition-colors');
      tr.innerHTML = `
        <td class="px-4 py-3 w-20">
          ${unit.fileboy_id ? 
            `<div class="w-16 h-12 bg-gray-200 rounded overflow-hidden">
              <img src="https://api.fileboy.io/images/get/${unit.fileboy_id}?transform=resize:100x_" class="w-full h-full object-cover" alt="${unit.name}" />
            </div>` : 
            `<div class="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span class="text-gray-400 text-xs">No Image</span>
            </div>`
          }
        </td>
        <td class="px-4 py-3 font-medium text-gray-900">${unit.name}</td>
        <td class="px-4 py-3 text-gray-600">${unit.views.toLocaleString()}</td>
      `;
      table.appendChild(tr);
    });
  }

  async loadSectionData(sectionId) {
    const section = this.sections.get(sectionId);
    if (!section) return;

    try {
      this.showSpinner(section);
      this.updateClearButton(section);

      const fd = new FormData(section.form);
      const response = await fetch(`${section.endpoint}?${new URLSearchParams(fd).toString()}`);
      const json = await response.json();
      
      section.data = json;
      
      this.renderSection(sectionId);
    } catch (e) {
      console.error(`Error loading ${sectionId} data:`, e);
    } finally {
      this.hideSpinner(section);
    }
  }

  renderSection(sectionId) {
    const section = this.sections.get(sectionId);
    if (!section || !section.data) return;

    // Destroy existing charts for this section
    this.destroySectionCharts(sectionId);

    // Render based on section type
    switch (sectionId) {
      case 'careers':
        this.renderCareersSection(section);
        break;
      case 'lessons':
        this.renderLessonsSection(section);
        break;
      case 'units':
        this.renderUnitsSection(section);
        break;
      case 'schools':
        this.renderSchoolsSection(section);
        break;
      // Add more cases as needed
    }
  }

  renderCareersSection(section) {
    const { data } = section;

    // Render tables
    this.renderCareersTable(section);
    this.renderJobFamiliesTable(section);

    // Render charts
    this.renderChart(`careers-top-families`, 'doughnut', {
      labels: data.families.slice(0, 5).map(f => f.name),
      datasets: [{
        data: data.families.slice(0, 5).map(f => f.no_of_impressions),
        backgroundColor: this.colorPalette,
        borderWidth: 0,
        hoverOffset: 8
      }]
    }, {
      plugins: {
        legend: {
          position: 'bottom',
          labels: { padding: 20, usePointStyle: true, font: { size: 12 } }
        }
      }
    });

    this.renderChart(`careers-top-careers`, 'bar', {
      labels: (data.careersForSelectedJobFamily || data.careers).slice(0, 5).map(c => c.name),
      datasets: [{
        data: (data.careersForSelectedJobFamily || data.careers).slice(0, 5).map(c => c.views),
        backgroundColor: this.chartColors.primary,
        borderRadius: 6,
        borderSkipped: false
      }]
    }, {
      plugins: { legend: { display: false } },
      scales: {
        y: { beginAtZero: true, grid: { color: '#f1f5f9' } },
        x: { grid: { display: false } }
      }
    });

    // Demographics charts
    this.renderDemographicsCharts('careers', data.jobFamilyViews || []);

    // Time series charts
    this.renderRegionalChart('careers', data);
    this.renderTrendsChart('careers', data);
  }

  renderLessonsSection(section) {
    const { data } = section;

    // Render table
    this.renderLessonsTable(section);

    // Demographics charts
    this.renderDemographicsCharts('lessons', data.views || []);

    // Time series charts
    this.renderLessonRegionalChart(section);
    this.renderTopLessonsChart(section);
  }

  renderCareersTable(section) {
    const table = section.container.querySelector('[data-careers-table] table tbody');
    if (!table || !section.data.careers) return;

    table.innerHTML = '';
    section.data.careers.forEach(career => {
      const tr = document.createElement('tr');
      tr.classList.add('hover:bg-gray-50', 'transition-colors');
      tr.innerHTML = `
        <td class="px-4 py-3 font-medium text-gray-900">${career.name}</td>
        <td class="px-4 py-3 text-gray-600">${career.family}</td>
        <td class="px-4 py-3 text-gray-600">${career.views.toLocaleString()}</td>
      `;
      table.appendChild(tr);
    });
  }

  renderJobFamiliesTable(section) {
    const table = section.container.querySelector('[data-job-familys] table tbody');
    const familyInput = section.container.querySelector('[name="family"]');
    if (!table || !section.data.families) return;

    // Update select options
    if (familyInput) {
      const currentValue = familyInput.value;
      familyInput.innerHTML = '<option value="all">All Job Families</option>';
      section.data.families.forEach(family => {
        const option = document.createElement('option');
        option.value = family.name;
        option.textContent = family.name;
        familyInput.appendChild(option);
      });
      familyInput.value = currentValue;
    }

    table.innerHTML = '';
    section.data.families.forEach(family => {
      const tr = document.createElement('tr');
      tr.classList.add('hover:bg-gray-50', 'cursor-pointer', 'transition-colors');
      tr.innerHTML = `
        <td class="px-4 py-3 font-medium text-gray-900">${family.name}</td>
        <td class="px-4 py-3 text-gray-600">${family.no_of_careers}</td>
        <td class="px-4 py-3 text-gray-600">${family.no_of_impressions.toLocaleString()}</td>
      `;
      tr.addEventListener('click', () => {
        if (familyInput) {
          familyInput.value = family.name;
          this.loadSectionData('careers');
        }
      });
      table.appendChild(tr);
    });
  }

  renderLessonsTable(section) {
    const table = section.container.querySelector('[data-lessons-table] table tbody');
    if (!table || !section.data.lessons) return;

    table.innerHTML = '';
    section.data.lessons.forEach(lesson => {
      const tr = document.createElement('tr');
      tr.classList.add('hover:bg-gray-50', 'transition-colors');
      tr.innerHTML = `
        <td class="px-4 py-3 w-20">
          ${lesson.fileboy_id ? 
            `<div class="w-16 h-12 bg-gray-200 rounded overflow-hidden">
              <img src="https://api.fileboy.io/images/get/${lesson.fileboy_id}" class="w-full h-full object-cover" alt="${lesson.name}" />
            </div>` : 
            `<div class="w-16 h-12 bg-gray-200 rounded flex items-center justify-center">
              <span class="text-gray-400 text-xs">No Image</span>
            </div>`
          }
        </td>
        <td class="px-4 py-3 font-medium text-gray-900">${lesson.name}</td>
        <td class="px-4 py-3 text-gray-600">${lesson.views.toLocaleString()}</td>
      `;
      table.appendChild(tr);
    });
  }

  renderDemographicsCharts(sectionId, viewsData) {
    if (!viewsData || viewsData.length === 0) return;

    const demographics = ['gender', 'age', 'ethnicity', 'region'];
    
    demographics.forEach(demo => {
      const chartId = `${sectionId}-${demo}-chart`;
      const canvas = document.getElementById(chartId);
      if (!canvas) return;

      const data = this.getCountsByField(viewsData, demo);
      
      this.renderChart(chartId, 'doughnut', {
        labels: data.map(d => d.label),
        datasets: [{
          data: data.map(d => d.value),
          backgroundColor: this.colorPalette,
          borderWidth: 0,
          hoverOffset: 6
        }]
      }, {
        plugins: {
          legend: {
            position: 'bottom',
            labels: { padding: 15, usePointStyle: true, font: { size: 11 } }
          }
        }
      });
    });
  }

  renderChart(chartId, type, data, options = {}) {
    const canvas = document.getElementById(chartId);
    if (!canvas) return;

    // Destroy existing chart
    this.destroyChart(chartId);

    const ctx = canvas.getContext('2d');
    const chart = new Chart(ctx, {
      type,
      data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        ...options
      }
    });

    this.charts.set(chartId, chart);
    return chart;
  }

  renderRegionalChart(sectionId, data) {
    if (!data.regionalViews || data.regionalViews.length === 0) return;

    const groupDataMap = {};
    data.regionalViews.forEach(view => {
      const date = view.created_at;
      const group = groupDataMap[date] || { date, regions: {} };
      groupDataMap[date] = group;
      group.regions[view.region] = (group.regions[view.region] || 0) + view.views;
    });

    const groupedData = Object.values(groupDataMap);
    groupedData.sort((a, b) => a.date.localeCompare(b.date));

    const regions = ["east_midlands", "east_of_england", "london", "north_east", "north_west", "south_east", "south_west", "west_midlands", "yorkshire_and_the_humber"];

    const datasets = regions.map((region, index) => ({
      label: this.titleize(region),
      data: groupedData.map(d => d.regions[region] || 0),
      backgroundColor: this.colorPalette[index % this.colorPalette.length],
      borderWidth: 0
    }));

    this.renderChart(`${sectionId}-regional-chart`, 'bar', {
      labels: groupedData.map(d => d.date),
      datasets
    }, {
      plugins: {
        legend: {
          position: 'top',
          labels: { usePointStyle: true, padding: 15, font: { size: 11 } }
        }
      },
      scales: {
        x: { stacked: true, grid: { display: false } },
        y: { stacked: true, beginAtZero: true, grid: { color: '#f1f5f9' } }
      }
    });
  }

  renderTrendsChart(sectionId, data) {
    if (!data.jobFamilyViews || data.jobFamilyViews.length === 0 || data.careersForSelectedJobFamily) return;

    const jobFamilies = Array.from(new Set(data.jobFamilyViews.map(entry => entry.name)));
    
    let groupedData = {};
    data.jobFamilyViews.forEach(entry => {
      const date = entry.date;
      if (!groupedData[date]) {
        groupedData[date] = {};
      }
      groupedData[date][entry.name] ??= 0;
      groupedData[date][entry.name]++;
    });

    const groupedDataArr = Object.entries(groupedData).map(([date, views]) => {
      return { date, views };
    }).sort((a, b) => a.date.localeCompare(b.date));

    const datasets = jobFamilies.map((family, index) => ({
      label: family,
      data: groupedDataArr.map(d => d.views[family] || 0),
      borderColor: this.colorPalette[index % this.colorPalette.length],
      backgroundColor: this.colorPalette[index % this.colorPalette.length] + '20',
      tension: 0.4,
      fill: false
    }));

    this.renderChart(`${sectionId}-trends-chart`, 'line', {
      labels: groupedDataArr.map(d => d.date),
      datasets
    }, {
      plugins: {
        legend: {
          position: 'top',
          labels: { usePointStyle: true, padding: 20 }
        }
      },
      scales: {
        y: { beginAtZero: true, grid: { color: '#f1f5f9' } },
        x: { grid: { display: false } }
      }
    });
  }

  renderLessonRegionalChart(section) {
    if (!section.data.views || section.data.views.length === 0) return;

    const dates = this.generateMonthsBetween(section.data.views, "created_at");
    const regions = ["east_midlands", "east_of_england", "london", "north_east", "north_west", "south_east", "south_west", "west_midlands", "yorkshire_and_the_humber"];

    const groupDataMap = {};
    dates.forEach(date => {
      groupDataMap[date] = { date, regions: {} };
    });

    section.data.views.forEach(view => {
      const date = view.created_at;
      if (!(date in groupDataMap)) return;
      groupDataMap[date].regions[view.region] = (groupDataMap[date].regions[view.region] || 0) + 1;
    });

    const groupedData = Object.values(groupDataMap);
    groupedData.sort((a, b) => a.date.localeCompare(b.date));

    const datasets = regions.map((region, index) => ({
      label: this.titleize(region),
      data: groupedData.map(d => d.regions[region] || 0),
      backgroundColor: this.colorPalette[index % this.colorPalette.length],
      borderWidth: 0
    }));

    this.renderChart('lessons-regional-chart', 'bar', {
      labels: groupedData.map(d => d.date),
      datasets
    }, {
      plugins: {
        legend: {
          position: 'top',
          labels: { usePointStyle: true, padding: 15, font: { size: 11 } }
        }
      },
      scales: {
        x: { stacked: true, grid: { display: false } },
        y: { stacked: true, beginAtZero: true, grid: { color: '#f1f5f9' } }
      }
    });
  }

  renderTopLessonsChart(section) {
    if (!section.data.topLessonViewsOverTime || section.data.topLessonViewsOverTime.length === 0) return;

    const dates = this.generateMonthsBetween(section.data.topLessonViewsOverTime, "date");
    const names = Array.from(new Set(section.data.topLessonViewsOverTime.map(({ name }) => name).filter(Boolean)));

    const groupedData = {};
    dates.forEach(date => {
      groupedData[date] = {};
    });
    section.data.topLessonViewsOverTime.forEach(view => {
      if (!(view.date in groupedData)) return;
      groupedData[view.date][view.name] = view.views;
    });

    const datasets = names.map((name, index) => ({
      label: name,
      data: Object.keys(groupedData).sort().map(date => groupedData[date][name] || 0),
      backgroundColor: this.colorPalette[index % this.colorPalette.length],
      borderWidth: 0
    }));

    this.renderChart('lessons-top-lessons-chart', 'bar', {
      labels: Object.keys(groupedData).sort(),
      datasets
    }, {
      plugins: {
        legend: {
          position: 'bottom',
          labels: { usePointStyle: true, padding: 15, font: { size: 11 } }
        }
      },
      scales: {
        x: { stacked: true, grid: { display: false } },
        y: { stacked: true, beginAtZero: true, grid: { color: '#f1f5f9' } }
      }
    });
  }

  renderUnitRegionalChart(section) {
    if (!section.data.views || section.data.views.length === 0) return;

    const dates = this.generateMonthsBetween(section.data.views, "created_at");
    const regions = ["east_midlands", "east_of_england", "london", "north_east", "north_west", "south_east", "south_west", "west_midlands", "yorkshire_and_the_humber"];

    const groupDataMap = {};
    dates.forEach(date => {
      groupDataMap[date] = { date, regions: {} };
    });

    section.data.views.forEach(view => {
      const date = view.created_at;
      if (!(date in groupDataMap)) return;
      groupDataMap[date].regions[view.region] = (groupDataMap[date].regions[view.region] || 0) + 1;
    });

    const groupedData = Object.values(groupDataMap);
    groupedData.sort((a, b) => a.date.localeCompare(b.date));

    const datasets = regions.map((region, index) => ({
      label: this.titleize(region),
      data: groupedData.map(d => d.regions[region] || 0),
      backgroundColor: this.colorPalette[index % this.colorPalette.length],
      borderWidth: 0
    }));

    this.renderChart('units-regional-chart', 'bar', {
      labels: groupedData.map(d => d.date),
      datasets
    }, {
      plugins: {
        legend: {
          position: 'top',
          labels: { usePointStyle: true, padding: 15, font: { size: 11 } }
        }
      },
      scales: {
        x: { stacked: true, grid: { display: false } },
        y: { stacked: true, beginAtZero: true, grid: { color: '#f1f5f9' } }
      }
    });
  }

  renderTopUnitsChart(section) {
    if (!section.data.topUnitViewsOverTime || section.data.topUnitViewsOverTime.length === 0) return;

    const dates = this.generateMonthsBetween(section.data.topUnitViewsOverTime, "date");
    const names = Array.from(new Set(section.data.topUnitViewsOverTime.map(({ name }) => name).filter(Boolean)));

    const groupedData = {};
    dates.forEach(date => {
      groupedData[date] = {};
    });
    section.data.topUnitViewsOverTime.forEach(view => {
      if (!(view.date in groupedData)) return;
      groupedData[view.date][view.name] = view.views;
    });

    const datasets = names.map((name, index) => ({
      label: name,
      data: Object.keys(groupedData).sort().map(date => groupedData[date][name] || 0),
      backgroundColor: this.colorPalette[index % this.colorPalette.length],
      borderWidth: 0
    }));

    this.renderChart('units-top-units-chart', 'bar', {
      labels: Object.keys(groupedData).sort(),
      datasets
    }, {
      plugins: {
        legend: {
          position: 'bottom',
          labels: { usePointStyle: true, padding: 15, font: { size: 11 } }
        }
      },
      scales: {
        x: { stacked: true, grid: { display: false } },
        y: { stacked: true, beginAtZero: true, grid: { color: '#f1f5f9' } }
      }
    });
  }

  renderSchoolsSection(section) {
    const { data } = section;

    // Render tables
    this.renderSchoolsTable(section);
    this.renderRegionalTable(section);

    // Render map and heatmap
    this.renderSchoolsMap(section);
    this.renderRegionalHeatmap(section);
  }

  renderUnitsSection(section) {
    const { data } = section;

    // Render table
    this.renderUnitsTable(section);

    // Demographics charts
    this.renderDemographicsCharts('units', data.views || []);

    // Time series charts
    this.renderUnitRegionalChart(section);
    this.renderTopUnitsChart(section);
  }

  destroyChart(chartId) {
    const chart = this.charts.get(chartId);
    if (chart) {
      chart.destroy();
      this.charts.delete(chartId);
    }
  }

  destroySectionCharts(sectionId) {
    const chartsToDestroy = [];
    this.charts.forEach((chart, chartId) => {
      if (chartId.startsWith(sectionId)) {
        chartsToDestroy.push(chartId);
      }
    });
    chartsToDestroy.forEach(chartId => this.destroyChart(chartId));
  }

  showSpinner(section) {
    if (section.spinner) {
      section.spinner.classList.remove('hidden');
    }
  }

  hideSpinner(section) {
    if (section.spinner) {
      section.spinner.classList.add('hidden');
    }
  }

  updateClearButton(section) {
    if (!section.clearBtn || !section.form) return;

    const fd = new FormData(section.form);
    const isDirty = Array.from(fd.entries()).some(([key, value]) => value !== 'all');
    
    if (isDirty) {
      section.clearBtn.classList.remove('hidden');
    } else {
      section.clearBtn.classList.add('hidden');
    }
  }

  getCountsByField(viewsData, field) {
    const counts = {};
    viewsData.filter(view => this.humanize(view[field])).forEach(view => {
      const key = this.humanize(view[field]);
      counts[key] = (counts[key] || 0) + 1;
    });
    return Object.entries(counts).map(([label, value]) => ({ label, value }));
  }

  generateMonthsBetween(views, prop) {
    const dates = views.map(view => new Date(view[prop] + '-01'));
    const earliestDate = new Date(Math.min(...dates));
    const currentDate = new Date();

    const months = [];
    let current = new Date(earliestDate);

    while (current <= currentDate) {
      const year = current.getFullYear();
      const month = (current.getMonth() + 1).toString().padStart(2, '0');
      months.push(`${year}-${month}`);
      current.setMonth(current.getMonth() + 1);
    }

    return months;
  }

  titleize(str) {
    return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  humanize(str) {
    if (!str) return '';
    return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
}

// Global utility functions that the original code expects
function withCharts(callback) {
  // This function was used in the original code for Google Charts
  // We can ignore it now since we're using Chart.js directly
}

function titleize(str) {
  return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function humanize(str) {
  if (!str) return '';
  return str.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function computeNextMonths(startMonth, count) {
  const months = [];
  const date = new Date(startMonth + '-01');
  for (let i = 0; i < count; i++) {
    date.setMonth(date.getMonth() + 1);
    months.push(date.getFullYear() + '-' + String(date.getMonth() + 1).padStart(2, '0'));
  }
  return months;
}

function smoothArray(arr) {
  return arr.map((val, i) => {
    if (i === 0 || i === arr.length - 1) return val;
    return (arr[i-1] + val + arr[i+1]) / 3;
  });
}

// Initialize the dashboard when DOM and Chart.js are ready
document.addEventListener('DOMContentLoaded', () => {
  // Check if Chart.js is loaded
  if (typeof Chart === 'undefined') {
    // If Chart.js isn't loaded yet, wait for it
    const checkChart = () => {
      if (typeof Chart !== 'undefined') {
        window.dashboardManager = new DashboardManager();
      } else {
        setTimeout(checkChart, 100);
      }
    };
    checkChart();
  } else {
    // Chart.js is already loaded
    window.dashboardManager = new DashboardManager();
  }
});