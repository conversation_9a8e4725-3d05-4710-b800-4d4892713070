document.addEventListener('DOMContentLoaded', () => {
  // --- CONFIGURATION ---
  // Detect if logged in and server settings are available
  const isLoggedIn = typeof window.presentationSettings === 'object' && window.presentationSettings !== null;
  // Optionally, allow ERB to set the update URL for AJAX PATCH
  const updateUrl = window.presentationSettingsUpdateUrl || null;

  const SETTINGS_DEFAULTS = {
      fontSize: "Medium",
      fontFamily: "proxima-soft",
      letterSpacing: "Normal",
      textCase: "none",
      colorScheme: "default",
      narrationVoice: "echo",
      narrationAutoPlay: false,
      sidebarSide: "right",
      sidebarVisible: true,
      topBottomTextVisible: true,
  };

  const FONT_SIZES_CONFIG = {
      options: ["Small", "Medium", "Large"],
      percentages: { Small: "87.5%", Medium: "100%", Large: "125%" },
      previewSizes: { Small: "1.75rem", Medium: "2rem", Large: "2.25rem" }
  };

  const LETTER_SPACINGS_CONFIG = {
      options: ["Tight", "Normal", "Wide", "Extra Wide"],
      values: { Tight: "-0.03em", Normal: "normal", Wide: "0.05em", "Extra Wide": "0.1em" }
  };

  const COLOR_SCHEMES_CONFIG = {
      default: { bgClass: "bg-white", textClass: "text-black", bgColor: "white", textColor: "black" },
      dark: { bgClass: "bg-black", textClass: "text-white", bgColor: "black", textColor: "white" },
      blue: { bgClass: "bg-blue-700", textClass: "text-white", bgColor: "#1d4ed8", textColor: "white" },
      green: { bgClass: "bg-green-700", textClass: "text-white", bgColor: "#15803d", textColor: "white" },
      purple: { bgClass: "bg-purple-700", textClass: "text-white", bgColor: "#7e22ce", textColor: "white" },
      contrast: { bgClass: "bg-black", textClass: "text-yellow-300", bgColor: "black", textColor: "#fde047" },
  };

  const LOCAL_STORAGE_KEYS = {
      fontSize: "fontSize",
      fontFamily: "fontFamily",
      letterSpacing: "letterSpacing",
      textCase: "textCase",
      colorScheme: "colorScheme",
      narrationVoice: "narration_voice",
      narrationAutoPlay: "narration_auto_play",
      sidebarSide: "sidebar_side",
      sidebarVisible: "sidebar_visible",
      topBottomTextVisible: "top_bottom_text_visible",
  };

  // --- DOM ELEMENTS CACHE ---
  const DOMElements = {};

  // --- STATE ---
  let currentSettings = { ...SETTINGS_DEFAULTS };
  window.currentSettings = currentSettings; 
  let globalPresentationColorClasses = {
      headerBg: COLOR_SCHEMES_CONFIG.default.bgClass,
      headerText: COLOR_SCHEMES_CONFIG.default.textClass,
      footerBg: COLOR_SCHEMES_CONFIG.default.bgClass,
      footerText: COLOR_SCHEMES_CONFIG.default.textClass,
  };
  let currentFontSizeIndex = FONT_SIZES_CONFIG.options.indexOf(SETTINGS_DEFAULTS.fontSize);
  let currentLetterSpacingIndex = LETTER_SPACINGS_CONFIG.options.indexOf(SETTINGS_DEFAULTS.letterSpacing);


  // --- UTILITY FUNCTIONS ---
  // Abstracted get/set for settings
  function getSetting(key, defaultValue) {
    if (isLoggedIn) {
      // Map JS keys to server keys
      const map = {
        fontSize: 'font_size',
        fontFamily: 'font_family',
        letterSpacing: 'letter_spacing',
        textCase: 'text_case',
        colorScheme: 'color_scheme',
        narrationVoice: 'narration_voice',
        narrationAutoPlay: 'narration_auto_play',
      };
      const serverKey = map[key] || key;
      let val = window.presentationSettings && window.presentationSettings[serverKey];
      console.log("getSetting", key, serverKey, val);
      
      // Convert string 'true'/'false' to boolean for narrationAutoPlay
      if (key === 'narrationAutoPlay') { // Fixed: check for camelCase key
        if (val === true || val === 'true') return true;
        if (val === false || val === 'false') return false;
        return defaultValue;
      }
      return typeof val !== 'undefined' && val !== null && val !== '' ? val : defaultValue;
    } else {
      // Fallback to localStorage
      let val = localStorage.getItem(LOCAL_STORAGE_KEYS[key]); // Use the mapped key
      if (key === 'narrationAutoPlay') {
        if (val === 'true') return true;
        if (val === 'false') return false;
        return defaultValue;
      }
      return val !== null && val !== '' ? val : defaultValue;
    }
  }

  function saveSetting(key, value) {
    if (isLoggedIn) {
      // Save to window.presentationSettings (for immediate UI update)
      const map = {
        fontSize: 'font_size',
        fontFamily: 'font_family',
        letterSpacing: 'letter_spacing',
        textCase: 'text_case',
        colorScheme: 'color_scheme',
        narrationVoice: 'narration_voice',
        narrationAutoPlay: 'narration_auto_play',
      };
      const serverKey = map[key] || key;
      if (!window.presentationSettings) window.presentationSettings = {};
      window.presentationSettings[serverKey] = value;
      // No-op: actual save is via saveAllSettings (AJAX)
    } else {
      localStorage.setItem(key, value);
    }
  }

  // Save all settings to backend (if logged in) or localStorage
  function persistSettingsToBackend(settingsObj) {
    console.log("Persisting settings to backend:", settingsObj);
    if (!isLoggedIn || !updateUrl) return Promise.resolve();
    // Rails expects PATCH with form data (not JSON)
    const formData = new FormData();
    for (const [k, v] of Object.entries(settingsObj)) {
      formData.append(`presentation_settings[${k}]`, v);
    }
    // CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
    return fetch(updateUrl, {
      method: 'PATCH',
      headers: Object.assign({ 'Accept': 'application/json' }, csrfToken ? { 'X-CSRF-Token': csrfToken } : {}),
      body: formData,
      credentials: 'same-origin',
    }).then(resp => {
      if (!resp.ok) throw new Error('Failed to save settings');
      return resp.json();
    }).then(data => {
      if (data && data.presentation_settings) {
        window.presentationSettings = data.presentation_settings;
      }
      return data;
    }).catch(err => {
      // Optionally show error to user
      console.error('Failed to save presentation settings:', err);
    });
  }

  function updateElementClass(element, removePrefixRegex, addClass) {
      if (!element) return;
      const classesToKeep = Array.from(element.classList).filter(cls => !removePrefixRegex.test(cls));
      element.className = [...classesToKeep, addClass].join(" ").trim();
  }

  function applyStyle(element, property, value) {
      if (element) element.style[property] = value;
  }

  function applyTextTransform(elements, textCase) {
      elements.forEach(selector => {
          const el = DOMElements[selector] || document.getElementById(selector);
          applyStyle(el, "textTransform", textCase);
      });
  }

  // --- CORE APPLICATION LOGIC ---
  function applyFontSizeSetting(size) {
      // Apply to presentation root
      if (document.documentElement) {
          document.documentElement.style.fontSize = FONT_SIZES_CONFIG.percentages[size];
      }
      // Apply to preview
      if (DOMElements.unifiedPreviewText) {
          DOMElements.unifiedPreviewText.style.fontSize = FONT_SIZES_CONFIG.previewSizes[size];
      }
  }

  // Map for Google Fonts
  const FONT_FAMILY_MAP = {
    'proxima-soft': 'proxima-soft',
    "'Atkinson Hyperlegible', Arial, sans-serif": "'Atkinson Hyperlegible', Arial, sans-serif",
    "'Inter', Arial, sans-serif": "'Inter', Arial, sans-serif",
    "'Roboto', Arial, sans-serif": "'Roboto', Arial, sans-serif",
    "'Lato', Arial, sans-serif": "'Lato', Arial, sans-serif",
    "'Open Sans', Arial, sans-serif": "'Open Sans', Arial, sans-serif"
  };

  function applyFontFamilySetting(family) {
      const fontFamily = FONT_FAMILY_MAP[family] || family;
      applyStyle(DOMElements.presentationSlide, "fontFamily", fontFamily);
      applyStyle(DOMElements.unifiedPreviewText, "fontFamily", fontFamily);
  }

  function applyLetterSpacingSetting(spacing) {
      const spacingValue = LETTER_SPACINGS_CONFIG.values[spacing];
      applyStyle(DOMElements.presentationSlide, "letterSpacing", spacingValue);
      applyStyle(DOMElements.unifiedPreviewText, "letterSpacing", spacingValue);
  }

  function applyTextCaseSetting(textCase) {
      applyStyle(DOMElements.presentationSlide, "textTransform", textCase);
      applyStyle(DOMElements.unifiedPreviewText, "textTransform", textCase);
  }

  function applyColorSchemeSetting(schemeName) {
      const scheme = COLOR_SCHEMES_CONFIG[schemeName] || COLOR_SCHEMES_CONFIG.default;

      // Update global presentation color classes (used by other parts if necessary, though direct application is preferred)
      globalPresentationColorClasses = {
          headerBg: scheme.bgClass,
          headerText: scheme.textClass,
          footerBg: scheme.bgClass,
          footerText: scheme.textClass,
      };

      // Apply to Preview
      if (DOMElements.unifiedPreviewHeader) {
          DOMElements.unifiedPreviewHeader.className = `${scheme.bgClass} p-4 transition-all duration-300`;
      }
      if (DOMElements.unifiedPreviewText) {
          DOMElements.unifiedPreviewText.className = `text-2xl font-bold ${scheme.textClass} transition-all duration-300`;
      }

      // Apply to Presentation
      const colorClassRemoveRegex = /^(bg-|text-)(white|black|blue-700|green-700|purple-700|yellow-300)/;
      updateElementClass(DOMElements.slideHeader, colorClassRemoveRegex, scheme.bgClass);
      updateElementClass(DOMElements.slideHeaderText, colorClassRemoveRegex, scheme.textClass);
      updateElementClass(DOMElements.slideFooter, colorClassRemoveRegex, scheme.bgClass);
      updateElementClass(DOMElements.slideFooterText, colorClassRemoveRegex, scheme.textClass);
  }


  function applyAllSettingsToPreview() {
      if (!DOMElements.unifiedPreviewText || !DOMElements.unifiedPreviewHeader) return;

      applyFontSizeSetting(currentSettings.fontSize); // Uses preview-specific logic within
      applyFontFamilySetting(currentSettings.fontFamily);
      applyLetterSpacingSetting(currentSettings.letterSpacing);
      applyTextCaseSetting(currentSettings.textCase);
      applyColorSchemeSetting(currentSettings.colorScheme); // Applies to preview

      // Brief highlight effect for preview
      [DOMElements.unifiedPreviewHeader, DOMElements.unifiedPreviewText].forEach(el => {
          if (el) {
              el.classList.add("ring-2", "ring-blue-500");
              setTimeout(() => el.classList.remove("ring-2", "ring-blue-500"), 300);
          }
      });
  }

  function applyAllSettingsToPresentation(isInitialLoad = false) {
      console.log("Applying all saved settings to presentation");
      applyFontSizeSetting(currentSettings.fontSize); // Applies to documentElement
      applyFontFamilySetting(currentSettings.fontFamily); // Applies to presentationSlide
      applyLetterSpacingSetting(currentSettings.letterSpacing); // Applies to presentationSlide
      applyTextCaseSetting(currentSettings.textCase); // Applies to specific presentation text elements
      applyColorSchemeSetting(currentSettings.colorScheme); // Applies to presentation header/footer
      applyLayoutSettings();

      if (isInitialLoad) {
           // The first block of code's logic is now integrated here if presentationSlide exists
          if (DOMElements.presentationSlide) {
              // This ensures that even before settings panel interaction, saved settings are applied.
              // Most of this is covered by the functions above, but this serves as a catch-all
              // for the initial setup mentioned in the original first block.
              DOMElements.presentationSlide.style.fontFamily = currentSettings.fontFamily;
              DOMElements.presentationSlide.style.letterSpacing = LETTER_SPACINGS_CONFIG.values[currentSettings.letterSpacing];
              DOMElements.presentationSlide.style.textTransform = currentSettings.textCase;
          }
      }
  }

  function updateUIWithNewNarrationSettings() {
      if (DOMElements.autoPlayButton && DOMElements.autoPlayCheckbox) {
          const autoPlayIcon = DOMElements.autoPlayButton.querySelector("i");
          if (autoPlayIcon) {
              autoPlayIcon.classList.toggle("opacity-50", !DOMElements.autoPlayCheckbox.checked);
          }
      }
  }


  function loadAllSettings() {
    currentSettings.fontSize = getSetting('fontSize', SETTINGS_DEFAULTS.fontSize);
    currentSettings.fontFamily = getSetting('fontFamily', SETTINGS_DEFAULTS.fontFamily);
    currentSettings.letterSpacing = getSetting('letterSpacing', SETTINGS_DEFAULTS.letterSpacing);
    currentSettings.textCase = getSetting('textCase', SETTINGS_DEFAULTS.textCase);
    currentSettings.colorScheme = getSetting('colorScheme', SETTINGS_DEFAULTS.colorScheme);
    currentSettings.narrationVoice = getSetting('narrationVoice', SETTINGS_DEFAULTS.narrationVoice);
    currentSettings.narrationAutoPlay = getSetting('narrationAutoPlay', SETTINGS_DEFAULTS.narrationAutoPlay);

    currentFontSizeIndex = FONT_SIZES_CONFIG.options.indexOf(currentSettings.fontSize);
    if (currentFontSizeIndex === -1) currentFontSizeIndex = FONT_SIZES_CONFIG.options.indexOf(SETTINGS_DEFAULTS.fontSize);

    currentLetterSpacingIndex = LETTER_SPACINGS_CONFIG.options.indexOf(currentSettings.letterSpacing);
    if (currentLetterSpacingIndex === -1) currentLetterSpacingIndex = LETTER_SPACINGS_CONFIG.options.indexOf(SETTINGS_DEFAULTS.letterSpacing);

    // Update UI elements in settings panel
    if (DOMElements.sizeLabel) DOMElements.sizeLabel.textContent = currentSettings.fontSize;
    if (DOMElements.fontFamilySelect) DOMElements.fontFamilySelect.value = currentSettings.fontFamily;
    if (DOMElements.letterSpacingLabel) DOMElements.letterSpacingLabel.textContent = currentSettings.letterSpacing;
    if (DOMElements.textCaseSelect) DOMElements.textCaseSelect.value = currentSettings.textCase;
    if (DOMElements.colorSchemeSelect) DOMElements.colorSchemeSelect.value = currentSettings.colorScheme;
    if (DOMElements.voiceSelect) DOMElements.voiceSelect.value = currentSettings.narrationVoice;
    if (DOMElements.autoPlayCheckbox) DOMElements.autoPlayCheckbox.checked = !!currentSettings.narrationAutoPlay;

    // Layout settings
    currentSettings.sidebarSide = getSetting(LOCAL_STORAGE_KEYS.sidebarSide, SETTINGS_DEFAULTS.sidebarSide);
    let sidebarVisibleVal = getSetting(LOCAL_STORAGE_KEYS.sidebarVisible, SETTINGS_DEFAULTS.sidebarVisible);
    currentSettings.sidebarVisible = (sidebarVisibleVal === true || sidebarVisibleVal === 'true');
    let topBottomTextVisibleVal = getSetting(LOCAL_STORAGE_KEYS.topBottomTextVisible, SETTINGS_DEFAULTS.topBottomTextVisible);
    currentSettings.topBottomTextVisible = (topBottomTextVisibleVal === true || topBottomTextVisibleVal === 'true');

    console.log("Loaded settings:", currentSettings, LOCAL_STORAGE_KEYS);

    if (DOMElements.sidebarSideSelect) DOMElements.sidebarSideSelect.value = currentSettings.sidebarSide;
    if (DOMElements.sidebarVisibleCheckbox) DOMElements.sidebarVisibleCheckbox.checked = !!currentSettings.sidebarVisible;
    if (DOMElements.topBottomTextVisibleCheckbox) DOMElements.topBottomTextVisibleCheckbox.checked = !!currentSettings.topBottomTextVisible;
  }

  function saveAllSettings() {
    // Save to localStorage or window.presentationSettings
    saveSetting(LOCAL_STORAGE_KEYS.fontSize, currentSettings.fontSize);
    saveSetting(LOCAL_STORAGE_KEYS.fontFamily, currentSettings.fontFamily);
    saveSetting(LOCAL_STORAGE_KEYS.letterSpacing, currentSettings.letterSpacing);
    saveSetting(LOCAL_STORAGE_KEYS.textCase, currentSettings.textCase);
    saveSetting(LOCAL_STORAGE_KEYS.colorScheme, currentSettings.colorScheme);
    saveSetting(LOCAL_STORAGE_KEYS.narrationVoice, currentSettings.narrationVoice);
    saveSetting(LOCAL_STORAGE_KEYS.narrationAutoPlay, String(currentSettings.narrationAutoPlay));

    // Layout settings
    saveSetting(LOCAL_STORAGE_KEYS.sidebarSide, currentSettings.sidebarSide);
    saveSetting(LOCAL_STORAGE_KEYS.sidebarVisible, currentSettings.sidebarVisible);
    saveSetting(LOCAL_STORAGE_KEYS.topBottomTextVisible, currentSettings.topBottomTextVisible);

    console.log("Saved settings:", currentSettings, LOCAL_STORAGE_KEYS);
    console.log(isLoggedIn, updateUrl);

    // If logged in, persist to backend
    if (isLoggedIn && updateUrl) {
      // Map JS keys to server keys for PATCH
      console.log("Saving settings to backend");
      const settingsForServer = {
        font_size: currentSettings.fontSize,
        font_family: currentSettings.fontFamily,
        letter_spacing: currentSettings.letterSpacing,
        text_case: currentSettings.textCase,
        color_scheme: currentSettings.colorScheme,
        narration_voice: currentSettings.narrationVoice,
        narration_auto_play: currentSettings.narrationAutoPlay,
        sidebar_side: currentSettings.sidebarSide,
        sidebar_visible: currentSettings.sidebarVisible,
        top_bottom_text_visible: currentSettings.topBottomTextVisible,
      };
      persistSettingsToBackend(settingsForServer).then(() => {
        // Optionally show a success message
      });
    }
  }

  // --- SETTINGS PANEL LOGIC ---
  function showSettingsPanel() {
      if (!DOMElements.settingsPanel || !DOMElements.settingsContent) return;
      DOMElements.settingsPanel.classList.add("visible");
      DOMElements.settingsContent.style.transform = "scale(0.95)";
      DOMElements.settingsContent.style.opacity = "0";
      setTimeout(() => {
          DOMElements.settingsContent.style.transition = "transform 0.3s ease, opacity 0.3s ease";
          DOMElements.settingsContent.style.transform = "scale(1)";
          DOMElements.settingsContent.style.opacity = "1";
      }, 10);
      document.body.style.overflow = "hidden";
      loadAllSettings(); // Load fresh settings when panel opens
      applyAllSettingsToPreview(); // Update preview with loaded/current settings
  }

  function hideSettingsPanel() {
      if (!DOMElements.settingsPanel) return;
      if (DOMElements.settingsContent) {
          DOMElements.settingsContent.style.transform = "scale(0.95)";
          DOMElements.settingsContent.style.opacity = "0";
          setTimeout(() => {
              DOMElements.settingsPanel.classList.remove("visible");
              DOMElements.settingsContent.style.transform = "";
              DOMElements.settingsContent.style.opacity = "";
              document.body.style.overflow = "";
          }, 200);
      } else {
          DOMElements.settingsPanel.classList.remove("visible");
          document.body.style.overflow = "";
      }
  }

  function initializeDOMElements() {
      DOMElements.settingsToggle = document.getElementById("settings-toggle");
      DOMElements.settingsPanel = document.getElementById("settings-panel");
      DOMElements.settingsContent = DOMElements.settingsPanel?.querySelector(".settings-content");
      DOMElements.closeSettings = document.getElementById("close-settings");
      DOMElements.saveSettingsButton = document.getElementById("save-settings"); // Renamed for clarity

      DOMElements.unifiedPreviewHeader = document.getElementById("unified-preview-header");
      DOMElements.unifiedPreviewText = document.getElementById("unified-preview-text");

      DOMElements.decreaseButton = document.getElementById("text-size-decrease");
      DOMElements.increaseButton = document.getElementById("text-size-increase");
      DOMElements.sizeLabel = document.getElementById("text-size-label");
      DOMElements.fontFamilySelect = document.getElementById("font-family-select");
      DOMElements.letterSpacingDecrease = document.getElementById("letter-spacing-decrease");
      DOMElements.letterSpacingIncrease = document.getElementById("letter-spacing-increase");
      DOMElements.letterSpacingLabel = document.getElementById("letter-spacing-label");
      DOMElements.textCaseSelect = document.getElementById("text-case-select");
      DOMElements.colorSchemeSelect = document.getElementById("color-scheme-select");

      DOMElements.voiceSelect = document.getElementById("narration-voice-select");
      DOMElements.autoPlayCheckbox = document.getElementById("narration-auto-play-checkbox");
      DOMElements.autoPlayButton = document.getElementById("narration-auto-play-button");

      // Presentation elements
      DOMElements.presentationSlide = document.getElementById("presentation-slide");
      DOMElements.slideHeader = document.getElementById("slide-header");
      DOMElements.slideHeaderText = document.getElementById("slide-header-text");
      DOMElements.slideFooter = document.getElementById("slide-footer");
      DOMElements.slideFooterText = document.getElementById("slide-footer-text");
      DOMElements.slideOutroTitle = document.getElementById("slide-outro-title");
       // Wrapper for querySelector, assuming 'wrapper' is available globally or passed appropriately
      // For this refactor, assuming these are globally accessible by ID if not part of wrapper
      const wrapper = window.wrapper || document; // Fallback to document if wrapper is not defined

      // Layout settings controls
      DOMElements.sidebarSideSelect = document.getElementById("sidebar-side-select");
      DOMElements.sidebarVisibleCheckbox = document.getElementById("sidebar-visible-checkbox");
      DOMElements.topBottomTextVisibleCheckbox = document.getElementById("top-bottom-text-visible-checkbox");
  }


  function initializeSettingsPanel() {

      // Layout settings event listeners
      DOMElements.sidebarSideSelect?.addEventListener("change", (e) => {
        currentSettings.sidebarSide = e.target.value;
        applyLayoutSettings();
      });
      DOMElements.sidebarVisibleCheckbox?.addEventListener("change", (e) => {
        currentSettings.sidebarVisible = e.target.checked;
        applyLayoutSettings();
      });
      DOMElements.topBottomTextVisibleCheckbox?.addEventListener("change", (e) => {
        currentSettings.topBottomTextVisible = e.target.checked;
        applyLayoutSettings();
      });
      console.log("Initializing settings panel with unified preview");
      if (!DOMElements.settingsToggle || !DOMElements.settingsPanel) return;

      DOMElements.settingsToggle.addEventListener("click", showSettingsPanel);
      DOMElements.closeSettings.addEventListener("click", hideSettingsPanel);
      DOMElements.settingsPanel.addEventListener("click", (e) => e.target === DOMElements.settingsPanel && hideSettingsPanel());

      // Font size controls
      DOMElements.decreaseButton?.addEventListener("click", () => {
          if (currentFontSizeIndex > 0) {
              currentFontSizeIndex--;
              currentSettings.fontSize = FONT_SIZES_CONFIG.options[currentFontSizeIndex];
              DOMElements.sizeLabel.textContent = currentSettings.fontSize;
              applyAllSettingsToPreview();
          }
      });
      DOMElements.increaseButton?.addEventListener("click", () => {
          if (currentFontSizeIndex < FONT_SIZES_CONFIG.options.length - 1) {
              currentFontSizeIndex++;
              currentSettings.fontSize = FONT_SIZES_CONFIG.options[currentFontSizeIndex];
              DOMElements.sizeLabel.textContent = currentSettings.fontSize;
              applyAllSettingsToPreview();
          }
      });

      // Font family
      DOMElements.fontFamilySelect?.addEventListener("change", (e) => {
          currentSettings.fontFamily = e.target.value;
          applyAllSettingsToPreview();
      });

      // Letter spacing
      DOMElements.letterSpacingDecrease?.addEventListener("click", () => {
          if (currentLetterSpacingIndex > 0) {
              currentLetterSpacingIndex--;
              currentSettings.letterSpacing = LETTER_SPACINGS_CONFIG.options[currentLetterSpacingIndex];
              DOMElements.letterSpacingLabel.textContent = currentSettings.letterSpacing;
              applyAllSettingsToPreview();
          }
      });
      DOMElements.letterSpacingIncrease?.addEventListener("click", () => {
          if (currentLetterSpacingIndex < LETTER_SPACINGS_CONFIG.options.length - 1) {
              currentLetterSpacingIndex++;
              currentSettings.letterSpacing = LETTER_SPACINGS_CONFIG.options[currentLetterSpacingIndex];
              DOMElements.letterSpacingLabel.textContent = currentSettings.letterSpacing;
              applyAllSettingsToPreview();
          }
      });

      // Text case
      DOMElements.textCaseSelect?.addEventListener("change", (e) => {
          currentSettings.textCase = e.target.value;
          applyAllSettingsToPreview();
      });

      // Colour scheme
      DOMElements.colorSchemeSelect?.addEventListener("change", (e) => {
          currentSettings.colorScheme = e.target.value;
          applyAllSettingsToPreview();
      });
      
      // Narration settings (only update currentSettings, applied on save)
      DOMElements.voiceSelect.addEventListener('change', (e) => {
          currentSettings.narrationVoice = e.target.value;
      });
      DOMElements.autoPlayCheckbox?.addEventListener('change', (e) => {
          currentSettings.narrationAutoPlay = e.target.checked;
      });


      // Save settings
      DOMElements.saveSettingsButton?.addEventListener("click", () => {
          // Ensure current selections for narration are captured if not already by change events
          if(DOMElements.voiceSelect) currentSettings.narrationVoice = DOMElements.voiceSelect.value;
          if(DOMElements.autoPlayCheckbox) currentSettings.narrationAutoPlay = DOMElements.autoPlayCheckbox.checked;

          saveAllSettings();
          applyAllSettingsToPresentation();
          updateUIWithNewNarrationSettings();
          hideSettingsPanel();
      });
  }

  // --- GLOBAL INITIALIZATION ---

  // Layout application logic
  function applyLayoutSettings() {
    console.log("Applying layout settings");
    const sidebar = document.getElementById("slides-navigation");
    const slideToggle = document.getElementById("slides-toggle");
    const mainContent = document.getElementById('presentation-slide');
    if (sidebar) {
      // Show/hide sidebar
      sidebar.style.display = currentSettings.sidebarVisible ? "block" : "none";
      
      if (currentSettings.sidebarVisible) {
        const windowWidth = window.innerWidth;
        if (windowWidth > 1024) {
          mainContent.style.width = "calc(100% - 160px)";
        } else {
          mainContent.style.width = "100%";
        }
      } else {
        mainContent.style.width = "100%";
      }

      if (window.innerWidth > 1024) {
        slideToggle.style.display = "none";
      } else {
        slideToggle.style.display = currentSettings.sidebarVisible ? "block" : "none";
      }

      // Apply sidebar side
      if (currentSettings.sidebarSide === "left" && window.innerWidth > 1024 && currentSettings.sidebarVisible) {
        sidebar.style.left = "0";
        sidebar.style.right = "auto";
        mainContent.style.marginLeft = "160px";
      } else {
        sidebar.style.left = "auto";
        sidebar.style.right = "0";
        mainContent.style.marginLeft = "0";
      }
    }
    // Show/hide top & bottom text
    const topText = document.getElementById("slide-header");
    const bottomText = document.getElementById("slide-footer");
    if (topText) topText.style.display = currentSettings.topBottomTextVisible ? "" : "none";
    if (bottomText) bottomText.style.display = currentSettings.topBottomTextVisible ? "" : "none";
  }
  function applyInitialPresentationSettings() {
      loadAllSettings(); // Load into currentSettings
      applyAllSettingsToPresentation(true); // Apply to the actual presentation
      updateUIWithNewNarrationSettings(); // Update things like autoplay button
  }

  // --- MAIN EXECUTION ---
  initializeDOMElements(); // Cache all DOM elements first
  initializeSettingsPanel(); // Setup panel listeners and behavior
  applyInitialPresentationSettings(); // Apply settings on page load

  // The initial anonymous block applying styles if (presentationSlide) and
  // the applyColorClassesToSlide() function are now handled by applyInitialPresentationSettings
  // and the consolidated applyColorSchemeSetting.

  if (window.updateContent) {
    console.log("updateContent function is available");
    window.applySettings = function() {
      initializeDOMElements(); // Cache all DOM elements first
      initializeSettingsPanel(); // Setup panel listeners and behavior
      applyInitialPresentationSettings(); // Apply settings on page load
    };
  }

  window.addEventListener("resize", applyLayoutSettings);
});
