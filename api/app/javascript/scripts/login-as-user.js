async function loginAsUser(userId) {
    const { target_token, return_token } = await getUserSignInToken(userId);
    await swapAccounts(target_token, return_token)
}

async function getUserSignInToken(userId) {
    try {
        const response = await fetch(`/sessions/${userId}/get_sign_in_token`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            }
        });
        return response.json()
    } catch (e) {
        console.error(e);
    }
}

async function swapAccounts(token, returnToken) {
    if (returnToken) {
        localStorage.setItem("returnToken", returnToken);
    }

    try {
        const response = await fetch("/sessions/swap_accounts", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({ token, return_token: returnToken })
        })

        if (response.redirected) {
            window.location.href = response.url;
        }
    } catch (e) {
        console.error(e);
    }
}
