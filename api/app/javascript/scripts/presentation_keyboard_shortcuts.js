function initKeyboardNavigation() {
  console.log('Initializing keyboard navigation for presentation');
  if (!document.getElementById('presentation-wrapper')) {
    setTimeout(initKeyboardNavigation, 100);
    return;
  }
  
  document.addEventListener('keydown', function(event) {
    // Don't capture keyboard events when user is typing in a text field
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }

    
    // Skip if a modal dialog is open
    const dialogOpen = document.querySelector('dialog[open]');

                  
    if (dialogOpen && event.key !== 'Escape') {
      return;
    }

    
    switch (event.key) {
      case 'ArrowRight':
      case ' ': // Space bar
        event.preventDefault();
        const nextSlide = document.getElementById('next-slide-link')
        console.log('Next slide link:', nextSlide);
        document.getElementById('next-slide-link')?.click();
        break;
        
      case 'ArrowLeft':
      case 'Backspace':
        event.preventDefault();
        document.getElementById('previous-slide-link')?.click();
        break;
        
      case 'Home':
        event.preventDefault();
        // Find the first slide link in navigation
        const firstSlideLink = document.querySelector('#slides-navigation ul li:first-child a');
        firstSlideLink?.click();
        break;
        
      case 'End':
        event.preventDefault();
        // Find the last slide link in navigation
        const lastSlideLink = document.querySelector('#slides-navigation ul li:last-child a');
        lastSlideLink?.click();
        break;
        
      case 'f':
      case 'F':
        event.preventDefault();
        document.getElementById('fullscreen-toggle')?.click();
        break;
        
      case 's':
      case 'S':
        event.preventDefault();
        // Find and click the settings button
        const settingsButton = document.getElementById('settings-toggle') || 
                               document.querySelector('[data-pop-out="button"]');
        settingsButton?.click();
        break;
        
      case 'n':
      case 'N':
        event.preventDefault();
        // Find and click the narration button
        const narrationButton = document.querySelector('[data-narration]');
        narrationButton?.click();
        break;
        
      case 'Escape':
        // If settings panel is open, close it
        const settingsPanel = document.getElementById('settings-panel');
        if (settingsPanel && window.getComputedStyle(settingsPanel).display !== 'none') {
          document.getElementById('close-settings')?.click();
          return;
        }
        
        // If slides navigation is visible, hide it
        const slidesNav = document.getElementById('slides-navigation');
        if (slidesNav && slidesNav.classList.contains('visible') && !slidesNav.classList.contains('pinned')) {
          slidesNav.classList.remove('visible');
          return;
        }
        
        // If in fullscreen, exit fullscreen
        if (document.fullscreenElement) {
          document.exitFullscreen();
        }
        break;
    }
  });
  
  // Manage focus when modals are opened
  const settingsToggle = document.getElementById('settings-toggle');
  const closeSettings = document.getElementById('close-settings');
  
  if (settingsToggle && closeSettings) {
    let lastFocusedElement = null;
    
    settingsToggle.addEventListener('click', function() {
      lastFocusedElement = document.activeElement;
      setTimeout(() => {
        closeSettings.focus();
      }, 100);
    });
    
    closeSettings.addEventListener('click', function() {
      setTimeout(() => {
        if (lastFocusedElement) {
          lastFocusedElement.focus();
        }
      }, 100);
    });
  }
  
  // Announce slide changes to screen readers
  const announceSlideChange = function() {
    const slideNumber = document.querySelector('#slide-controls [aria-live="polite"] [name="current-slide-number"]');
    if (slideNumber) {
      // Trigger aria-live update by slightly modifying and restoring content
      const originalText = slideNumber.textContent;
      slideNumber.textContent = originalText + ' ';
      setTimeout(() => {
        slideNumber.textContent = originalText;
      }, 50);
    }
  };
  
  // Observe DOM changes to detect slide transitions
  const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
      if (mutation.type === 'childList' && mutation.target.id === 'presentation-wrapper') {
        announceSlideChange();
      }
    });
  });
  
  observer.observe(document.getElementById('presentation-wrapper'), { 
    childList: true 
  });
}
