document.addEventListener("DOMContentLoaded", function () {
  let choice_nodes = [];
  function buildFieldSelectListChoices() {
    choice_nodes.forEach((node) => node.destroy());
    const elements = document.querySelectorAll(".field-select");
    elements.forEach((element) => {
      const choices = new Choices(element, {
        removeItemButton: true, // enables the removal of selected items
        searchEnabled: true, // enables the search feature
        searchResultLimit: 5, // limits the number of search results displayed
      });
      choice_nodes.push(choices);
    });
  }
  window.buildFieldSelectListChoices = buildFieldSelectListChoices;
  buildFieldSelectListChoices();
});
