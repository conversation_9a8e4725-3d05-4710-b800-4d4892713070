// @ts-check
function quizGenerateUID(type) {
  return `${type}_${new Date().valueOf()}_${Math.random().toString(36).substring(2, 10)}`;
}
const UID = {
  option: () => quizGenerateUID("o"),
  question: () => quizGenerateUID("q"),
  bucket: () => quizGenerateUID("b"),
};

function alphabet(index) {
  return "abcdefghijklmnopqrstuvwxyz"[index];
}

class QuipQuizBuilder {
  _name = "";
  questions = [];

  data_change_events = [];

  constructor(quizData) {
    this.name = quizData.name;
    this.questions = quizData.questions
      .sort((a, b) => (a.weight ?? 0) - (b.weight ?? 0))
      .map((question, index) => {
        const QuestionClass = QUIP_QUIZ_TYPE_CLASS_MAP[question.question_type];
        const q = new QuestionClass(question, { useDefault: false });
        q.weight = index;
        return q;
      });
    this.triggerDataChangeEvents();
  }

  get valid() {
    return this.validations.every((x) => x.valid);
  }

  get validations() {
    return [
      { message: "Must have a title", valid: !!this.name.trim() },
      {
        message: "Must have at least 1 question",
        valid: this.questions.length > 0,
      },
      {
        message: "All questions must be valid",
        valid: this.questions.every((question) => question.valid),
      },
    ];
  }

  get name() {
    return this._name;
  }
  set name(value) {
    this._name = value;
    this.triggerDataChangeEvents();
  }

  findOrCreateDataChangeEventById(id, callback) {
    const existingEvent = this.data_change_events.find((x) => x.id === id);
    if (existingEvent) {
      existingEvent.callback = callback;
    } else {
      this.data_change_events.push({ id, callback });
    }
  }

  triggerDataChangeEvents() {
    this.data_change_events.forEach((event) => {
      event.callback({ name: this.name, questions: this.questions });
    });
  }

  typeToName(type) {
    return QUIP_QUIZ_TYPE_NAME_MAP[type];
  }

  // build the JSON format
  export() {
    const questions = Array.isArray(this.questions) ? this.questions : [];
    return {
      name: this.name,
      questions: questions.map((question) => question.build()),
      total_score: questions.reduce((acc, question) => acc + question.score, 0),
    };
  }

  // This is for adding NEW questions (so old format types are not required)
  addQuestion(type) {
    const validQuestionTypes = [
      "multi_choice",
      "fill_in_blanks",
      "image_bucket",
      "text_bucket",
      "sort_list",
      "free_text",
    ];

    if (!validQuestionTypes.includes(type)) {
      throw new Error(`Invalid question type: ${type}`);
    }

    const QuestionClass = QUIP_QUIZ_TYPE_CLASS_MAP[type];
    const question = new QuestionClass({ question_type: type, weight: this.questions.length }, { useDefault: true });
    question.weight = this.questions.length;
    this.questions.push(question);
    this.triggerDataChangeEvents();
    return question;
  }

  deleteQuestion(key) {
    const questions = this.questions.filter((x) => x.key !== key);
    if (this.questions.length - questions.length !== 1) {
      throw new Error(`Found multiple questions with key ${key}`);
    }
    this.questions = questions;
    this.triggerDataChangeEvents();
  }

  setOrder(questions) {
    this.questions = questions.map((q, i) => {
      q.weight = i;
      return q;
    });
    this.triggerDataChangeEvents();
  }

  getQuestionByKey(key) {
    return this.questions.find((x) => x.key === key);
  }
}

// MARK: QUESTION
class Question {
  id = null;
  key = "";
  question = {};

  data_change_events = [];

  constructor(data, _data) {
    this.id = data.id;
    this.key = UID.question();
    this.question_type = data.question_type;
    this.weight = data.weight;
    this.data_json = { ...(data.data_json || {}) };
  }

  findOrCreateDataChangeEventById(id, callback) {
    const existingEvent = this.data_change_events.find((x) => x.id === id);
    if (existingEvent) {
      existingEvent.callback = callback;
    } else {
      this.data_change_events.push({ id, callback });
    }
  }

  triggerDataChangeEvents() {
    this.data_change_events.forEach((event) => {
      event.callback();
    });
  }

  get score() {
    return 0; // placeholder for individual questions
  }

  get valid() {
    return this.validations.every((x) => x.valid);
  }

  get name() {
    return `Question ${this.weight + 1} (${QUIP_QUIZ_TYPE_NAME_MAP[this.question_type]})`;
  }

  set prompt(value) {
    this.data_json.prompt = value;
    this.triggerDataChangeEvents();
  }
  get prompt() {
    return this.data_json.prompt;
  }

  set feedback(value) {
    this.data_json.feedback = value;
    this.triggerDataChangeEvents();
  }
  get feedback() {
    return this.data_json.feedback;
  }

  set fileboyImageId(value) {
    this.data_json.fileboyImageId = value;
    this.triggerDataChangeEvents();
  }
  get fileboyImageId() {
    return this.data_json.fileboyImageId;
  }

  get validations() {
    return [
      { message: "Must have a prompt", valid: !!this.prompt },
      { message: "Must have a type", valid: !!this.question_type },
      { message: "Must have a weight", valid: !isNaN(this.weight) },
      {
        message: "Must have question data",
        valid: !!this.data_json,
      },
    ];
  }

  setPartial(data, item) {
    Object.assign(data, item);
    this.triggerDataChangeEvents();
  }

  compile() {}
  build() {}
}

// MARK: FILL IN BLANKS
class FillInBlank extends Question {
  constructor(data, opts) {
    super(data, opts);
    if (data?.data_json?.options) {
      // we store the options as { [key]: [options] } so we need to convert it back to an array
      // as the array format is much easier to work with when editing the question
      this.data_json.options = Object.entries(data?.data_json?.options).map(([key, values]) => {
        values = values.map((val) => ({ ...val, key: val.value || val.label }));
        if (!values.find((x) => x.correct)) {
          const idx = values.findIndex((opt) => opt.value === data?.data_json.validResponse[key]);
          if (idx > -1) {
            values[idx].correct = true;
          }
        }
        return {
          key: key,
          options: values,
        };
      });
    }
    if (opts.useDefault) {
      // this is just the friendly editable format.
      this.data_json = {
        prompt: "Fill in the blanks",
        options: [
          {
            key: "rises",
            options: [
              { key: UID.option(), label: "North", correct: false },
              { key: UID.option(), label: "East", correct: true },
              { key: UID.option(), label: "South", correct: false },
              { key: UID.option(), label: "West", correct: false },
            ],
          },
          {
            key: "sets",
            options: [
              { key: UID.option(), label: "North", correct: false },
              { key: UID.option(), label: "East", correct: false },
              { key: UID.option(), label: "South", correct: false },
              { key: UID.option(), label: "West", correct: true },
            ],
          },
        ],
        feedback: "",
        template: "The sun rises in the {{rises}} and sets in the {{sets}}.",
        validResponse: [],
        fileboyImageId: "",
      };
      this.setAnswers();
    }
  }

  get score() {
    return this.data_json.validResponse.length;
  }

  get valid() {
    return super.valid && Array.isArray(this.validations) && this.validations.every((x) => x.valid);
  }

  set template(value) {
    this.data_json.template = value;
    this.compile();
    this.triggerDataChangeEvents();
  }
  get template() {
    return this.data_json.template;
  }

  get options() {
    return this.data_json.options;
  }

  get validations() {
    return [
      ...super.validations,
      {
        message: "Must have at least 1 option",
        valid: this.options.length > 0,
      },
      {
        message: "Each template must have at least 1 correct answer",
        valid: this.options.every((x) => x.options.length > 0 && x.options.find((x) => x.correct)),
      },
      {
        message: "All template options must have a label",
        valid: this.options.every((x) => x.options.every((subOpt) => subOpt.label.trim())),
      },
      {
        message: "All template options must have unique labels",
        valid: this.options.every(
          (template) =>
            template.options.filter((x) => template.options.filter((y) => y.label.trim() === x.label.trim()).length > 1)
              .length == 0
        ),
      },
    ];
  }

  addOption(key, data) {
    const option = { key: UID.option(), ...data };
    this.options.find((x) => x.key == key).options.push(option);
    this.triggerDataChangeEvents();
    return option;
  }

  editOption(groupKey, key, data) {
    const option = this.data_json.options.find((x) => x.key === groupKey).options.find((x) => x.key === key);
    if (!option) {
      throw new Error(`Option with key ${key} does not exist`);
    }
    this.setPartial(option, data);
    if (data.correct) {
      // make other options not correct
      this.data_json.options
        .find((x) => x.key == groupKey)
        .options.forEach((x) => {
          if (x.key !== key) {
            x.correct = false;
          }
        });
    }

    this.setAnswers();
  }

  deleteOption(groupKey, key) {
    const bucket = this.options.find((x) => x.key === groupKey);
    if (!bucket) throw new Error(`Bucket with key ${groupKey} does not exist`);
    bucket.options = bucket.options.filter((x) => x.key !== key);
    this.setAnswers();
    this.triggerDataChangeEvents();
  }

  setAnswers() {
    this.data_json.validResponse = this.options.map((data) => {
      const correct = data.options.find((x) => x.correct);
      return { field: data.key, value: correct?.key ?? "" };
    });
    this.triggerDataChangeEvents();
  }

  get template_as_array() {
    const template = this.data_json.template;
    const regex = /{{(.*?)}}/g;
    const matches = [];
    let match;
    while ((match = regex.exec(template)) !== null) {
      matches.push(match[1]);
    }
    return matches;
  }

  // convert the template into the "buckets"
  compile() {
    const matches = this.template_as_array;
    // rebuilding the options etc, but keep the existing values
    this.data_json.options = Object.values(
      matches.reduce((acc, match) => {
        const existing = this.data_json.options.find((x) => x.key === match);
        if (existing) {
          acc[match] = { key: match, options: existing.options };
        } else {
          acc[match] = {
            key: match,
            options: [
              {
                key: match,
                label: match,
                value: match,
                correct: true,
              },
            ],
          };
        }
        return acc;
      }, {})
    );
    this.data_json.options = Array.from(this.data_json.options).map((opt) => ({
      ...opt,
      options: opt.options.map((o) => ({ ...o, value: o.label })),
    }));
  }

  build() {
    this.compile();
    return {
      id: this.id,
      question_type: this.question_type,
      weight: this.weight,
      score: this.score,
      data_json: {
        key: this.key,
        prompt: this.prompt,
        options: this.options.reduce((acc, item) => {
          acc[item.key] = item.options;
          return acc;
        }, {}),
        feedback: this.feedback,
        template: this.template,
        validResponse: this.options.reduce((acc, item) => {
          acc[item.key] = item.options.find((x) => x.correct)?.value;
          return acc;
        }, {}),
        fileboyImageId: this.fileboyImageId,
      },
    };
  }
}

// MARK: FREE TEXT
class FreeText extends Question {
  constructor(data, opts) {
    super(data, opts);
    if (opts.useDefault) {
      this.data_json = {
        prompt: "Enter your free text question here",
        feedback: "",
        minLength: null,
        maxLength: null,
        fileboyImageId: "",
      };
    }
  }

  get score() {
    return 1;
  }

  set maxLength(val) {
    this.data_json.maxLength = val;
    this.triggerDataChangeEvents();
  }
  get maxLength() {
    const value = this.data_json.maxLength;
    if (value == "" || value == null || value == undefined) {
      return undefined;
    }
    return value;
  }

  set minLength(val) {
    this.data_json.minLength = val;
    this.triggerDataChangeEvents();
  }
  get minLength() {
    const value = this.data_json.minLength;
    if (value == "" || value == null || value == undefined) {
      return undefined;
    }
    return value;
  }

  get valid() {
    return super.valid && this.validations.every((x) => x.valid);
  }

  // return true if min length is present and a number
  get hasMinLengthNum() {
    return this.minLength && !isNaN(Number(this.minLength));
  }
  // return true if max length is present and a number
  get hasMaxLengthNum() {
    return this.maxLength && !isNaN(Number(this.maxLength));
  }
  // parse min length to int
  get minLengthInt() {
    return this.minLength ? parseInt(this.minLength) : 0;
  }
  // parse max length to int
  get maxLengthInt() {
    return this.maxLength ? parseInt(this.maxLength) : 0;
  }

  get validations() {
    return [
      ...super.validations,
      {
        message: "Min length must be greater than or equal to 0",
        valid: this.hasMinLengthNum ? this.minLengthInt >= 0 : true,
      },
      {
        message: "Max length must be greater than 0",
        valid: this.hasMaxLengthNum ? this.maxLengthInt > 0 : true,
      },
      {
        message: "Min length must be less than or equal to Max length",
        valid: this.hasMinLengthNum && this.hasMaxLengthNum ? this.minLengthInt <= this.maxLengthInt : true,
      },
      {
        message: "Max length must be greater than or equal to Min length",
        valid: this.hasMinLengthNum && this.hasMaxLengthNum ? this.maxLengthInt >= this.minLengthInt : true,
      },
    ];
  }

  build() {
    return {
      id: this.id,
      question_type: this.question_type,
      weight: this.weight,
      score: this.score,
      data_json: {
        key: this.key,
        prompt: this.prompt,
        feedback: this.feedback,
        minLength: this.minLength,
        maxLength: this.maxLength,
        fileboyImageId: this.fileboyImageId,
      },
    };
  }
}

// MARK: IMAGE BUCKET
class ImageBucket extends Question {
  constructor(data, opts) {
    super(data, opts);
    if (opts.useDefault) {
      this.data_json = {
        prompt: "Place the images into the correct bucket",
        buckets: [
          {
            key: "bucket1",
            name: "Foxes",
          },
          {
            key: "bucket2",
            name: "Dogs",
          },
        ],
        options: [
          {
            key: UID.option(),
            bucket: "bucket1",
            label: "Animal 1",
            fileboyId: "8cd79520-4756-48a6-9d2e-bf3f68c82f89",
          },
          {
            key: UID.option(),
            bucket: "bucket2",
            label: "Animal 2",
            fileboyId: "5b601c2a-7b03-4c0e-be2a-9253a7584c86",
          },
        ],
        feedback: "",
        fileboyImageId: "",
      };
    }

    this.buckets.forEach((bucket) => {
      this.setPartial(bucket, { label: bucket.label || bucket.name });
    });
    this.options.forEach((option, index) => {
      this.setPartial(option, { label: option.label || option.name || alphabet(index).toUpperCase() });
    });
  }

  get score() {
    return this.options.length;
  }

  set buckets(buckets) {
    this.data_json.buckets = buckets;
    this.triggerDataChangeEvents();
  }
  get buckets() {
    return this.data_json.buckets;
  }
  set options(data) {
    this.data_json.options = data;
    this.triggerDataChangeEvents();
  }
  get options() {
    return this.data_json.options;
  }

  get valid() {
    return super.valid && this.validations.every((x) => x.valid);
  }

  get validations() {
    return [
      ...super.validations,
      {
        message: "Must have at least 1 bucket",
        valid: this.buckets.length > 0,
      },
      {
        message: "Must have at least 1 option",
        valid: this.options.length > 0,
      },
      {
        message: "All options must have a bucket",
        valid: this.options.every((x) => x.bucket && x.bucket.trim()),
      },
      {
        message: "All buckets must have a name",
        valid: this.buckets.every((x) => x.name.trim()),
      },
      {
        message: "Bucket names must be unique",
        valid:
          this.buckets.filter((x) => this.buckets.filter((y) => y.name.trim() === x.name.trim()).length > 1).length ==
          0,
      },
      {
        message: "All options must have a label",
        valid: this.options.every((x) => x.label.trim()),
      },
      {
        message: "All options must have an image",
        valid: this.options.every((x) => x.fileboyId),
      },
      {
        message: "Options must have unique labels",
        valid:
          this.options.filter((x) => this.options.filter((y) => y.label.trim() === x.label.trim()).length > 1).length ==
          0,
      },
    ];
  }

  addBucket(name) {
    const bucket = { key: UID.bucket(), name };
    this.data_json.buckets.push(bucket);
    this.triggerDataChangeEvents();
    return bucket;
  }

  editBucket(key, name) {
    const bucket = this.data_json.buckets.find((x) => x.key === key);
    if (!bucket) {
      throw new Error(`Bucket with key ${key} does not exist`);
    }
    bucket.name = name?.trim();
    this.triggerDataChangeEvents();
  }

  deleteBucket(key) {
    this.data_json.buckets = this.data_json.buckets.filter((x) => x.key !== key);
    this.data_json.options = this.data_json.options.map((opt) => (opt.bucket == key ? { ...opt, bucket: null } : opt));
    this.triggerDataChangeEvents();
  }

  addOption(data) {
    const option = {
      key: UID.option(),
      ...data,
    };
    this.data_json.options.push(option);
    this.triggerDataChangeEvents();
    return option;
  }

  editOption(key, data) {
    const option = this.data_json.options.find((x) => x.key === key);
    if (!option) {
      throw new Error(`Option with key ${key} does not exist`);
    }
    this.setPartial(option, data);
  }

  deleteOption(key) {
    this.data_json.options = this.data_json.options.filter((x) => x.key !== key);
    this.triggerDataChangeEvents();
  }

  build() {
    return {
      id: this.id,
      question_type: this.question_type,
      weight: this.weight,
      score: this.score,
      data_json: {
        key: this.key,
        prompt: this.prompt,
        feedback: this.feedback,
        buckets: this.buckets,
        options: this.options.map((option) => ({ ...option, value: option.key })),
        fileboyImageId: this.fileboyImageId,
      },
    };
  }
}

// MARK: MULTI CHOICE
class MultiChoice extends Question {
  constructor(data, opts) {
    super(data, opts);
    if (opts.useDefault) {
      this.data_json = {
        prompt: "Select the correct answer",
        options: [
          { key: UID.option(), label: "Answer 1", correct: true },
          { key: UID.option(), label: "Answer 2", correct: false },
          { key: UID.option(), label: "Answer 3", correct: false },
          { key: UID.option(), label: "Answer 4", correct: false },
        ],
        feedback: "",
        fileboyImageId: "",
        limitChoices: { sameNumberAsCorrectAnswers: true, min: null, max: null },
      };
    }
    if (!this.data_json["limitChoices"]) {
      this.data_json["limitChoices"] = { sameNumberAsCorrectAnswers: true };
    }

    if (!this.options.find((x) => x.correct)) {
      const correctValues = data.data_json.validResponse;
      correctValues.forEach((correctValue) => {
        const idx = this.options.findIndex((opt) => opt.value === correctValue);
        if (idx > -1) {
          this.options[idx].correct = true;
        }
      });
    }

    this.compile();
    this.triggerDataChangeEvents();
  }

  get score() {
    return this.options.filter((x) => x.correct).length;
  }

  set options(data) {
    this.data_json.options = data;
    this.triggerDataChangeEvents();
  }
  get options() {
    return this.data_json.options;
  }

  get valid() {
    return super.valid && this.validations.every((x) => x.valid);
  }

  get validations() {
    return [
      ...super.validations,
      {
        message: "Must have at least 1 option",
        valid: this.options.length > 0,
      },
      {
        message: "Must have at least 1 correct option",
        valid: this.options.some((x) => x.correct),
      },
      {
        message: "All options must have a label",
        valid: this.options.every((x) => x.label.trim()),
      },
      {
        message: "Options must have unique labels",
        valid:
          this.options.filter((x) => this.options.filter((y) => y.label.trim() === x.label.trim()).length > 1).length ==
          0,
      },
      (() => {
        if (this.limitChoices.sameNumberAsCorrectAnswers) {
          return { message: "", valid: true };
        }
        const countCorrect = this.options.filter((x) => x.correct).length;
        const min = this.limitChoices.min;
        return {
          message: "Min choices must be less than or equal to the number of correct choices",
          valid: !(min && min > countCorrect),
        };
      })(),
      (() => {
        if (this.limitChoices.sameNumberAsCorrectAnswers) {
          return { message: "", valid: true };
        }
        const min = this.limitChoices.min;
        const max = this.limitChoices.max;
        return { message: "Min choices must be less than or equal to max choices", valid: !(min && max && min > max) };
      })(),
      (() => {
        if (this.limitChoices.sameNumberAsCorrectAnswers) {
          return { message: "", valid: true };
        }
        const countCorrect = this.options.filter((x) => x.correct).length;
        const max = this.limitChoices.max;
        return {
          message: "Max choices must be greater than or equal to the number of correct choices",
          valid: !(max && max < countCorrect),
        };
      })(),
    ];
  }

  get limitChoices() {
    return this.data_json.limitChoices;
  }

  // { sameNumberAsCorrectAnswers: true, min: 1, max: null }
  setLimitChoices(data) {
    if ("min" in data && data.min != null) {
      data.min = Number(data.min);
    }
    if ("max" in data && data.max != null) {
      data.max = Number(data.max);
    }
    this.data_json.limitChoices = { ...this.data_json.limitChoices, ...data };
  }

  addOption(data) {
    const option = {
      key: UID.option(),
      ...data,
    };
    this.data_json.options.push(option);
    this.compile();
    return option;
  }

  editOption(key, data) {
    const option = this.data_json.options.find((x) => x.key === key);
    if (!option) {
      throw new Error(`Option with key ${key} does not exist`);
    }
    this.setPartial(option, data);
    this.compile();
  }

  deleteOption(key) {
    this.data_json.options = this.options.filter((x) => x.key !== key);
    this.compile();
  }

  compile() {
    this.options = Array.from(this.options).map((opt) => ({ ...opt, value: opt.key }));
    this.data_json.validResponse = this.options.filter((x) => x.correct).map((x) => x.key);
    this.triggerDataChangeEvents();
  }

  build() {
    this.compile();
    return {
      id: this.id,
      question_type: this.question_type,
      weight: this.weight,
      score: this.score,
      data_json: {
        key: this.key,
        prompt: this.prompt,
        feedback: this.feedback,
        options: this.options.map((option) => ({ ...option, value: option.key })),
        fileboyImageId: this.fileboyImageId,
        limitChoices: this.data_json.limitChoices,
        validResponse: this.data_json.validResponse,
      },
    };
  }
}

// MARK: TEXT BUCKET
class TextBucket extends Question {
  constructor(data, opts) {
    super(data, opts);
    if (opts.useDefault) {
      this.data_json = {
        prompt: "Place the descriptions in the correct groups",
        buckets: [
          {
            key: "bucket1",
            name: "Bucket A",
          },
          {
            key: "bucket2",
            name: "Bucket B",
          },
        ],
        options: [
          { key: "bucket1", bucket: "bucket1", label: "A" },
          { key: "bucket2", bucket: "bucket2", label: "B" },
        ],
        feedback: "",
        fileboyImageId: "",
      };
    }
  }

  get score() {
    return this.options.length;
  }

  set buckets(buckets) {
    this.data_json.buckets = buckets;
    this.triggerDataChangeEvents();
  }
  get buckets() {
    return this.data_json.buckets;
  }
  set options(data) {
    this.data_json.options = data;
    this.triggerDataChangeEvents();
  }
  get options() {
    return this.data_json.options;
  }

  get valid() {
    return super.valid && this.validations.every((x) => x.valid);
  }

  get validations() {
    return [
      ...super.validations,
      {
        message: "Must have at least 1 bucket",
        valid: this.buckets.length > 0,
      },
      {
        message: "Must have at least 1 option",
        valid: this.options.length > 0,
      },
      {
        message: "All options must have a bucket",
        valid: this.options.every((x) => x.bucket && x.bucket.trim()),
      },
      {
        message: "All buckets must have a name",
        valid: this.buckets.every((x) => x.name.trim()),
      },
      {
        message: "Bucket names must be unique",
        valid:
          this.buckets.filter((x) => this.buckets.filter((y) => y.name.trim() === x.name.trim()).length > 1).length ==
          0,
      },
      {
        message: "All options must have a label",
        valid: this.options.every((x) => x.label.trim()),
      },
      {
        message: "Options must have unique labels",
        valid:
          this.options.filter((x) => this.options.filter((y) => y.label.trim() === x.label.trim()).length > 1).length ==
          0,
      },
    ];
  }

  addBucket(name) {
    const bucket = { key: UID.bucket(), name };
    this.data_json.buckets.push(bucket);
    this.triggerDataChangeEvents();
    return bucket;
  }

  editBucket(key, name) {
    const bucket = this.data_json.buckets.find((x) => x.key === key);
    if (!bucket) {
      throw new Error(`Bucket with key ${key} does not exist`);
    }
    bucket.name = name?.trim();
    this.triggerDataChangeEvents();
  }

  deleteBucket(key) {
    this.data_json.buckets = this.data_json.buckets.filter((x) => x.key !== key);
    this.data_json.options = this.data_json.options.map((opt) => (opt.bucket == key ? { ...opt, bucket: null } : opt));
    this.triggerDataChangeEvents();
  }

  /**
   * @param {{ bucket: string, label: string, fileboyImageId: string }} data
   */
  addOption(data) {
    const option = {
      key: UID.option(),
      ...data,
    };
    this.data_json.options.push(option);
    this.triggerDataChangeEvents();
    return option;
  }

  /**
   * @param {string} key
   * @param {{ bucket: string, label: string, fileboyImageId: string }} data
   */
  editOption(key, data) {
    const option = this.data_json.options.find((x) => x.key === key);
    if (!option) {
      throw new Error(`Option with key ${key} does not exist`);
    }
    this.setPartial(option, data);
  }

  deleteOption(key) {
    this.data_json.options = this.data_json.options.filter((x) => x.key !== key);
    this.triggerDataChangeEvents();
  }

  build() {
    return {
      id: this.id,
      question_type: this.question_type,
      weight: this.weight,
      score: this.score,
      data_json: {
        key: this.key,
        prompt: this.prompt,
        feedback: this.feedback,
        buckets: this.buckets,
        options: this.options.map((option) => ({ ...option, value: option.key })),
        fileboyImageId: this.fileboyImageId,
      },
    };
  }
}

// MARK: SORT LIST
class SortList extends Question {
  constructor(data, opts) {
    super(data, opts);

    if (opts.useDefault) {
      this.data_json = {
        prompt: "Sort the items",
        options: [
          { key: UID.option(), label: "Item 1", fileboyId: null, weight: 0 },
          { key: UID.option(), label: "Item 2", fileboyId: null, weight: 1 },
          { key: UID.option(), label: "Item 3", fileboyId: null, weight: 2 },
          { key: UID.option(), label: "Item 4", fileboyId: null, weight: 3 },
        ],
        validResponse: [],
        feedback: "",
        shuffleOptions: true,
        fileboyImageId: "",
      };
    }
    this.setOrder(this.options);
    this.compile();
  }

  get score() {
    return this.options.length;
  }

  set options(data) {
    this.data_json.options = data;
    this.triggerDataChangeEvents();
  }
  get options() {
    return this.data_json.options;
  }
  get validResponse() {
    return this.data_json.validResponse;
  }
  get shuffleOptions() {
    return this.data_json.shuffleOptions;
  }

  get valid() {
    return super.valid && this.validations.every((x) => x.valid);
  }

  get validations() {
    return [
      ...super.validations,
      {
        message: "Must have at least 1 option",
        valid: this.options.length > 0,
      },
      {
        message: "All options must have a label",
        valid: this.options.every((opt) => opt.label),
      },
      {
        message: "Options must have unique labels",
        valid:
          this.options.filter((x) => this.options.filter((y) => y.label.trim() === x.label.trim()).length > 1).length ==
          0,
      },
    ];
  }

  addOption(data) {
    const option = {
      key: UID.option(),
      ...data,
    };
    this.data_json.options.push({
      ...option,
      weight: this.data_json.options.length,
    });
    this.compile();
    return option;
  }

  editOption(key, data) {
    const option = this.data_json.options.find((x) => x.key === key);
    if (!option) {
      throw new Error(`Option with key ${key} does not exist`);
    }
    this.setPartial(option, data);
    this.compile();
  }

  deleteOption(key) {
    this.data_json.options = this.data_json.options.filter((x) => x.key !== key);
    this.compile();
  }

  setOrder(options) {
    this.options = options.map((q, i) => ({
      ...q,
      weight: i,
    }));
    this.compile();
  }

  compile() {
    if (!Array.isArray(this.options)) {
      this.options = [];
    }
    this.options = Array.from(this.options)
      .sort((a, b) => a.weight - b.weight)
      .map((x, i) => ({
        ...x,
        weight: i,
        value: x.label,
      }));
    // set the valid response
    this.data_json.validResponse = this.data_json.options.map((x) => x.value);
    this.triggerDataChangeEvents();
  }

  build() {
    this.compile();
    return {
      id: this.id,
      question_type: this.question_type,
      weight: this.weight,
      score: this.score,
      data_json: {
        key: this.key,
        prompt: this.prompt,
        feedback: this.feedback,
        options: this.options,
        fileboyImageId: this.fileboyImageId,
        validResponse: this.validResponse,
        shuffleOptions: this.data_json.shuffleOptions,
      },
    };
  }
}

// MARK: CONSTANTS
const QUIP_QUIZ_TYPE_NAME_MAP = {
  multi_choice: "Multi Choice",
  fill_in_blanks: "Fill in the Blanks",
  image_bucket: "Image Bucket",
  sort_list: "Sort List",
  free_text: "Free Text",
  text_bucket: "Text Bucket",
};
const QUIP_QUIZ_TYPE_CLASS_MAP = {
  multi_choice: MultiChoice,
  fill_in_blanks: FillInBlank,
  image_bucket: ImageBucket,
  sort_list: SortList,
  free_text: FreeText,
  text_bucket: TextBucket,
};

window.QuipQuizBuilder = QuipQuizBuilder;
