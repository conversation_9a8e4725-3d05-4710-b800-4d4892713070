class QuipQuizController {
  id = null;
  container = null;
  quizData = null;
  showingResults = false;
  isStarted = false;
  isComplete = false;
  debug = true;
  disableSubmitOnAnswer = false;
  validPoll = null;
  userId = null;

  results = []; // { question: any, result: any, score: number, total: number }[]

  consoleLog(...message) {
    if (this.debug) {
      console.log("[Quiz Controller]", `[${this.id}]`, ...message);
    }
  }

  constructor(quiz, container, overrides = {}) {
    this.id = quiz.id;
    if (overrides?.debug) {
      this.debug = overrides.debug;
    }
    this.consoleLog("[constructor]");
    const { showResultsBetweenQuestions } = overrides;

    this.quizData = quiz;
    this.questions = this.quizData.questions;
    this.container = container;
    if (showResultsBetweenQuestions !== undefined) {
      this.quizData.options = {
        ...this.quizData.options,
        showAnswersBetweenQuestions: showResultsBetweenQuestions,
      };
      this.consoleLog(
        "[constructor]",
        "assigned overrides",
        this.quizData.options
      );
    }
    if (overrides && overrides.disableSubmitOnAnswer) {
      this.disableSubmitOnAnswer = overrides.disableSubmitOnAnswer;
    }
    this.userId = overrides?.user_id ?? null;

    this.init();
    this.validPollFn();
  }

  async validPollFn() {
    await new Promise((resolve) => {
      setTimeout(() => {
        this.validateSubmit();
        resolve();
      }, 200);
    });
    await this.validPollFn();
  }

  init() {
    this.consoleLog("[init]", "Initializing");
    this.startQuizButton?.addEventListener("click", this.startQuiz.bind(this));
    this.submitButton?.addEventListener(
      "click",
      this.submitQuestion.bind(this)
    );
    this.backButton?.addEventListener(
      "click",
      this.showPrevQuestion.bind(this)
    );
    this.consoleLog("[init]", "Actions bound");
    this.injectUser();
  }

  /** returns the quiz wrapper */
  get context() {
    return this.container;
  }

  /** returns the active question based on [data-active] */
  get questionContext() {
    return this.context.querySelector("[data-active]");
  }

  questionByIndexContext(index) {
    return this.questionNodes[index];
  }

  /** returns the active question json data based on [data-active] question */
  get questionData() {
    return this.questions.find((q) => q.id === this.questionContext.id);
  }

  get questionType() {
    return this.questionData.type;
  }

  get isLastQuestion() {
    return this.activeQuestionIndex == this.questions.length - 1;
  }

  /** returns all question elements based on [data-question] */
  get questionNodes() {
    return Array.from(this.context.querySelectorAll("[data-question]"));
  }

  get showResultsBetweenQuestions() {
    return !!this.quizData.options?.showAnswersBetweenQuestions;
  }

  get questionFeedbackNode() {
    return this.context.querySelector("[data-question-feedback]");
  }

  resetRocketWordFeedback() {
    this.questionFeedbackNode.style.display = "none";
    // clear prompt
    this.questionFeedbackNode.querySelector(
      "[data-question-prompt]"
    ).innerHTML = "";
    // clear body
    this.questionFeedbackNode.querySelector("[data-body]").innerText = "";
    // clear hide video
    this.questionFeedbackNode.querySelector("[data-video]").style.display =
      "none";
    // clear video
    this.questionFeedbackNode.querySelector("[data-video]").innerHTML = "";
    // hide image
    this.questionFeedbackNode.querySelector("[data-image]").style.display =
      "none";
    // clear image
    this.questionFeedbackNode.querySelector("[data-image]").style[
      "background-image"
    ] = "";
  }
  setRocketWordFeedbackIncorrect() {
    const question = this.questionData;
    const data = question?.dataJson?.question_data;
    if (!data) {
      this.questionFeedbackNode.style.display = "none";
      return false;
    }

    this.questionFeedbackNode.querySelector(
      "[data-question-prompt]"
    ).innerHTML = question.dataJson.prompt;

    this.questionFeedbackNode.querySelector("[data-body]").innerText =
      data.body;

    const videoContainer =
      this.questionFeedbackNode.querySelector("[data-video]");

    const imageContainer =
      this.questionFeedbackNode.querySelector("[data-image]");

    if (data.video) {
      videoContainer.style.display = "block";
      imageContainer.style.display = "hidden";
      const videoUrl = data.video.url;
      videoContainer.innerHTML = ``;
      if (data.video.type == "fileboy") {
        videoContainer.innerHTML = `
          <div class="video-player">
            <div class="[&>div]:h-full" data-fileboy-video="height=100%" id="${videoUrl}"></div>
          </div>
        `;
        setTimeout(() => {
          fb.reloadVideoPlayers();
        }, 200);
        setTimeout(() => {
          videoContainer.querySelector("video").click();
        }, 200);
      } else if (data.video.type == "youtube" || data.video.type == "vimeo") {
        videoContainer.innerHTML = `
        <iframe
          id="disable-external-touch-events"
          src="${videoUrl}"
          width="100%"
          height="100%"
          scrolling="no"
          frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share; fullscreen"
          allowfullscreen
          class="presentation-video-container h-full"
        ></iframe>
      `;
      }
      imageContainer.style["background-image"] = ``;
    } else if (data.image_url) {
      videoContainer.style.display = "hidden";
      imageContainer.style.display = "block";

      imageContainer.style["background-image"] = `url(${data.image_url})`;
    } else {
      videoContainer.style.display = "hidden";
      imageContainer.style.display = "hidden";
    }
    this.questionFeedbackNode.style.display = "block";
    // cant use hide question as it will clear the current index and break some stuff.
    this.questionContext.style.display = "none";
    return true;
  }

  /** returns active question index based on the element with [data-active]
   * returns 0 if no active question is found
   */
  get activeQuestionIndex() {
    const index = this.questionNodes.findIndex((node) =>
      node.hasAttribute("data-active")
    );
    if (index == -1) {
      return 0;
    }
    return index;
  }

  /** returns the submit button */
  get submitButton() {
    return this.context.querySelector("[data-submit-button]");
  }
  get backButton() {
    return this.context.querySelector("[data-back-button]");
  }
  toggleBackButton(on) {
    this.consoleLog("[toggleBackButton]", "toggled", on);
    if (on) {
      this.backButton?.classList.remove("hidden");
    } else {
      this.backButton?.classList.add("hidden");
    }
  }
  /** returns the start quiz button */
  get startQuizButton() {
    this.consoleLog("[startQuizButton]");
    return this.context.querySelector("[data-start-quiz]");
  }

  /**
   * @param {string} string
   */
  set submitButtonText(text) {
    if (this.submitButton) {
      this.submitButton.textContent = text;
    }
  }

  set submitDisabled(value) {
    if (value) {
      this.submitButton.setAttribute("disabled", "true");
      this.submitButton.classList.add("btn-disabled");
    } else {
      this.submitButton.removeAttribute("disabled");
      this.submitButton.classList.remove("btn-disabled");
    }
  }

  // adds data-disabled to the COMPONENT wrapper (in the component itself, not the show page)
  // can use this to disable the entire component from the component side
  setDisabled(questionId) {
    this.consoleLog("[setDisabled]", questionId);
    const node = this.questionNodes
      .find((node) => node.id === questionId)
      .querySelector("[data-question-wrapper]");
    node.setAttribute("data-disabled", "true");
    node
      .querySelectorAll("[data-submit-disabled]")
      .forEach((el) => (el.disabled = true));
    this.consoleLog("[setDisabled]", node);
    if (this.disableSubmitOnAnswer) {
      this.submitButton.setAttribute("disabled", "true");
    }
  }
  // reverses the above operation, uses ID
  clearDisabled(questionId) {
    this.consoleLog("[clearDisabled]", questionId);
    const node = this.questionNodes
      .find((node) => node.id === questionId)
      .querySelector("[data-question-wrapper]");
    node.removeAttribute("data-disabled", "true");
    node
      .querySelectorAll("[data-submit-disabled]")
      .forEach((el) => el.removeAttribute("disabled"));
    this.consoleLog("[clearDisabled]", node);
    if (this.disableSubmitOnAnswer) {
      this.submitButton.removeAttribute("disabled");
    }
  }

  /** start the quiz, hides the start quiz panel and displays first question */
  startQuiz() {
    this.consoleLog("[startQuiz]");
    this.isStarted = true;
    this.consoleLog("startQuiz");
    const startQuizPanel = this.context.querySelector(
      "[data-start-quiz-panel]"
    );
    if (startQuizPanel) {
      startQuizPanel.style.display = "none"; // hide start panel
    }
    this.submitButton?.parentElement.classList.remove("hidden"); // show submit button

    this.showQuestion(0);
    this.setStartTime();
  }

  injectUser() {
    // inject a node containing the start time for this quiz.
    const resultsNode = this.context.querySelector('[name="results"]');
    const node = this.context.querySelector("[name='user_id']");

    // remove any existing user_id node if no user id was provided
    if(!this.userId) {
      if(node) {
        node.remove();
      }
      return
    }

    if (!node) {
      const input = document.createElement("input");
      input.type = "hidden";
      input.name = "user_id";
      input.value = this.userId

      // place it next to the result node to ensure it's inside the form
      resultsNode.parentNode.insertBefore(input, resultsNode);
    } else {
      node.value = this.userId;
    }
  }

  setStartTime() {
    // inject a node containing the start time for this quiz.
    const resultsNode = this.context.querySelector('[name="results"]');
    const node = this.context.querySelector("[name='start_time']");
    if (!node) {
      const input = document.createElement("input");
      input.type = "hidden";
      input.name = "start_time";
      input.value = new Date().toISOString();

      // place it next to the result node to ensure it's inside the form
      resultsNode.parentNode.insertBefore(input, resultsNode);
    } else {
      node.value = new Date().toISOString();
    }
  }

  /** trigger validations and submit handling for the active question */
  submitQuestion() {
    this.consoleLog("[submitQuestion]");
    this.consoleLog("[submitQuestion]", "show results?", this.showingResults);
    if (this.showingResults) {
      this.showNextQuestion();
      return;
    }

    const isValid = this.handleQuestionSubmit();
    this.consoleLog("[submitQuestion]", "isValid?", isValid);
    if (isValid) {
      this.setDisabled(this.questionContext.id);
      if (
        this.showResultsBetweenQuestions &&
        this.questionType !== "free-text" // free text doesn't really have a "show results"
      ) {
        this.showResults();
      } else {
        this.showNextQuestion();
      }
    }
    setTimeout(() => {
      this.questionContext.scrollIntoView({
        block: "center",
        inline: "center",
        behavior: "instant",
      });
      console.log("DID SCROLL");
    }, 100);
  }

  validateSubmit() {
    if (
      !this.isStarted ||
      !this.questionContext ||
      !this.questionData ||
      this.showingResults
    ) {
      this.submitDisabled = false;
      return;
    }
    const isValid = this.validateQuestionSubmit();
    this.submitDisabled = !isValid;
  }

  /** add results to the cache
   * { questionId: string, result: any, score: number, total: number }
   */
  addResults(data) {
    this.consoleLog("[addResults]", "Appending data", data);
    const existingResultIndex = this.results.findIndex(
      (r) => r.question.id === this.questionData.id
    );
    const item = { result: data, question: { ...this.questionData } };
    if (existingResultIndex >= 0) {
      this.consoleLog(
        "[addResults]",
        "overwrite",
        this.results[existingResultIndex],
        "with",
        item
      );
      this.results[existingResultIndex] = item;
    } else {
      this.consoleLog("[addResults]", "new", item);
      this.results.push({ result: data, question: { ...this.questionData } });
    }
  }

  updateSubmitText() {
    this.consoleLog("[updateSubmitText]");
    const showSubmit =
      // show the complete quiz text if we are showing results between questions and we are showing the last question
      // and we are showing the results or the question type is free text as we don't show results for free text
      this.showResultsBetweenQuestions &&
      (this.showingResults || this.questionType == "free-text") &&
      this.isLastQuestion;
    this.consoleLog("[updateSubmitText]", "showSubmit", showSubmit);
    // show submit text if we are not showing results between questions and we are showing the last question
    const showSubmitAlt =
      !this.showResultsBetweenQuestions && this.isLastQuestion;
    this.consoleLog("[updateSubmitText]", "showSubmitAlt", showSubmitAlt);
    if (showSubmit || showSubmitAlt) {
      this.consoleLog("[updateSubmitText]", "showSubmit | Alt");
      this.submitButtonText = "Submit quiz";
      return;
    }

    if (this.showingResults) {
      this.consoleLog("[updateSubmitText]", "showing results");
      this.submitButtonText = "Next question";
      return;
    }

    this.consoleLog("[updateSubmitText]", "default");
    this.submitButtonText = "Submit";
  }

  showResults() {
    this.showingResults = true;
    this.updateSubmitText();

    const questionType = this.questionType;
    this.consoleLog("[showResults]", questionType);
    if (questionType == "multi-choice") {
      const options = this.questionContext.querySelectorAll("[data-validate]");
      options.forEach((opt) => {
        const value = opt.getAttribute("data-validate");
        const isChecked = opt.parentElement.querySelector("input").checked;
        // rocket words, value is a number. doing this to work around possibly having strings vs numbers
        const correct = this.questionData.dataJson.validResponse.find(
          (x) => x == value
        );

        // if the option we are checking is a correct choice
        // (note this isn't checking if the user selected it yet)
        if (correct) {
          // if the user selected the correct answer - highlight with .correct class
          // otherwise highlight with .correct-unselected
          opt.classList.add(isChecked ? "border-green-500" : "border-orange-500");
          this.consoleLog(
            "[showResults]",
            "selected -",
            opt,
            "is correct answer?",
            correct,
            "and checked?",
            isChecked
          );
        } else if (isChecked) {
          // user selected an incorrect answer
          this.consoleLog("[showResults]", "selected -", opt, "wrong answer");
          opt.classList.add("border-red-500");

          // if its a rocket word question from the loader, this data will be present
          // - show the custom feedback node
          if (this.questionData?.dataJson?.question_data) {
            this.setRocketWordFeedbackIncorrect();
          }
        }
      });
      // set borders green or red
      // show score
    } else if (questionType === "fill-in-blanks") {
      const selects =
        this.questionContext.querySelectorAll("[data-field-name]");
      selects.forEach((selectNode) => {
        const field = selectNode.getAttribute("data-field-name");
        const value = selectNode.value;
        const correctAnswerValue =
          this.questionData.dataJson.validResponse[field];
        const correct = correctAnswerValue == value;
        selectNode.classList.add(correct ? "text-green-500" : "text-red-500");
        const resultEl = document.createElement("span");
        resultEl.classList.add(correct ? "text-green-500" : "text-red-500");
        resultEl.textContent = this.questionData.dataJson.options[field].find(
          (x) => x.value == correctAnswerValue
        ).label;
        selectNode.parentNode.insertBefore(resultEl, selectNode.nextSibling);
        selectNode.remove();
      });
    } else if (questionType === "sort-list") {
      const options = this.questionContext.querySelectorAll("[data-validate]");
      options.forEach((opt, index) => {
        const optValue = opt.getAttribute("data-option-value");
        const correct =
          this.questionData.dataJson.validResponse[index] == optValue;
        opt.classList.add(correct ? "border-green-500" : "border-red-500");
      });
    } else if (questionType == "image-bucket") {
      this.questionContext
        .querySelectorAll('[name="bucket"]')
        .forEach((bucket) => {
          const childOptions = bucket.querySelectorAll('[name="option"]');
          const bucketOptions = this.questionData.dataJson.options.filter(
            (opt) => opt.bucket == bucket.getAttribute("data-option-key")
          );

          for (const childOption of childOptions) {
            const correct = bucketOptions.find(
              (opt) => opt.value == childOption.getAttribute("data-option-id")
            );
            
            childOption.classList.remove("bg-white");
            childOption.classList.add(correct ? "bg-green-200" : "bg-red-200");
          }
        });
    } else if (questionType == "text-bucket") {
      this.questionContext
        .querySelectorAll('[name="bucket"]')
        .forEach((bucket) => {
          const childOptions = bucket.querySelectorAll('[name="option"]');
          const bucketOptions = this.questionData.dataJson.options.filter(
            (opt) => opt.bucket == bucket.getAttribute("data-option-key")
          );
          for (const childOption of childOptions) {
            const correct = bucketOptions.find(
              (opt) => opt.value == childOption.getAttribute("data-option-id")
            );
            childOption.classList.remove("bg-white");
            childOption.classList.add(correct ? "bg-green-200" : "bg-red-200");
          }
        });
    } else if (questionType === "free-text") {
      const value =
        this.questionContext.querySelector(`[data-input-value]`).value;
      const correct = this.questionData.dataJson.validResponse == value;
      this.questionContext
        .querySelector(`[data-input-value]`)
        .classList.add(correct ? "border-green-500" : "border-red-500");
    }
  }

  validateQuestionSubmit() {
    const node = this.questionContext;
    const type = this.questionType;
    const question = this.questionData;
    // this.consoleLog("[validateQuestionSubmit]", type);
    if (type == "multi-choice") {
      const valueInput = node.querySelector(`[data-input-value]`);
      const value = valueInput.value.split(":|:").filter(Boolean);

      // no items selected - cancel
      if (value.length == 0) {
        return false;
      }

      // use valid answer count if setting or no max specified
      const useValidAnswerAmount =
        !!question["dataJson"]?.["limitChoices"]?.[
          "sameNumberAsCorrectAnswers"
        ] || !question["dataJson"]?.["limitChoices"]?.["max"];
      const maxChoices = useValidAnswerAmount
        ? question["dataJson"]?.validResponse?.length ?? 1
        : question["dataJson"]["options"].length ?? 1;
      const useValidAnswerAmountMin =
        !!question["dataJson"]?.["limitChoices"]?.[
          "sameNumberAsCorrectAnswers"
        ];
      const minChoices = useValidAnswerAmountMin
        ? question["dataJson"]?.validResponse?.length ?? 1
        : question["dataJson"]?.["limitChoices"]?.["min"] ?? 1;
      if (value.length > maxChoices || value.length < minChoices) {
        return false;
      }
    } else if (type == "fill-in-blanks") {
      const selectNames = Object.keys(question.dataJson.options);
      const answer = [];

      selectNames.forEach((field) => {
        const value = node.querySelector(`[data-field-name="${field}"]`).value;
        if (value) {
          answer.push(value);
        }
      });
      if (answer.length !== selectNames.length) {
        return false;
      }
    } else if (type == "image-bucket") {
      const holster = this.questionContext.querySelector(`[data-holster]`);
      const items = holster.querySelectorAll(`[name="option"]`);
      // still have items not assigned to buckets
      if (items.length > 0) {
        return false;
      }
    } else if (type == "text-bucket") {
      const holster = this.questionContext.querySelector(`[data-holster]`);
      const items = holster.querySelectorAll(`[name="option"]`);
      // still have items not assigned to buckets
      if (items.length > 0) {
        return false;
      }
    } else if (type == "sort-list") {
      const correctOrder = question.dataJson.validResponse;
      const value = node
        .querySelector(`[data-input-value]`)
        .value.split(",")
        .filter(Boolean);
      if (value.length === 0) {
        return false;
      }
    } else if (type == "free-text") {
      const value = node.querySelector(`[data-input-value]`).value;
      const minLength = question.dataJson.minLength ?? 0;
      const maxLength = question.dataJson.maxLength ?? null;
      if (minLength > 0 && value.length < minLength) {
        return false;
      }
      if (maxLength && value.length > maxLength) {
        return false;
      }
    }
    return true;
  }
  //** Validate and parse results for active question */
  handleQuestionSubmit() {
    const node = this.questionContext;
    const type = this.questionType;
    const question = this.questionData;
    this.consoleLog("[handleQuestionSubmit]", type);
    if (type == "multi-choice") {
      const valueInput = node.querySelector(`[data-input-value]`);
      const value = valueInput.value.split(":|:").filter(Boolean);

      // no items selected - cancel
      if (value.length == 0) {
        this.consoleLog(
          "[handleQuestionSubmit]",
          "Nothing selected - cancelled"
        );
        return false;
      }

      // use valid answer count if setting or no max specified
      const useValidAnswerAmount =
        !!question["dataJson"]?.["limitChoices"]?.[
          "sameNumberAsCorrectAnswers"
        ] || !question["dataJson"]?.["limitChoices"]?.["max"];
      const maxChoices = useValidAnswerAmount
        ? question["dataJson"]?.validResponse?.length ?? 1
        : question["dataJson"]["options"].length ?? 1;
      const useValidAnswerAmountMin =
        !!question["dataJson"]?.["limitChoices"]?.[
          "sameNumberAsCorrectAnswers"
        ];
      const minChoices = useValidAnswerAmountMin
        ? question["dataJson"]?.validResponse?.length ?? 1
        : question["dataJson"]?.["limitChoices"]?.["min"] ?? 1;
      if (value.length > maxChoices || value.length < minChoices) {
        this.consoleLog(
          "[handleQuestionSubmit]",
          "Invalid selection count",
          value.length,
          minChoices,
          maxChoices
        );
        return false;
      }

      const options = question.dataJson.options; // {}[]
      const correctAnswers = question.dataJson.validResponse;

      const answer = [];
      value.forEach((opt) => {
        answer.push({
          option: options.find((x) => x.value == opt),
          correct: correctAnswers.find((x) => x == opt),
        });
      });
      const score = answer.reduce((acc, ele) => acc + (ele.correct ? 1 : 0), 0);
      this.addResults({ score: score, total: correctAnswers.length, answer });
    } else if (type == "fill-in-blanks") {
      const selectNames = Object.keys(question.dataJson.options);
      const answer = [];

      selectNames.forEach((field) => {
        const value = node.querySelector(`[data-field-name="${field}"]`).value;
        const correct = question.dataJson.validResponse[field] == value;

        answer.push({ field, value, correct });
      });
      const score = answer.reduce((acc, ele) => acc + (ele.correct ? 1 : 0), 0);
      this.addResults({
        score: score,
        total: selectNames.length,
        answer,
      });
    } else if (type == "image-bucket") {
      const holster = this.questionContext.querySelector(`[data-holster]`);
      const items = holster.querySelectorAll(`[name="option"]`);
      // still have items not assigned to buckets
      if (items.length > 0) {
        return false;
      }

      const buckets = question.dataJson.buckets;
      const result = [];

      buckets.forEach((bucket) => {
        const values = node
          .querySelector(`[data-bucket='${bucket.name}']`)
          .value.split(",");
        const options = question.dataJson.options.filter(
          (opt) => opt.bucket == bucket.key
        );
        const bucketScore = options.filter((x) =>
          values.find((y) => y == x.value)
        ).length;
        const correct = bucketScore == options.length;
        result.push({ bucket, values, correct, bucketScore });
      });
      const score = result.reduce((acc, ele) => acc + ele.bucketScore, 0);
      this.addResults({
        score: score,
        total: question.dataJson.options.length,
        answer: result,
      });
    } else if (type == "text-bucket") {
      const holster = this.questionContext.querySelector(`[data-holster]`);
      const items = holster.querySelectorAll(`[name="option"]`);
      // still have items not assigned to buckets
      if (items.length > 0) {
        return false;
      }

      const buckets = question.dataJson.buckets;
      const result = [];

      buckets.forEach((bucket) => {
        const values = node
          .querySelector(`[data-bucket='${bucket.name}']`)
          .value.split(",");
        const options = question.dataJson.options.filter(
          (opt) => opt.bucket == bucket.key
        );
        const bucketScore = options.filter((x) =>
          values.find((y) => y == x.value)
        ).length;
        const correct = bucketScore == options.length;
        result.push({ bucket, values, correct, bucketScore });
      });
      const score = result.reduce((acc, ele) => acc + ele.bucketScore, 0);
      this.addResults({
        score: score,
        total: question.dataJson.options.length,
        answer: result,
      });
    } else if (type == "sort-list") {
      const correctOrder = question.dataJson.validResponse;
      const value = node
        .querySelector(`[data-input-value]`)
        .value.split(",")
        .filter(Boolean);
      if (value.length === 0) {
        return;
      }
      const score = correctOrder.reduce(
        (acc, ele, index) => acc + (ele == value[index] ? 1 : 0),
        0
      );
      this.addResults({
        score: score,
        total: correctOrder.length,
        answer: value,
      });
    } else if (type == "free-text") {
      const value = node.querySelector(`[data-input-value]`).value;
      const minLength = question.dataJson.minLength ?? 0;
      const maxLength = question.dataJson.maxLength ?? null;
      if (minLength > 0 && value.length < minLength) {
        return false;
      }
      if (maxLength && value.length > maxLength) {
        return false;
      }
      const score = 1;
      this.addResults({
        score: score,
        total: 1,
        answer: value,
      });
    }
    return true;
  }

  async handleQuizComplete() {
    this.consoleLog("[handleQuizComplete]");
    this.isComplete = true;
    const resultsNode = this.context.querySelector('[name="results"]');
    if (resultsNode) {
      this.context.querySelector('[name="results"]').value = JSON.stringify(
        this.results
      );
      window.htmx.trigger(
        `#${this.container.id} #submit-form-action`,
        "submit"
      );
    }
  }

  /** show question element by index */
  showQuestion(index) {
    this.consoleLog("[showQuestion]", index);
    if (this.questionNodes[index].style.display == "none") {
      // only do this if the question is hidden
      this.questionNodes[index].style.display = "block"; // show question
    }
    this.questionNodes[index].setAttribute("data-active", "true");

    if (this.isLastQuestion) {
      this.updateSubmitText();
    }
  }
  /** hide question element by index */
  hideQuestion(index) {
    this.consoleLog("[hideQuestion]", index);
    this.questionNodes[index].style.display = "none"; // hide question
    this.questionNodes[index].removeAttribute("data-active");
  }

  goToQuestion(index) {
    // always reset the feedback on change to ensure it's not present when changing to the question
    this.resetRocketWordFeedback();

    this.consoleLog("[goToQuestion]", index);
    if (index > this.questions.length - 1) {
      throw new Error("Index out of bounds");
    }
    this.toggleBackButton(index > 0);
    if (!this.isStarted) {
      this.startQuiz();
    }
    this.questionNodes.forEach((_, i) => {
      if (i !== index) {
        this.hideQuestion(i);
      }
      if (i === index) {
        this.showQuestion(i);
      }
    });
  }

  /** show next question by active index + 1 */
  showNextQuestion() {
    this.consoleLog("[showNextQuestion]");
    const shouldEnd =
      this.isLastQuestion &&
      (this.showingResults ||
        this.questionType == "free-text" ||
        !this.showResultsBetweenQuestions);

    if (shouldEnd) {
      this.consoleLog("[showNextQuestion]", "Last question - ending quiz");
      this.handleQuizComplete();
      return;
    }

    // if the question has already been answered and we are showing results between questions
    // then we flag as showing results so the next button will skip the "question" step
    const nextQuestionContext = this.questionByIndexContext(
      this.activeQuestionIndex + 1
    );
    if (
      this.showResultsBetweenQuestions &&
      (this.isLastQuestion ||
        this.results.find((r) => r.question.id === nextQuestionContext.id))
    ) {
      this.showingResults = true;
    } else {
      // default behaviour assuming we are not navigating back to a question that has already been answered
      this.showingResults = false;
    }

    this.updateSubmitText();

    const index = this.activeQuestionIndex;
    this.goToQuestion(index + 1);

    if (this.showingResults) {
      this.showResults();
    }
  }

  showPrevQuestion() {
    this.consoleLog("[showPrevQuestion]");
    const prevIndex = this.activeQuestionIndex - 1;
    if (prevIndex < 0) return;
    this.goToQuestion(prevIndex);
    if (!this.showResultsBetweenQuestions) {
      this.clearDisabled(this.questionContext.id);
    } else {
      this.showResults();
    }
    this.updateSubmitText();
  }

  resetControllerState() {
    this.consoleLog("[resetControllerState]");
    this.showingResults = false;
    this.isStarted = false;
    this.isComplete = false;
    this.results = [];
    this.showQuestion(0);
    const startQuizPanel = this.context.querySelector(
      "[data-start-quiz-panel]"
    );
    startQuizPanel.style.display = "block"; // unhide start panel
    this.submitButton?.parentElement.classList.add("hidden"); // hide submit button
    this.backButton?.classList.add("hidden");
  }

  // show feedback action
  // show custom feedback action
  // show end screen
}

window.QuipQuizController = QuipQuizController;
