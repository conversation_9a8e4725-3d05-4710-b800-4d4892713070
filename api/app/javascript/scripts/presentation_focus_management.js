// Focus management for slide transitions
document.addEventListener('DOMContentLoaded', function() {
  // Function to initialize focus management
  function initFocusManagement() {
    if (!document.getElementById('presentation-wrapper')) {
      setTimeout(initFocusManagement, 100);
      return;
    }
    
    // Create a live region for announcements
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('class', 'sr-only');
    liveRegion.setAttribute('id', 'slide-announcer');
    document.body.appendChild(liveRegion);
    
    // Track the current slide
    let currentSlideId = '';
    
    // Save the last focused element before a slide change
    let lastFocusedElement = null;
    
    // Function to announce slide changes to screen readers
    function announceSlideChange() {
      const slideHeader = document.querySelector('#slide-header-text');
      const slideNumber = document.querySelector('#slide-controls [aria-live="polite"]');
      
      let announcement = '';
      
      if (slideNumber) {
        announcement += `${slideNumber.textContent.trim()}. `;
      }
      
      if (slideHeader) {
        announcement += `Heading: ${slideHeader.textContent.trim()}`;
      } else {
        const slideContent = document.querySelector('#slide-content');
        if (slideContent) {
          announcement += 'Slide content loaded.';
        }
      }
      
      // Update the live region
      liveRegion.textContent = announcement;
    }
    
    // Function to manage focus after slide transitions
    function manageFocus() {
      const slideControls = document.getElementById('slide-controls');
      if (!slideControls) return;
      
      // First check if a previously focused element still exists
      if (lastFocusedElement && document.body.contains(lastFocusedElement)) {
        // If it was a navigation control, focus an equivalent in the new slide
        if (lastFocusedElement.id === 'next-slide-link' || lastFocusedElement.id === 'previous-slide-link') {
          const sameControl = document.getElementById(lastFocusedElement.id);
          if (sameControl) {
            sameControl.focus();
            return;
          }
        }
      }
      
      // Default focus handling - focus on the next slide control
      const nextSlideLink = document.getElementById('next-slide-link');
      if (nextSlideLink) {
        nextSlideLink.focus();
      } else {
        // Fallback to the previous slide control
        const prevSlideLink = document.getElementById('previous-slide-link');
        if (prevSlideLink) {
          prevSlideLink.focus();
        }
      }
    }
    
    // Intercept slide navigation clicks to save last focused element
    document.addEventListener('click', function(e) {
      const clickedElement = e.target.closest('a[id$="slide-link"]');
      if (clickedElement) {
        lastFocusedElement = clickedElement;
        
        // Get current slide ID for comparison
        const slideIdMatch = window.location.search.match(/[?&]slide=([^&]*)/);
        currentSlideId = slideIdMatch ? slideIdMatch[1] : '';
      }
    }, true);
    
    // Observe DOM changes to detect slide transitions
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.target.id === 'presentation-wrapper') {
          // Check if slide actually changed by comparing URL
          const slideIdMatch = window.location.search.match(/[?&]slide=([^&]*)/);
          const newSlideId = slideIdMatch ? slideIdMatch[1] : '';
          
          if (newSlideId !== currentSlideId) {
            currentSlideId = newSlideId;
            
            // Short delay to allow DOM to update
            setTimeout(function() {
              announceSlideChange();
              manageFocus();
              ensureImageAltText();
            }, 100);
          }
        }
      });
    });
    
    observer.observe(document.getElementById('presentation-wrapper'), { 
      childList: true,
      subtree: false
    });
    
    // Ensure images have alt text
    function ensureImageAltText() {
      document.querySelectorAll('img:not([alt]), [role="img"]:not([aria-label])').forEach(img => {
        let altText = 'Slide image';
        
        // Try to derive better alt text from context
        const parentWithCaption = img.closest('[data-caption]');
        if (parentWithCaption && parentWithCaption.dataset.caption) {
          altText = parentWithCaption.dataset.caption;
        }
        
        // Apply alt text
        if (img.tagName === 'IMG') {
          img.setAttribute('alt', altText);
        } else {
          img.setAttribute('aria-label', altText);
        }
      });
      
      // Ensure videos have appropriate descriptions
      document.querySelectorAll('video:not([aria-label])').forEach(video => {
        video.setAttribute('aria-label', 'Slide video content');
      });
      
      // Ensure iframes have titles
      document.querySelectorAll('iframe:not([title])').forEach(iframe => {
        iframe.setAttribute('title', 'Embedded content');
      });
    }
    
    // Implementation for skip to content link
    function addSkipToContentLink() {
      // Check if it already exists
      if (document.getElementById('skip-to-content')) {
        return;
      }
      
      const skipLink = document.createElement('a');
      skipLink.setAttribute('id', 'skip-to-content');
      skipLink.setAttribute('href', '#presentation-slide');
      skipLink.setAttribute('class', 'sr-only focus:not-sr-only focus:absolute focus:z-50 focus:top-4 focus:left-4 focus:p-4 focus:bg-blue-600 focus:text-white focus:rounded');
      skipLink.textContent = 'Skip to content';
      
      // Add to the beginning of the body
      document.body.insertBefore(skipLink, document.body.firstChild);
      
      // Add event listener to focus the main content
      skipLink.addEventListener('click', function(e) {
        e.preventDefault();
        const mainContent = document.getElementById('presentation-slide');
        if (mainContent) {
          mainContent.setAttribute('tabindex', '-1');
          mainContent.focus();
          // Remove tabindex after focus to avoid keyboard trap
          setTimeout(() => {
            mainContent.removeAttribute('tabindex');
          }, 1000);
        }
      });
    }
    
    // Add the skip link on page load
    addSkipToContentLink();
    
    // Initial calls to ensure accessibility on page load
    ensureImageAltText();
    
    // Initialize narration keyboard shortcut
    function initNarrationShortcut() {
      const narrationButtons = document.querySelectorAll('[data-narration]');
      if (narrationButtons.length === 0) return;
      
      // Create a global function to play the first narration button
      window.playSlideNarration = function() {
        const narrationBtn = document.querySelector('[data-narration]');
        if (narrationBtn) {
          narrationBtn.click();
        }
      };
    }
    
    initNarrationShortcut();
    
    // Ensure aria-current is correctly set in slide navigation
    function updateAriaCurrentInNavigation() {
      const slideLinks = document.querySelectorAll('#slides-navigation [name="nav_slide"]');
      const currentSlideWeight = window.location.search.match(/[?&]slide=(\d+)/);
      
      if (currentSlideWeight && currentSlideWeight[1]) {
        slideLinks.forEach(link => {
          const linkWeight = link.getAttribute('id').replace('nav_slide_', '');
          if (linkWeight === currentSlideWeight[1]) {
            link.setAttribute('aria-current', 'page');
          } else {
            link.removeAttribute('aria-current');
          }
        });
      }
    }
    
    // Call on each slide change
    window.updateAriaCurrentInNavigation = updateAriaCurrentInNavigation;
    
    // Add to the existing updateContent function
    const originalUpdateContent = window.updateContent;
    if (originalUpdateContent) {
      window.updateContent = function(newContentHTML) {
        originalUpdateContent(newContentHTML);
        // Add accessibility enhancements after content update
        setTimeout(() => {
          ensureImageAltText();
          updateAriaCurrentInNavigation();
        }, 200);
      };
    }
  }
  
  // Start initialization
  initFocusManagement();
});