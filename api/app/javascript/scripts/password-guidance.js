document.addEventListener('DOMContentLoaded', function() {
    var pwdInput = document.getElementById('password');
    var pwdReqDiv = document.getElementById('pwdReqs');
    var pwdLen = document.getElementById('pwdLen');
    var pwdNum = document.getElementById('pwdNum');
    var pwdLetter = document.getElementById('pwdLetter');
    var pwdSpecial = document.getElementById('pwdSpecial');

    var submitBtn = document.getElementById('submit');
    
    pwdInput.addEventListener('input', function() {
        var pwd = pwdInput.value;

        pwdReqDiv.style.display = pwd.length ? 'block' : 'none';

        var hasEightChars = pwd.length >= 8;
        var hasNumber = /\d/.test(pwd);
        var hasLetter = /[a-z]/i.test(pwd);
        var hasSpecialChar = /[!-\/:-@[-`{-~]/.test(pwd);

        checkRequirement(pwdLen, hasEightChars);
        checkRequirement(pwdNum, hasNumber);
        checkRequirement(pwdLetter, hasLetter);
        checkRequirement(pwdSpecial, hasSpecialChar);

        if (hasEightChars && hasNumber && hasLetter && hasSpecialChar) {
            submitBtn.disabled = false;
        } else {
            submitBtn.disabled = true;
        }
    });

    function checkRequirement(element, condition) {
        if (condition) {
            element.classList.remove('fa-xmark', 'text-red-500');
            element.classList.add('fa-check', 'text-green-500');
        } else {
            element.classList.remove('fa-check', 'text-green-500');
            element.classList.add('fa-xmark', 'text-red-500');
        }
    }
});
