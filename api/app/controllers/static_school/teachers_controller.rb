module StaticSchool
  class StaticSchool::TeachersController < StaticSchoolController
    include BreadcrumbsHelper
    before_action :set_teacher, only: [:edit, :update, :actions, :password, :destroy, :new_password, :pupils, :classes, :send_password_reset_email, :generate_password_reset_link]
    
    before_action :set_base_breadcrumbs
    before_action :set_show_breadcrumbs, only: [:edit, :actions, :classes, :password]
    before_action :set_new_breadcrumbs, only: [:new]

    # GET /school/teachers
    def index
      @teachers = Teacher.accessible_by(current_ability).ransack(name_cont: params[:query]).result
    end

    # GET /school/teachers/new
    def new
      @teacher = Teacher.new
    end

    # POST /school/teachers
    def create
      @teacher = Teacher.new(teacher_params)
      @teacher.school = current_user&.school
      authorize! :manage, @teacher
      if @teacher.save
        redirect_to "/school/teachers/#{@teacher.id}/edit", notice: 'Teacher was successfully created.'
      else
        render :new
      end
    end

    # GET /school/teachers/:id/edit
    def edit
    end

    # GET /school/teachers/:id/classes
    def classes; end

    # PATCH /school/teachers/:id
    def update
      authorize! :manage, @teacher
      if @teacher.update(teacher_params)
        redirect_to "/school/teachers/#{@teacher.id}/edit", notice: 'Teacher was successfully updated.'
      else
        render :edit
      end
    end

    # POST /school/teachers/:id
    def new_password
      if @teacher.update(password: params[:teacher][:password])
        if params[:teacher][:email]
          # Enhanced email with proper user tracking
          SchoolMailer.with(
            triggered_by: current_user,    
            recipient: @teacher            
          ).password_changed(@teacher, params[:teacher][:password]).deliver_now
        end

        redirect_to password_school_teacher_path(@teacher), notice: 'Password was successfully updated.'
      else
          render :password, alert: @teacher.errors.full_messages.to_sentence
      end
    end

    # DELETE /school/teachers/:id
    def destroy
      authorize! :manage, @teacher
      @teacher.destroy
      redirect_to "/school/teachers", notice: 'Teacher was successfully deleted.'
    end

    def actions; end

    def password; end

    # POST /school/teachers/:id/send_password_reset_email
    def send_password_reset_email
      @teacher.send_password_reset_email(token: @teacher.new_recovery_token)
      redirect_to password_school_teacher_path(@teacher), notice: "Password reset email sent to #{@teacher.name}."
    end

    # GET /school/teachers/:id/generate_password_reset_link
    def generate_password_reset_link
      token = @teacher.new_recovery_token
      render json: { success: true, url: "#{request.base_url}/accounts/password/edit?token=#{token}" }
    end

    private

    def set_teacher
      @teacher = Teacher.accessible_by(current_ability).find(params[:id])
    end

    def teacher_params
      data = params.require(:teacher).permit(
        :name,
        :email,
        :job_title,
        :gender,
        :dob,
        :working_days,
        :is_school_admin,
        :password,
      )
      data.delete(:password) if data[:password] == '' || data[:password].nil? || data[:password].empty?
      data
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb "Teachers", "/school/teachers"
    end

    def set_show_breadcrumbs
      add_breadcrumb @teacher.name, "/school/teachers/#{@teacher.id}/edit"
      add_breadcrumb action_name.titleize
    end

    def set_new_breadcrumbs
      add_breadcrumb "New Teacher"
    end
  end
end