# app/controllers/static_school/school_invites_controller.rb
module StaticSchool
  class SchoolInvitesController < StaticSchoolController
    include BreadcrumbsHelper

    before_action :ensure_teacher!
    before_action :set_school_invite, only: [:show, :destroy, :resend]
    before_action :set_base_breadcrumbs
    
    def index
      @pending_invites = current_user.school_invites.pending
      @accepted_invites = current_user.school_invites.accepted
      @expired_invites = current_user.school_invites.expired
    end
    
    def show
    end
    
    def new
      @school_invite = current_user.school_invites.build
      add_breadcrumb 'Invite a Colleague', new_school_invite_path
    end
    
    def create
      @school_invite = current_user.school_invites.build(school_invite_params)
      @school_invite.school = current_user.school
      @school_invite.invited_at = Time.current
      
      if @school_invite.save
        SchoolInviteMailer.invitation_email(@school_invite).deliver_now
        redirect_to school_invites_path, notice: 'Invitation sent successfully!'
      else
        render :new
      end
    rescue ActionController::ParameterMissing
      redirect_to school_invites_path, alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end
    
    def destroy
      @school_invite.update!(status: :cancelled)
      redirect_to school_invites_path, notice: 'Invitation cancelled.'
    end
    
    def resend
      if @school_invite.pending? && @school_invite.expires_at > Time.current
        SchoolInviteMailer.invitation_email(@school_invite).deliver_now
        redirect_to school_invites_path, notice: 'Invitation resent!'
      else
        redirect_to school_invites_path, alert: 'Cannot resend this invitation.'
      end
    end
    
    private
    
    def ensure_teacher!
      redirect_to root_path unless current_user.is_a?(Teacher)
    end
    
    def set_school_invite
      @school_invite = current_user.school_invites.find(params[:id])
    end
    
    def school_invite_params
      params.require(:school_invite).permit(:email)
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb 'Invites', school_invites_path
    end
  end
end