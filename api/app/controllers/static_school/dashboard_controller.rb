module StaticSchool
  class DashboardController < StaticSchoolController
    include <PERSON>Helper
    before_action :set_school

    def index
      @greeting = DateTime.now.hour < 12 ? 'Good Morning' : 'Good Afternoon'

      render :dashboard_restricted and return unless @current_user.subscribed_to_any_service?

      @social_posts = DeSocial.joins(:de_media)
                              .where(published: true)
                              .group('de_socials.id')
                              .order(created_at: :desc)
                              .limit(3)
                              .select('de_socials.*, ARRAY_AGG(de_media.fileboy_image_id) AS fileboy_image_ids')

      @updates = Motd.for_dashboard(params['currentLocale'], current_user.is_school_admin ? :for_admin_teachers : :for_teachers, current_user&.school).first(2)

      # Get "What's New" features
      @whats_new_features = whats_new_features
      @whats_new = @whats_new_features.map { |feature| feature[:title] }

      @quick_links = [
        # Library, users etc
        { key: 'library', path: unit_library_path, label: 'Units & Lessons' },
        { key: 'community_library', path: '/s/library/community', label: 'School Lesson Library', school_only: true },
        { key: 'lesson_editing', path: '/s/lesson-templates', label: 'Lesson Editing Tools', requires_lesson_creation: true, school_only: true },
        { key: 'mark_book', path: school_mark_book_index_path, label: 'Mark Book', school_only: true, disabled: !@school.show_mark_book },
        { key: 'homework', path: '/s/homework', label: 'Independent Learning', school_only: true },
        { key: 'my_classes', path: '/school/classes', label: 'My Classes', school_only: true },
        { key: 'my_pupils', path: '/school/pupils', label: 'My Pupils', school_only: true },

        # Admin management
        { key: 'manage_teachers', path: '/school/teachers', label: 'Manage Teachers', admin_only: true },
        { key: 'manage_subscription', path: lesson_subscription_path, label: 'Manage Subscription', admin_only: true },
        { key: 'all_classes', path: '/school/classes?all=true', label: 'Manage Classes', admin_only: true },
        { key: 'all_pupils', path: '/school/pupils?all=true', label: 'Manage Pupils', admin_only: true },
        { key: 'school_details', path: school_settings_path, label: 'School Details', admin_only: true },
        { key: 'import_data', path: school_data_imports_path, label: 'Import Data', admin_only: true },

        # AI stuff
        { key: 'ai_lesson_builder', path: '/ai', label: 'AI Lesson Builder', ai_required: true },
        { key: 'ai_career_builder', path: '/career-builder', label: 'AI Career Builder', ai_required: true },

        # Other resources
        { key: 'glossary', path: glossary_index_path, label: 'Glossary' },
        { key: 'cur_docs', path: school_curriculum_documents_path, label: 'Curriculum Documents' },
        { key: 'exemplar_works', path: school_exemplar_works_path, label: 'Exemplar Work', school_only: true },
        { key: 'live_lesson', path: '/s/youtube', label: 'Live Lesson' },
        { key: 'faq', path: '/knowledge', label: 'Frequently Asked Questions' },
        { key: 'enquiry', path: '/enquiry', label: 'Contact us' },
        { key: 'welcome_pack', path: 'https://www.developingexperts.com/file-cdn/files/get/92ff4093-f09c-4ab0-9851-cf76204f019c+welcome_pack?download', label: 'Welcome Pack' },
      ].reject do |item|
        (item[:admin_only] && (!@current_user.is_school_admin || !@current_user.has_school_feature_access?)) ||
          (item[:ai_required] && !@current_user.subscribed_to_service?(:ai)) ||
          (item[:requires_lesson_creation] && !@current_user.can_create_lessons) ||
          (item[:school_only] && !@current_user.has_school_feature_access?) ||
          item[:disabled]
      end

      @live_lessons = LiveStream.live.for_school(@school)

      render :dashboard_user_license_only and return unless @current_user.has_school_feature_access?

      @leaderboard_school = UserRankEvent.top_n_users(10, Time.now, @school.pupil_ids, false).slice(0, 10)
      @leaderboard_global = UserRankEvent.top_n_users(10, Time.now).slice(0, 10)

      render :dashboard_available
    end

    def update_finance_contact
      if @school.update(finance_contact_params)
        flash[:notice] = 'Finance contact updated'
      else
        flash[:alert] = 'Failed to update finance contact'
      end
      redirect_to school_static_dashboard_path
    end

    def update_science_leaders
      if @school.update(science_leaders_params)
        flash[:notice] = 'Science leaders updated'
      else
        flash[:alert] = 'Failed to update science leaders'
      end
      redirect_to school_static_dashboard_path
    end

    def update_geography_leaders
      if @school.update(geography_leaders_params)
        flash[:notice] = 'Geography leaders updated'
      else
        flash[:alert] = 'Failed to update geography leaders'
      end
      redirect_to school_static_dashboard_path
    end

    private

    def set_school
      @school = @current_user.school
    end

    def finance_contact_params
      params.require(:school).permit(:finance_name, :finance_email)
    end

    def science_leaders_params
      params.require(:school).permit(science_leader_ids: [])
    end

    def geography_leaders_params
      params.require(:school).permit(geography_leader_ids: [])
    end

    def whats_new_features
      # Get all features from our constants file
      all_features = WhatsNew::FEATURES.map do |feature|
        { id: feature[:id],
          title: feature[:title],
          description: feature[:description],
          image_url: view_context.asset_path(feature[:image_url]),
          date_added: feature[:date_added] }
      end

      # Return empty array if there are no features
      return [] if all_features.empty?

      # Sort features by newest first (highest ID)
      all_features = all_features.sort_by { |feature| -feature[:id] }

      # Get the last seen feature ID from cookies
      last_seen_feature_id = cookies[:last_seen_feature_id].to_i

      # Determine if we should auto-show the popup
      @should_auto_show_whats_new = false

      # If user has never seen any feature or there's a new feature
      if last_seen_feature_id == 0 || all_features.first[:id] > last_seen_feature_id
        @should_auto_show_whats_new = true

        # Set cookie to remember we've shown the latest feature
        cookies[:last_seen_feature_id] = {
          value: all_features.first[:id],
          expires: 1.year.from_now
        }
      end

      # Return all features to allow navigation in the UI
      all_features
    end

  end
end
