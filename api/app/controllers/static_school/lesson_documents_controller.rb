# app/controllers/static_school/lesson_documents_controller.rb
module StaticSchool
  class LessonDocumentsController < StaticSchoolController
    include Fileboy
    before_action :set_lesson_template
    before_action :set_document, only: %i[update destroy]

    # POST /school/templates/:lesson_template_id/documents
    def create
      @document = Lesson::Document.new(document_params.merge(last_modified_by: @current_user.id, last_modified_at: Time.now))
      @document.template = @lesson_template

      if @document.save
        redirect_to show_documents_school_lesson_editing_path(@lesson_template), notice: 'Document was successfully created.'
      else
        # Show error message
        error_message = @document.errors.full_messages.join(', ')
        redirect_to show_documents_school_lesson_editing_path(@lesson_template), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to show_documents_school_lesson_editing_path(@lesson_template),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # PUT/PATCH /school/templates/:lesson_template_id/documents/:id
    def update
      if @document.update(document_params.merge(last_modified_by: @current_user.id, last_modified_at: Time.now))
        redirect_to show_documents_school_lesson_editing_path(@lesson_template), notice: 'Document was successfully updated.'
      else
        error_message = @document.errors.full_messages.join(', ')
        redirect_to show_documents_school_lesson_editing_path(@lesson_template), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to show_documents_school_lesson_editing_path(@lesson_template),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # DELETE /school/templates/:lesson_template_id/documents/:id
    def destroy
      @document.destroy
      redirect_to show_documents_school_lesson_editing_path(@lesson_template), notice: 'Document was successfully deleted.'
    end

    private

    def set_lesson_template
      @lesson_template = Lesson::Template.find(params[:lesson_editing_id])
    end

    def set_document
      @document = @lesson_template.documents.find(params[:id])
    end

    def document_params
      params.require(:document).permit(:name, :fileboy_file_id, :file_name)
    end

  end
end
