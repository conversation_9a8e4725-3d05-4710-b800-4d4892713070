module StaticSchool
  class StaticSchool::ExemplarWorksController < StaticSchoolController
    include Fileboy
    include BreadcrumbsHelper
    before_action :set_exemplar_work, only: [:edit, :update, :destroy]

    before_action :set_base_breadcrumbs
    before_action :set_show_breadcrumbs, only: [:edit]

    # GET /school/exemplar-works
    def index
      @exemplar_works = ExemplarWork.accessible_by(current_ability)
                                    .includes(:user, :new_library_unit, :lesson_template)
                                    .ransack(user_id_eq: @current_user.id, title_cont: params[:query], status_eq: params[:status])

      @exemplar_works.sorts = 'created_at desc'

      @exemplar_works = @exemplar_works.result
    end

    # GET /school/exemplar-works/new
    def new
      redirect_to "/s" unless params[:lesson].present? || params[:unit].present?

      @exemplar_works = [ExemplarWork.new]
    end

    # POST /school/exemplar-works
    def create
      @exemplar_works = []
    
      if params[:exemplar_works].present?
        exemplar_works_params.each_with_index do |work_params, i|
          # Check if fileboy_image exists before trying to iterate
          if params[:exemplar_works][i][:fileboy_image].present?
            params[:exemplar_works][i][:fileboy_image].each_with_index do |fileboy_image, j|
              work = ExemplarWork.new(work_params)
              fileboy_image_id = upload_image fileboy_image if fileboy_image.present?
              work.fileboy_id = fileboy_image_id if fileboy_image_id
    
              work.user_id = @current_user.id
    
              work.title = "#{work.title} (#{j+1})" if params[:exemplar_works][i][:fileboy_image].count > 1
    
              @exemplar_works << work
            end
          else
            # Create a single work without an image if no fileboy_image is present
            work = ExemplarWork.new(work_params)
            work.user_id = @current_user.id
            @exemplar_works << work
          end
        end
      end
    
      if @exemplar_works.any? && @exemplar_works.all?(&:save)
        user = @exemplar_works[0].user
        if user&.teacher? && user.allows_notification?(:exemplar_work)
          SchoolMailer.exemplar_work_submitted(user, @exemplar_works).deliver_now
        end

        redirect_to school_exemplar_works_path, notice: @exemplar_works.count > 1 ? 'Exemplar works were successfully created' : 'Exemplar work was successfully created.'
      else
        flash.now[:alert] = 'No exemplar works were submitted or there was an error saving them.' if @exemplar_works.empty?
        render :new
      end
    end

    # GET /school/exemplar-works/:id/edit
    def edit; end

    # PATCH /school/exemplar-works/:id
    def update
      authorize! :manage, @exemplar_work
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @exemplar_work.fileboy_id = fileboy_image_id if fileboy_image_id

      if @exemplar_work.update(edit_exemplar_work_params)
        redirect_to edit_school_exemplar_work_path(@exemplar_work), notice: 'Exemplar Work was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /school/exemplar-works/:id
    def destroy
      @exemplar_work.destroy
      redirect_to school_exemplar_works_url, notice: 'Exemplar work was successfully deleted.'
    end

    private

    def set_exemplar_work
      @exemplar_work = ExemplarWork.accessible_by(current_ability).find(params[:id])
    end

    def edit_exemplar_work_params
      params.require(:exemplar_work).permit(:title, :body)
    end

    def exemplar_works_params
      params.require(:exemplar_works).map do |work_params|
        work_params.permit(:title, :body, :lesson_template_id, :new_library_unit_id)
      end
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb "Exemplar Works", school_exemplar_works_path
    end

    def set_show_breadcrumbs
      add_breadcrumb "Edit #{@exemplar_work.title}", edit_school_exemplar_work_path(@exemplar_work.id)
    end

  end
end
