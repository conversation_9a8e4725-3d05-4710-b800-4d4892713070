module StaticSchool
  class AddLessonsController < StaticSchoolController
    before_action :load_data, only: %i[add_to_form add_to_form_submit]

    def add_to_form
      # If we have already tried to infer, don't try again.
      return if params[:infer] == '1'

      # Infer Curriculum ID and Year ID as required from template ids
      if params[:template_ids].present? && [@selected_curriculum, @selected_subject, @selected_year].any?(&:blank?)
        template_ids = params[:template_ids].split(',')

        # make sure all ids are for the source templates, user generated don't have units.
        templates = Lesson::Template.accessible_by(current_ability).where(available: true).where(id: template_ids).includes([:source_template])
        templates = templates.map { |template| template.source_template || template }

        # get the units from source templates
        unit_ids = templates.map { |t| t.new_library_units.pluck(:id) }.flatten.uniq.compact
        units = NewLibrary::Unit.where(id: unit_ids).left_joins(year: :curriculum)

        # if we were given a year id, use that to filter the units
        year_id = params[:year_id]
        units = units.where(year_id: year_id) if year_id.present?

        # If we were given a subject id, use that to filter the units
        subject_id = params[:subject_id]
        units = units.left_joins(:year).where({ new_library_years: { subject_id: subject_id } }) if subject_id.present?

        # if we were given a curriculum id, use that to filter the units
        curriculum_id = params[:curriculum_id]
        units = units.left_joins(year: :curriculum).where({ new_library_curricula: { id: curriculum_id } }) if curriculum_id.present?

        # use the first matched unit as the source for the rest of the params.
        year = units.first&.year
        subject = year&.subject
        curriculum = subject&.curriculum

        redirect_to params: request.query_parameters.merge(curriculum_id: curriculum&.id, subject_id: subject&.id, year_id: year&.id, infer: '1')
        return
      end

      # If year present and missing subject or curriculum, infer them
      if params[:year_id].present? && [params[:subject_id], params[:curriculum_id]].any?(&:blank?)
        year = NewLibrary::Year.find_by(id: params[:year_id])
        subject_id = year&.subject_id
        curriculum_id = year&.curriculum_id
        redirect_to params: request.query_parameters.merge(subject_id: subject_id, curriculum_id: curriculum_id, infer: '1')
        return
      end

      # If subject present and missing curriculum, infer it
      if params[:subject_id].present? && params[:curriculum_id].blank?
        subject = NewLibrary::Subject.find_by(id: params[:subject_id])
        curriculum_id = subject&.curriculum_id
        redirect_to params: request.query_parameters.merge(curriculum_id: curriculum_id, infer: '1')
        return
      end

      # Infer curriculum from unit ids
      if !params[:template_ids].present? && !params[:curriculum_id].present? && params[:unit_ids].present?
        unit_ids = params[:unit_ids].split(',').reject { |x| x == 'user-generated' }
        if unit_ids.empty?
          redirect_to params: request.query_parameters.merge(infer: '1')
          return
        end

        units = NewLibrary::Unit.where(id: unit_ids).left_joins(year: :curriculum)
        year = units.first&.year
        redirect_to params: request.query_parameters.merge(curriculum_id: year&.curriculum_id, year_id: year&.id, infer: '1')
        return
      end
    end

    def add_to_form_submit
      @errors = {}
      data = add_to_form_params

      form_id = data[:form_id]
      @errors[:form] = 'Class must be selected' if form_id.blank?

      form = Form.find_by(id: form_id) if form_id.present?
      @errors[:form] = 'Class not found' if form.blank? && form_id.present?

      curriculum_id = data[:curriculum_id]
      @errors[:curriculum] = 'Curriculum must be selected' if curriculum_id.blank?

      year_id = data[:year_id]
      @errors[:year] = 'Year must be selected' if year_id.blank?

      template_ids = data[:template_ids].split(',')
      @errors[:templates] = 'Select at least 1 lesson' if template_ids.empty?

      render :add_to_form and return if @errors.any?

      date = data[:date].present? ? Date.parse(data[:date]) : nil

      # adds form units for units that are not already added via form units
      current_lib_unit_ids = form.form_units.pluck(:new_library_unit_id)
      units = NewLibrary::Unit
              .left_joins(:lesson_templates, year: :curriculum)
              .where({ new_library_years: { id: year_id } })
              .where({ new_library_curricula: { id: curriculum_id } })
              .where('lesson_templates.id IN (?)', template_ids)
              .where.not(id: current_lib_unit_ids)
              .distinct
      units.each.with_index do |unit, index|
        time = date.present? ? date : DateTime.now
        time += index.days
        form.create_form_unit_for(unit, date: date)
      end

      result = []
      begin
        result = form.add_accessible_lesson_template_ids(@current_user, template_ids, date)
      rescue => e
        Rails.logger.error("Failed to add lessons to form #{form.id}: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
        @errors[:form] = "Failed to add lessons: #{e.message}"
        render :add_to_form and return
      end

      redirect_to "/school/classes/#{form_id}/lessons", notice: "#{result.count} Lesson#{result.count == 1 ? '' : 's'} added to form"
    end

    private

    def load_data
      @forms = current_user.is_school_admin? ? current_user.school.forms : current_user.forms

      @curriculum = NewLibrary::Curriculum.published

      curriculum_id = params[:curriculum_id].presence || @current_user.school&.new_library_curriculum_id
      @selected_curriculum = @curriculum.find_by(id: curriculum_id)

      @subjects = NewLibrary::Subject.none
      @subjects = @selected_curriculum.subjects.order(name: :asc) if @selected_curriculum.present?

      @selected_subject = @subjects.find_by(id: params[:subject_id]) if params[:subject_id]

      @years = NewLibrary::Year.none
      @years = @selected_subject.years.order(name: :asc) if @selected_subject.present?

      @selected_year = @years.find_by(id: params[:year_id]) if params[:year_id]

      @units = @selected_year&.units || NewLibrary::Unit.none

      @user_templates = @units.map do |unit|
        unit.user_lesson_templates.accessible_by(current_ability).where(available: true).includes([:user])
      end.flatten.uniq
    end

    def add_to_form_params
      params.permit(:curriculum_id, :form_id, :year_id, :template_ids, :date)
    end
  end
end
