module StaticSchool
  class StaticSchool::LiveLessonsController < StaticSchoolController
    include BreadcrumbsHelper
    before_action :set_stream, only: [:history_show]

    before_action :set_base_breadcrumbs
    before_action :set_show_breadcrumbs, only: [:live, :upcoming, :history, :history_show]

    ACTION_BREADCRUMBS = {
      live: 'Live Stream',
      upcoming: 'Upcoming',
      history: 'Past Streams'
    }.freeze

    def index
      redirect_to live_school_live_lessons_path
    end

    def live
      return unless current_user&.school

      @stream = LiveStream.accessible_by(current_ability)
                              .where(published: true)
                              .where("start_time <= ? AND end_time >= ?", DateTime.now, DateTime.now)
                              .left_joins(:schools)
                              .group(:id)
                              .having("COUNT(schools.id) = 0 OR ? = ANY(ARRAY_AGG(schools.id))", current_user.school.id)
                              .first
    end

    def new_live_stream_message
      message = LiveStreamMessage.new(params.permit(:live_stream_id, :message))
      message.user = current_user
      authorize! :create, message

      message.save
    end

    def live_stream_messages
      @messages = LiveStreamMessage.accessible_by(current_ability)
                                      # eager load replies
                                      .preload(:live_stream_messages)
                                      .where(live_stream_id: params[:stream_id], user_id: current_user.id)
                                      .order(created_at: :asc)

      render layout: false
    end

    def upcoming      
      @streams = LiveStream.accessible_by(current_ability)
                              .where(published: true)
                              .where("start_time >= ?", DateTime.now)
                              .left_joins(:schools)
                              .group(:id)
                              .having("COUNT(schools.id) = 0 OR ? = ANY(ARRAY_AGG(schools.id))", current_user.school.id)
                              .order("DATE_TRUNC('month', start_time) ASC, start_time ASC")
    end

    def history
      category = params[:category]
      category = LiveStream.categories.keys unless category.present? && LiveStream.categories.keys.include?(category.downcase)
      @streams = LiveStream.accessible_by(current_ability)
                              .includes(:video)
                              .where(category: category)
                              .where(published: true)
                              .left_joins(:schools)
                              .group(:id)
                              .having("COUNT(schools.id) = 0 OR ? = ANY(ARRAY_AGG(schools.id))", current_user.school.id)
                              .order(start_time: :desc)

      unless category == "webinar"
        @streams = @streams.where("end_time <= ?", DateTime.now)
      end
    end

    def history_show; end

    private
    
    def set_stream
      @stream = LiveStream.find(params[:id])
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb "DE Live", school_live_lessons_path
    end

    def set_show_breadcrumbs
      case action_name
      when "history_show"
        return unless @stream.present?

        add_breadcrumb ACTION_BREADCRUMBS[:history], history_school_live_lessons_path
        add_breadcrumb @stream.title

      when "live", "upcoming", "history"
        breadcrumb_text = ACTION_BREADCRUMBS[action_name.to_sym]
        add_breadcrumb breadcrumb_text
      end
    end

  end
end
