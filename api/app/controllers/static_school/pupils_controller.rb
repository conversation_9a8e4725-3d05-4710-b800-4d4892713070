module StaticSchool
  class StaticSchool::PupilsController < StaticSchoolController
    include PaginationHelper
    include BreadcrumbsHelper
    before_action :set_pupil, only: [:edit, :update, :classes, :letter, :print_letter, :destroy]
    before_action :set_view_mode

    before_action :set_form_breadcrumbs
    before_action :set_new_breadcrumbs, only: [:new]

    # GET /school/pupils
    def index
      @pupils = fetch_all_pupils
      @pupils = @pupils.accessible_by(current_ability)
                       .ransack(name_cont: params[:query])
                       .result
      @pupils = safe_paginate(@pupils, page: params[:page], per_page: 10)
    end

    # GET /school/pupils/:id/edit
    def edit; end

    # GET /school/pupils/new
    def new
      @pupil = Pupil.new
    end
    # POST /school/pupils
    def create
      @pupil = Pupil.new(pupil_params)
      @pupil.school = current_user&.school
      authorize! :manage, @pupil
      if @pupil.save
        redirect_to '/school/pupils?all=true', notice: 'Pupil was successfully created.'
      else
        render :new
      end
    end

    def classes
      @forms = @pupil.forms
    end

    def letter; end

    def print_letter
      render partial: "static_school/pupils/print_letter"
    end

    # PATCH/PUT /school/pupils/:id
    def update
      authorize! :manage, @pupil
      if @pupil.update(pupil_params)
        redirect_to "/school/pupils/#{@pupil.id}/edit", notice: 'Pupil was successfully updated.'
      else
        render :edit
      end
    end

    def download_login_codes
      pupils = fetch_all_pupils.group(:id).order("users.name ASC").select('users.name AS "Name"', 'users.identifier AS "Login Code"')
      csv_data = pupils.to_csv

      respond_to do |format|
        format.csv do
          send_data csv_data,
                    filename: "pupil_login_codes_for_#{current_user.name}.csv",
                    type: 'text/csv; charset=utf-8'
        end
      end
    end

    def destroy
      authorize! :manage, @pupil
      @pupil.destroy
      redirect_to "/school/pupils", notice: 'Pupil was successfully deleted.'
    end

    private

    def set_pupil
      @pupil = Pupil.accessible_by(current_ability).find(params[:id])
    end

    def fetch_all_pupils
      if current_user&.is_school_admin? && params[:all] == 'true'
        current_user.school&.pupils
      else
        current_user&.pupils
      end
    end

    def pupil_params
      params.require(:pupil).permit(
          :name,
          :identifier,
          :dob,
          :gender
        )
    end

    def set_view_mode
      @view_all = @current_user&.is_school_admin? && params[:all] == 'true'
    end
    
    def set_form_breadcrumbs
      build_contextual_breadcrumbs do
        if params[:from_context] != 'pupil'
          if @view_all
            add_breadcrumb "All Pupils", "/school/pupils?all=true"
          else
            add_breadcrumb "Pupils", "/school/pupils"
          end
        end

        if @pupil
          parent_path = if action_name == 'edit'
                          nil
                        else
                          "/school/pupils/#{@pupil.id}/edit" 
                        end
          add_breadcrumb @pupil.name, parent_path

          if %w[edit classes letter].include?(action_name)
            add_breadcrumb action_name.titleize, nil
          end
        end
      end
    end

    def set_new_breadcrumbs
      add_breadcrumb "New Pupil"
    end

  end
end
