module StaticSchool
    class StaticSchool::SettingsController < StaticSchoolController
      include Fileboy
      before_action :authenticate_admin_teacher!
      before_action :set_school, only: [:index, :update, :user_roles, :update_user_roles, :platform_configuration, :update_platform_configuration]
  
      # GET /school/settings
      def index
        blacklist = %w[US]

        countries = Country.all.joins(:available_templates).group(:id).order("countries.machine_name = 'GB' desc", name: :asc)
        countries = countries.where.not('countries.machine_name IN (?)', blacklist)

        @countries = countries.pluck(:name, :id)
        @all_countries = Country.all.order("countries.machine_name = 'GB' desc", name: :asc).pluck(:name, :id)
        @curricula = NewLibrary::Curriculum.all.order('name asc')
      end

      # PATCH/PUT /school/profile/:id
      def update
        if params[:remove_fileboy_image] == "true"
          @school.fileboy_image_id = nil
        elsif params[:fileboy_image].present?
          fileboy_image_id = upload_image params[:fileboy_image]
          @school.fileboy_image_id = fileboy_image_id if fileboy_image_id
        end

        if @school.update(profile_params)
            redirect_to school_settings_path, notice: 'School was successfully updated.'
        else
            render :index
        end
      end

      # GET /school/user_roles
      def user_roles
        @teachers = @school.teachers
      end

      # PATCH /school/user_roles
      def update_user_roles
        if @school.update(user_roles_params)
          redirect_to school_user_roles_path, notice: 'User roles updated.'
        else
          @teachers = @school.teachers
          render :user_roles
        end
      end

      # GET /school/platform_configuration
      def platform_configuration
      end

      # PATCH /school/platform_configuration
      def update_platform_configuration
        if @school.update(platform_configuration_params)
          redirect_to school_platform_configuration_path, notice: 'Platform configuration updated.'
        else
          render :platform_configuration
        end
      end

      private

      # Only allow a list of trusted parameters through.
      def set_school
        @school = @current_user.school
      end

      def profile_params
        params.require(:school).permit(
            :category,
            :name,
            :term_length,
            :telephone,
            :postcode,
            :country_id,
            :actual_country_id,
            :new_library_curriculum_id,
            :show_lesson_plan_pupil_area,
            :default_billing_address_line_1,
            :default_billing_address_line_2,
            :default_billing_city,
            :default_billing_state,
            :default_billing_postal_code,
            :default_billing_country_id,
            science_leader_ids: []
          )
      end

      def user_roles_params
        params.require(:school).permit(
          { science_leader_ids: [], geography_leader_ids: [] },
          :finance_name,
          :finance_email
        )
      end

      def platform_configuration_params
        params.require(:school).permit(
          :show_lesson_plan_pupil_area,
          :show_mark_book
          )
      end
  
    end
  end




