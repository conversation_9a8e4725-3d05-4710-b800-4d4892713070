module StaticSchool
  class StaticSchool::CurriculumDocumentsController < StaticSchoolController
    def index
      if params[:query].present?
        @documents = CurriculumDocument.where(active: true)
                                      .where("name ILIKE ?", "%#{params[:query]}%")
                                      .order(:weight, :name)
        @groups = [] # Don't show groups when searching
      else
        # Get documents that are NOT in any group (ungrouped documents)
        grouped_document_ids = CurriculumDocumentGrouping.pluck(:curriculum_document_id)
        @documents = CurriculumDocument.where(active: true)
                                      .where.not(id: grouped_document_ids)
                                      .order(:weight, :name)
        
        # Get groups that belong to the current user and have documents
        @groups = CurriculumDocumentGroup
                              .includes(:curriculum_documents)
                              .joins(:curriculum_documents)
                              .where(curriculum_documents: { active: true })
                              .distinct
                              .order(:name)
      end
      
      @no_documents = @documents.empty? && @groups.empty?
    end
  end
end
