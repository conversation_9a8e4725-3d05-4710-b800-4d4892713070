# app/controllers/static_admin/lesson_keywords_controller.rb
module StaticSchool
  class LessonKeywordsController < StaticSchoolController
    before_action :set_lesson_template
    before_action :set_keyword, only: %i[update destroy]

    # POST /admin/templates/:lesson_template_id/keywords
    def create
      @keyword = Lesson::Keyword.new(keyword_params)
      @keyword.template = @lesson_template

      if @keyword.save
        redirect_to show_keywords_school_lesson_editing_path(@lesson_template), notice: 'Keyword was successfully created.'
      else
        # Show error message
        error_message = @keyword.errors.full_messages.join(', ')
        redirect_to show_keywords_school_lesson_editing_path(@lesson_template), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to show_keywords_school_lesson_editing_path(@lesson_template),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # PUT/PATCH /admin/templates/:lesson_template_id/keywords/:id
    def update
      if @keyword.update(keyword_params)
        redirect_to show_keywords_school_lesson_editing_path(@lesson_template), notice: 'Keyword was successfully updated.'
      else
        error_message = @keyword.errors.full_messages.join(', ')
        redirect_to show_keywords_school_lesson_editing_path(@lesson_template), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to show_keywords_school_lesson_editing_path(@lesson_template),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # DELETE /admin/templates/:lesson_template_id/keywords/:id
    def destroy
      @keyword.destroy
      redirect_to show_keywords_school_lesson_editing_path(@lesson_template), notice: 'Keyword was successfully deleted.'
    end

    # PUT /admin/templates/:lesson_template_id/keywords/reorder
    def reorder
      keyword_ids = params[:keyword_ids]

      if keyword_ids.present?
        # Update each keyword's weight based on its position in the array,
        start_weight = 0

        ActiveRecord::Base.transaction do
          # Update the weights of the regular keywords
          keyword_ids.each_with_index do |id, index|
            Lesson::Keyword.where(id: id).update_all(weight: index + start_weight)
          end
        end

        head :ok
      else
        head :bad_request
      end     
    end

    private

    def keyword_params
      permitted = params.require(:keyword).permit(
        :name,
        :body,
        :fileboy_image_id,
        :image_url,
        quiz_question_attributes: [
          :body,
          :answer,
          :use_data,
          :lesson_slide_id,
          :question_body,
          :question_fileboy_image_id,
          :question_video_url
        ]
      )

      if permitted[:quiz_question_attributes]
        permitted[:quiz_question_attributes][:lesson_template_id] = @lesson_template.id
      end
    
      permitted
    end    

    def set_lesson_template
      @lesson_template = Lesson::Template.find(params[:lesson_editing_id])
    end

    def set_keyword
      @keyword = @lesson_template.keywords.find(params[:id])
    end

  end
end
