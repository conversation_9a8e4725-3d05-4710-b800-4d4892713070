module StaticSchool
  class StaticSchool::XlsDataImportsController < StaticSchoolController
    include Pa<PERSON>ationHelper
    include Fileboy
    include BreadcrumbsHelper
    before_action :set_xls_import, only: [:show, :update]
    before_action :set_warnings, only: [:show, :update]

    before_action :set_base_breadcrumbs
    before_action :set_show_breadcrumbs, only: [:show]
    before_action :set_new_breadcrumbs, only: [:new]

    # GET /school/data-imports/xls
    def index
      @imports = XlsImport
        .where(school: @current_user.school)
        .ransack(name_cont: params[:query])

      @imports.sorts = 'created_at desc'

      @imports = safe_paginate(@imports.result, page: params[:page], per_page: 10)
    end

    # GET /school/data-imports/xls/new
    def new
      @import = XlsImport.new
    end

    # POST /school/data-imports/xls
    def create
      @import = XlsImport.new(xls_import_params)

      unless params[:file].present?
        redirect_to new_school_xl_path, alert: 'You must choose a file to upload'
        return
      end

      if params[:file].present? && !(params[:file].content_type == 'application/vnd.ms-excel' || params[:file].content_type == 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        redirect_to new_school_xl_path, alert: 'Unsupported file format. Supported formats: .xls, .xlsx'
        return
      end

      file_name = params[:file].original_filename.split('.')[0]
      fileboy_file_id = Fileboy.upload_sheet(params[:file], file_name, params[:file].content_type) if params[:file].present?
      @import.fileboy_file_id = fileboy_file_id if fileboy_file_id.present?
      @import.file_name = file_name

      if @import.save
        redirect_to school_xl_path(@import)
      else
        redirect_to new_school_xl_path
      end
    end

     # GET /school/data-imports/xls/:id
    def show; end

    # PATCH /school/data-imports/xls/:id
    def update
      if params[:file].present?
        file_name = params[:file].original_filename.split('.')[0]
        fileboy_file_id = Fileboy.upload_sheet(params[:file], file_name) if params[:file].present?
        params[:fileboy_file_id] = fileboy_file_id if fileboy_file_id.present?
        params[:file_name] = file_name
      else
        params[:data][:teachers].each do |teacher|
          is_admin = teacher["admin"].present? && teacher["admin"]["content"] == "on"
          teacher["admin"] = { content: is_admin.to_s }
        end
      end

      params[:ignore_warnings] = params[:ignore_warnings] == '1'
      params[:build_lessons] = params[:build_lessons] == '1'

      @xls_import.assign_attributes(xls_import_params)

      if @xls_import.save
        if @xls_import.complete
          redirect_to school_xl_path(@xls_import), notice: 'Data was successfully imported.'
        else
          total_warnings = @warnings.values.sum + @errors.values.sum
          flash.now[:alert] = total_warnings > 1 ? "There are still #{total_warnings} problems with this import." : "There is still a problem with this import."
          render :show
        end
      else
        render :show
      end
    end

    private

    def set_xls_import
      @xls_import = XlsImport.find(params[:id])
    end

    def set_warnings
      @warnings = Hash.new(0)
      @errors = Hash.new(0)
      @pupil_years = []

      return if @xls_import[:data].empty?

      @pupil_years = @xls_import[:data]['pupils'].map { |pupil| pupil.dig("year", "content") }.uniq if @xls_import[:data]['pupils'].present?

      data = @xls_import[:data]

      if @xls_import[:data]['pupils'].present?
        data["pupils"].each do |pupil|
          year = pupil["year"]["content"]
          pupil.each do |key, value|
            @warnings[year] += 1 if value.is_a?(Hash) && value.key?("warning")
            @errors[year] += 1 if value.is_a?(Hash) && value.key?("error")
          end
        end
      end

      if @xls_import[:data]['teachers'].present?
        data["teachers"].each do |teacher|
          teacher.each do |key, value|
            @warnings["teachers"] += 1 if value.is_a?(Hash) && value.key?("warning")
            @errors["teachers"] += 1 if value.is_a?(Hash) && value.key?("error")
          end
        end
      end
    end

    def xls_import_params
      params
        .permit(:school_id, :file_name, :fileboy_file_id, :run_import, :ignore_warnings, :build_lessons, data: {})
        .merge(school_id: @current_user.school.id, locale: :en, user_id: @current_user.id)
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb "Data Imports", school_data_imports_path
      add_breadcrumb "Spreadsheet Importer", school_xls_path
    end

    def set_show_breadcrumbs
      add_breadcrumb @xls_import.file_name
    end

    def set_new_breadcrumbs
      add_breadcrumb "New Spreadsheet Import"
    end

  end
end