module StaticSchool
  class StaticSchool::FormsController < StaticSchoolController
    include PaginationHelper
    include BreadcrumbsHelper
    before_action :set_form, only: %i[pupils lessons add_pupil remove_pupil remove_lessons reschedule_lesson edit update add_teacher remove_teacher print_letters destroy]
    before_action :set_days, only: %i[edit update new create]
    before_action :set_teachers, only: %i[edit update]
    before_action :set_view_mode

    before_action :set_form_breadcrumbs
    before_action :set_new_breadcrumbs, only: [:new]

    # GET /school/forms
    def index
      @forms = fetch_all_forms
      @forms = @forms.accessible_by(current_ability)
                     .ransack(name_cont: params[:query])
                     .result
    end

    def new
      @form = Form.new(lesson_weekdays: {})
    end

    def create
      @form = Form.new(form_params)
      @form.school = current_user.school
      authorize! :manage, @form
      if @form.save
        redirect_to "/school/classes/#{@form.id}/pupils", notice: 'Class was successfully created.'
      else
        render :new
      end
    end

    # GET /school/forms/:id/pupils
    def pupils
      @pupils = safe_paginate(@form.pupils, page: params[:page], per_page: 10)
    end

    def pupil_search
      form_id = params[:form_id]
      pupils_in_class = Form.find(form_id).pupils.pluck(:id)
      all_school_pupils = @current_user.school.pupils.group(:id).pluck(:id, :name, :identifier)
      @all_pupils = all_school_pupils.reject { |pupil| pupils_in_class.include?(pupil[0]) }

      render layout: false
    end

    def add_pupil
      pupil_id = params[:pupil_id]&.to_i

      unless @form.pupil_ids.include?(pupil_id)
        Enrollment.insert_all([{ user_id: pupil_id, form_id: @form.id, deleted: false, created_at: Time.current, updated_at: Time.current }])
        flash[:notice] = 'Pupil was successfully added.'
      end

      redirect_to "/school/classes/#{@form.id}/pupils"
    end

    def remove_pupil
      pupil_id = params[:pupil_id]&.to_i

      if pupil_id.present? && @form.pupil_ids.include?(pupil_id)
        updated_pupil_ids = @form.pupil_ids - [pupil_id]

        if @form.update(pupil_ids: updated_pupil_ids)
          flash[:notice] = 'Pupil was removed.'
          Rails.logger.info "Successfully removed pupil ID: #{pupil_id} from form #{@form.id}"
        else
          flash[:alert] = 'Could not remove pupil. ' + @form.errors.full_messages.to_sentence
          Rails.logger.error "Failed to remove pupil ID: #{pupil_id} from form #{@form.id}: #{@form.errors.full_messages.join(', ')}"
        end
      else
        flash[:alert] = "Pupil ID #{pupil_id} not found or already removed."
        Rails.logger.warn "Attempted to remove non-existent pupil ID: #{pupil_id} from form #{@form.id}"
      end

      redirect_to "/school/classes/#{@form.id}/pupils"
    end

    # GET /school/forms/:id/lessons
    def lessons
      @form_units = @form.lessons.includes(:template, form_unit: { new_library_unit: { year: :subject } }).group_by(&:form_unit)
      @grouped = @form_units.sort_by { |unit, lessons|
        lessons.min_by { |lesson| lesson.time || Time.at(0) }&.time || Time.at(0)
      }
    end

    # PATCH /school/forms/:id/remove_lessons
    def remove_lessons
      authorize! :manage, @form
      lesson_ids_to_remove = params[:lesson_ids].map(&:to_i)
      updated_lesson_ids = @form.lesson_ids - lesson_ids_to_remove

      if @form.update(lesson_ids: updated_lesson_ids)
        respond_to do |format|
          format.html { redirect_to "/school/classes/#{@form.id}/lessons", notice: 'Class was successfully updated.' }
          format.json { render json: { redirect_url: "/school/classes/#{@form.id}/lessons", notice: 'Class was successfully updated.' }, status: :ok }
        end
      else
        respond_to do |format|
          format.html { render :lessons }
          format.json { render json: { errors: @form.errors.full_messages }, status: :unprocessable_entity }
        end
      end
    end

    # PATCH /school/forms/:id/reschedule_lesson
    def reschedule_lesson
      authorize! :manage, @form

      unless params[:date].present? && params[:lesson_id].present?
        errors = []
        errors << 'Date is required' unless params[:date].present?
        errors << 'Lesson is required' unless params[:lesson_id].present?
        render json: { errors: errors }, status: :unprocessable_entity
        return
      end

      lesson = Lesson::Lesson.find(params[:lesson_id])
      new_time = DateTime.parse(params[:date])

      if lesson.update(time: new_time)
        respond_to do |format|
          format.html { redirect_to "/school/classes/#{@form.id}/lessons", notice: 'Class was successfully updated.' }
          format.json { render json: { redirect_url: "/school/classes/#{@form.id}/lessons", notice: 'Class was successfully updated.' }, status: :ok }
        end
      else
        respond_to do |format|
          format.html { render :lessons }
          format.json { render json: { errors: @form.errors.full_messages }, status: :unprocessable_entity }
        end
      end
    end

    # GET /school/forms/:id/edit
    def edit
    end

    # PATCH /school/forms/:id/update
    def update
      authorize! :manage, @form
      lesson_weekdays = (params[:form][:lesson_weekdays].compact || []).select(&:present?).map { |day| [day, 1] }.to_h

      if @form.update(name: params[:form][:name], lesson_weekdays: lesson_weekdays)
        redirect_to "/school/classes/#{@form.id}/edit", notice: 'Class was successfully updated.'
      else
        render :edit
      end
    end

    # PATCH /school/forms/:id/add_teacher
    def add_teacher
      authorize! :manage, @form
      updated_teacher_ids = (@form.teacher_ids << params[:teacher_id]).uniq

      if @form.update(teacher_ids: updated_teacher_ids)
        redirect_to "/school/classes/#{@form.id}/edit", notice: 'Teacher was successfully added.'
      else
        redirect_to "/school/classes/#{@form.id}/edit"
      end
    end

    # PATCH /school/forms/:id/remove_teacher
    def remove_teacher
      authorize! :manage, @form
      teacher_ids = @form.teacher_ids
      teacher_ids.delete(params[:teacher_id].to_i)

      if @form.update(teacher_ids: teacher_ids)
        redirect_to "/school/classes/#{@form.id}/edit", notice: 'Teacher was successfully updated.'
      else
        render :edit
      end
    end

    def print_letters
      pupils_data = @form.pupils.pluck_to_hash(:name, :identifier, :school_id, :id).map do |pupilData|
        pupil = Pupil.find(pupilData[:id])
        pupilData[:qr_code] = pupil.qr_signin_link
        pupilData[:school_name] = pupil.school.name
        pupilData[:class_name] = pupil.forms.last.name
        pupilData
      end

      render partial: "static_school/forms/print_letters", locals: { pupils_data: pupils_data }
    end

    def destroy
      authorize! :manage, @form
      @form.destroy
      redirect_to "/school/classes", notice: 'Class was successfully deleted.'
    end


    private

    def form_params
      params.require(:form).permit(:name)
    end

    def set_form
      @form = Form.accessible_by(current_ability).find(params[:id])
    end

    def set_teachers
      @teachers = @form.teachers
      @all_teachers = @form.school.teachers
    end

    def set_days
      @days = [['Monday', '1'],
      ['Tuesday', '2'],
      ['Wednesday', '3'],
      ['Thursday', '4'],
      ['Friday', '5'],
      ['Saturday', '6'],
      ['Sunday', '7']].sort_by { |d| d[1] }
    end

    def fetch_all_forms
      if current_user&.is_school_admin? && params[:all] == 'true'
        current_user.school&.forms
      else
        current_user&.forms
      end
    end

    def set_view_mode
      @view_all = @current_user&.is_school_admin? && params[:all] == 'true'
    end

    def set_form_breadcrumbs
      build_contextual_breadcrumbs do
        if params[:from_context] != 'class'
          if @view_all
            add_breadcrumb "All Classes", "/school/classes?all=true"
          else
            add_breadcrumb "Classes", "/school/classes"
          end
        end

        if @form
          parent_path = if action_name == 'pupils'
                        nil
                      else
                        "/school/classes/#{@form.id}/pupils"
                      end
          add_breadcrumb @form.name, parent_path

          if %w[pupils lessons edit].include?(action_name)
            add_breadcrumb action_name.titleize, nil
          end
        end
      end
    end

    def set_new_breadcrumbs
      add_breadcrumb "New Class"
    end

  end
end
