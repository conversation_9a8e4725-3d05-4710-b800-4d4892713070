module StaticSchool
  class StaticSchool::PagesController < StaticSchoolController
    def subscription
      # Assuming you have some base URL for the subscription
      base_path = "/school/subscription"

      # Check if current_user and firstname exist and the request does not already have the query parameters
      if current_user && !params[:firstname] && !params[:lastname]
        # Append the firstname and lastname as query parameters
        name = current_user.name
        first_name = name.split(' ').first
        last_name = name.split(' ').last

        school = current_user&.school
        uk_school = school&.uk_school

        phone = uk_school&.phone || school&.telephone
        finance_contact_name = school&.finance_name
        finance_contact_email = school&.finance_email

        name = uk_school&.name
        postcode = uk_school&.postcode || school&.postcode
        country = school&.country&.name
        school_type = "Home school"

        query_params = {
          firstname: first_name,
          lastname: last_name,
          email: current_user.email,
          phone: phone,
          finance_contact_name: finance_contact_name,
          finance_contact_email: finance_contact_email,
          name: name,
          zip: postcode,
          country: country,
          school_type: school_type
        }
        full_path = "#{base_path}?#{query_params.to_query}"

        # Redirect to the URL with query parameters
        redirect_to full_path and return
      end
    end

  end
end
