# app/controllers/test_webhooks_controller.rb
class TestWebhooksController < ActionController::Base
  # Only allow in development environment
  before_action :ensure_development_environment

  def index
    @event_types = [
      'invoice.payment_succeeded',
      'invoice.payment_failed',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'customer.updated',
      'customer.subscription.trial_will_end',
      'invoice.upcoming',
      'payment_method.attached',
      'invoice.payment_action_required',
      'checkout.session.completed'
    ]

    # Get subscribers for testing
    @subscribers = Subscriber.order(created_at: :desc).limit(20)

    # Get recent webhook events
    @recent_events = StripeEvent.recent.limit(10)
  end

  def trigger
    event_type = params[:event_type]
    subscriber_id = params[:subscriber_id]
    force_fail = params[:force_fail] == '1'
    error_message = params[:error_message]

    unless event_type.present? && subscriber_id.present?
      flash[:error] = 'Event type and subscriber ID are required'
      redirect_to test_webhooks_path
      return
    end

    subscriber = Subscriber.find(subscriber_id)
    customer_id = subscriber.stripe_customer_id
    subscription_id = subscriber.stripe_subscription_id

    # Create a mock event data structure based on the event type
    event_data = case event_type
                 when 'invoice.payment_succeeded'
                   create_invoice_event(subscriber, 'paid')
                 when 'invoice.payment_failed'
                   create_invoice_event(subscriber, 'payment_failed')
                 when 'customer.subscription.updated'
                   create_subscription_event(subscriber, 'active')
                 when 'customer.subscription.deleted'
                   create_subscription_event(subscriber, 'canceled')
                 when 'customer.updated'
                   create_customer_updated_event(subscriber)
                 when 'payment_method.attached'
                   create_payment_method_event(subscriber)
                 when 'checkout.session.completed'
                   create_checkout_event(subscriber)
                 else
                   flash[:error] = "Unsupported event type: #{event_type}"
                   redirect_to test_webhooks_path
                   return
                 end

    # Create a Stripe::Event object
    event = Stripe::Event.construct_from({
                                           id: "evt_test_#{SecureRandom.hex(10)}",
                                           object: 'event',
                                           api_version: '2020-08-27',
                                           created: Time.now.to_i,
                                           data: {
                                             object: event_data
                                           },
                                           type: event_type
                                         })

    # If we're forcing a failure, skip processing and record an error
    if force_fail
      error_message = error_message.present? ? error_message : 'Simulated webhook failure'
      # Determine if this is a critical webhook event
      critical = %w[
        invoice.payment_succeeded
        invoice.payment_failed
        customer.subscription.deleted
        customer.subscription.updated
        checkout.session.completed
      ].include?(event_type)

      # Record the failed event
      StripeEvent.record(event.id, event.type, event_data, error_message, nil, critical)

      flash[:warning] = "Simulated failed #{event_type} event with error: #{error_message}"
      Rails.logger.info "Created simulated failed #{event_type} event for subscriber #{subscriber.id}"
      redirect_to test_webhooks_path
      return
    end

    # Process the event directly
    begin
      webhook_controller = StripeWebhooksController.new
      webhook_controller.request = request

      # Call the appropriate handler based on the event type
      result = case event_type
               when 'invoice.payment_succeeded'
                 webhook_controller.send(:handle_successful_payment, event.data.object)
               when 'invoice.payment_failed'
                 webhook_controller.send(:handle_failed_payment, event.data.object)
               when 'customer.subscription.updated'
                 webhook_controller.send(:handle_subscription_update, event.data.object)
               when 'customer.subscription.deleted'
                 webhook_controller.send(:handle_subscription_deletion, event.data.object)
               when 'customer.subscription.trial_will_end'
                 webhook_controller.send(:handle_trial_ending, event.data.object)
               when 'payment_method.attached'
                 webhook_controller.send(:handle_payment_method_attached, event.data.object)
               when 'checkout.session.completed'
                 webhook_controller.send(:handle_checkout_session_completed, event.data.object)
               when 'customer.updated'
                 webhook_controller.send(:handle_customer_updated, event.data.object)
               end

      # Determine if this is a critical webhook event
      critical = %w[
        invoice.payment_succeeded
        invoice.payment_failed
        customer.subscription.deleted
        customer.subscription.updated
        checkout.session.completed
      ].include?(event_type)

      # Record the successful event
      StripeEvent.record(event.id, event.type, event_data, nil, nil, critical)

      flash[:success] = "Successfully processed #{event_type} event"
      Rails.logger.info "Successfully processed test #{event_type} event for subscriber #{subscriber.id}"
    rescue => e
      error_message = "Error processing event: #{e.message}"
      Rails.logger.error error_message
      Rails.logger.error e.backtrace.join("\n")

      # Record the event with error
      StripeEvent.record(event.id, event.type, event_data, error_message, nil, critical)

      flash[:error] = error_message
    end

    redirect_to test_webhooks_path
  end

  def view_event
    @event = StripeEvent.find(params[:id])
  end

  def retry_events
    # Find failed events
    failed_events = StripeEvent.with_errors

    if failed_events.empty?
      flash[:notice] = 'No failed events to retry'
      redirect_to test_webhooks_path
      return
    end

    # Count of successfully retried events
    retried_count = 0

    failed_events.each do |event|
      event_data = event.event_data
      event_type = event.event_type

      # Create a Stripe event object
      stripe_event = Stripe::Event.construct_from({
                                                    id: event.stripe_id,
                                                    object: 'event',
                                                    api_version: '2020-08-27',
                                                    data: {
                                                      object: event_data
                                                    },
                                                    type: event_type
                                                  })

      # Process the event
      webhook_controller = StripeWebhooksController.new
      webhook_controller.request = request

      # Call the appropriate handler based on the event type
      result = case event_type
               when 'invoice.payment_succeeded'
                 webhook_controller.send(:handle_successful_payment, stripe_event.data.object)
               when 'invoice.payment_failed'
                 webhook_controller.send(:handle_failed_payment, stripe_event.data.object)
               when 'customer.subscription.updated'
                 webhook_controller.send(:handle_subscription_update, stripe_event.data.object)
               when 'customer.subscription.deleted'
                 webhook_controller.send(:handle_subscription_deletion, stripe_event.data.object)
               when 'customer.subscription.trial_will_end'
                 webhook_controller.send(:handle_trial_ending, stripe_event.data.object)
               when 'payment_method.attached'
                 webhook_controller.send(:handle_payment_method_attached, stripe_event.data.object)
               when 'checkout.session.completed'
                 webhook_controller.send(:handle_checkout_session_completed, stripe_event.data.object)
               end

      # Mark as retried
      event.update(error: nil, processed_at: Time.current)
      retried_count += 1
    rescue => e
      Rails.logger.error "Error retrying event #{event.stripe_id}: #{e.message}"
      # Update the error message
      event.update(error: "Retry failed: #{e.message}", processed_at: Time.current)
    end

    flash[:success] = "Successfully retried #{retried_count} of #{failed_events.count} events"
    redirect_to test_webhooks_path
  end

  private

  def ensure_development_environment
    return if Rails.env.development?
    render plain: 'Only available in development environment', status: :forbidden
  end

  def create_invoice_event(subscriber, status)
    # Create a realistic invoice object with the subscriber's actual IDs
    {
      id: "in_test_#{SecureRandom.hex(10)}",
      object: 'invoice',
      customer: subscriber.stripe_customer_id,
      subscription: subscriber.stripe_subscription_id,
      status: status,
      total: 10_000,
      currency: 'gbp',
      created: Time.now.to_i,
      period_start: Time.now.to_i - 30.days.to_i,
      period_end: Time.now.to_i,
      lines: {
        object: 'list',
        data: [
          {
            id: "il_test_#{SecureRandom.hex(10)}",
            object: 'line_item',
            amount: 10_000,
            currency: 'gbp',
            subscription: subscriber.stripe_subscription_id
          }
        ]
      }
    }
  end

  def create_subscription_event(subscriber, status, trial_end = nil)
    # Create a realistic subscription object with the subscriber's actual IDs
    subscription = {
      id: subscriber.stripe_subscription_id,
      object: 'subscription',
      customer: subscriber.stripe_customer_id,
      status: status,
      current_period_start: Time.now.to_i - 30.days.to_i,
      current_period_end: Time.now.to_i + 30.days.to_i,
      created: Time.now.to_i - 60.days.to_i,
      items: {
        object: 'list',
        data: [
          {
            id: "si_test_#{SecureRandom.hex(10)}",
            object: 'subscription_item',
            price: {
              id: "price_test_#{SecureRandom.hex(10)}",
              object: 'price',
              product: "prod_test_#{SecureRandom.hex(10)}",
              unit_amount: 10_000,
              currency: 'gbp'
            }
          }
        ]
      }
    }

    # Add trial end if specified
    subscription[:trial_end] = trial_end if trial_end

    # Add cancel_at_period_end for deleted events
    if status == 'canceled'
      subscription[:canceled_at] = Time.now.to_i
      subscription[:cancel_at_period_end] = false
      subscription[:ended_at] = Time.now.to_i
    end

    subscription
  end

  def create_payment_method_event(subscriber)
    {
      id: "pm_test_#{SecureRandom.hex(10)}",
      object: 'payment_method',
      customer: subscriber.stripe_customer_id,
      type: 'card',
      card: {
        brand: 'visa',
        last4: '4242',
        exp_month: 12,
        exp_year: 2025
      }
    }
  end

  def create_checkout_event(subscriber)
    {
      id: "cs_test_#{SecureRandom.hex(10)}",
      object: 'checkout.session',
      customer: subscriber.stripe_customer_id,
      subscription: subscriber.stripe_subscription_id,
      mode: 'subscription',
      payment_status: 'paid',
      status: 'complete'
    }
  end

  def create_customer_updated_event(subscriber)
    {
      id: subscriber.stripe_customer_id,
      object: "customer",
      address: {
        city: "London",
        country: "GB",
        line1: "123 School Lane example",
        line2: "",
        postal_code: "SW1A 1AA",
        state: ""
      },
      balance: 0,
      created: 1588957060,
      currency: "gbp",
      default_source: "card_1NxCd3HpHmcciVa8rLuIjVnT",
      delinquent: false,
      description: "School Customer",
      discount: nil,
      email: "<EMAIL>",
      invoice_prefix: "60A41D0F",
      invoice_settings: {
        custom_fields: nil,
        default_payment_method: "pm_1NxCd3HpHmcciVa8sKwCpRtL",
        footer: nil
      },
      livemode: false,
      metadata: {
        school_id: "123",
        user_id: "456"
      },
      name: "Updated School Name",
      next_invoice_sequence: 4,
      phone: "+447123456789",
      preferred_locales: [],
      shipping: nil,
      tax_exempt: "none",
      tax_ids: {
        object: "list",
        data: [],
        has_more: false,
        total_count: 0,
        url: "/v1/customers/cus_HCESMjIPMqRE69/tax_ids"
      }
    }
  end
end
