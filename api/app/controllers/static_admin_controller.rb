class StaticAdminController < WebApplicationController
  layout 'admin'
  before_action :authenticate_admin!
  before_action :set_current_user
  before_action :mark_admin_request

  class_attribute :admin_section_name, instance_accessor: false
  before_action :authorize_admin_section

  include <PERSON><PERSON>ontroll<PERSON>

  def self.admin_section(name)
    self.admin_section_name = name
  end

  private

  def paginate items
    items.length >= 100 ? items.paginate(page: params[:page], per_page: 25) : items.paginate(page: 1, per_page: items.length)
  end

  def prevent_in_dev
    if Rails.env.development?
      sleep(5)
      Rails.logger.info "#{action_name.upcase} - blocked in dev"
      head :ok
    end
  end

  def mark_admin_request
    RequestStore.store[:admin_request] = true
  end

  def authorize_admin_section
    section = self.class.admin_section_name

    if section && !current_user.allows_admin_section?(section)
      redirect_to helpers.universal_dashboard_link(@current_user), alert: 'You are not authorized view this page'
    end
  end
end
