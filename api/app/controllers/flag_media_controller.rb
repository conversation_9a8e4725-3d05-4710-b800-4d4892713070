class FlagMediaController < ApplicationController
  def flag_media
    reference = params[:reference]
    type = params[:type] || 'image'
    reason = params[:reason]
    source_model_name = params[:source]
    source_id = params[:id]

    return render json: { error: 'Invalid reference' }, status: :bad_request unless reference.present?
    return render json: { error: 'Invalid type' }, status: :bad_request unless %w[image video].include?(type)
    return render json: { error: 'Invalid reason' }, status: :bad_request unless %w[subject nsfw copyright].include?(reason)

    flaggable_record = nil
    image_path = nil

    if source_model_name.present? && source_id.present?
      model_class = source_model_name.safe_constantize
      return render json: { error: 'Invalid source type' }, status: :bad_request unless model_class

      flaggable_record = model_class.find(source_id)

      image_path ||= walk_find_and_remove_image(flaggable_record.info, reference, ['info']) if flaggable_record.respond_to?(:info)
      image_path ||= walk_find_and_remove_image(flaggable_record.career_path, reference, ['career_path']) if flaggable_record.respond_to?(:career_path)
      image_path ||= walk_find_and_remove_image(flaggable_record.careers, reference, ['careers']) if flaggable_record.respond_to?(:careers)

      flaggable_record.save! if image_path
    end

    FlaggedImage.create!(
      image_id: reference,
      type: type,
      reason: reason,
      user_id: current_user&.id,
      image_path: image_path,
      flaggable: flaggable_record
    )

    render json: { success: true }
  end
end

# Takes a hash and deeply walks it, removing any key-value pairs matching { "image": value } and return the path
def walk_find_and_remove_image(obj, value, current_path = [])
  case obj
  when Hash
    obj.each do |k, v|
      if k == 'image' && v == value
        obj.delete(k)
        return current_path + [k]
      else
        found_path = walk_find_and_remove_image(v, value, current_path + [k])
        return found_path if found_path
      end
    end
  when Array
    obj.each_with_index do |item, i|
      found_path = walk_find_and_remove_image(item, value, current_path + [i])
      return found_path if found_path
    end
  end

  nil
end
