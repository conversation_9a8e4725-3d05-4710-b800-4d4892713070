class StaticQuizBuilderController < WebApplicationController
  ALLOWED_USER_TYPES = %w[Teacher Admin].freeze
  layout :dynamic_layout
  before_action :set_user
  before_action :set_quiz, only: %i[edit update]
  before_action :set_question, only: %i[edit update] 

  before_action :authenticate_user!
  include Warden<PERSON>ontroller
  include <PERSON><PERSON>

  def new
    @quiz = QuipQuiz.new(name: '')
  end

  def show
    quiz = QuipQuiz.find(params[:id])
    begin
      authorize! :read, quiz
    rescue CanCan::AccessDenied
      redirect_to helpers.universal_dashboard_link, alert: 'You are not authorized to view this quiz.'
      return
    end

    @quiz = quiz.format_for_static
    @submit_path = quip_quiz_by_id_path(quiz, user_id: params[:user_id])
  end

  def edit
    if (params[:return_to_name].present? && params[:return_to_url].present?)
      @return_path = {
        to: params[:return_to_url],
        label: params[:return_to_name]
      }
    end 
  end

  def create
    quiz_data = quiz_params
    quiz = QuipQuiz.new(quiz_data.permit(:name))
    quiz.user = current_user

    quiz_data[:questions].each do |question_data|
      quiz.quip_questions.new(question_type: question_data[:question_type], weight: question_data[:weight], data_json: question_data[:data_json])
    end
    return render json: { errors: quiz.errors.full_messages }, status: :unprocessable_entity unless quiz.save

    redirect_path = edit_quiz_path(quiz)
    if (params[:nested_in_lesson_template_id].present?)
      lesson_template = Lesson::Template.find_by(id: params[:nested_in_lesson_template_id])
      if lesson_template
        lesson_template.quip_quiz = quiz
        lesson_template.save!
        if lesson_template.user && current_user.teacher?
          redirect_path = show_quiz_school_lesson_editing_path(lesson_template)
        elsif current_user.admin?
          redirect_path = quiz_admin_lesson_template_path(lesson_template)
        else
          return render json: { errors: ["Unauthorized"] }, status: :unauthorized
        end
      else
        return render json: { errors: ["Lesson template not found"] }, status: :not_found
      end
    end
    render json: { quiz_id: quiz.id, redirect_path: redirect_path }
  end

  def update
    quiz_data = quiz_params
    begin
      ActiveRecord::Base.transaction do
        # Only update quiz name if we're not in single question mode
        @quiz.update!(quiz_data.permit(:name)) unless params[:question_id].present?
        update_quiz_questions(quiz_data[:questions])
      end

      redirect_path = edit_quiz_path(@quiz)
      # Preserve question_id in redirect if it exists
      redirect_path += "?question_id=#{params[:question_id]}" if params[:question_id].present?
      
      if (params[:nested_in_lesson_template_id].present?)
        lesson_template = Lesson::Template.find_by(id: params[:nested_in_lesson_template_id])
        if lesson_template.user && current_user.teacher?
          redirect_path = show_quiz_school_lesson_editing_path(lesson_template)
        elsif current_user.admin?
          redirect_path = quiz_admin_lesson_template_path(lesson_template)
        else
          return render json: { errors: ["Unauthorized"] }, status: :unauthorized
        end
      end
      render json: { quiz_id: @quiz.id, redirect_path: redirect_path }
    rescue ActiveRecord::RecordInvalid => e
      render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity
    rescue StandardError => e
      render json: { errors: ["An error occurred: #{e.message}"] }, status: :unprocessable_entity
    end
  end

  def upload_file
    render head :unauthorized unless current_user
    file = params[:file]
    file_id = upload_image(file)
    render json: { fileboy_id: file_id }
  end

  private

  def quiz_params
    params.require(:quiz).permit(
      :name,
      questions: [:id, :question_type, :weight, { data_json: {} }]
    )
  end

  def update_quiz_questions(questions_data)
    return unless questions_data

    # If we're in single question mode, only update that specific question
    if params[:question_id].present?
      question_data = questions_data.first
      return unless question_data

      question = @quiz.quip_questions.find(params[:question_id])
      question.update!(
        question_type: question_data[:question_type],
        weight: question_data[:weight],
        data_json: question_data[:data_json]
      )
    else
      # Original logic for updating all questions
      question_ids = questions_data.map { |q| q[:id] }.compact
      @quiz.quip_questions.where.not(id: question_ids).destroy_all

      questions_data.each do |question_data|
        question = @quiz.quip_questions.find_by(id: question_data[:id])
        attributes = {
          question_type: question_data[:question_type],
          weight: question_data[:weight],
          data_json: question_data[:data_json],
        }

        if question
          question.update!(attributes)
        else
          @quiz.quip_questions.create!(attributes)
        end
      end
    end
  end

  def authenticate_user!
    redirect_to root_path unless current_user&.type&.in?(ALLOWED_USER_TYPES)
  end

  def dynamic_layout
    if current_user&.type == 'Admin'
      'admin'
    elsif current_user&.type == 'Teacher'
      'school'
    end
  end

  def set_quiz
    @quiz = QuipQuiz.find(params[:id])
    begin
      authorize! :manage, @quiz
    rescue CanCan::AccessDenied
      redirect_to helpers.universal_dashboard_link, alert: 'You are not authorized to manage this quiz.'
      return
    end
    
    @quiz_data_json = @quiz.format_for_builder.to_json.html_safe
  end

  def set_question
    if params[:question_id].present?
      @question = @quiz.quip_questions.find(params[:question_id])
      # Filter the quiz data to only include this question
      quiz_data = @quiz.format_for_builder
      puts quiz_data
      quiz_data[:questions] = quiz_data[:questions].select { |q| q['id'] == @question.id }
      @quiz_data_json = quiz_data.to_json.html_safe
    end
  end

  def set_user
    @current_user = current_user
  end
end
