class StaticGameController < WebApplicationController
  layout :dynamic_layout
  before_action :set_current_pupil
  before_action :set_user

  def lesson_template_word_search
    user = User.accessible_by(current_ability).find_by(id: params[:user_id]) if params[:user_id].present?
    user = @current_user unless user.present?
    @user_id = user&.id

    template_id = params[:template_id]
    @template = Lesson::Template.find(template_id)

    lesson = params[:lesson_id] ? Lesson::Lesson.find(params[:lesson_id]) : @template.lesson_for_user(user)
    @lesson_id = lesson&.id

    @words = @template.keywords.pluck(:name)
    @best_times = {
      overall: @template.best_word_search_result,
      personal: user.word_search_results.where(lesson_template_id: template_id).minimum(:time_taken)
    }
    TrackingService.track_word_search_view(@template, user, { lesson_id: @lesson_id })
  end

  def submit_lesson_template_word_search
    user = User.accessible_by(current_ability).find_by(id: params[:user_id]) if params[:user_id].present?
    user = @current_user unless user.present?

    render json: {} and return unless user.present?

    user.word_search_results.create!(
      time_taken: params[:time_taken],
      lesson_template: Lesson::Template.find(params[:template_id])
    )

    UserRankEvent.add_points(user, 10, "word_search")

    completion_params = params.permit(:lesson_id, :time_taken, :template_id)
    template = Lesson::Template.find(completion_params[:template_id])
    lesson = Lesson::Lesson.find(completion_params[:lesson_id]) rescue nil

    TrackingService.track_word_search_completion(
      user,
      template,
      { time_taken: completion_params[:time_taken], lesson_id: lesson&.id }
    )

    render json: {}
  end

  def dynamic_layout
    if current_user&.type == 'Teacher'
      'school'
    elsif current_user&.type == 'Pupil'
      'pupil'
    else
      'static'
    end
  end

  private

  def set_user
    @current_user = current_user
  end

  def set_current_pupil
    if current_user.present? && current_user.pupil?
      @current_pupil = current_user
    end

    if current_user.present? && current_user.teacher?
      pupil_id = session[:pupil_id]
      allowed_pupils = current_user.pupils
      @current_pupil = allowed_pupils.find_by(id: pupil_id)
    end
  end

end
