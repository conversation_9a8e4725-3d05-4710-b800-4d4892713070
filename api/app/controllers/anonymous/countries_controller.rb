class Anonymous::CountriesController < AnonymousController
  def for_select_list
    blacklist = %w[US]

    countries = Country.all.joins(:available_templates).group(:id).order("countries.machine_name = 'GB' desc", name: :asc)
    countries = countries.where.not('countries.machine_name IN (?)', blacklist)

    render json: countries.pluck(:id, :name).map {|id, name| { label: name, value: id }}
  end

  def all_for_select_list
    blacklist = %w[US]

    countries = Country.all.order("countries.machine_name = 'GB' desc", name: :asc)
    countries = countries.where.not('countries.machine_name IN (?)', blacklist)

    render json: countries.pluck(:id, :name).map { |id, name| { label: name, value: id } }
  end
end
