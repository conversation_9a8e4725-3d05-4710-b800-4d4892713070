class Anonymous::LessonTemplatesController < AnonymousController
  before_action :set_lesson_template, only: %i[show load_rocket_words_quiz]

  def show
    render json: @lesson_template.data_for_show
  end

  private

  def set_lesson_template
#     templates_collection = current_user.admin? ? Lesson::Template.all : Lesson::Template.where(anonymous: true)
    @lesson_template = Lesson::Template.all.find(params[:id])
  end
end
