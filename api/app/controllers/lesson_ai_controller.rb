class LessonAiController < ActionController::Base
  before_action :authenticate_simple_token

  # /deai/videos
  def videos
    # Define a list of allowed parameter keys and allowed types for filtering
    allowed_params = %i[query type filter ids age subject]
    allowed_types = %w[sponsored assignment expert career]
    param_error = validate_params(allowed_params)
    return render json: param_error if param_error.present?

    videos = Video.includes(lesson_slides: { template: { new_library_units: :year } })
                  .where(is_available_in_ai_search: true)
                  .group('videos2.id')

    if params[:ids].present?
      ids = params[:ids].split(',')
      videos = videos.where('videos2.id': ids)
    end

    query = params[:query].to_s # Converts to string, empty if nil
    videos = videos.search(query) if query.present?

    # Filter videos based on the 'type' and 'filter' parameters
    %i[type filter].each do |param|
      next unless params[param].present?

      types = params[param].split(',') & allowed_types
      conditions = types.map { |type| "is_#{type} = true" }

      if param == :type
        videos = videos.where(conditions.join(' OR ')) if conditions.any?
      elsif param == :filter
        videos = videos.where(conditions.join(' AND ')) if conditions.any?
      end
    end

    # Filter by subject if provided
    if params[:subject].present?
      subject = params[:subject].downcase
      unless Video::VALID_SUBJECTS.include?(subject)
        return render json: { error: 'Invalid subject parameter' }, status: :unprocessable_entity
      end
      videos = videos.where('LOWER(subjects) LIKE ?', "%#{subject}%")
    end

    # Filter by age if provided
    if params[:age].present?
      age = params[:age].to_i
      videos = videos.references(lesson_slides: { template: { new_library_units: :year } })
                     .where('new_library_years.min_age <= ? AND new_library_years.max_age >= ?', age, age)
                     .group('videos2.id, lesson_slides.id, lesson_templates.id, new_library_units.id, new_library_years.id')
                     .distinct
    end

    videos = videos.order('videos2.created_at DESC')
    # Hard limit to 30
    videos = videos.limit(30)

    # Modify the rendering of each video to include min_age and max_age
    videos_json = videos.map do |video|
      units = video.lesson_slides.map { |slide| slide.template.new_library_units }.flatten.pluck(:name).uniq.compact
      years = video.lesson_slides.map { |slide|
        slide.template.new_library_units.map(&:year)
      }.flatten.compact

      min_ages = years.map(&:min_age).compact
      max_ages = years.map(&:max_age).compact

      min_age = min_ages.min
      max_age = max_ages.max

      {
        id: video.id,
        name: video.name,
        thumbnail_url: video.thumbnail_url,
        min_age: min_age,
        max_age: max_age,
        tags: video.tags.pluck(:name),
        units: units,
        years: years.pluck(:name).uniq,
        source: video.source,
        external_id: video.external_id,
        is_assignment: video.is_assignment,
        is_sponsored: if video.respond_to?(:is_sponsered)
                        video.is_sponsered
                      else
                        video.is_sponsored
                      end,
        is_expert: video.is_expert,
        is_career: video.is_career,
        documents: video.documents.map { |doc| { id: doc.id, name: doc.name, url: "https://www.developingexperts.com/file-cdn/files/get/#{doc.fileboy_file_id}" } }
      }
    end

    render json: { videos: videos_json }
  end

  # /deai/videos/:id
  def video_show
    id = params[:id]
    video = Video.includes(lesson_slides: { template: { new_library_units: :year } }).where(is_available_in_ai_search: true).find(id)

    units = video.lesson_slides.map { |slide| slide.template.new_library_units }.flatten.pluck(:name).uniq
    years = video.lesson_slides.map { |slide|
      slide.template.new_library_units.map(&:year)
    }.flatten

    min_ages = years.map(&:min_age).compact
    max_ages = years.map(&:max_age).compact

    min_age = min_ages.min
    max_age = max_ages.max

    render json: {
      id: video.id,
      name: video.name,
      thumbnail_url: video.thumbnail_url,
      min_age: min_age,
      max_age: max_age,
      tags: video.tags.pluck(:name),
      units: units,
      years: years.pluck(:name).uniq,
      source: video.source,
      external_id: video.external_id,
      is_assignment: video.is_assignment,
      is_sponsored: video.is_sponsored,
      is_expert: video.is_expert,
      is_career: video.is_career,
      documents: video.documents.map { |doc| { id: doc.id, name: doc.name, url: "https://www.developingexperts.com/file-cdn/files/get/#{doc.fileboy_file_id}" } }
    }
  end

  # /deai/careers
  def careers
    # Define a list of allowed parameter keys
    allowed_params = [:query, :ids]
    param_error = validate_params(allowed_params)
    return render param_error if param_error.present?

    careers = Career.all.group('careers.id')

    if params[:ids].present?
      ids = params[:ids].split(',')
      careers = careers.where(id: ids)
    end

    query = params[:query].to_s # Converts to string, empty if nil
    careers = careers.search(query) if query.present?

    careers = careers.order('created_at DESC')
    # Hard limit to 30
    careers = careers.limit(30)

    render json: { careers: careers.as_json(include: { tags: { only: [:name] } }) }
  end

  # /deai/careers/:id
  def career_show
    id = params[:id]
    career = Career.find(id)

    render json: career.as_json(include: { tags: { only: [:name] } })
  end

  # /deai/documents
  def documents
    # Define a list of allowed parameter keys
    allowed_params = [:query, :ids, :age]
    param_error = validate_params(allowed_params)
    return render json: param_error if param_error.present?

    # Fetching documents associated with Curriculum ID 18
    curriculum = NewLibrary::Curriculum.find(18)
    document_ids = curriculum.years.joins(units: { lesson_templates: :documents })
                             .select('distinct lesson_documents.id')

    # Preload necessary associations to avoid N+1 queries
    documents = Lesson::Document.joins(:template)
                                .includes(template: { new_library_units: :year })
                                .where(id: document_ids)
                                .where('lesson_documents.name NOT ILIKE ? AND lesson_documents.name NOT ILIKE ?', '%PRESENTATION%', '%SLIDE')
                                .group('lesson_documents.id')

    if params[:ids].present?
      ids = params[:ids].split(',')
      documents = documents.where(id: ids)
    end

    query = params[:query].to_s
    documents = documents.where('lesson_documents.name ILIKE ? OR lesson_templates.name ILIKE ?', "%#{query}%", "%#{query}%") if query.present?

    # Filter by age if provided
    if params[:age].present?
      age = params[:age].to_i
      documents = documents.joins(template: { new_library_units: :year })
                           .where('new_library_years.min_age <= ? AND new_library_years.max_age >= ?', age, age)
                           .distinct
    end

    documents = documents.order('lesson_documents.created_at DESC')
    # Hard limit to 30
    documents = documents.limit(30)

    # Modify the rendering of each document
    documents_json = documents.map do |document|
      units = document.template.new_library_units
      years = units.map(&:year).compact

      min_ages = years.map(&:min_age).compact
      max_ages = years.map(&:max_age).compact

      min_age = min_ages.min
      max_age = max_ages.max

      {
        id: document.id,
        name: "#{document.template.name} -- #{document.name}",
        min_age: min_age,
        max_age: max_age,
        fileboy_id: document.fileboy_file_id,
        units: units.pluck(:name).uniq, # Assuming unit names are stored in a 'name' attribute
        years: years.pluck(:name).uniq # Assuming year names are stored in a 'name' attribute
      }
    end

    render json: { documents: documents_json }
  end

  # /deai/documents/:id
  def document_show
    id = params[:id]
    document = Lesson::Document.find(id)

    render json: document.as_json()
  end

  # /deai/curriculum_definitions
  def curriculum_definitions
    curriculum_definitions = CurriculumDefinition.all
    render json: curriculum_definitions
  end

  def user
    @user ||= User.find_by(id: cookies.encrypted[:user_id], is_blocked: false)
    render json: nil and return unless @user
    render json: @user.as_json(only: %i[id email type]).merge(can_access_lesson_ai: @user.subscribed_to_service?(:ai))
  end

  def flagged_images
    type = params[:type]

    flagged_media = FlaggedImage.all

    if type.present? && ['image', 'video'].include?(type)
      flagged_media = flagged_media.where(type: type)
    end

    flagged_media = flagged_media.map do |flagged_image|
      { type: flagged_image.type, image_id: flagged_image.image_id }
    end

    render json: { flagged_media: flagged_media }
  end

  def flag_image
    image_id = params[:image_id]
    type = params[:type] || 'image'
    reason = params[:reason] || ''
    user_id = params[:user_id]

    render json: { error: 'Image ID is required' }, status: :bad_request and return unless image_id.present?
    render json: { error: 'Reason is required' }, status: :bad_request and return unless reason.present?

    FlaggedImage.find_or_create_by(image_id: image_id, type: type, reason: reason, user_id: user_id)

    render json: { success: true }
  end

  def author_user
    user = User.find_by(id: params[:id])
    render json: user ? { id: user.id, name: user.name } : nil
  end

  private

  def validate_params allowed_params
    # System parameters that are always allowed
    system_keys = %i[controller action format token]
    complete_allowed_params = allowed_params + system_keys

    # Identify unexpected parameters
    unexpected_params = params.keys.map(&:to_sym) - complete_allowed_params

    # Return an error structure if there are unexpected parameters, otherwise return nil
    if unexpected_params.any?
      {
        json: { error: 'Unexpected parameters', got: unexpected_params, allowed: allowed_params },
        status: :bad_request,
      }
    else
      nil
    end
  end

  # Check the Authorization header OR the token parameter
  def authenticate_simple_token
    correct_token = 'deai-1234567890'

    token = request.headers['Authorization'] || params[:token]
    if correct_token != token
      render json: { error: 'Unauthorized' }, status: :unauthorized
    end
  end

end
