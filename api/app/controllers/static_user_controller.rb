class StaticUserController < WebApplicationController
  layout :dynamic_layout
  before_action :authenticate_user!
  before_action :set_current_user
  include WardenController

  private

  def is_user
    current_user.present? && !current_user.pupil?
  end

  def authenticate_user!
    redirect_to root_path unless is_user
  end

  def set_current_user
    @current_user = current_user
  end

  def dynamic_layout
    if current_user&.type == 'Teacher'
      'school'
    elsif current_user&.type == 'Admin'
      'admin'
    else
      'user'
    end
  end
end
