# frozen_string_literal: true
include ActionView::Helpers::TextHelper

class AppController < ApplicationController
  api_version 1

  require 'net/http'
  require 'uri'

  def library
    country_id = current_user&.country&.id.presence || 1

    lessons = (current_user ? current_user.accessible_lesson_templates : Lesson::Template.accessible).where.not(disable_viewing: true)
                                                                                                     .joins(:available_countries, :new_library_units)
                                                                                                     .left_joins(:new_library_years, :new_library_curricula)
                                                                                                     .where(countries: { id: country_id })
                                                                                                     .group(:id)

    # Passed up from the user lesson template copy page
    lessons = lessons.where(viewable_only: false) if params[:excludeViewableOnly] == 'true'

    units = NewLibrary::Unit.joins(year: :curriculum)
    curricula = NewLibrary::Curriculum.published
    years = NewLibrary::Year.joins(:curriculum).where("new_library_curricula.published = ?", true)

    lessons = lessons.where("lesson_templates.name ILIKE ?", "%#{params[:query]}%") if params[:query].present?

    if params[:unit].present?
      lessons = lessons.where(new_library_units: { id: params[:unit] })
      year_id = units.find_by(id: params[:unit])&.year_id
      curriculum_id = units.find_by(id: params[:unit])&.curriculum&.id if year_id.present?
      years = years.where(id: [year_id]) if year_id.present?
      curricula = curricula.where(id: [curriculum_id]) if curriculum_id.present?
    end

    if params[:year].present?
      lessons = lessons.where(new_library_years: { id: params[:year] })
      units = units.where(year_id: params[:year])
      curriculum_id = years.find_by(id: params[:year])&.curriculum_id
      curricula = curricula.where(id: [curriculum_id]) if curriculum_id.present?
    end

    if params[:curriculum].present?
      lessons = lessons.where(new_library_curricula: { id: params[:curriculum] })
      years = years.where(curriculum_id: params[:curriculum])
      units = units.where(year_id: years.pluck(:id))
    end

    units = units
            .joins(:lesson_templates, year: :curriculum)
            .group(:id)
            .where(lesson_templates: { id: lessons })
            .where(new_library_curricula: { country_id: country_id })
            .order(weight: :asc, name: :asc)
            .pluck_to_hash(
              :id,
              :name,
              year: 'FIRST(new_library_years.name)',
              curriculum: 'FIRST(new_library_curricula.name)'
            )

    years = years
            .joins(:lesson_templates, :curriculum)
            .group(:id)
            .where(lesson_templates: { id: lessons })
            .where(new_library_curricula: { country_id: country_id })
            .order(name: :asc)
            .pluck_to_hash(
              :id,
              :name,
              curriculum: 'FIRST(new_library_curricula.name)',
            )

    curricula = curricula
                .joins(years: :lesson_templates)
                .group(:id)
                .where(lesson_templates: { id: lessons })
                .where(country_id: country_id)
                .order(name: :asc)
                .pluck_to_hash(:id, :name)

    lessons = lessons.limit(30).order(name: :asc)

    forms = []
    # moved user= into the IF block so that we don't need to check if user is present again
    if params[:user].present? && (user = User.find_by(id: params[:user]))
      forms = user.forms
      forms = user.school.forms if user.is_school_admin?

      forms = forms
              .joins(:lessons)
              .joins(:enrollments)
              .where(lesson_lessons: { template_id: lessons.select(:id) })
              .group(:id)
              .pluck_to_hash(
                :id,
                :name,
                pupil_ids: "coalesce(array_remove(ARRAY_AGG(enrollments.user_id), NULL), '{}')",
                template_ids: "coalesce(array_remove(ARRAY_AGG(template_id), NULL), '{}')",
              )
    end

    subquery = NewLibrary::Unit
               .joins(:lesson_template_libary_units, year: :curriculum)
               .where(new_library_curricula: { country_id: country_id })
               .select(
                 'new_library_units.name AS unit',
                 'new_library_years.name AS year',
                 'new_library_curricula.name AS curriculum',
                 'template_id'
               )

    lessons = Lesson::Template
              .where(id: lessons)
              .joins("LEFT JOIN (#{sql(subquery)}) AS sq ON sq.template_id = lesson_templates.id")
              .left_joins(:career_tags)
              .group(:id)
              .pluck_to_hash(
                :id,
                :fileboy_image_id,
                :name,
                :objectives,
                :anonymous,
                :demo,
                :viewable_only,
                units: "coalesce(array_remove(ARRAY_AGG(DISTINCT unit), NULL), '{}')",
                years: "coalesce(array_remove(ARRAY_AGG(DISTINCT year), NULL), '{}')",
                curricula: "coalesce(array_remove(ARRAY_AGG(DISTINCT curriculum), NULL), '{}')",
                career_tags: "coalesce(array_remove(ARRAY_AGG(DISTINCT career_tags.name), NULL), '{}')",
              )
              .map do |l|
                l.merge({
                  forms: forms.select { |form| form[:template_ids].include?(l[:id]) },
                  has_taught: !!current_user&.lessons&.where(template_id: l[:id])&.pluck(:lesson_taught)&.any? || false,
                  default_lesson: Lesson::Template.default_lesson_for_user(l[:id], current_user),
                  scientific_enquiry_types: Lesson::Template.find(l[:id]).scientific_enquiry_types,
                })
              end

    render json: {
      lessons: lessons,
      units: units,
      years: years,
      curricula: curricula,
    }
  end

  def community_library
    country_id = current_user ? current_user.country.id : 1

    lessons = (current_user ? current_user.accessible_lesson_templates : Lesson::Template.anonymous)
                .accessible_by(current_ability)
                .where(user_generated: true, available: true)
                .joins(:available_countries)
                .where(countries: { id: country_id })
                .group(:id)

    if params[:query].present?
      lessons = lessons.where("lesson_templates.name ILIKE ?", "%#{params[:query]}%")
    end

    lessons = lessons.order(name: :asc)

    forms = []
    if params[:user].present?
      user = User.find(params[:user])
      forms = user.forms

      if user.is_school_admin?
        forms = user.school.forms
      end

      forms = forms
                .joins(:lessons)
                .joins(:enrollments)
                .where(lesson_lessons: { template_id: lessons.select(:id) })
                .group(:id)
                .pluck_to_hash(
                  :id,
                  :name,
                  pupil_ids: "coalesce(array_remove(ARRAY_AGG(enrollments.user_id), NULL), '{}')",
                  template_ids: "coalesce(array_remove(ARRAY_AGG(template_id), NULL), '{}')",
                )
    end

    available_units = NewLibrary::Unit.all
    if current_user&.school&.new_library_curriculum_id.present?
      available_units = NewLibrary::Curriculum.find(current_user&.school&.new_library_curriculum_id).units
    end

    templates = Lesson::Template.where(id: lessons)
    lessons = templates.left_joins(:career_tags)
                       .left_joins(:lesson_template_folder)
                       .group(:id, "lesson_template_folders.name")
                       .pluck_to_hash(
                         :id,
                         :fileboy_image_id,
                         :name,
                         :objectives,
                         public_folder_name: "lesson_template_folders.name",
                         career_tags: "coalesce(array_remove(ARRAY_AGG(DISTINCT career_tags.name), NULL), '{}')",
                       )
                       .map do |l|
      l.merge({
                new_library_units: templates.find_by(id: l[:id])&.source_template&.new_library_units&.where(id: available_units.pluck(:id))&.map { |unit| { id: unit.id, name: unit.name, year: unit.year ? { id: unit.year.id, name: unit.year.name } : nil } } || [],
                forms: forms.select { |form| form[:template_ids].include?(l[:id]) },
                has_taught: !!current_user.lessons.where(template_id: l[:id]).pluck(:lesson_taught).any?
              })
    end

    render json: {
      lessons: lessons,
    }
  end

  def create_word_search_result
    if current_user
      current_user.word_search_results.create!(
        time_taken: params[:time_taken],
        lesson_template: Lesson::Template.find(params[:template_id])
      )
      UserRankEvent.add_points(current_user, 10, "word_search")
      if current_user.pupil? && params[:lesson_id].present?
        completion_params = params.permit(:lesson_id, :time_taken, :template_id)
        lesson = Lesson::Lesson.find(completion_params[:lesson_id])
        TrackingService.track_word_search_completion(current_user, lesson.template,
                                                     { time_taken: completion_params[:time_taken], lesson_id: lesson.id }
        )
      end
    end
    render json: {}
  end
end
