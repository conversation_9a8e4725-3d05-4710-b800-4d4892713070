require Rails.root.join('lib', 'utils', 'error_logger')
class StripeWebhooksController < ActionController::Base
  protect_from_forgery with: :null_session
  include E<PERSON>r<PERSON>ogger

  def create
    # Read the request data ONCE and store it
    @request_body ||= request.body.read
    payload = @request_body

    sig_header = request.env['HTTP_STRIPE_SIGNATURE']
    endpoint_secret = Rails.configuration.stripe[:endpoint_secret]

    begin
      JSON.parse(payload) unless payload.blank?
      event = Stripe::Webhook.construct_event(
        payload, sig_header, endpoint_secret
      )
    rescue JSON::ParserError => e
      Rails.logger.error "JSON parse error in webhook: #{e.message}"
      Rails.logger.error "Payload received: #{payload[0..100]}..." # Log partial payload for debugging
      render json: { error: e.message, status: 'invalid_payload' }, status: 400
      return
    rescue Stripe::SignatureVerificationError => e
      Rails.logger.error "Signature verification failed: #{e.message}"
      Rails.logger.error "Signature header: #{sig_header}"
      Rails.logger.error "Endpoint secret: #{endpoint_secret ? '[SECRET PRESENT]' : '[SECRET MISSING]'}"
      render json: { error: e.message, status: 'invalid_signature' }, status: 400
      return
    end

    # Check if we've already processed this event
    event_id = event.id
    if StripeEvent.exists?(stripe_id: event_id)
      Rails.logger.info "Stripe event #{event_id} already processed, skipping"
      render json: { received: true, status: 'already_processed' }
      return
    end

    # Process the event
    begin
      event_data = event.data.object.to_hash
      Rails.logger.info "Processing Stripe webhook: #{event.type} (#{event_id})"

      # Determine if this is a critical webhook event
      critical = critical_webhook?(event.type)

      # Route the event to the appropriate handler
      process_stripe_event(event)

      # Record that we've successfully processed this event
      StripeEvent.record(event_id, event.type, event_data, nil, sig_header, critical)

      render json: { received: true, status: 'processed' }
    rescue => e
      error_message = "Error processing #{event.type} event: #{e.message}\n#{e.backtrace.slice(0, 4).join("\n")}"
      Rails.logger.error error_message
      Rails.logger.error e.backtrace.join("\n")

      # Record the error
      StripeEvent.record(event_id, event.type, event_data, error_message, sig_header, critical)

      ErrorLog.create(
        error: {
          errorData: e,
          backtrace: e.backtrace,
          request_details: {
            webhook_type: event.type,
            event_id: event_id,
            signature: sig_header&.truncate(50)
          }
        }.to_json
      )

      # Schedule a retry job for critical events
      if critical
        begin
          # Queue a retry with a delay
          StripeWebhookRetryJob.set(wait: 5.minutes).perform_later(event_id)
          Rails.logger.info "Scheduled retry for critical webhook event #{event_id}"
        rescue => retry_error
          Rails.logger.error "Failed to schedule retry job: #{retry_error.message}"
        end
      end

      # Return a 200 status code so Stripe won't retry automatically
      # We'll handle retries ourselves through our job system
      render json: { received: true, status: 'error', message: error_message }
    end
  end

  def handle_invoice_overdue(invoice)
    Rails.logger.info "Processing overdue invoice: Invoice #{invoice.id} for customer #{invoice.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{invoice.customer}"
        return
      end

      # Check if this is a subscription invoice
      # Mark the subscriber as past_due if not already
      if invoice.subscription.present? && subscriber.subscription_status != 'past_due'
        previous_status = subscriber.subscription_status
        subscriber.update(subscription_status: 'past_due')
        Rails.logger.info "Updated subscriber #{subscriber.id} status from #{previous_status} to past_due"
      end

      # Send overdue notification email
      begin
        SubscriptionMailer.invoice_overdue(subscriber, invoice.id).deliver_later
        Rails.logger.info "Queued invoice overdue email for subscriber #{subscriber.id} for invoice #{invoice.id}"
      rescue => e
        Rails.logger.error "Failed to queue invoice overdue email: #{e.message}"
      end
    rescue => e
      Rails.logger.error "Error handling overdue invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise # Re-raise to be caught by the main error handler
    end
  end

  private

  def process_stripe_event(event)
    # Route the event to the appropriate handler

    case event.type
    when 'invoice.payment_succeeded'
      handle_successful_payment(event.data.object)
    when 'invoice.payment_failed'
      handle_failed_payment(event.data.object)
    when 'invoice.overdue'
      handle_invoice_overdue(event.data.object)
    when 'customer.subscription.created'
      handle_subscription_creation(event.data.object)
    when 'customer.subscription.updated'
      handle_subscription_update(event.data.object)
    when 'customer.subscription.deleted'
      handle_subscription_deletion(event.data.object)
    when 'invoice.upcoming'
      handle_upcoming_invoice(event.data.object)
    when 'payment_method.attached'
      handle_payment_method_attached(event.data.object)
    when 'invoice.payment_action_required'
      handle_payment_action_required(event.data.object)
    when 'checkout.session.completed'
      handle_checkout_session_completed(event.data.object)
    when 'invoice.finalized'
      handle_invoice_finalized(event.data.object)
    when 'invoice.sent'
      handle_invoice_sent(event.data.object)
    when 'customer.updated'
      handle_customer_updated(event.data.object)
    else
      Rails.logger.info "Unhandled event type: #{event.type} - No specific handler implemented"
      # Log unhandled event type
      ErrorLog.create(
        error: {
          errorData: "Unhandled webhook event type: #{event.type}",
          event_id: event.id,
          event_summary: event.data.object.to_hash.slice('id', 'object', 'customer', 'subscription', 'status')
        }.to_json
      )
    end
  rescue => e
    Rails.logger.error "Error in handler for #{event.type}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    raise # Re-raise to be caught by the main error handler
  end

  # Determine if a webhook event is critical for business operations
  def critical_webhook?(event_type)
    %w[
      invoice.payment_succeeded
      invoice.payment_failed
      customer.subscription.deleted
      customer.subscription.updated
      checkout.session.completed
    ].include?(event_type)
  end

  def handle_successful_payment(invoice)
    Rails.logger.info "Processing successful payment: Invoice #{invoice.id} for customer #{invoice.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{invoice.customer}"
        return
      end

      # Log the previous status for audit purposes
      previous_status = subscriber.subscription_status

      # Update subscription status
      subscriber.update(subscription_status: 'active')
      subscriber.update_referral_status

      # Log the status change
      Rails.logger.info "Updated subscriber #{subscriber.id} status from #{previous_status} to active"

      # Fetch the expanded invoice with product details to prevent the error
      begin
        expanded_invoice = Stripe::Invoice.retrieve({
                                                      id: invoice.id,
                                                      expand: ['lines.data.price.product']
                                                    })

        # Always send payment confirmation email for all successful payments
        SubscriptionMailer.payment_success(subscriber, expanded_invoice.id).deliver_later

        if invoice.billing_reason == 'subscription_cycle' # https://docs.stripe.com/api/invoices/object#invoice_object-billing_reason
          SubscriptionMailer.subscription_renewed(subscriber).deliver_later 
        end

        Rails.logger.info "Queued payment success email for subscriber #{subscriber.id}"
      rescue => e
        Rails.logger.error "Error expanding invoice for email: #{e.message}"
        # Still try to send the email with the original invoice
        SubscriptionMailer.payment_success(subscriber, invoice.id).deliver_later
        
        if invoice.billing_reason == 'subscription_cycle' # https://docs.stripe.com/api/invoices/object#invoice_object-billing_reason
          SubscriptionMailer.subscription_renewed(subscriber).deliver_later 
        end

        Rails.logger.info "Queued payment success email with original invoice for subscriber #{subscriber.id}"
      end
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Database error updating subscriber after payment: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling successful payment for invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_failed_payment(invoice)
    Rails.logger.info "Processing failed payment: Invoice #{invoice.id} for customer #{invoice.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{invoice.customer}"
        return
      end

      # Log the previous status for audit purposes
      previous_status = subscriber.subscription_status

      # Update subscription status
      subscriber.update(subscription_status: 'past_due')

      # Log the status change
      Rails.logger.info "Updated subscriber #{subscriber.id} status from #{previous_status} to past_due"

      # Send a notification email about the failed payment
      begin
        SubscriptionMailer.payment_failed(subscriber, invoice.id).deliver_later
        Rails.logger.info "Queued payment failed email for subscriber #{subscriber.id}"
      rescue => e
        Rails.logger.error "Failed to queue payment failed email: #{e.message}"
      end
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Database error updating subscriber after failed payment: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling failed payment for invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_subscription_update(subscription)
    Rails.logger.info "Processing subscription update: Subscription #{subscription.id} for customer #{subscription.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_subscription_id: subscription.id)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for subscription #{subscription.id}"
        return
      end

      # Get the previous attributes from the event
      previous_attributes = params.dig('data', 'previous_attributes') || {}
      Rails.logger.info "Previous attributes: #{previous_attributes.inspect}"

      # Check if this is a reactivation (cancellation being undone)f
      if previous_attributes['cancel_at_period_end'] == true &&
         !subscription.cancel_at_period_end &&
         subscription.status == 'active'

        Rails.logger.info "Subscription #{subscription.id} has been reactivated"

        # Update subscriber status and clear cancellation data
        subscriber.update(
          subscription_status: 'active',
          cancellation_date: nil,
          cancellation_reason: nil
        )
        # Send reactivation email
        begin
          SubscriptionMailer.subscription_reactivated(subscriber).deliver_later
          Rails.logger.info "Queued subscription reactivated email for subscriber #{subscriber.id}"
        rescue => e
          Rails.logger.error "Failed to queue subscription reactivated email: #{e.message}"
        end
      end

      # Check if this is a plan or item change (upgrade/downgrade)
      if previous_attributes &&
         (previous_attributes['plan'] ||
         (previous_attributes['items'] && previous_attributes['items']['data']))

        Rails.logger.info "Detected subscription change for #{subscription.id}"

        # Update subscriber status if needed
        if subscriber.subscription_status != subscription.status
          subscriber.update(subscription_status: subscription.status)
        end

        # Send notification about the subscription change
        begin
          SubscriptionMailer.subscription_updated(subscriber, subscription.id).deliver_later
          Rails.logger.info "Queued subscription updated email for subscriber #{subscriber.id}"
        rescue => e
          Rails.logger.error "Failed to queue subscription updated email: #{e.message}"
        end
      end

      # Check if this is a cancellation at period end
      if subscription.cancel_at_period_end && !(previous_attributes.key?('canceled_at') && previous_attributes['canceled_at'])
        Rails.logger.info "Subscription #{subscription.id} scheduled for cancellation at period end"
        subscriber.update(
          subscription_status: 'active_until_period_end',
          cancellation_date: Time.at(subscription.current_period_end).to_datetime
        )

        # Send a cancellation notification
        begin
          SubscriptionMailer.scheduled_cancellation(subscriber, Time.at(subscription.current_period_end).to_datetime).deliver_later
          SubscriptionMailer.admin_scheduled_cancellation(subscriber, Time.at(subscription.current_period_end).to_datetime).deliver_later
          Rails.logger.info "Queued scheduled cancellation email for subscriber #{subscriber.id}"
        rescue => e
          Rails.logger.error "Failed to queue scheduled cancellation email: #{e.message}"
        end
      end

      # If the status changed AND we are not waiting to cancel
      if subscriber.subscription_status != subscription.status && !subscription.cancel_at_period_end
        # Regular status change handling for other cases
        previous_status = subscriber.subscription_status
        subscriber.update(subscription_status: subscription.status)

        Rails.logger.info "Updated subscriber #{subscriber.id} status from #{previous_status} to #{subscription.status}"

        # Notify about important status changes
        if %w[past_due unpaid canceled active].include?(subscription.status)
          begin
            SubscriptionMailer.status_change(subscriber, previous_status, subscription.status).deliver_later
            Rails.logger.info "Queued status change email for subscriber #{subscriber.id}"
          rescue => e
            Rails.logger.error "Failed to queue status change email: #{e.message}"
          end
        end
      end

      SubscriptionSyncJob.perform_now(subscriber.id)
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Database error updating subscriber after subscription update: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling subscription update for subscription #{subscription.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_subscription_creation(subscription)
    Rails.logger.info "Processing subscription creating: Subscription #{subscription.id} for customer #{subscription.customer}"

    begin
      subscriber = fetch_with_retry(subscription.id, max_retries: 3, delay: 3)

      if subscriber.nil?
        Rails.logger.error "No subscriber found for subscription #{subscription.id}"
        raise ActiveRecord::RecordNotFound, "Subscriber not found for subscription #{subscription.id}"
      end

      SubscriptionSyncJob.perform_now(subscriber.id)

    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Database error updating subscriber after subscription creation: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling subscription creation for subscription #{subscription.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end

    # Send welcome email
    begin
      SubscriptionMailer.subscription_created(subscriber).deliver_later
      Rails.logger.info "Queued subscription created email for subscriber #{subscriber.id}"
    rescue => e
      Rails.logger.error "Failed to queue subscription created email: #{e.message}"
    end

    # Send admin notification
    begin
      SubscriptionMailer.admin_new_subscription_notification(subscriber, subscription.id).deliver_later
      Rails.logger.info "Queued subscription created notification email for admin for subscriber #{subscriber.id}"
    rescue => email_error
      Rails.logger.error "Failed to queue subscription created email for admin: #{email_error.message}"
    end
  end

  def handle_subscription_deletion(subscription)
    Rails.logger.info "Processing subscription deletion: Subscription #{subscription.id} for customer #{subscription.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_subscription_id: subscription.id)
      subscriber ||= Subscriber.find_by(stripe_customer_id: subscription.customer)
      if subscriber.nil?
        Rails.logger.warn "No subscriber found for subscription #{subscription.id}"
        return
      end

      # Update subscription status
      previous_status = subscriber.subscription_status
      subscriber.update(
        subscription_status: 'canceled',
        stripe_subscription_id: nil,
        cancellation_date: Time.now
      )

      Rails.logger.info "Updated subscriber #{subscriber.id} status from #{previous_status} to cancelled"

      # Notify about subscription cancellation
      begin
        SubscriptionMailer.subscription_canceled(subscriber).deliver_later
        SubscriptionMailer.admin_subscription_canceled(subscriber).deliver_later
        Rails.logger.info "Queued subscription cancelled email for subscriber #{subscriber.id}"
      rescue => e
        Rails.logger.error "Failed to queue subscription cancelled email: #{e.message}"
      end
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Database error updating subscriber after subscription deletion: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling subscription deletion for subscription #{subscription.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_upcoming_invoice(invoice)
    Rails.logger.info "Processing upcoming invoice: Invoice for customer #{invoice.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{invoice.customer}"
        return
      end

      # Send notification email about upcoming invoice
      begin
        SubscriptionMailer.upcoming_invoice_notification(subscriber, invoice.id).deliver_later
        Rails.logger.info "Queued upcoming invoice email for subscriber #{subscriber.id}"
      rescue => e
        Rails.logger.error "Failed to queue upcoming invoice email: #{e.message}"
      end
    rescue => e
      Rails.logger.error "Error handling upcoming invoice notification for invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_payment_method_attached(payment_method)
    Rails.logger.info "Processing payment method attached: Method #{payment_method.id} for customer #{payment_method.customer}"

    begin
      # Check if this is a new customer or existing customer
      customer = Stripe::Customer.retrieve(payment_method.customer)
      subscriber = Subscriber.find_by(stripe_customer_id: customer.id)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{customer.id}"
        return
      end

      # Update default payment method if needed
      if customer.invoice_settings&.default_payment_method.nil?
        begin
          Stripe::Customer.update(
            customer.id,
            invoice_settings: { default_payment_method: payment_method.id }
          )
          Rails.logger.info "Updated default payment method for customer #{customer.id}"
        rescue Stripe::StripeError => e
          Rails.logger.error "Error updating default payment method: #{e.message}"
        end
      end

      # NOTE: We don't typically send emails for this event
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error handling payment method attached: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling payment method attached for method #{payment_method.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_payment_action_required(invoice)
    Rails.logger.info "Processing payment action required: Invoice #{invoice.id} for customer #{invoice.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{invoice.customer}"
        return
      end

      # Just log the event but don't send email
      Rails.logger.info "Payment action required for subscriber #{subscriber.id} with invoice #{invoice.id}"

      # Get payment intent details for better logging
      begin
        if invoice.payment_intent
          payment_intent = Stripe::PaymentIntent.retrieve(invoice.payment_intent)
          Rails.logger.info "Payment intent #{payment_intent.id} requires action: #{payment_intent.status}"

          # Log additional details that might be useful for monitoring
          Rails.logger.warn "Last payment error: #{payment_intent.last_payment_error.message}" if payment_intent.last_payment_error
        end
      rescue => e
        Rails.logger.error "Error retrieving payment intent details: #{e.message}"
      end
    rescue => e
      Rails.logger.error "Error handling payment action required for invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_checkout_session_completed(session)
    Rails.logger.info "Processing checkout session completed: Session #{session.id}"

    begin
      # Skip if not a subscription checkout
      unless session.mode == 'subscription'
        Rails.logger.info "Skipping non-subscription checkout session: #{session.id}"
        return
      end

      # Retrieve the subscription ID from the session
      subscription_id = session.subscription

      unless subscription_id
        Rails.logger.warn "No subscription ID found in checkout session: #{session.id}"
        return
      end

      # Get the subscriber from the customer ID
      subscriber = Subscriber.find_by(stripe_customer_id: session.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{session.customer}"
        return
      end

      # Update the subscriber with the new subscription ID and active status
      subscriber.update(
        stripe_subscription_id: subscription_id,
        subscription_status: 'active'
      )

      Rails.logger.info "Updated subscriber #{subscriber.id} with subscription ID #{subscription_id}"

      # Retrieve expanded subscription details to log more information
      begin
        subscription = Stripe::Subscription.retrieve({
                                                       id: subscription_id,
                                                       expand: ['items.data.price.product']
                                                     })

        Rails.logger.info "Subscription details: status=#{subscription.status}, items=#{subscription.items.data.length}"

        # Log each product in the subscription
        subscription.items.data.each_with_index do |item, index|
          Rails.logger.info "Product #{index + 1}: #{item.price.product.name} (#{item.price.nickname})" if item.price&.product
        end

      rescue => e
        Rails.logger.error "Error retrieving detailed subscription info: #{e.message}"
      end
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Database error updating subscriber after checkout completion: #{e.message}"
      raise
    rescue => e
      Rails.logger.error "Error handling checkout session completion for session #{session.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_invoice_finalized(invoice)
    Rails.logger.info "Processing invoice finalized: Invoice #{invoice.id} for customer #{invoice.customer}"

    begin
      # Record the event - we're not sending customer notifications for this event
      # But we could optionally notify admins here
      Rails.logger.info "Invoice #{invoice.id} has been finalized. Status: #{invoice.status}"
    rescue => e
      Rails.logger.error "Error handling invoice finalization for invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def handle_invoice_sent(invoice)
    Rails.logger.info "Processing invoice sent: Invoice #{invoice.id} for customer #{invoice.customer}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: invoice.customer)

      if subscriber.nil?
        Rails.logger.warn "No subscriber found for customer #{invoice.customer}"
        return
      end

      # Fetch expanded invoice for better data
      begin
        expanded_invoice = Stripe::Invoice.retrieve({
                                                      id: invoice.id,
                                                      expand: ['lines.data.price.product', 'subscription', 'customer']
                                                    })

        invoice_to_use = expanded_invoice
      rescue => e
        Rails.logger.error "Error expanding invoice: #{e.message}"
        invoice_to_use = invoice
      end

      # Handle different invoice types with a single method
      if invoice_to_use.billing_reason == 'subscription_update'
        # This is a prorated invoice due to plan changes
        SubscriptionMailer.invoice_notification(subscriber, invoice_to_use.id, :subscription_change).deliver_later
        Rails.logger.info "Queued subscription change invoice email for subscriber #{subscriber.id}"
      else
        # Standard invoice (new subscription, renewal, etc.)
        SubscriptionMailer.invoice_notification(subscriber, invoice_to_use.id, :standard).deliver_later
        Rails.logger.info "Queued standard invoice email for subscriber #{subscriber.id}"
      end
    rescue => e
      Rails.logger.error "Error handling invoice sent notification for invoice #{invoice.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      log_error(e, 'StripeWebhookController#handle_invoice_sent')
      raise
    end
  end

  def handle_customer_updated(customer)
    Rails.logger.info "Processing customer update: Customer #{customer.id}"

    begin
      subscriber = Subscriber.find_by(stripe_customer_id: customer.id)
      return unless subscriber

      mutation = {}

      begin
        po_number_data = customer.invoice_settings&.custom_fields&.find { |field| field['name'] == 'PO Number' }

        if po_number_data&.present? && subscriber.po_number != po_number_data.value
          mutation.merge!(po_number: po_number_data.value)
          Rails.logger.info "Updated PO number for subscriber #{subscriber.id} to #{mutation[:po_number]}"
        end
      rescue => e
        Rails.logger.error "Error retrieving PO number data: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end

      # Check if address was updated
      if customer.address.present?
        mutation.merge!(
          address_line1: customer.address.line1,
          address_line2: customer.address.line2,
          city: customer.address.city,
          state: customer.address.state,
          postal_code: customer.address.postal_code,
          country: customer.address.country
        )

        Rails.logger.info "Updated address information for subscriber #{subscriber.id}"
      end

      if mutation.empty?
        Rails.logger.info "No changes detected for subscriber #{subscriber.id}"
        return
      end

      subscriber.update!(mutation)
    rescue => e
      Rails.logger.error "Error handling customer update: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise
    end
  end

  def fetch_with_retry(stripe_subscription_id, max_retries: 3, delay: 1)
    retries ||= 0
    begin
      # Force the request to uncache so it doesnt just re-do the same cached fetch and not find the record.
      record = ActiveRecord::Base.uncached do
        Subscriber.find_by(stripe_subscription_id: stripe_subscription_id)
      end
      raise ActiveRecord::RecordNotFound unless record.present?
      record
    rescue ActiveRecord::RecordNotFound => e
      if retries < max_retries
        retries += 1
        sleep delay
        retry
      end
    end
  end
end
