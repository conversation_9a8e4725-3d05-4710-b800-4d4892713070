# frozen_string_literal: true

DEFAULT_LOCALE = :en
ACCEPTED_LOCALES = [:en, :zh]

class ApplicationController < ActionController::API
  include ActionController::Cookies
  include Pagy::Backend
  include StandardJsonCrudResponse
  include ParamsWithImage
  include Warden<PERSON>ontroller
  before_action :parse_json_data
  before_action :check_not_blocked

  rescue_from StandardError, with: :handle_error

  before_action do
    if current_user && (!current_user.last_activity_at.present? || (current_user.last_activity_at < 1.hour.ago))
      current_user.update_columns(last_activity_at: Time.current)

      logged_in_today = TrackingService.track_active_day(current_user).present?
      # Add 2 points for logging in each day
      UserRankEvent.add_points(current_user, 2, "login") if !logged_in_today

      # Add 10 points for logging in 5 days in a row when they first login if today is the 5th day.
      if ActiveDay.where(user: current_user).longest_streak_since(DateTime.now.beginning_of_week) == 5 && !logged_in_today
        UserRankEvent.add_points(current_user, 10, "login_streak")
      end

    end
  end

  def current_user
    request.env['warden'].user
  end

  def current_school
    @current_school ||= current_user&.school
  end

  def authenticate_admin
    head 401 unless current_user&.admin?
  end

  def authenticate_pupil
    head 401 unless current_user&.pupil?
  end

  def authenticate_guardian
    head 401 unless current_user&.guardian?
  end

  def authenticate_tutor
    head 401 unless (current_user&.tutor?) || current_user&.admin?
  end

  def authenticate_teacher
    head 401 unless current_user&.teacher? || current_user&.admin?
  end

  def authenticate_organisation
    head 401 unless current_user&.employer? || current_user&.admin?
  end

  def authenticate_pupil
    head 401 unless current_user&.pupil? || current_user&.admin?
  end

  def authenticate_admin_teacher
    head 401 unless (current_user&.teacher? && current_user.is_school_admin?) || current_user&.admin?
  end

  def current_locale
    @current_locale ||= get_best_locale_from_request
  end

  delegate :escape, :sql, :load_sql, to: ApplicationRecord

  def self.api_version version
    raise "Unknown API version: #{version}" unless version.in? [1, 2]

    around_action :set_api_version

    define_method :set_api_version do |&block|
      Thread.current[:api_version] = version
      block.call
    ensure
      Thread.current[:api_version] = nil
    end
  end

  #################
  #################
  ################# THINGS FOR V2
  #################
  #################

  def paginate(collection, items = 30)
    meta, records = pagy(collection, items: items)

    formattedMeta = { page: meta.vars[:page], perPage: meta.vars[:items], totalCount: meta.count, itemCount: meta.items }
    return formattedMeta, records
  end

  #################
  #################
  #################

  private

  def get_best_locale_from_request
    DEFAULT_LOCALE
  end

  def parse_json_data
    if params["__json-data"]
      json_data = JSON.parse(params["__json-data"])
      params.merge!(json_data) # Update params with parsed JSON
    end
  end

  def check_not_blocked
    if current_user&.is_blocked?
      sign_out
      render json: { error: 'This account has been deactivated. Please contact support.' }, status: 403
    end
  end

  # ERROR HANDLING

  def request_details(request)
    return {} unless request
    headers = [
      'HTTP_HOST',
      'HTTP_USER_AGENT',
      'HTTP_ACCEPT',
      'HTTP_REFERER',
    ]
    {
      method: request.method,
      path: request.fullpath,
      ip: request.remote_ip,
      params: request.filtered_parameters,
      headers: request.headers.env.select { |k, _| headers.include?(k) },
    }
  end

  # Log the error details
  def log_error(exception, request)
    Rails.logger.error("Exception occurred: #{exception.message}")
    Rails.logger.error(exception.backtrace.join("\n"))

    # Optionally, send to an external logging service or save to database
    return if ErrorLog.should_not_log(exception, request, source: :api)

    Sentry.capture_exception(e) if defined?(Sentry)

    ErrorLog.create(
      error: { errorData: exception, backtrace: exception.backtrace, request_details: request_details(request) }.to_json,
      user_info: current_user ? { id: current_user.id, type: current_user.type, school_id: current_user.school_id } : {}
    )
  end

  def simple_response(message, status)
    case request.format.symbol
    when :html
      render plain: message, status: status
    when :json
      render json: { error: message }, status: status
    else
      render plain: message, status: status
    end
  end

  # Customize error handling as needed
  def handle_error(exception)
    return simple_response('Unprocessable Entity', :unprocessable_entity) if exception.is_a?(ActionController::UnknownFormat)

    log_error(exception, request)

    return simple_response('Not authorized', 401) if exception.is_a?(CanCan::AccessDenied)
    return simple_response('Not found', :not_found) if exception.is_a?(ActiveRecord::RecordNotFound)

    # DEFAULT HANDLING
    simple_response('Internal Server Error', :internal_server_error)
  end
end

class ActionDispatch::Request
  # If given form data with a "__json-data" key, JSON parse it and merge it into the params
  def parse_multipart
    result = super
    result.merge!(JSON.parse(result.delete("__json-data"))) if result && result["__json-data"]
    result
  end
end
