class WebApplicationController < ActionController::Base
  # Common functionality for non-API controllers
  before_action :check_affiliate_param
  before_action :check_device_token
  before_action :check_not_blocked
  before_action :set_sentry_context

  include WardenController

  private

  def request_details(request)
    return {} unless request
    headers = %w[
      HTTP_HOST
      HTTP_USER_AGENT
      HTTP_ACCEPT
      HTTP_REFERER
    ]
    {
      method: request.method,
      path: request.fullpath,
      ip: request.remote_ip,
      params: request.filtered_parameters,
      headers: request.headers.env.select { |k, _| headers.include?(k) },
    }
  end

  def check_affiliate_param
    return unless params[:affiliate_ref_code].present?
    affiliate = Affiliate.find_by(affiliate_code: params[:affiliate_ref_code])
    session[:affiliate_id] = affiliate.id if affiliate.present?
  end

  def check_device_token
    return unless current_user

    # Skip device token check in test environment
    return if Rails.env.test?

    device_token = cookies[:device_token]
    return if device_token && current_user.device_logins.exists?(device_token: device_token)
    sign_out_and_clear_auth
  end

  def sign_out_and_clear_auth
    sign_out
    render 'static/logout', layout: false
  end

  def check_not_blocked
    return unless current_user&.is_blocked?
    sign_out
    redirect_to '/', alert: 'This account has been deactivated. Please contact support.'
  end

  # Finds and authorizes an ActiveRecord resource, handling missing records or unauthorized access.
  #
  # @param model [ActiveRecord::Base] The ActiveRecord model class to query.
  # @param param_key [Symbol] (optional) The param key for finding the ID. Defaults to :id.
  # @param scope [ActiveRecord::Relation, nil] (optional) Scope for querying; defaults to accessible_by.
  # @param redirect_path [String] (optional) Path for redirection upon failure; defaults to root_path.
  #
  # @return [ActiveRecord::Base, nil] Resource instance if found; nil otherwise.
  #
  def find_resource(model, param_key: :id, scope: nil, redirect_path: root_path)
    scope ||= model.accessible_by(current_ability)
    scope.find(params[param_key])
  rescue ActiveRecord::RecordNotFound
    flash[:error] = "#{model.name.demodulize} not found or you don't have access to it"
    redirect_to redirect_path
    nil
  end

  def authenticate_user!
    redirect_to root_path unless current_user.present?
  end

  def set_sentry_context
    Sentry.set_user(id: current_user.id, email: current_user.email) if current_user
    Sentry.set_extras(params: params.to_unsafe_h, url: request.url, school: current_user&.school&.name, user_type: current_user&.type)
  end

  def capture_posthog_marketing_event(event_name)
    if Rails.env.development? || Rails.env.test?
      # Skip event capture in development and test environments
      return
    end
    $posthog_marketing.capture(
      distinct_id: current_user&.id || 'anonymous',
      event: event_name,
      properties: {
        user_type: current_user&.type || 'anonymous',
        school: current_user&.school&.name || '',
        email: current_user&.email || '<EMAIL>'
      }
    )
  end

  def skip_bullet
    previous_value = Bullet.enable?
    Bullet.enable = false
    yield
  ensure
    Bullet.enable = previous_value
  end

  def fileboy_image_url(fileboy_image_id, width: 400, height: "_", fit: "cover")
    return nil unless fileboy_image_id
    transform = "resize:#{width}x#{height}~fit:#{fit};format:webp;quality:80"
    "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=#{transform}"
  end

  def is_teacher
    (current_user&.teacher? || current_user&.is_school_admin?)
  end

  def authenticate_admin!
    if current_user.nil?
      session[:user_return_to] = request.fullpath
      redirect_to accounts_sign_in_path
    elsif !current_user.admin?
      redirect_to helpers.universal_dashboard_link(current_user), alert: "You are logged in as a #{current_user.type} but need admin access. Please switch accounts to continue."
    end
  end

  def authenticate_teacher!
    if current_user.nil?
      session[:user_return_to] = request.fullpath
      redirect_to accounts_sign_in_path
    elsif !is_teacher
      if (request.path == '/school/subscription')
        redirect_to lesson_subscription_path and return
      else
        redirect_to helpers.universal_dashboard_link(current_user), alert: "You are logged in as a #{current_user.type} but need teacher access. Please switch accounts to continue."
      end
    end
  end

  def authenticate_admin_teacher!
    if current_user.nil?
      session[:user_return_to] = request.fullpath
      redirect_to accounts_sign_in_path
    elsif !(current_user.teacher? && current_user.is_school_admin?)
      redirect_to helpers.universal_dashboard_link(current_user), alert: "You are logged in as a #{current_user.type} but need admin teacher access. Please switch accounts to continue."
    end
  end

  def authenticate_teacher_or_admin!
    if current_user.nil?
      session[:user_return_to] = request.fullpath
      redirect_to accounts_sign_in_path
    elsif !(current_user.teacher? || current_user.is_school_admin? || current_user.admin?)
      redirect_to helpers.universal_dashboard_link(current_user), alert: "You are logged in as a #{current_user.type} but need teacher or admin access. Please switch accounts to continue."
    end
  end

  def set_current_user
    @current_user = current_user
  end
end
