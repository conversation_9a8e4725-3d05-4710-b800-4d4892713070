# frozen_string_literal: true
class DocumentsController < ApplicationController
  api_version 1

  def document_url_redirect
    document = Lesson::Document.find(params[:id])

    completion_params = params.permit(:lesson_id, :document_type, :pupil_id)

    if completion_params[:pupil_id].present? && completion_params[:lesson_id].present?
      @pupil = Pupil.find(completion_params[:pupil_id])

      lesson = Lesson::Lesson.find(completion_params[:lesson_id])

      TrackingService.track_document_download(document, @pupil, {
        document_type: completion_params[:document_type].to_sym,
        lesson_id: lesson.id,
      })
    end

    redirect_to "https://www.developingexperts.com/file-cdn/files/get/#{document.fileboy_file_id}" and return
  end
end
