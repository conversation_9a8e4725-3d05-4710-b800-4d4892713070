require 'csv'

module StaticAdmin
  class StaticAdmin::ImportSchoolsController < StaticAdminController
    before_action :set_expected_columns, only: [:map_columns, :validate_data, :import, :download_template]

    def new
      # Initial view for uploading CSV
    end

    def create
      # Handle CSV upload and redirect to processing page
      if params[:csv_file].blank?
        flash[:error] = "Please select a CSV file to upload."
        redirect_to new_admin_import_school_path
        return
      end

      uploaded_file = params[:csv_file]

      # Basic validation for file type and size
      unless uploaded_file.content_type == 'text/csv'
        flash[:error] = "Invalid file type. Please upload a CSV file."
        redirect_to new_admin_import_school_path
        return
      end

      if uploaded_file.size > 5.megabytes
        flash[:error] = "File size exceeds 5MB limit."
        redirect_to new_admin_import_school_path
        return
      end

      tmp_dir = Rails.root.join('tmp', 'csv_imports')
      FileUtils.mkdir_p(tmp_dir) unless Dir.exist?(tmp_dir)
      sanitized_filename = uploaded_file.original_filename.gsub(/[^a-zA-Z0-9_\.-]/, '_')
      @csv_file_path = tmp_dir.join("#{Time.now.to_i}-#{sanitized_filename}")

      File.open(@csv_file_path, 'wb') do |file|
        file.write(uploaded_file.read)
      end

      session[:csv_file_path] = @csv_file_path.to_s
      redirect_to map_columns_admin_import_schools_path
    end

    def map_columns
      @csv_file_path = session[:csv_file_path]
      if @csv_file_path.blank? || !File.exist?(@csv_file_path)
        flash[:error] = "CSV file not found. Please upload again."
        redirect_to new_admin_import_school_path
        return
      end

      begin
        # Read the CSV with original headers preserved for display
        csv_data = CSV.read(@csv_file_path, headers: true, return_headers: true)
        
        # Store the original headers for display in the view
        @csv_headers = csv_data.headers.compact.map(&:to_s)
        session[:csv_headers] = @csv_headers
        
        # Define common synonym mappings for each field
        field_synonyms = {
          uk_school_name: ['uk school', 'ukschool', 'uk_school', 'school uk', 'school_uk'],
          name: ['school name', 'schoolname', 'school_name'],
          new_library_curriculum_id: ['curriculum', 'curriculum_id', 'curriculum id'],
          postcode: ['postcode', 'post code', 'post_code', 'zip', 'zip code', 'zipcode'],
          telephone: ['telephone', 'phone', 'phone number', 'phonenumber', 'tel', 'contact number'],
          category: ['school category', 'schoolcategory', 'school_category', 'category', 'school type', 'schooltype', 'school_type', 'type'],
          region: ['region', 'region', 'school region', 'school_region'],
          lead_source: ['lead source', 'leadsource', 'lead_source', 'source'],
          term_length: ['term length', 'termlength', 'term_length'],
          teacher_name: ['teacher name', 'teachername', 'teacher_name', 'staff name', 'staffname', 'staff_name'],
          teacher_email: ['teacher email', 'teacheremail', 'teacher_email', 'staff email', 'staffemail', 'staff_email'],
          teacher_password: ['teacher password', 'teacherpassword', 'teacher_password', 'staff password', 'staffpassword', 'staff_password']
        }

        @suggested_mappings = {}
        
        @csv_headers.each do |header|
          next if header.nil? # Skip nil headers
          normalized_header = header.downcase.strip
          
          # Try to find a matching field
          matching_field = nil
          
          field_synonyms.each do |field, synonyms|
            if synonyms.any? { |s| normalized_header.include?(s) || s.include?(normalized_header) }
              matching_field = field
              break
            end
          end
          
          # Only add to suggested mappings if we found a matching field
          @suggested_mappings[header] = matching_field.to_s if matching_field
        end

      rescue CSV::MalformedCSVError => e
        flash[:error] = "Failed to parse CSV: #{e.message}. Please ensure it is a valid CSV file."
        redirect_to new_admin_import_school_path
      end
    end

    def validate_data
      csv_file_path = params[:csv_file_path] || session[:csv_file_path]
      mappings = params[:mappings]

      if csv_file_path.blank? || !File.exist?(csv_file_path)
        flash[:error] = "CSV file not found. Please upload again."
        redirect_to new_admin_import_school_path
        return
      end

      if mappings.blank?
        flash[:error] = "Column mappings are missing. Please map the columns first."
        redirect_to map_columns_admin_import_schools_path(csv_file_path: csv_file_path)
        return
      end

      processed_rows = []
      @errors = []
      row_number = 1

      CSV.foreach(csv_file_path, headers: true, header_converters: ->(h) { h.strip.downcase.gsub(/[^a-z0-9_]/, '') if h.present? }) do |row|
        row_number += 1
        school_data = {}
        errors_for_row = {}

        @expected_columns.each do |ec|
          original_csv_header_for_field = mappings[ec[:field].to_s]
          normalized_csv_header_key = original_csv_header_for_field&.strip&.downcase&.gsub(/[^a-z0-9_]/, '')

          value = nil
          if normalized_csv_header_key.present? && row.header?(normalized_csv_header_key) && row[normalized_csv_header_key].present?
            value = row[normalized_csv_header_key]
          else
            value = ec[:default]
          end
          school_data[ec[:field]] = value.is_a?(String) ? value.strip : value
        end

        uk_school_name_or_id = school_data[:uk_school_name]
        uk_school_matched = nil

        if uk_school_name_or_id.present?
          potential_uk_schools = UkSchool.where("lower(name) = ?", uk_school_name_or_id.to_s.strip.downcase)

          matched_and_available = potential_uk_schools.find { |uks| uks.school.nil? }

          if matched_and_available
            uk_school_matched = matched_and_available
            school_data[:uk_school_id] = uk_school_matched.id
            school_data[:name] = uk_school_matched.name
            school_data[:postcode] = uk_school_matched.postcode
            school_data[:telephone] = uk_school_matched.phone
            school_data[:category] = uk_school_matched.category
            school_data[:region] = uk_school_matched.region
          elsif potential_uk_schools.any? { |uks| uks.school.present? }
            errors_for_row[:uk_school_name] = "'#{uk_school_name_or_id}' matches a UK School that is already associated with an existing school."
          else
            errors_for_row[:uk_school_name] = "UK School '#{uk_school_name_or_id}' not found or is not available for import."
          end
        end

        unless uk_school_matched
          @expected_columns.each do |ec|
            if ec[:required] && school_data[ec[:field]].blank?
              errors_for_row[ec[:field]] ||= "#{ec[:name]} is required and was not provided or mapped."
            end
          end
          
          # Improved school category validation with case-insensitive comparison and normalization
          if school_data[:category].present?
            valid_categories = School.categories_for_select.map(&:last).map(&:downcase)
            normalized_category = school_data[:category].strip.downcase.gsub(/[^a-z0-9]/, '_')

            puts valid_categories.inspect
            puts normalized_category.inspect
            
            # Try to find a match with case-insensitive comparison
            matching_category = valid_categories.find do |valid_category| 
              valid_category.downcase == normalized_category.downcase
            end
            
            if matching_category
              # Use the properly cased category from the system
              school_data[:category] = matching_category
            else
              # No match found, show error with valid options
              errors_for_row[:category] = "Invalid School Category: '#{school_data[:category]}'. Must be one of: #{School.categories_for_select.map(&:first).join(', ')}."
            end
          end

          if school_data[:region].present?
            valid_regions = School.regions.keys.map(&:to_s).map(&:downcase)
            normalized_region = school_data[:region].strip.downcase.gsub(/[^a-z0-9_]/, '_')
          
            matching_region = valid_regions.find do |valid_region|
              valid_region.downcase == normalized_region
            end
          
            if matching_region
              school_data[:region] = matching_region
            else
              errors_for_row[:region] = "Invalid School Region: '#{school_data[:region]}'. Must be one of: #{valid_regions.join(', ')}."
            end
          end

          if school_data[:teacher_email].present?
            unless school_data[:teacher_email] =~ URI::MailTo::EMAIL_REGEXP
              errors_for_row[:teacher_email] = "Teacher Email '#{school_data[:teacher_email]}' is not a valid email address."
            end
          
            if User.exists?(email: school_data[:teacher_email].strip.downcase)
              errors_for_row[:teacher_email] = "Teacher Email '#{school_data[:teacher_email]}' already exists in the system."
            end
          end
          
          if school_data[:teacher_name].present? && school_data[:teacher_email].present?
            if school_data[:teacher_password].blank?
              errors_for_row[:teacher_password] = "Teacher Password is required when creating a new teacher."
            else
              password = school_data[:teacher_password]
              password_errors = []
              password_errors << "must be at least 8 characters." unless password.length > 7
              password_errors << "must contain a number." unless password.match?(/[0-9]/)
              password_errors << "must contain a letter." unless password.match?(/[a-z]/i)
              password_errors << "must contain a special character." unless password.match?(/[^a-zA-Z0-9 ]/)
              errors_for_row[:teacher_password] = "Password #{password_errors.join(' ')}" if password_errors.any?
              
              # Join multiple password errors into one sentence
              if errors_for_row[:teacher_password].is_a?(Array) && errors_for_row[:teacher_password].any?
                errors_for_row[:teacher_password] = "Password #{errors_for_row[:teacher_password].join(' ')}"
              end
            end
          end
          if school_data[:teacher_name].present? && school_data[:teacher_email].present? && school_data[:teacher_password].blank?
            errors_for_row[:teacher_password] = "Teacher Password is required when creating a new teacher."
          end
        end

        processed_rows << { data: school_data, errors: errors_for_row, original_row: row.to_h, row_number: row_number }
        @errors << { row_number: row_number, errors: errors_for_row, original_data: row.to_h } if errors_for_row.any?
      end

      tmp_dir = Rails.root.join('tmp', 'csv_imports')
      FileUtils.mkdir_p(tmp_dir) unless Dir.exist?(tmp_dir)
      
      processed_data_file = tmp_dir.join("processed_rows_#{Time.now.to_i}.json")
      File.open(processed_data_file, 'w') do |file|
        file.write(processed_rows.to_json)
      end
      
      session[:processed_rows_file_path] = processed_data_file.to_s
      session[:processed_rows_count] = processed_rows.length

      if @errors.any?
        flash.now[:error] = "Found #{@errors.count} #{'row'.pluralize(@errors.count)} with errors. Please review below."
        render :edit_data
      else
        @processed_rows_count = session[:processed_rows_count] || 0
        @processed_rows_file_path = session[:processed_rows_file_path]

        puts "Processed rows count: #{@processed_rows_count}"
        puts "Processed rows file path: #{@processed_rows_file_path}"
        render :confirm_import
      end
    end

    def confirm_import
      @processed_rows_count = session[:processed_rows_count] || 0
      
      if @processed_rows_count == 0
        flash[:error] = "No valid data found to import."
        redirect_to new_admin_import_school_path
      end
    end

    def import
      processed_data_file = session[:processed_rows_file_path]
      
      if processed_data_file.blank? || !File.exist?(processed_data_file)
        flash[:error] = "No data to import or session expired. Please validate your CSV again."
        redirect_to new_admin_import_school_path
        return
      end
      
      processed_rows = JSON.parse(File.read(processed_data_file), symbolize_names: true)

      @imported_schools_summary = []
      @import_errors = []

      ActiveRecord::Base.transaction do
        processed_rows.each_with_index do |row_data_wrapper, index|
          school_attributes = row_data_wrapper[:data].with_indifferent_access
          original_csv_row_number = row_data_wrapper[:row_number]

          if row_data_wrapper[:errors].any?
            @import_errors << { row_number: original_csv_row_number, message: "Skipped due to validation errors: #{row_data_wrapper[:errors].map { |k, v| "#{k.to_s.humanize}: #{v}" }.join('; ')}" }
            next
          end

          begin
            school = nil
            teacher = nil

            teacher_name = school_attributes.delete(:teacher_name)
            teacher_email = school_attributes.delete(:teacher_email)&.strip&.downcase
            teacher_password = school_attributes.delete(:teacher_password)
            school_attributes.delete(:uk_school_name)

            [:uk_school_id].each do |id_field|
              school_attributes[id_field] = school_attributes[id_field].to_i if school_attributes[id_field].present?
            end

            school_attributes[:country_id] = 1
            school_attributes[:actual_country_id] = 1
            school_attributes[:new_library_curriculum_id] = 18

            if school_attributes[:uk_school_id].present? && school_attributes[:uk_school_id] > 0
              uk_school = UkSchool.find_by(id: school_attributes[:uk_school_id])
              if uk_school && uk_school.school.nil?
                school = School.new(school_attributes.except(:uk_school_id))
                school.uk_school = uk_school
              else
                @import_errors << { row_number: original_csv_row_number, message: "UK School ID #{school_attributes[:uk_school_id]} is invalid, already associated, or school data mismatch." }
                next
              end
            else
              school = School.new(school_attributes.except(:uk_school_id))
            end

            if school.save
              if teacher_name.present? && teacher_email.present? && teacher_password.present?
                if User.exists?(email: teacher_email)
                  @import_errors << { row_number: original_csv_row_number, school_name: school.name, message: "School created, but teacher email '#{teacher_email}' already exists (possibly from this import batch). Teacher not created." }
                else

                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  puts teacher_password
                  teacher = User.new(
                    name: teacher_name,
                    email: teacher_email,
                    password: teacher_password,
                    school_id: school.id,
                    is_school_admin: true,
                    type: 'Teacher',
                    science_lead: true
                  )
                  unless teacher.save
                    teacher_error_messages = teacher.errors.full_messages.join(', ')
                    @import_errors << { row_number: original_csv_row_number, school_name: school.name, message: "School created, but failed to create teacher: #{teacher_error_messages}" }
                  else
                    school.science_leaders << teacher
                  end
                end
              elsif teacher_name.present? || teacher_email.present? || teacher_password.present?
                @import_errors << { row_number: original_csv_row_number, school_name: school.name, message: "School created, but primary teacher details were incomplete. Teacher not created." }
              end
              @imported_schools_summary << { name: school.name, id: school.id, teacher_email: teacher&.email }
            else
              school_error_messages = school.errors.full_messages.join(', ')
              @import_errors << { row_number: original_csv_row_number, message: "Failed to import school: #{school_error_messages}" }
            end

          rescue ActiveRecord::RecordNotUnique => e
            @import_errors << { row_number: original_csv_row_number, message: "Failed to import due to a uniqueness constraint: #{e.message}. This might be a duplicate record or a race condition if importing identical data simultaneously." }
          rescue StandardError => e
            @import_errors << { row_number: original_csv_row_number, message: "An unexpected error occurred: #{e.message}" }
          end
        end

        if @import_errors.any? && @imported_schools_summary.empty?
          flash.now[:error] = "Import process failed for all records. No schools were imported. Please review errors below."
        elsif @import_errors.any?
          flash.now[:error] = "Import completed with some errors. See summary below."
        else
          flash[:success] = "Schools imported successfully!"
        end
      end

      begin
        File.delete(processed_data_file) if File.exist?(processed_data_file)
      rescue => e
        Rails.logger.error("Failed to delete temp file: #{e.message}")
      end
      
      session.delete(:processed_rows_file_path)
      session.delete(:processed_rows_count)
      session.delete(:csv_file_path)
      session.delete(:csv_headers)
      render :summary
    end

    def summary
      if @imported_schools_summary.blank? && @import_errors.blank?
        unless flash[:success] || flash.now[:error] || flash[:error]
          flash[:notice] = "No import summary to display. Please start a new import."
          redirect_to new_admin_import_school_path
          return
        end
      end
    end

    def download_template
      csv_header = @expected_columns.map { |col| col[:name] }
      csv_string = CSV.generate do |csv|
        csv << csv_header
      end
      send_data csv_string, filename: "school_import_template.csv", type: 'text/csv'
    end

    private

    def set_expected_columns
      @expected_columns = [
        { name: 'UK School Name', field: :uk_school_name, default: nil, required: false, 
          description: 'Name of an existing UK school to link with' },
        { name: 'School Name', field: :name, default: nil, required: true,
          description: 'Full name of the school' },
        { name: 'Postcode', field: :postcode, default: nil, required: true,
          description: 'Postal code of the school' },
        { name: 'Telephone', field: :telephone, default: nil, required: true,
          description: 'Contact phone number' },
        { name: 'School Category', field: :category, default: nil, required: true,
          description: 'Type of school (e.g., Primary School, Secondary School)' },
        { name: 'School Region', field: :region, default: nil, required: false,
          description: 'Name of the region/location' },
        { name: 'Lead Source', field: :lead_source, default: nil, required: false,
          description: 'How the school found the platform' },
        { name: 'Term Length', field: :term_length, default: nil, required: false,
          description: 'Length of the subscription term' },
        { name: 'Primary Teacher Name', field: :teacher_name, default: nil, required: true,
          description: 'Full name of the primary teacher' },
        { name: 'Primary Teacher Email', field: :teacher_email, default: nil, required: true,
          description: 'Email address of the primary teacher' },
        { name: 'Primary Teacher Password', field: :teacher_password, default: nil, required: true,
          description: 'Password for the primary teacher account' }
      ]
    end
  end
end