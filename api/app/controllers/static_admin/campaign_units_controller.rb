# app/controllers/static_admin/campaign_units_controller.rb
module <PERSON>aticAdmin
  class CampaignUnitsController < StaticAdminController
    before_action :set_campaign
    before_action :set_campaign_unit, only: %i[update destroy]

    # POST /admin/campaigns/:campaign_id/campaign_units
    def create
      @campaign_unit = CampaignUnit.new(campaign_unit_params)
      @campaign_unit.campaign = @campaign
      @campaign_unit.videos2_id = handle_video_for(
        @campaign_unit,
        params['campaign_unit']['source'],
        params['campaign_unit']['external_id']
      )

      if @campaign_unit.save
        ensure_campaign_lessons(@campaign_unit)
        redirect_to units_admin_campaign_path(@campaign), notice: 'Campaign unit was successfully created.'
      else
        # Show error message
        error_message = @campaign_unit.errors.full_messages.join(', ')
        redirect_to units_admin_campaign_path(@campaign), alert: error_message
      end
    rescue ActionController::ParameterMissing
      logger.error "Parameter missing in CampaignUnit creation: #{params.inspect}"
      redirect_to units_admin_campaign_path(@campaign),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # PUT/PATCH /admin/campaigns/:campaign_id/campaign_units/:id
    def update
      data = campaign_unit_params
      data[:videos2_id] = handle_video_for(
        @campaign_unit,
        params['campaign_unit']['source'],
        params['campaign_unit']['external_id']
      )
      if @campaign_unit.update(data)
        ensure_campaign_lessons(@campaign_unit)
        redirect_to units_admin_campaign_path(@campaign), notice: 'Campaign unit was successfully updated.'
      else
        error_message = @campaign_unit.errors.full_messages.join(', ')
        redirect_to units_admin_campaign_path(@campaign), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to units_admin_campaign_path(@campaign),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # DELETE /admin/campaigns/:campaign_id/campaign_units/:id
    def destroy
      @campaign_unit.destroy
      redirect_to units_admin_campaign_path(@campaign), notice: 'Campaign unit was successfully deleted.'
    end

    private

    def ensure_campaign_lessons(unit)
      unit.new_library_units.each do |nlu|
        nlu.lesson_templates.each do |template|
          unit.campaign_lessons.find_or_create_by(
            campaign_unit_id: unit.id,
            lesson_template_id: template.id,
            new_library_unit_id: nlu.id,
            campaign_id: unit.campaign_id
          )
        end
      end
      # remove the campaign lessons that are no longer associated with the unit via its new_library_units
      unit.campaign_lessons.where.not(new_library_unit_id: unit.new_library_units.ids).destroy_all
    end

    def set_campaign
      @campaign = Campaign.find(params[:campaign_id])
    end

    def set_campaign_unit
      @campaign_unit = @campaign.campaign_units.find(params[:id])
    end

    def campaign_unit_params
      data = params.require(:campaign_unit).permit(
        :body,
        :tour_id,
        :external_url,
        :show_on_unit_page,
        new_library_unit_ids: []
      )
      data[:new_library_unit_ids] = data[:new_library_unit_ids].reject(&:blank?) if data[:new_library_unit_ids].is_a?(Array)
      data
    end

    def handle_video_for(model, video_source, external_video_id)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)
      video.name = model.name if video.new_record? || video.name.blank?
      video.save if video.changed?

      video.id
    end
  end
end
