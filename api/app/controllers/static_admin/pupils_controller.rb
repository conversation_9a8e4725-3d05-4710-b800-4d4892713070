module StaticAdmin
  class StaticAdmin::PupilsController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_pupil, only: [:show, :destroy]
    before_action :set_school, only: [:new, :create, :destroy]

    # GET /admin/pupils
    def index
      @school_id = params[:static_school_id]
      @pupils = @school_id == 'all' ? Pupil.joins(:school) : School.find(@school_id).pupils
      @pupils = @pupils.includes(enrollments: :form)

      if params[:name].present?
        @pupils = @pupils.where("users.name ILIKE :query", query: "%#{params[:name]}%")
      end

      @pupils = safe_paginate(@pupils.order("created_at desc"), page: params[:page], per_page: 20)

      render :layout => false
    end

    # GET /admin/school/:school_id/pupils/new
    def new
      @pupil = Pupil.new
    end

    # POST /admin/school/:school_id/pupils
    def create
      @pupil = Pupil.new(pupil_params)

      @pupil.school = @school
      @pupil.uid = @pupil.email.downcase

      if @pupil.save
        redirect_to pupils_admin_static_school_path(@school), notice: 'Pupil was successfully created.'
      else
        render :new
      end
    end

    # DELETE /admin/pupils/:id
    def destroy
      @pupil.destroy
      redirect_to pupils_admin_static_school_path(@school), notice: 'Pupil was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_pupil
      @pupil = Pupil.find(params[:id])
    end

    def set_school
      @school = School.find(params[:static_school_id])
    end

    # Only allow a list of trusted parameters through.
    def pupil_params
      params.require(:pupil).permit(:name, :identifier, :email, :gender, :ethnicity, :location)
    end

  end
end