module StaticAdmin
  class StaticAdmin::TeamMembersController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_team_member, only: [:show, :edit, :update, :destroy]

    admin_section :website

    # GET /admin/team_members
    def index
      sortable_columns = ['name', 'role', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'name'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'asc'

      @team_members = TeamMember.all
      if params[:query].present?
        @team_members = @team_members.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      if params[:scope].present?
        @team_members = @team_members.where(role: params[:scope])
      end

      @team_members = safe_paginate(@team_members.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/team_members/new
    def new
      @team_member = TeamMember.new
    end

    # POST /admin/team_members
    def create
      @team_member = TeamMember.new(team_member_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @team_member.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @team_member.save
        redirect_to [:edit, :admin, @team_member], notice: 'Team member was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/team_members/:id/edit
    def edit; end

    # PATCH/PUT /admin/team_members/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @team_member.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @team_member.update(team_member_params)
        redirect_to [:edit, :admin, @team_member], notice: 'Team member was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/team_members/:id
    def destroy
      @team_member.destroy
      
      redirect_to admin_team_members_url, notice: 'Team member was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_team_member
      @team_member = TeamMember.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def team_member_params
      params.require(:team_member).permit(:name, :title, :role, :body, :fileboy_image_id)
    end

  end
end
