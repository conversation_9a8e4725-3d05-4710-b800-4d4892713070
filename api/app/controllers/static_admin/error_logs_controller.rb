module StaticAdmin
  class StaticAdmin::ErrorLogsController < StaticAdminController
    include PaginationHelper

    admin_section :misc

    # GET /admin/error_logs
    def index
      sortable_columns = ['created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @error_logs = ErrorLog.all

      if params[:query].present?
        @error_logs = @error_logs.where('error ILIKE ?', "%#{params[:query]}%")
      end

      @error_logs = safe_paginate(@error_logs.order("#{sort} #{order}"), page: params[:page])
    end

    def delete_related
      error = ErrorLog.find(params[:id])
      deleted = error.related_errors.destroy_all
      redirect_to admin_error_logs_url, notice: "#{deleted.count} Error logs were successfully deleted."
    end

    # GET /admin/error_logs/:id/delete
    def delete
      @error_log = ErrorLog.find(params[:id])
      @error_log.destroy
      redirect_to admin_error_logs_url, notice: 'Error log was successfully deleted.'
    end

  end
end
