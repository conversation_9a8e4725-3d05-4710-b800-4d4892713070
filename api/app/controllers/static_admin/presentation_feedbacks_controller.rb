module StaticAdmin
  class PresentationFeedbacksController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_feedback, only: %i[show edit update destroy new_comment]

    admin_section :schools

    # GET /admin/feedback
    def index
      sortable_columns = %w[associate_to created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @feedback = NewPresentationFeedback.all.includes([:user])

      @feedback = safe_paginate(@feedback.order("#{sort} #{order}"), page: params[:page])
    end

    def new_comment
      lesson_template = Lesson::Template.find(params[:lesson_template_id])
      comment = comment_params[:admin_comment]

      fallback_path = feedback_admin_lesson_template_path(lesson_template)
      
      if @feedback.update(admin_comment: comment)
        user = @feedback.user
        if user&.teacher? && user.allows_notification?(:lesson_feedback)
          SchoolMailer.lesson_feedback_received_comment(user, @feedback, comment).deliver_now
        end
        
        redirect_back fallback_location: fallback_path, notice: 'Comment was successfully created.'
      else
        # Show error message
        error_message = @feedback.errors.full_messages.join(', ')
        redirect_back fallback_location: fallback_path, alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_back fallback_location: fallback_path,
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_feedback
      @feedback = NewPresentationFeedback.find(params[:id])
    end

    def comment_params
      params.permit(:admin_comment)
    end
  end

end
