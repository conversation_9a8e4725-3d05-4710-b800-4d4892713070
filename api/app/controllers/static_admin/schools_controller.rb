module StaticAdmin
  class StaticAdmin::SchoolsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_school, only: %i[show edit update destroy update_subscription send_welcome_emails send_teacher_welcome_emails schools_for_merge merge_with_school manage_subscriber stripe_subscription legacy_subscription details hubspot teachers pupils classes actions delete_subscriber school_invites]
    before_action :get_data_for_selects, only: %i[new edit create update update_subscription manage_subscriber]
    before_action :prevent_in_dev, only: %i[send_welcome_emails send_teacher_welcome_emails]

    admin_section :schools

    # GET /admin/schools
    def index
      blacklist = %w[US]
      countries = Country.joins(:available_templates)
                         .where.not(machine_name: blacklist)
                         .group(:id)
                         .order("countries.machine_name = 'GB' desc", name: :asc)
                         .pluck(:name, :id)

      @countries = countries
      @local_authorities = School.local_authorities

      sortable_columns = ['id', 'name', 'created_at', 'hubspot_id', 'postcode', 'hubspot_subscription_status', 'renewal_month', 'created_at', 'uk_schools.urn', 'cache_activity_30_days', 'cache_activity_60_days']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @schools = School.includes([:uk_school, :science_leaders]).left_joins(:uk_school, :teachers, :science_leaders, :organisation)

      if params[:query].present?
        query_list = params[:query].split(',').map(&:strip).map { |name| "%#{name}%" }
        @schools = @schools.where('(schools.name ILIKE ANY (ARRAY[:query_list]) OR uk_schools.name ILIKE ANY (ARRAY[:query_list])) OR (schools.postcode ILIKE ANY (ARRAY[:query_list]) OR uk_schools.postcode ILIKE ANY (ARRAY[:query_list]))', query_list: query_list)
      end

      if params[:name].present?
        name_list = params[:name].split(',').map(&:strip).map { |name| "%#{name}%" }
        @schools = @schools.where('(schools.name ILIKE ANY (ARRAY[?]) OR uk_schools.name ILIKE ANY (ARRAY[?]))', name_list, name_list)
      end

      if params[:postcode].present?
        postcode_list = params[:postcode].split(',').map(&:strip).map { |postcode| "%#{postcode}%" }
        @schools = @schools.where('(schools.postcode ILIKE ANY (ARRAY[?]) OR uk_schools.postcode ILIKE ANY (ARRAY[?]))', postcode_list, postcode_list)
      end

      @schools = @schools.where(hubspot_id: params[:hubspot_id]) if params[:hubspot_id].present?
      @schools = @schools.where(hubspot_subscription_status: params[:hubspot_subscription_status]) if params[:hubspot_subscription_status].present?
      @schools = @schools.where(category: params[:type]) if params[:type].present?
      @schools = @schools.where(country_id: params[:country]) if params[:country].present?
      @schools = @schools.where('uk_schools.local_authority = ?', params[:auth]) if params[:auth].present?
      @schools = @schools.where('organisations.use_white_labelling IS TRUE') if params[:label] == 'true'

      if params[:created_at].present?
        date = DateTime.parse(params[:created_at])
        @schools = @schools.where('DATE(schools.created_at) = ?', date)
      end

      @schools = @schools.where('schools.renewal_month >= ?', params[:start]) if params[:start].present?
      @schools = @schools.where('schools.renewal_month <= ?', params[:end]) if params[:end].present?
      @schools = @schools.group('schools.id')

      # If we are sorting by a table that isn't schools, the table name is already included
      # otherwise, append schools to prevent ambiguity
      sort_table = sort.start_with?('uk_schools.') ? '' : 'schools.'
      # If we are not sorting by schools, we need to group by the column to keep sql happy.
      @schools = @schools.group('schools.id', sort) if sort_table != 'schools.'
      @schools = @schools.order("#{sort_table}#{sort} #{order}")

      @schools = safe_paginate(@schools, page: params[:page], per_page: 25)
    end

    def schools_for_merge
      if params[:query].present?
        @schools = School.left_joins(:uk_school).where.not(id: @school.id)
        query_list = params[:query].split(',').map(&:strip).map { |name| "%#{name}%" }
        @schools = @schools.where('(schools.name ILIKE ANY (ARRAY[:query_list]) OR uk_schools.name ILIKE ANY (ARRAY[:query_list])) OR (schools.postcode ILIKE ANY (ARRAY[:query_list]) OR uk_schools.postcode ILIKE ANY (ARRAY[:query_list]))', query_list: query_list)
        @schools = @schools.group('schools.id')
        @schools = @schools.order("schools.created_at desc")
        @schools = @schools.paginate(page: params[:page], per_page: 25)
      else
        @schools = []
      end

      render partial: 'schools_for_merge'
    end

    # GET /admin/schools/new
    def new
      @curricula = NewLibrary::Curriculum.all.order('name asc')
      @school = School.new
    end

    # POST /admin/schools
    def create
      data = school_params
      data[:lead_source] = params[:lead_source_other] if params[:lead_source] == 'other' && params[:lead_source_other].present?
      data[:fileboy_image_id] = upload_image(params[:fileboy_image]) if params[:fileboy_image]

      @school = School.new(data)
      ActiveRecord::Base.transaction do
        @school.created_by = current_user
        @school.created_by_email = current_user&.email
        @school.save!
        @teacher = create_teacher_for_school(@school) if teacher_params[:teacher_email].present?

        redirect_to edit_admin_static_school_path(@school), notice: 'School was successfully created.'
      end
    rescue ActiveRecord::RecordInvalid => e
      flash.now[:alert] = e.record.errors.full_messages.join(', ')
      render :new, status: :unprocessable_entity
    rescue => e
      flash.now[:alert] = e.message
      render :new, status: :unprocessable_entity
    end

    # GET /admin/schools/:id/edit
    def edit; end

    def stripe_subscription
      load_subscription
    end

    def details; end
    
    def legacy_subscription; end

    def hubspot; end

    def teachers; end

    def pupils; end

    def classes; end

    def actions
      @school_admins = @school.teachers.where(is_school_admin: true).map do |admin|
        ["#{admin.name} (#{admin.email})", admin.id]
      end
    end
   
    def school_invites; end

    # PATCH/PUT /admin/schools/:id
    def update
      return_page = params[:return_page].presence&.to_sym || :edit

      data = school_params
      data[:lead_source] = params[:lead_source_other] if params[:lead_source] == 'other' && params[:lead_source_other].present?
      data[:fileboy_image_id] = upload_image(params[:fileboy_image]) if params[:fileboy_image]

      if @school.update(data)
        redirect_to redirect_path_for(return_page), notice: 'School was successfully updated.'
      else
        render return_page
      end
    end

    # PUT /admin/schools/:id/subscription
    def update_subscription
      return_page = params[:return_page].presence&.to_sym || :edit

      if @school.update(subscription_params)
        redirect_to redirect_path_for(return_page), notice: 'Subscription updated.'
      else
        render return_page
      end
    end

    # DELETE /admin/schools/:id
    def destroy
      @school.destroy
      redirect_to admin_static_schools_url, notice: 'School was successfully deleted.'
    end

    # POST /admin/schools/:id/generate_sample_class
    def generate_sample_class
      school_id = params[:id]

      units = NewLibrary::Year.find(76).units
      sample_form = Form.find_or_create_by!(name: 'Sample', school_id: school_id, lesson_weekdays: {})

      all_teacher_ids = School.joins(:users).find(school_id).teacher_ids
      sample_form.teacher_ids = all_teacher_ids

      form_unit_ids = []
      template_ids = []

      # assign units to form
      FormUnit.where(form_id: sample_form.id).delete_all
      units.each_with_index do |unit, i|
        form_unit = FormUnit.new(form_id: sample_form.id, new_library_unit_id: unit.id)
        form_date = (Date.tomorrow + i * 2.days)
        form_unit.start_date = form_date.beginning_of_day
        form_unit.end_date = (form_date + 1.day).end_of_day
        form_unit.save

        form_unit_ids.push(form_unit.id)
        template_ids.push(unit.lesson_template_ids.sample(2))
      end

      sample_form.form_unit_ids = form_unit_ids
      sample_form.template_ids = []

      pupils = []
      pupil_numbers = Array.new(15) { |i| i + 1 }

      pupil_numbers.each do |i|
        pupil = Pupil.find_or_create_by!(name: "Sample Pupil #{i}", school_id: school_id)
        pupils << pupil
      end

      puts "Pupils: #{pupils.map(&:id)}"

      # create presentation views, rocket word/assessment quiz marks for sample pupil
      pupils.each do |pupil|
        template_ids.flatten.each do |template_id|
          lesson = Lesson::Template.joins(:slides).find(template_id)

          PresentationProgress.set_slide(pupil.id, template_id, lesson.slide_ids.last, true) if rand(4) != 0

          rocket_mark = QuizOld::Attempt.find_or_initialize_by(user_id: pupil.id, lesson_template_id: template_id)
          rocket_mark.score = rand(6)
          rocket_mark.save if rocket_mark.changed?

          assessment_mark = QuipQuizResult.find_or_initialize_by(pupil_id: pupil.id, lesson_template_id: template_id, total: 5.to_d)
          assessment_mark.score = rand(6).to_d
          assessment_mark.results_json = { '0' => true }
          assessment_mark.save if assessment_mark.changed?
        end

        pupil.save if pupil.changed?
      end

      sample_form.pupil_ids = pupils.map(&:id)
      sample_form.add_lesson_template_ids(template_ids.flatten)
      sample_form.save if sample_form.changed?

      redirect_to classes_admin_static_school_path(id: school_id)
    end

    def sync_hubspot
      school = School.find(params[:id])

      begin
        result = school.sync_hubspot_single_school

        render json: result
      rescue => e
        Rails.logger.error e.message
        render json: { success: false, message: 'Hubspot sync failed' }
      end
    end

    def send_welcome_emails
      SendWelcomeEmailsJob.perform_later(@school.teacher_ids)

      @school.complete_checklist_item(:send_welcome_email)
      @school.save
      head :ok
    end

    def send_teacher_welcome_emails
      SendWelcomeEmailsJob.perform_later(params[:ids])
      head :ok
    end

    def admin_wipe_data
      school = School.find(params[:id])
      authorize! :manage, school
      throw 'Permission denied' unless current_user.admin?
      # TODO
      # Add a clear data button for after a school is no longer subscribed. Wipes all data from the account, apart from the admin teacher
      time_took = school.purge_content
      puts "#{school.id}: Purged content in #{time_took} seconds"
      head :ok
    end

    def merge_with_school
      school = School.find(params[:id])
      other_school = School.find(params[:other_school_id])
      authorize! :manage, school
      authorize! :manage, other_school
      school.merge_with_school(other_school.id)
      head :ok
    end

    def manage_subscriber
      subscriber = @school.get_or_create_subscriber
      if params[:subscriber].nil? || subscriber.update(subscriber_params)
        redirect_to stripe_subscription_admin_static_school_path(@school), notice: 'Subscriber saved.'
      else
        load_subscription
        render :stripe_subscription
      end
    end

    def delete_subscriber
      redirect_to stripe_subscription_admin_static_school_path(@school), alert: 'This school does not have a subscriber' unless @school.subscriber.present?
      redirect_to stripe_subscription_admin_static_school_path(@school), alert: 'Subscriber has a Stripe subscription ID' if @school.subscriber.stripe_subscription_id.present?

      if @school.subscriber.destroy
        redirect_to stripe_subscription_admin_static_school_path(@school), notice: 'Subscriber removed'
      else
        Rails.logger.error(@school.subscriber.errors.full_messages)
        redirect_to stripe_subscription_admin_static_school_path(@school), alert: 'Error removing subscriber'
      end
    end

    private

    def load_subscription
      @subscribable = @school
      if @school.subscriber.present?
        service = SubscriptionService.new(@subscribable)
        @subscription = service.get_subscription_details
      end
    end

    def create_teacher_for_school(school)
      t_params = teacher_params
      @teacher = Teacher.new(
        email: t_params[:teacher_email],
        name: t_params[:teacher_name],
        password: t_params[:teacher_password],
        is_school_admin: true,
        science_lead: true,
        school: school
      )
      @teacher.save!
      @teacher
    end

    def teacher_params
      params.require(:school).permit(:teacher_email, :teacher_name, :teacher_password)
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_school
      @school = School.find(params[:id])
    end

    def get_data_for_selects
      blacklist = %w[US]

      countries = Country.all.joins(:available_templates).group(:id).order("countries.machine_name = 'GB' desc", name: :asc)
      countries = countries.where.not('countries.machine_name IN (?)', blacklist)

      @countries = countries.pluck(:name, :id)
      @all_countries = Country.all.order("countries.machine_name = 'GB' desc", name: :asc).pluck(:name, :id)
      @all_schools = School.all.order(name: :asc).pluck(:name, :id)
      @white_label_orgs = Organisation.where(use_white_labelling: true).order(name: :asc).pluck(:name, :id)
      @sign_up_events = SignUpEvent.all.order(name: :asc).pluck(:name, :id)
      @sponsors = Sponsor.all.order(name: :asc).pluck(:name, :id)
      @curricula = NewLibrary::Curriculum.all.order('name asc')
    end

    # Only allow a list of trusted parameters through.
    def school_params
      params.require(:school).permit(
        :approaching_renewal,
        :year_start,
        :trial_end_date,
        :hubspot_renewal_date,
        :new_library_curriculum_id,
        :fileboy_image,
        :fileboy_image_id,
        :name,
        :country_id,
        :actual_country_id,
        :uk_region,
        :phase_of_education,
        :sponsor_ids,
        :postcode,
        :telephone,
        :category,
        :lead_source,
        :sign_up_event_id,
        :term_length,
        :uk_school_id,
        :organisation_id,
        :wonde_id,
        :wonde_request_status,
        :sync_wonde_weekly,
        :show_lesson_plan_pupil_area,
        :approved_domains,
        :memo,
        :finance_email,
        :finance_name,
        :po_number,
        :subscription_externally_covered,
        :hubspot_id,
        :hubspot_deal_id,
        :default_billing_address_line_1,
        :default_billing_address_line_2,
        :default_billing_city,
        :default_billing_postal_code,
        :default_billing_state,
        :default_billing_country_id,
        science_leader_ids: [],
        sponsor_ids: []
      )
    end

    def subscriber_params
      params.require(:subscriber).permit(
        free_subscription: [
          :ai, :ai_body, :ai_expires_at,
          :science, :science_body, :science_expires_at, 
          :geography, :geography_body, :geography_expires_at,
          :ai_trial_start, :science_trial_start, :geography_trial_start,
          :bypass_ai, :bypass_science, :bypass_geography,
        ]
      )
    end

    # Only allow a list of trusted parameters through.
    def subscription_params
      params.require(:school).permit(
        :approaching_renewal,
        :hubspot_subscription_status,
        :trial_start_date,
        :trial_end_date,
        :hubspot_renewal_date,
        :finance_email,
        :finance_name,
        :po_number,
        :subscription_externally_covered,
        :hubspot_ai_subscription_status
      )
    end

    def redirect_path_for(page)
      route_helpers = {
        edit: :edit_admin_static_school_path,
        stripe_subscription: :stripe_subscription_admin_static_school_path,
        legacy_subscription: :legacy_subscription_admin_static_school_path,
        wonde: :wonde_sync_admin_static_school_path,
        hubspot: :hubspot_admin_static_school_path
      }

      helper = route_helpers.fetch(page, :edit_admin_static_school_path)
      send(helper, @school)
    end
  end
end
