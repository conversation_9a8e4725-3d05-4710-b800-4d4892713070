module StaticAdmin
  class StaticAdmin::CurriculumDocumentsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_document, only: [:edit, :update, :destroy]

    admin_section :curriculum

    # GET /admin/curriculum-documents
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'

      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @documents = CurriculumDocument.all
      if params[:group].present?
        @documents = CurriculumDocumentGroup.find(params[:group]).curriculum_documents
      end
      if params[:query].present?
        @documents = @documents.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @groups = CurriculumDocumentGroup.pluck(:id, :name)

      @documents = safe_paginate(@documents.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/curriculum-documents/new
    def new
      @document = CurriculumDocument.new
    end

    # POST /admin/curriculum-documents
    def create
      @document = CurriculumDocument.new(document_params)

      fileboy_id = upload_image params[:fileboy] if params[:fileboy].present?
      @document.fileboy_id = fileboy_id if fileboy_id

      if @document.save
        redirect_to edit_admin_curriculum_document_path(@document), notice: 'Curriculum document was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/curriculum-documents/:id/edit
    def edit; end

    # PATCH/PUT /admin/curriculum-documents/:id
    def update
      fileboy_id = upload_image params[:fileboy] if params[:fileboy].present?
      @document.fileboy_id = fileboy_id if fileboy_id

      if @document.update(document_params)
        redirect_to edit_admin_curriculum_document_path(@document), notice: 'Curriculum document was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/curriculum-documents/:id
    def destroy
      @document.destroy
      redirect_to admin_curriculum_documents_url, notice: 'Curriculum document was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_document
      @document = CurriculumDocument.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def document_params
      params.require(:curriculum_document).permit(:name, :fileboy_id, :active, :available_to_trial, curriculum_document_group_ids: [])
    end

  end
end
