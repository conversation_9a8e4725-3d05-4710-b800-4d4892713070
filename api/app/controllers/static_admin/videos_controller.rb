# frozen_string_literal: true

module StaticAdmin
  class VideosController < StaticAdminController
    include PaginationHelper
    before_action :set_video, only: %i[show edit update destroy reanalyze fetch_video_data copy_keywords_to_tags]
    before_action :set_campaigns, only: %i[new create edit update]

    admin_section :content

    # GET /admin/videos
    def index
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @videos = Video.all
      @videos = @videos.search(params[:query].to_s) if params[:query].present?

      if params[:tags].present?
        tags = params[:tags].split(',')
        @videos = @videos.joins(:tags).where(tags: { name: tags }).distinct
      end

      case params[:scope]
      when 'sponsored'
        @videos = @videos.where(is_sponsored: true)
      when 'assignment'
        @videos = @videos.where(is_assignment: true)
      when 'expert'
        @videos = @videos.where(is_expert: true)
      when 'career'
        @videos = @videos.where(is_career: true)
      when 'ai'
        @videos = @videos.where(is_available_in_ai_search: true)
      when 'analyzed'
        @videos = @videos.where.not(analyzed_at: nil)
      end

      @videos = safe_paginate(@videos.order("#{sort} #{order}"), page: params[:page])
    end

    def search
      query = params[:query]

      if query.present? && query.length >= 2
        videos = Video.where('name ILIKE ? OR external_id ILIKE ?', "%#{query}%", "#{query}%").limit(50)

        # Transform into the standardized format for nice-select
        formatted = videos.map do |video|
          {
            value: video.id,
            label: video.name,
            sub: "#{video.source.titleize}: #{video.external_id}",
          }
        end

        render json: formatted, status: :ok
      else
        render json: { error: 'Query must be at least 2 characters long' }, status: :unprocessable_entity
      end
    end

    # GET /admin/videos/new
    def new
      @video = Video.new
    end

    # POST /admin/videos
    def create
      @video = Video.new(video_params)
      if @video.save
        redirect_to [:edit, :admin, @video], notice: 'Video was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/videos/:id/edit
    def edit; end

    # PATCH/PUT /admin/videos/:id
    def update
      if @video.update(video_params)
        redirect_to [:edit, :admin, @video], notice: 'Video was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/videos/:id
    def destroy
      @video.destroy
      redirect_to admin_videos_url, notice: 'Video was successfully deleted.'
    end

    # GET /admin/tours/build_sheet
    def build_sheet
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      videos = Video.includes(:campaign, :tags).all
      videos = videos.search(params[:query].to_s) if params[:query].present?

      if params[:tags].present?
        tags = params[:tags].split(',')
        videos = videos.joins(:tags).where(tags: { name: tags }).distinct
      end

      case params[:scope]
      when 'sponsored'
        videos = videos.where(is_sponsored: true)
      when 'assignment'
        videos = videos.where(is_assignment: true)
      when 'expert'
        videos = videos.where(is_expert: true)
      when 'career'
        videos = videos.where(is_career: true)
      when 'ai'
        videos = videos.where(is_available_in_ai_search: true)
      end

      videos = videos.order("#{sort} #{order}")

      Axlsx::Package.new do |p|
        p.workbook.add_worksheet(name: 'Videos') do |sheet|
          bold = sheet.styles.add_style b: true
          top_row = ['Name', 'Source', 'Campaign', 'Tags', 'Sponsored', 'Assignment?', 'Expert?', 'Career?', 'Availiable in AI builder?', 'Created']
          sheet.add_row top_row, style: Array.new(top_row.count, bold)

          videos.each do |video|
            sheet.add_row [
              video.name,
              "#{video.source&.titleize}: #{video.external_id}",
              video.campaign&.name,
              video.tags.map(&:name).join(', '),
              video.is_sponsored ? 'Yes' : '',
              video.is_assignment ? 'Yes' : '',
              video.is_expert ? 'Yes' : '',
              video.is_career ? 'Yes' : '',
              video.is_available_in_ai_search ? 'Yes' : '',
              video.created_at.strftime('%d %b %Y')
            ]
          end
        end

        timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
        tempfile_path = "#{Rails.root}/tmp/videos_#{timestamp}.xlsx"

        p.serialize(tempfile_path)
        send_file(tempfile_path, filename: "videos_#{timestamp}.xlsx", type: 'application/vnd.ms-excel')
      end
    end

    def reanalyze
      @video.analyze_with_ai!
      redirect_back fallback_location: admin_videos_path, notice: 'Video queued for re-analysis.'
    end

    def fetch_video_data
      @video.fetch_video_data
      redirect_back fallback_location: admin_videos_path, notice: 'Video data fetched successfully.'
    end

    # PATCH /admin/videos/:id/copy_keywords_to_tags
    def copy_keywords_to_tags
      if @video.keywords.blank?
        return respond_to do |format|
          format.html { redirect_back(fallback_location: admin_videos_path, alert: 'No keywords found to copy.') }
          format.json { render json: { success: false, message: 'No keywords found to copy.' } }
        end
      end

      keywords = @video.keywords.split(',').map(&:strip).reject(&:blank?)
      if keywords.empty?
        return respond_to do |format|
          format.html { redirect_back(fallback_location: admin_videos_path, alert: 'No valid keywords found.') }
          format.json { render json: { success: false, message: 'No valid keywords found.' } }
        end
      end

      # Get existing tag names (case insensitive)
      existing_tag_names = @video.tags.pluck(:name).map(&:downcase)
      
      # Filter out keywords that are already tags
      new_keywords = keywords.reject { |keyword| existing_tag_names.include?(keyword.downcase) }
      
      if new_keywords.empty?
        message = 'All keywords are already selected as tags.'
        return respond_to do |format|
          format.html { redirect_back(fallback_location: admin_videos_path, notice: message) }
          format.json { render json: { success: true, message: message } }
        end
      end

      # Find or create tags for new keywords
      new_tags = new_keywords.map do |keyword|
        Tag.find_or_create_by(name: keyword.strip)
      end

      # Add new tags to the video
      @video.tags << new_tags
      
      success_message = "Added #{new_tags.count} new tags: #{new_tags.map(&:name).join(', ')}"
      
      respond_to do |format|
        format.html { redirect_back(fallback_location: admin_videos_path, notice: success_message) }
        format.json { render json: { success: true, message: success_message } }
      end
    rescue StandardError => e
      error_message = "Error copying keywords: #{e.message}"
      respond_to do |format|
        format.html { redirect_back(fallback_location: admin_videos_path, alert: error_message) }
        format.json { render json: { success: false, message: error_message } }
      end
    end


    private

    # Use callbacks to share common setup or constraints between actions.
    def set_video
      @video = Video.find(params[:id])
    end

    def set_campaigns
      @campaigns = Campaign.all
    end

    # Only allow a list of trusted parameters through.
    def video_params
      params.require(:video).permit(
        :name,
        :subjects,
        :url,
        :source,
        :external_id,
        :campaign_id,
        :is_sponsored,
        :is_assignment,
        :is_expert,
        :is_career,
        :is_available_in_ai_search,
        :appears_in_search,
        tag_ids: []
      ).tap do |video|
        # Create new tags if they don't exist
        video[:tag_ids].reject!(&:blank?)
        video[:tag_ids].each_with_index do |tag, i|
          if Tag.where(id: tag).empty?
            new_tag = Tag.create(name: tag)
            video[:tag_ids][i] = new_tag.id if new_tag.persisted?
          end
        end
      end
    end
  end
end
