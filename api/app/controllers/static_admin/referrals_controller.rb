module StaticAdmin
  class ReferralsController < StaticAdminController
    include PaginationHelper

    admin_section :misc

    def index
      collection = User.joins(:referred_from_user, :school).where.not(referred_from_user_id: nil)

      if params[:from_date].present?
        collection = collection.where(
          'users.created_at >= ?', DateTime.parse(params[:from_date]).beginning_of_day
        )
      end

      if params[:to_date].present?
        collection = collection.where(
          'users.created_at <= ?', DateTime.parse(params[:to_date]).end_of_day
        )
      end

      collection = collection.where(schools: { hubspot_subscription_status: params[:status] }) if params[:status].present?

      if params[:scope] == 'incomplete'
        collection = collection.where(referral_actioned: false)
      elsif params[:scope] == 'complete'
        collection = collection.where(referral_actioned: true)
      end

      if params[:query].present?
        collection = collection
                     .joins('LEFT JOIN users ref_users ON users.referred_from_user_id = ref_users.id')
                     .where('users.name ILIKE :q OR users.email ILIKE :q OR ref_users.referral_code ILIKE :q', { q: "%#{params[:query]}%" })
      end

      sortable_columns = %w[created_at name email]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @referrals = safe_paginate(collection.order("users.#{sort} #{order}"), page: params[:page])
    end

    def toggle_actioned
      user = User.find(params[:id])
      authorize! :manage, user
      user.update!(referral_actioned: !user.referral_actioned)
      # keep the relevant query params so we can redirect back to the same page
      redirect_to admin_referrals_path(params.permit(:sort, :order, :page, :query, :to_date, :from_date, :status, :scope).to_h)
    end

    def referrals_for
      user = User.find(params[:id])
      authorize! :read, user
      collection = user.referred_users.select(:id, :name, :referral_share_name, :type, :deleted, :created_at)

      meta, records = paginate(collection)
      render json: { records: records, meta: meta }
    end

    def create
      authorize! :manage, User
      @errors = {}

      referred_email = params.require(:referred_email)
      referrer_email = params.require(:referrer_email)

      if referred_email == referrer_email
        @errors[:referred_email] = 'Referrer and referred emails cannot be the same'
        return render :new
      end

      referred_user = User.find_by(email: referred_email)
      referring_user = User.find_by(email: referrer_email)


      @errors[:referred_email] = 'User not found' if referred_user.nil?
      @errors[:referrer_email] = 'User not found' if referring_user.nil?

      if @errors.any?
        return render :new
      end
      authorize! :manage, referred_user
      authorize! :manage, referring_user

      referred_user.update(referred_from_user_id: referring_user.id, referral_actioned: false)

      if referred_user.save
        redirect_to admin_referrals_path, notice: 'Referral created successfully'
      else
        render :new
      end
    end
  end
end
