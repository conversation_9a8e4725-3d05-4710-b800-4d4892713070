# app/controllers/static_admin/event_registrations_controller.rb
module StaticAdmin
  class EventRegistrationsController < StaticAdminController
    include PaginationHelper
    before_action :set_event_registration, only: [:show, :edit, :update, :destroy, :registrants, :export_registrants, :toggle_active]

    def index
      @event_registrations = EventRegistration.order(created_at: :desc)
      
      if params[:status] == 'active'
        @event_registrations = @event_registrations.active
      elsif params[:status] == 'closed'
        @event_registrations = @event_registrations.where(active: false)
      end

      @event_registrations = safe_paginate(@event_registrations, page: params[:page], per_page: 20)
    end

    def show
      @recent_registrants = @event_registration.event_registrants
                                              .includes(:user)
                                              .order(created_at: :desc)
                                              .limit(10)
    end

    def new
      @event_registration = EventRegistration.new
      @event_registration.registration_fields = [
        { 'name' => 'company', 'label' => 'Company/School', 'type' => 'text', 'required' => false }
      ]
    end

    def create
      @event_registration = EventRegistration.new(event_registration_params)
      
      if @event_registration.save
        redirect_to admin_event_registration_path(@event_registration), 
                   notice: 'Event registration was successfully created.'
      else
        render :new
      end
    end

    def edit
    end

    def update
      if @event_registration.update(event_registration_params)
        redirect_to admin_event_registration_path(@event_registration), 
                   notice: 'Event registration was successfully updated.'
      else
        render :edit
      end
    end

    def destroy
      @event_registration.destroy
      redirect_to admin_event_registrations_path, 
                 notice: 'Event registration was successfully deleted.'
    end

    def registrants
      @registrants = @event_registration.event_registrants
                                       .includes(:user)
                                       .order(created_at: :desc)
      
      if params[:sync_status].present?
        @registrants = @registrants.where(mailchimp_sync_status: params[:sync_status])
      end

      if params[:search].present?
        @registrants = @registrants.where(
          'name ILIKE ? OR email ILIKE ?', 
          "%#{params[:search]}%", 
          "%#{params[:search]}%"
        )
      end

      @registrants = safe_paginate(@registrants, page: params[:page], per_page: 50)
    end

    def export_registrants
      respond_to do |format|
        format.csv do
          send_data generate_csv, 
                   filename: "#{@event_registration.name.parameterize}-registrants-#{Date.current}.csv"
        end
      end
    end

    def toggle_active
      @event_registration.update!(active: !@event_registration.active?)
      status = @event_registration.active? ? 'activated' : 'deactivated'
      
      redirect_to admin_event_registration_path(@event_registration),
                 notice: "Event registration #{status}."
    end

    private

    def set_event_registration
      @event_registration = EventRegistration.find(params[:id])
    end

    def event_registration_params
      params.require(:event_registration).permit(
        :name, :description, :mailchimp_tag, :active, :closes_at,
        registration_fields: {}
      ).tap do |permitted_params|
        # Convert the registration_fields hash to an array, or set to empty array if none
        if permitted_params[:registration_fields].present?
          fields_array = []
          permitted_params[:registration_fields].each do |key, field_data|
            # Only add fields that have at least a name
            if field_data[:name].present?
              fields_array << {
                'name' => field_data[:name],
                'label' => field_data[:label],
                'type' => field_data[:type] || 'text',
                'required' => field_data[:required] == 'true'
              }
            end
          end
          permitted_params[:registration_fields] = fields_array
        else
          permitted_params[:registration_fields] = []
        end
      end
    end

    def generate_csv
      require 'csv'
      
      CSV.generate(headers: true) do |csv|
        # Build headers
        headers = ['Name', 'Email', 'Registered At', 'User Account', 'Mailchimp Status']
        headers += @event_registration.field_names.map(&:humanize)
        csv << headers
        
        # Add data rows
        @event_registration.event_registrants.includes(:user).each do |registrant|
          row = [
            registrant.display_name,
            registrant.display_email,
            registrant.created_at.strftime('%Y-%m-%d %H:%M'),
            registrant.user.present? ? 'Yes' : 'No',
            registrant.mailchimp_sync_status.humanize
          ]
          
          # Add custom field values
          @event_registration.field_names.each do |field_name|
            row << registrant.field_value(field_name)
          end
          
          csv << row
        end
      end
    end
  end
end