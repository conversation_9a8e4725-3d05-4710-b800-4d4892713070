module StaticAdmin
  class FlowStepsController < StaticAdminController
    before_action :check_access
    before_action :set_flow
    before_action :set_flow_step, only: [:edit, :update, :destroy]

    def check_access
      redirect_to universal_dashboard_link unless current_user.beta_feature_enabled?('flows')
    end

    # GET /admin/flows/:flow_id/steps/new
    def new
      @flow_step = @flow.flow_steps.build
    end

    # POST /admin/flows/:flow_id/steps
    def create
      @flow_step = @flow.flow_steps.build(flow_step_params)

      if @flow_step.save
        query_params = params['reopen_draw'].present? ? { flow_step_id: @flow_step.id } : {}
        redirect_to steps_admin_flow_path(@flow, **query_params), notice: 'Step was successfully created.'
      else
        error_message = @flow_step.errors.full_messages.join(', ')
        redirect_to steps_admin_flow_path(@flow), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to steps_admin_flow_path(@flow),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # GET /admin/flows/:flow_id/steps/:id/edit
    def edit
    end

    # PATCH/PUT /admin/flows/:flow_id/steps/:id
    def update
      query_params = params['reopen_draw'].present? ? { flow_step_id: @flow_step.id } : {}
      if @flow_step.update(flow_step_params)
        redirect_to steps_admin_flow_path(@flow, **query_params), notice: 'Step was successfully updated.'
      else
        error_message = @flow_step.errors.full_messages.join(', ')
        redirect_to steps_admin_flow_path(@flow, **query_params), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to steps_admin_flow_path(@flow, **query_params),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # DELETE /admin/flows/:flow_id/steps/:id
    def destroy
      @flow_step.destroy
      redirect_to steps_admin_flow_path(@flow), notice: 'Step was successfully deleted.'
    end

    # PUT /admin/flows/:flow_id/steps/reorder
    def reorder
      step_ids = params[:step_ids]

      if step_ids.present?
        start_weight = 0

        ActiveRecord::Base.transaction do
          step_ids.each_with_index do |id, index|
            FlowStep.where(id: id).update_all(weight: index + start_weight)
          end
        end

        head :ok
      else
        head :bad_request
      end
    end

    private

    def set_flow
      @flow = Flow.find(params[:flow_id])
    end

    def set_flow_step
      @flow_step = @flow.flow_steps.find(params[:id])
    end

    def flow_step_params
      permitted = params.require(:flow_step).permit(
        :name,
        :description,
        :step_type,
        :video_id,
        :quiz_id,
        :rich_text_content,
        :estimated_duration_minutes,
        :fileboy_image_id
      )

      permitted
    end
  end
end
