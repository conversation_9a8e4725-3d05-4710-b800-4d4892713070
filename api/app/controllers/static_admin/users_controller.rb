module StaticAdmin
  class UsersController < StaticAdminController
    include <PERSON><PERSON><PERSON>Helper
    before_action :set_user, only: %i[show edit update send_password_reset_email generate_password_reset_link toggle_restrict sign_out_all_devices send_welcome_email manage_subscriber stripe_subscription legacy_subscription password devices school actions connect_school_info delete_subscriber mailchimp sync_mailchimp permissions update_permissions]
    before_action :prevent_in_dev, only: %i[send_welcome_email]

    admin_section :misc

    # GET /admin/users
    def index
      sortable_columns = %w[name email created_at last_sign_in_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'name'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'asc'

      includes = { school: [:uk_school, :subscriber] }

      if params[:type] != 'Admin'
        includes[:ai_subscription] = {}
        includes[:curriculum_subscription] = {}
        includes[:subscriber] = {}

        includes[:school] = [:uk_school] if params[:type] == 'IndividualUser'
      end

      @users = User.all.includes(includes)
      @users = @users.where.not(type: 'AnonymousUser')
      @users = @users.where('users.name ilike :q OR email like :q', q: "%#{params[:query].to_s}%") if params[:query].present?

      if params[:subscription_type].present?
        if params[:subscription_type] == 'ai'
          @users = @users.subscribed_to_service(:ai)
        end
        if params[:subscription_type] == 'science'
          @users = @users.subscribed_to_service(:science)
        end
        if params[:subscription_type] == 'geography'
          @users = @users.subscribed_to_service(:geography)
        end
      end

      if params[:payment_type].present?
        if params[:payment_type] == 'user'
          @users = @users.joins(:stripe_subscriptions).where("stripe_subscriptions.free_subscription = false AND stripe_subscriptions.stripe_cache->>'status' = ?", 'active')
        elsif params[:payment_type] == 'school'
          @users = @users.joins(:school).where('schools.hubspot_ai_subscription_status = true OR schools.hubspot_subscription_status = ?', 'Subscribed')
        elsif params[:payment_type] == 'free'
          @users = @users.joins(:stripe_subscriptions).where('stripe_subscriptions.free_subscription = true')
        end
      end

      @users = @users.where(type: params[:type]) if params[:type].present?
      @users = @users.joins(:school).where('schools.name ilike :s', s: "%#{params[:school].to_s}%") if params[:school].present?

      if sort == 'last_sign_in_at'
        nulls_order = order == 'desc' ? 'NULLS LAST' : 'NULLS FIRST'
        @users = safe_paginate(@users.order(Arel.sql("last_sign_in_at #{order.upcase} #{nulls_order}")), page: params[:page], per_page: 50)
      else
        @users = safe_paginate(@users.order("#{sort} #{order}"), page: params[:page], per_page: 50)
      end
    end

    # GET /admin/users/new
    def new
      @user = User.new
      @user.type = params[:type] if params[:type].present?
    end

    # POST /admin/users
    def create
      user_type = user_params[:type] || 'IndividualUser'
      @user = user_type.constantize.new(user_params)

      if @user.save!
        redirect_to edit_admin_user_path(@user), notice: 'User was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/users/:id/edit
    def edit; end

    def stripe_subscription
      load_subscription
    end

    def legacy_subscription; end

    def password; end

    def devices; end

    def school
      # Fetch schools with unrestricted HubSpot subscription status
      unrestricted_school_ids = School.generic
          .where.not(hubspot_subscription_status: School::HS_SUBSCRIPTION_STATUS[:restricted])
          .pluck(:id)

      # Fetch schools with active or free Stripe subscriptions
      stripe_subscribed_school_ids = School.generic
          .joins(teachers: :stripe_subscriptions)
          .where("stripe_subscriptions.free_subscription OR
                  (stripe_cache IS NOT NULL AND stripe_cache->>'status' = 'active')")
          .pluck(:id)

      school_ids = (unrestricted_school_ids + stripe_subscribed_school_ids).uniq
      @schools = School.where(id: school_ids).pluck(:id, :name, :postcode)
    end

    def connect_school_info
      @school = School.generic.find(params[:school_id]) if params[:school_id]
      render layout: false
    end

    def actions; end


    # PATCH/PUT /admin/users/:id
    def update
      return_page = user_params[:password].present? ? :password : :edit

      if @user.update(user_params)
        redirect_to [return_page, :admin, @user], notice: 'User was successfully updated.'
      else
        render return_page
      end
    end

    def connect_school
      user = User.find(params[:id])
      school = School.find(params[:school_id])
      user.update(type: 'Teacher', school: school)
      redirect_to edit_admin_static_school_path(school, back_to: school_admin_user_path(user)), notice: 'User was successfully connected.'
    end

    # POST /admin/users/:id/send_password_reset_email
    def send_password_reset_email
      @user.send_password_reset_email(token: @user.new_recovery_token)
      render json: { success: true, notice: "Sent password reset email to user." }
    end

    # GET /admin/users/:id/generate_password_reset_link
    def generate_password_reset_link
      token = @user.new_recovery_token
      render json: { success: true, url: "#{request.base_url}/accounts/password/edit?token=#{token}" }
    end

    # PATCH /admin/users/:id/toggle_restrict
    def toggle_restrict
      @user.is_blocked? ? @user.unblock! : @user.block!
      render json: { success: true, notice: "User restriction successfully updated." }
    end

    # DELETE /admin/users/:id/sign_out_all_devices
    def sign_out_all_devices
      @user.device_logins.destroy_all
      render json: { success: true, notice: 'Successfully signed user out of all devices.' }
    end

    def manage_subscriber
      subscriber = @user.get_or_create_subscriber
      if params[:subscriber].nil? || subscriber.update(subscriber_params)
        redirect_to stripe_subscription_admin_user_path(@user), notice: 'Subscriber saved.'
      else
        load_subscription
        render :stripe_subscription
      end
    end

    def delete_subscriber
      redirect_to stripe_subscription_admin_user_path(@user), alert: 'This user does not have a subscriber' unless @user.subscriber.present?
      redirect_to stripe_subscription_admin_user_path(@user), alert: 'Subscriber has a Stripe subscription ID' if @user.subscriber.stripe_subscription_id.present?

      if @user.subscriber.destroy
        redirect_to stripe_subscription_admin_user_path(@user), notice: 'Subscriber removed'
      else
        Rails.logger.error(@user.subscriber.errors.full_messages)
        redirect_to stripe_subscription_admin_user_path(@user), alert: 'Error removing subscriber'
      end
    end

    def mailchimp
    end

    def sync_mailchimp
      begin
        sync_log = Mailchimp::SyncService.new.sync_user(@user, trigger_type: 'manual', force: true)
        
        if sync_log.success?
          flash[:notice] = "User successfully synced to Mailchimp"
        else
          flash[:alert] = "Failed to sync user: #{sync_log.error_message}"
        end
      rescue => e
        flash[:alert] = "Failed to sync user: #{e.message}"
      end
      
      redirect_to mailchimp_admin_user_path(@user)
    end

    def permissions
      redirect_to edit_admin_user_path(@user) unless @user.admin? && @user.id != @current_user.id

      @admin_permissions = User::ADMIN_PERMISSIONS
    end

    def update_permissions
      redirect_to edit_admin_user_path(@user) unless @user.admin? && @user.id != @current_user.id

      admin_permissions = params.dig(:user, :admin_permissions) || []

      @user.update_admin_permissions(admin_permissions)

      redirect_to permissions_admin_user_path(@user), notice: 'Admin permissions were successfully updated.'
    rescue => e
      flash[:alert] = "Failed to update permissions: #{e.message}"
      redirect_to permissions_admin_user_path(@user)
    end

    private

    def load_subscription
       # check the users subscription first
       
       if @user.subscriber.present?
         service = SubscriptionService.new(@user)
         @subscription = service.get_subscription_details
         @subscribable = @user
       end
       # If the user has no subscription, check the schools
       if @subscription.nil? && @user.school&.subscriber&.present?
        @subscribable = @user.school
        # if the school has a free subscription, set a flag and skip the look up on service
        @has_school_subscription = @user.school.subscriber.has_free_subscription?
        return if @has_school_subscription

        # look up the subscription via the school
        service = SubscriptionService.new(@user.school)
        @subscription = service.get_subscription_details

        # If the school has a subscription, set the flag so it will link through to the school instead
        @has_school_subscription = @subscription.present?
       else 
        @subscribable = @user
        @has_school_subscription = false
       end
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_user
      @user = User.find(params[:id]).becomes(User) # coerce it back to a User
    end

    def subscriber_params
      params.require(:subscriber).permit(
        free_subscription: [
          :ai, :ai_body, :ai_expires_at,
          :science, :science_body, :science_expires_at, 
          :geography, :geography_body, :geography_expires_at,
          :ai_trial_start, :science_trial_start, :geography_trial_start,
          :bypass_ai, :bypass_science, :bypass_geography,
        ]
      )
    end

    # Only allow a list of trusted parameters through.
    def user_params
      p = params.require(:user).permit(
        :name,
        :email,
        :dob,
        :gender,
        :working_days,
        :password,
        :stripe_customer_id,
        :is_school_admin,
        :use_new_presentation_player,
        :alias,
        :identifier,
        :type,
        :school_id,
        :job_title
      )
      p.delete(:password) if p[:password].blank?
      p
    end
  end
end
