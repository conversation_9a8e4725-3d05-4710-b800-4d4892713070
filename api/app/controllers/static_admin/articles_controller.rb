module StaticAdmin
  class StaticAdmin::ArticlesController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_article, only: %i[show edit update destroy]

    admin_section :content

    # GET /admin/articles
    def index
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @articles = Article.all
      @articles = @articles.where('name ILIKE :query OR body ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      case params[:scope]
      when 'drafts'
        @articles = @articles.drafts
      when 'published'
        @articles = @articles.published
      when 'featured'
        @articles = @articles.featured
      end

      # Filter by AI generated status
      if params[:ai_generated_filter].present?
        case params[:ai_generated_filter]
        when 'true'
          @articles = @articles.where(ai_generated: true)
        when 'false'
          @articles = @articles.where(ai_generated: false)
        end
      end

      @articles = safe_paginate(@articles.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/articles/new
    def new
      @article = Article.new
    end

    # POST /admin/articles
    def create
      @article = Article.new(article_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @article.fileboy_image_id = fileboy_image_id if fileboy_image_id

      handle_video_for(@article, params['article']['video_source'], params['article']['external_video_id'])

      if @article.save
        redirect_to [:edit, :admin, @article], notice: 'Article was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/articles/:id/edit
    def edit; end

    # PATCH/PUT /admin/articles/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?

      @article.fileboy_image_id = fileboy_image_id if fileboy_image_id

      video_source = params['article']['video_source']
      external_video_id = params['article']['external_video_id']

      handle_video_for(@article, video_source, external_video_id)

      if video_source.present? && external_video_id.present?
        video = Video.find_or_create_by(source: video_source, external_id: external_video_id)
        @article.video = video if video.present?
      end

      if @article.update(article_params)
        redirect_to [:edit, :admin, @article], notice: 'Article was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/articles/:id
    def destroy
      @article.destroy
      redirect_to admin_articles_url, notice: 'Article was successfully deleted.'
    end

    # GET /admin/articles/new_ai_article
    def new_ai_article
      @article_types = ARTICLE_TYPES
    end

    # POST /admin/articles/generate_titles
    def generate_titles
      article_type = params[:article_type]
      prompt = params[:prompt]
      
      if article_type.blank? || prompt.blank?
        render json: { error: 'Article type and prompt are required' }, status: 422
        return
      end

      generator = ArticleGenerator.new
      suggested_titles = generator.generate_titles(article_type, prompt)
      
      respond_to do |format|
        format.json { render json: { titles: suggested_titles } }
      end
    rescue => e
      Rails.logger.error "Error generating titles: #{e.message}"
      render json: { error: 'Failed to generate titles. Please try again.' }, status: 500
    end

    # POST /admin/articles/generate_article
    def generate_article
      title = params[:title]
      article_type = params[:article_type]
      prompt = params[:prompt]
      
      if title.blank? || article_type.blank? || prompt.blank?
        render json: { error: 'Title, article type, and prompt are required' }, status: 422
        return
      end

      # Generate a unique session ID for this generation process
      session_id = SecureRandom.hex(16)
      
      # Queue the job
      GenerateAiArticleJob.perform_later(title, article_type, prompt, session_id)
      
      # Return the session ID for polling
      render json: { 
        success: true, 
        session_id: session_id,
        message: 'Article generation started. Please wait...' 
      }
    rescue => e
      Rails.logger.error "Error queuing AI article generation: #{e.message}"
      render json: { 
        success: false, 
        errors: ["Failed to start article generation: #{e.message}"] 
      }
    end

    # GET /admin/articles/check_generation_status/:session_id
    def check_generation_status
      session_id = params[:session_id]
      
      status_data = Rails.cache.read("ai_generation_status_#{session_id}")
      
      if status_data.nil?
        render json: { status: 'not_found' }
      elsif status_data == 'processing'
        render json: { status: 'processing' }
      else
        # Parse the JSON result
        result = JSON.parse(status_data)
        render json: result
      end
    rescue => e
      Rails.logger.error "Error checking generation status: #{e.message}"
      render json: { status: 'error', errors: [e.message] }
    end

    # POST /admin/articles/create_from_ai (back to synchronous)
    def create_from_ai
      @article = Article.new(ai_article_params)
      @article.draft = true
      @article.ai_generated = true
      
      if @article.save
        render json: { success: true, redirect_url: edit_admin_article_path(@article) }
      else
        render json: { success: false, errors: @article.errors.full_messages }
      end
    end

    # POST /admin/articles/create_from_ai
    def create_from_ai
      @article = Article.new(ai_article_params)
      @article.draft = true
      @article.ai_generated = true if @article.respond_to?(:ai_generated)
      
      if @article.save
        render json: { success: true, redirect_url: edit_admin_article_path(@article) }
      else
        render json: { success: false, errors: @article.errors.full_messages }
      end
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_article
      @article = Article.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def article_params
      params.require(:article).permit(:name, :body, :featured, :draft, :summary, :slug, :title, :meta_description, :fileboy_image_id, :ai_generated)
    end

    def handle_video_for(model, video_source, external_video_id)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)
      video.name = model.name if video.new_record? || video.name.blank?
      video.save if video.changed?

      model.video = video
    end

    def ai_article_params
      params.require(:article).permit(:name, :body, :summary, :title, :meta_description, :slug, :ai_generated)
    end
  end
end

ARTICLE_TYPES = [
  'Teacher Guides',
  'Lifestyle',
  'Platform Features',
  'Education Releases',
  'Curriculum Updates',
  'Teaching Tips',
  'Educational Technology',
  'Classroom Management'
].freeze