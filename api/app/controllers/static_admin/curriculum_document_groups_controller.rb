module StaticAdmin
  class StaticAdmin::CurriculumDocumentGroupsController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_group, only: [:edit, :update, :destroy]

    # GET /admin/curriculum-documents/groups
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'

      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @groups = CurriculumDocumentGroup.all
      if params[:query].present?
        @groups = @groups.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @groups = safe_paginate(@groups.order("#{sort} #{order}"), page: params[:page], per_page: 20)
    end

    # GET /admin/curriculum-documents/groups/new
    def new
      @group = CurriculumDocumentGroup.new
    end

    # POST /admin/curriculum-documents/groups
    def create
      doc_ids = group_params[:document_ids].reject(&:empty?)

      @group = CurriculumDocumentGroup.new(name: group_params[:name], user: current_user)

      if @group.save
        @group.curriculum_document_ids = doc_ids

        redirect_to admin_curriculum_document_groups_path, notice: 'Curriculum document group was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/curriculum-documents/groups/:id/edit
    def edit; end

    # PATCH/PUT /admin/curriculum-documents/groups/:id
    def update
      doc_ids = group_params[:document_ids].reject(&:empty?)

      if @group.update(name: group_params[:name])
        @group.curriculum_document_ids = doc_ids

        redirect_to admin_curriculum_document_groups_path, notice: 'Curriculum document group was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/curriculum-documents/:id
    def destroy
      @group.destroy
      redirect_to admin_curriculum_document_groups_url, notice: 'Curriculum document group was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_group
      @group = CurriculumDocumentGroup.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def group_params
      params.require(:curriculum_document_group).permit(:name, document_ids: [])
    end

  end
end
  