module StaticAdmin
  class SchoolWondeController < StaticAdminController
    before_action :set_school
    before_action :set_wonde_import, only: %i[
      cancel
      download
      edit
      update_teacher
      invalid_teachers
      review
      finalize
      save
      processing
    ]

    def index
      @wonde_import = @school.wonde_imports.last

      if @wonde_import.nil?
        @wonde_data = nil
        return
      end

      wonde_teacher_ids = @wonde_import.wonde_import_teachers.pluck(:wonde_id)
      wonde_pupil_ids = @wonde_import.wonde_import_pupils.pluck(:wonde_id)
      wonde_form_ids = @wonde_import.wonde_import_forms.pluck(:wonde_id)

      # pupils that do not have a wonde ID or the wonde ID is not in the current import
      extra_pupil_ids = @school.pupils.where(wonde_id: nil).or(@school.pupils.where.not(wonde_id: wonde_pupil_ids)).pluck(:id)
      # teachers that are not admins and do not have a wonde ID or teachers that are not admins and the wonde ID is not in the current import
      extra_teacher_ids = @school.teachers.where.not(is_school_admin: true).where(wonde_id: nil).or(@school.teachers.where.not(is_school_admin: true).where.not(wonde_id: wonde_teacher_ids)).pluck(:id)
      # forms that do not have a wonde ID or the wonde ID is not in the current import
      extra_form_ids = @school.forms.where(wonde_id: nil).or(@school.forms.where.not(wonde_id: wonde_form_ids)).pluck(:id)

      @wonde_data = {
        wonde_import: @wonde_import,
        extra_pupil_ids: extra_pupil_ids,
        extra_pupils: @school.pupils.where(id: extra_pupil_ids).map { |record| %i[id name identifier wonde_id].map { |key| [key, record[key]] }.to_h },
        extra_teacher_ids: extra_teacher_ids,
        extra_teachers: @school.teachers.where(id: extra_teacher_ids).map { |record| %i[id name email wonde_id].map { |key| [key, record[key]] }.to_h },
        extra_form_ids: extra_form_ids,
        extra_forms: @school.forms.where(id: extra_form_ids).map { |record| %i[id name wonde_id].map { |key| [key, record[key]] }.to_h },
      }
    end

    def create
      last_import = @school.wonde_imports.last

      # Start a new import if there is no import running
      if !last_import || last_import.imported? || last_import.cancelled?
        last_import = @school.wonde_imports.create(success: false, retain_data: false)

        # Trigger the background job BEFORE redirecting
        begin
          DownloadWondeDataJob.perform_later(last_import)
        rescue => e
          last_import.update(import_status: :cancelled, success: false)
          WondeImportError.create({
            error_data: {
              fatal: ['A fatal error occurred'],
              raw: e.message
            },
            warning_data: {},
            wonde_import: last_import
          })
          flash[:error] = "Failed to start import: #{e.message}"
          return redirect_to wonde_sync_admin_static_school_path(@school)
        end
      end

      # Redirect after the job has been enqueued (ensuring the user doesn’t wait)
      redirect_to wonde_sync_download_admin_static_school_path(@school, wonde_import_id: last_import.id)
    end

    def cancel
      @wonde_import.update(import_status: 'cancelled', success: false)
      redirect_to wonde_sync_admin_static_school_path(@school)
    end

    def download
      redirect_path = redirect_based_on_import_status(@wonde_import)

      respond_to do |format|
        if redirect_path
          format.html { redirect_to redirect_path }
        else
          format.html { render :download }
        end

        format.json { render json: { redirect_path: redirect_path } }
      end
    end

    def edit
      redirect_to wonde_sync_admin_static_school_path(@school) unless @wonde_import.complete?
    end

    def update_teacher
      begin
        data = params.require(:wonde_import_teacher).permit(:id, :email, :user_id)
        row = @wonde_import.wonde_import_teachers.find_by(id: data[:id])

        return render json: { saved: false, message: 'Record not found' }, status: :not_found unless row

        update_data = data.slice(:email, :user_id).to_h.compact

        if data.key?(:user_id)
          update_data[:user_id] = data[:user_id] if data[:user_id].nil? || @school.teachers.exists?(data[:user_id])
        end

        return render json: { saved: true } if update_data.empty?

        saved = row.update(update_data)
        render json: { saved: saved }
      rescue ActiveRecord::RecordNotFound
        Rails.logger.error(e)
        render json: { saved: false, message: 'Related record not found' }, status: :not_found
      rescue => e
        Rails.logger.error(e)
        render json: { saved: false, message: 'An unexpected error occurred' }, status: :internal_server_error
      end
    end

    def invalid_teachers
      render json: @wonde_import.invalid_teachers
    end

    def review
      redirect_to wonde_sync_admin_static_school_path(@school) unless @wonde_import.complete?
    end

    def finalize
      redirect_to wonde_sync_admin_static_school_path(@school) unless @wonde_import.complete?
    end

    def save
      redirect_to wonde_sync_processing_admin_static_school_path(@school, wonde_import_id: @wonde_import.id) and return if @wonde_import.importing?
      ImportWondeDataJob.perform_later(@wonde_import)
      redirect_to wonde_sync_processing_admin_static_school_path(@school, wonde_import_id: @wonde_import.id)
    end

    def processing
      redirect_path = wonde_sync_admin_static_school_path(@school)

      respond_to do |format|
        if !@wonde_import.importing?
          format.html { redirect_to redirect_path }
          format.json { render json: { redirect_path: redirect_path } }
        else
          format.html { render :processing }
          format.json { render json: { redirect_path: nil } }
        end
      end
    end

    private

    def set_school
      @school = School.find(params[:id])
    end

    def set_wonde_import
      @wonde_import = @school.wonde_imports.find(params[:wonde_import_id])
    end

    def redirect_based_on_import_status(wonde_import)
      # if status is importing, redirect to the processing page (data is being saved)
      # if status is complete, redirect to the edit page
      # if status is not running, redirect to the initial page (as any other status would indicate it's either been processed or cancelled)
      return nil if wonde_import.running?
      if wonde_import.importing?
        wonde_sync_processing_admin_static_school_path(wonde_import.school)
      elsif wonde_import.complete?
        wonde_sync_edit_admin_static_school_path(wonde_import.school, wonde_import_id: wonde_import.id)
      else
        wonde_sync_admin_static_school_path(wonde_import.school)
      end
    end
  end
end
