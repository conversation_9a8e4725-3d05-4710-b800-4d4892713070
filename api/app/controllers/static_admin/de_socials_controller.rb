module StaticAdmin
  class StaticAdmin::DeSocialsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_de_social, only: [:show, :edit, :update, :destroy]

    admin_section :marketing

    # GET /admin/de_socials
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @de_socials = DeSocial.all
      if params[:query].present?
        @de_socials = @de_socials.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @de_socials = safe_paginate(@de_socials.order("#{sort} #{order}"), page: params[:page], per_page: 25)
    end

    # GET /admin/de_socials/new
    def new
      @de_social = DeSocial.new
    end

    # POST /admin/de_socials
    def create
      @de_social = DeSocial.new(de_social_params.except(:de_media_attributes))

      if @de_social.save
        @de_social.update(de_media_attributes: handle_de_media_attributes(params, @de_social)) if params[:de_social][:de_media_attributes].present?

        redirect_to [:edit, :admin, @de_social], notice: 'Social post was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/de_socials/:id/edit
    def edit
      de_media = DeMedia.where(de_social_id: @de_social.id)
      @de_images = de_media.where.not(fileboy_image_id: nil)
      @de_videos = de_media.joins(:video).select('de_media.*', 'videos2.source AS video_source', 'videos2.external_id as external_video_id')
    end

    # PATCH/PUT /admin/de_socials/:id
    def update
      params[:de_social][:de_media_attributes] = handle_de_media_attributes(params, @de_social) if params[:de_social][:de_media_attributes].present?

      if @de_social.update(de_social_params)
        redirect_to [:edit, :admin, @de_social], notice: 'Social post was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/de_socials/:id
    def destroy
      @de_social.destroy
      redirect_to admin_de_socials_url, notice: 'Social post was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_de_social
      @de_social = DeSocial.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def de_social_params
      params.require(:de_social).permit(:name, :body, :published, de_media_attributes: [:id, :fileboy_image, :external_id, :source, :_destroy, :de_social_id, :videos2_id, :fileboy_image_id])
    end

    def handle_de_media_attributes params, model
      de_media_attributes = {}
      params[:de_social][:de_media_attributes].each do |key, media|
        if media[:_destroy].present? && media[:_destroy] == '1'
          de_media_attributes[key] = { id: media[:id], _destroy: true }
          next
        end

        if media[:source].present? && media[:external_id].present?
          video = Video.find_or_initialize_by(source: media[:source], external_id: media[:external_id])
          video.name = model.name if video.new_record? || video.name.blank?
          video.save

          next unless video.id
          de_media_attributes[key] = { de_social_id: model.id, videos2_id: video.id }
        elsif media[:fileboy_image].present?
          puts media[:fileboy_image]
          fileboy_image_id = upload_image media[:fileboy_image]
          de_media_attributes[key] = { de_social_id: model.id, fileboy_image_id: fileboy_image_id } if fileboy_image_id
        end

        de_media_attributes[key][:id] = media[:id] if media[:id].present? && de_media_attributes[key].present?
      end

      de_media_attributes
    end

  end
end
