# frozen_string_literal: true

module StaticAdmin
  class StaticAdmin::EventsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_event, only: %i[show edit update destroy remove_file]

    admin_section :website

    # GET /admin/events
    def index
      puts "params: #{params}"
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @events = Event.all
      case params[:scope]
      when 'published'
        @events = @events.published
      when 'unpublished'
        @events = @events.unpublished
      end

      @events = @events.where('name ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      @events = safe_paginate(@events.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/events/new
    def new
      @event = Event.new
    end

    # POST /admin/events
    def create
      @event = Event.new(event_params)

      fileboy_image_id = upload_image params[:fileboy] if params[:fileboy].present?
      @event.fileboy_image_id = fileboy_image_id if fileboy_image_id

      @event.for_arr = params[:event][:for_arr]

      if @event.save
        redirect_to [:edit, :admin, @event], notice: 'Event was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/events/:id/edit
    def edit; end

    # PATCH/PUT /admin/events/:id
    def update
      fileboy_image_id = upload_image(params[:fileboy]) if params[:fileboy].present?
      @event.fileboy_image_id = fileboy_image_id if fileboy_image_id

      @event.for_arr = params[:event][:for_arr]

      if @event.update(event_params)
        redirect_to [:edit, :admin, @event], notice: 'Event was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/events/:id
    def destroy
      @event.destroy
      redirect_to admin_events_url, notice: 'Event was successfully deleted.'
    end

    # DELETE /admin/events/:id/remove-event
    def remove_file
      @event.fileboy_image_id = nil

      if @event.save

        redirect_to [:edit, :admin, @event], notice: 'Event was successfully removed.'
      else
        render :edit
      end
    end

    # POST /admin/events/:id/toggle_publish
    def toggle_publish
      @event = Event.find(params[:id])
      new_state = !@event.published
      @event.update(published: new_state)
      redirect_to admin_events_url, notice: new_state ? 'Event published.' : 'Event unpublished.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_event
      @event = Event.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def event_params
      params.require(:event).permit(
        :name,
        :summary,
        :body,
        :published,
        :start_date,
        :end_date,
        :external_url,
        :fileboy_image_id,
        :for,
        :hubspot_form_id,
        dates: %i[start_date end_date],
        for_arr: []
      )
    end
  end
end
