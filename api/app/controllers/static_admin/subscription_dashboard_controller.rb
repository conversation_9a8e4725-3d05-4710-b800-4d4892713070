# app/controllers/static_admin/subscription_dashboard_controller.rb
module <PERSON><PERSON><PERSON>d<PERSON>
  class SubscriptionDashboardController < StaticAdminController
    include PaginationHelper

    admin_section :schools

    def index
      # Gather key metrics for the dashboard
      @stats = {
        active_subscribers: Subscriber.where(subscription_status: 'active').count,
        past_due: Subscriber.where(subscription_status: 'past_due').count,
        canceled: Subscriber.where(subscription_status: 'canceled').count,
        total: Subscriber.count
      }

      # Recent activity
      @recent_subscribers = Subscriber.with_associations.order(created_at: :desc).limit(5)
      @recent_events = StripeEvent.order(created_at: :desc).limit(5)

      # Webhook health
      @webhook_health = StripeEvent.status_counts
    end

    def subscribers
      # Base query
      @subscribers = Subscriber.with_associations.all

      
      @subscribers = @subscribers.where("active_products->>'synced_at' IS NULL") if params[:never_synced] == '1'

      # Apply filters
      @subscribers = @subscribers.where(subscription_status: params[:status]) if params[:status].present?

      # Apply search
      if current_user.beta_feature_enabled?(:september_1)
        @subscribers = @subscribers.search(params[:search]).includes(subscriber: [:uk_school]) if params[:search].present?
      else
        @subscribers = @subscribers.search_by_name_or_email(params[:search]) if params[:search].present?
      end

      # Sort
      sort_column = params[:sort] || 'created_at'
      sort_direction = params[:direction] || 'desc'
      
      case sort_column
      when 'name'
        @subscribers = @subscribers.order_by_name(sort_direction)
      when 'email'
        @subscribers = @subscribers.order_by_email(sort_direction)
      else
        @subscribers = @subscribers.order("subscribers.#{sort_column} #{sort_direction}")
      end

      # Paginate
      @subscribers = safe_paginate(@subscribers, page: params[:page], per_page: 20)

      # Get counts for filter badges
      @subscriber_counts = {
        all: Subscriber.count,
        active: Subscriber.where(subscription_status: 'active').count,
        past_due: Subscriber.where(subscription_status: 'past_due').count,
        canceled: Subscriber.where(subscription_status: 'canceled').count
      }
    end
    
    def show
      @subscriber = Subscriber.find(params[:id])

      # Fetch subscription details
      if @subscriber.stripe_subscription_id.present?
        begin
          @subscription = Stripe::Subscription.retrieve({
                                                          id: @subscriber.stripe_subscription_id,
                                                          expand: ['items.data.price.product', 'latest_invoice']
                                                        })
        rescue Stripe::StripeError => e
          @subscription_error = e.message
        end
      end

      # Fetch customer details
      if @subscriber.stripe_customer_id.present?
        begin
          @customer = Stripe::Customer.retrieve({
                                                  id: @subscriber.stripe_customer_id,
                                                  expand: ['invoice_settings.default_payment_method']
                                                })

          # Get invoices
          @invoices = Stripe::Invoice.list({
                                             customer: @subscriber.stripe_customer_id,
                                             limit: 10,
                                             expand: ['data.subscription']
                                           })
        rescue Stripe::StripeError => e
          @customer_error = e.message
        end
      end

      # Get event logs for this subscriber
      @events = StripeEvent.where("event_data->>'customer' = ?", @subscriber.stripe_customer_id)
                           .order(created_at: :desc)
                           .limit(10)
    end

    def analytics
      # Get data for analytics charts

      # Subscription growth with product breakdown
      @growth_data = {}

      # Last 12 months of subscriber counts with product breakdown
      12.downto(0) do |i|
        date = i.months.ago.beginning_of_month
        end_date = date.end_of_month
        
        # Get all subscribers created up to this date
        subscribers_at_date = Subscriber.where('created_at <= ?', end_date)
        
        # Count total subscribers
        total_count = subscribers_at_date.count
        
        # Count subscribers by product (based on active_products or free subscriptions)
        science_count = subscribers_at_date.select { |s| s.has_access_to?('science') }.count
        geography_count = subscribers_at_date.select { |s| s.has_access_to?('geography') }.count
        ai_count = subscribers_at_date.select { |s| s.has_access_to?('ai') }.count
        
        @growth_data[date.strftime('%b %Y')] = {
          total: total_count,
          science: science_count,
          geography: geography_count,
          ai: ai_count
        }
      end

      @subscriber_count = Subscriber.where(subscription_status: 'active').count

      # Status distribution
      @status_distribution = Subscriber.group(:subscription_status).count

      # Size distribution based on subscription type
      @size_distribution = calculate_size_distribution

      # Calculate real subscription metrics
      @metrics = calculate_subscription_metrics
    end

    def void_invoice
      begin
        # Since void_invoice is related to an existing subscription, find what subscription the user has
        subscriber = Subscriber.find_by(id: params[:id])
        if subscriber.nil?
          flash[:error] = 'Subscriber not found.'
          redirect_to admin_subscription_dashboard_path(id: params[:id]) and return
        end

        @subscribable = subscriber.subscriber

        invoice_id = params[:invoice_id]

        if invoice_id.blank?
          flash[:error] = 'No invoice ID provided.'
          redirect_to admin_subscription_dashboard_path(id: params[:id])
          return
        end

        service = SubscriptionService.new(@subscribable)

        if service.void_invoice(invoice_id)
          flash[:success] = 'Invoice has been voided successfully.'
        else
          flash[:error] = 'Failed to void the invoice. It may have already been processed or cannot be voided.'
        end
      rescue Stripe::StripeError => e
        Rails.logger.error "Stripe error voiding invoice: #{e.message}"
        Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
        Rails.logger.error e.backtrace.join("\n")

        error_message = case e.code
                        when 'resource_missing'
                          'Invoice not found. It may have been already processed.'
                        when 'invoice_no_customer_line_items'
                          'This invoice cannot be voided as it has already been finalized.'
                        else
                          "Error voiding invoice: #{e.message}"
                        end

        flash[:error] = error_message
      rescue => e
        Rails.logger.error "Error voiding invoice: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")

        flash[:error] = 'An unexpected error occurred while voiding the invoice. Please try again later.'
      end

      redirect_to admin_subscription_dashboard_path(id: params[:id])
    end

    def mark_invoice_as_paid
      puts "HERE!"
      begin
        subscriber = Subscriber.find_by(id: params[:id])
        unless subscriber
          flash[:error] = 'Subscriber not found.'
          redirect_to admin_subscription_dashboard_path(id: params[:id]) and return # Or a more general admin page
        end

        subscribable = subscriber.subscriber
        unless subscribable
          flash[:error] = 'Subscribable entity (User/School) not found for this subscriber.'
          redirect_to admin_subscription_dashboard_path(id: params[:id]) and return
        end

        invoice_id = params[:invoice_id]
        if invoice_id.blank?
          flash[:error] = 'No invoice ID provided.'
          redirect_to admin_subscription_dashboard_path(id: params[:id]) and return
        end

        service = SubscriptionService.new(subscribable)
        result = service.mark_invoice_as_paid(invoice_id)

        if result[:success]
          flash[:success] = result[:message] || "Invoice #{invoice_id} successfully marked as paid."
          # Optionally trigger a background job to sync subscriber status if not handled by webhooks
          # SubscriptionSyncJob.perform_later(subscriber.id)
        else
          flash[:error] = "Failed to mark invoice as paid: #{result[:error]}"
        end

      rescue Stripe::StripeError => e
        Rails.logger.error "Stripe error marking invoice #{params[:invoice_id]} as paid for subscriber #{params[:id]}: #{e.message}"
        flash[:error] = "Stripe Error: #{e.message}"
      rescue StandardError => e
        Rails.logger.error "Error marking invoice #{params[:invoice_id]} as paid for subscriber #{params[:id]}: #{e.message}"
        flash[:error] = "An unexpected error occurred: #{e.message}"
      end

      redirect_to admin_subscription_dashboard_path(id: params[:id])
    end

    # get test_emails
    def test_emails
      # For GET requests, just render the form
      @email_templates = [
        ['All Templates', 'all'],
        ['Subscription Created', 'subscription_created'],
        ['Payment Success', 'payment_success'],
        ['Payment Failed', 'payment_failed'],
        ['Subscription Cancelled', 'subscription_canceled'],
        ['Admin Subscription Cancelled', 'admin_subscription_canceled'],
        ['Subscription Reactivated', 'subscription_reactivated'],
        ['Subscription Renewed', 'subscription_renewed'],
        ['Scheduled Cancellation', 'scheduled_cancellation'],
        ['Admin Scheduled Cancellation', 'admin_scheduled_cancellation'],
        ['Status Change (to Active)', 'status_change_to_active'],
        ['Status Change (to Past Due)', 'status_change_to_past_due'],
        ['Status Change (to Unpaid)', 'status_change_to_unpaid'],
        ['Subscription Updated', 'subscription_updated'],
        ['Invoice Notification', 'invoice_notification_standard'],
        ['Subscription Change Invoice', 'invoice_notification_subscription_change'],
        ['Upcoming Invoice', 'upcoming_invoice'],
        ['Invoice Overdue', 'invoice_overdue'],
        ['Admin new subscription notification', 'admin_new_subscription_notification'],
        ['Invoice Reminder', 'invoice_reminder'],
        ['Welcome Email', 'welcome_email'],
        ['Password Reset', 'password_reset'],
      ]
    end

    def send_test_emails
      # If form submitted
      return unless request.post?
      email = params[:email]

      if email.present? && email.match?(/\A[^@\s]+@[^@\s]+\z/)
        # Create a test subscriber with the provided email
        test_subscriber = Subscriber.new(
          stripe_customer_id: "cus_test_#{SecureRandom.hex(10)}",
          stripe_subscription_id: "sub_test_#{SecureRandom.hex(10)}",
          subscription_status: 'active',
          cancellation_date: Time.current,
          po_number: "PO123456",
          subscriber: School.new(
            name: "Demo School",
            finance_email: email,
            finance_name: 'Test Recipient',
            postcode: 'NR32NX'
          )
        )

        # Mock data for emails
        invoice_id = 'in_test_' + SecureRandom.hex(10)
        end_date = Time.current + 30.days

        # Build a list of all available email templates with their mailer methods
        email_templates = {
          'subscription_created' => -> { SubscriptionMailer.subscription_created(test_subscriber, true) },
          'payment_success' => -> { SubscriptionMailer.payment_success(test_subscriber, invoice_id, true) },
          'payment_failed' => -> { SubscriptionMailer.payment_failed(test_subscriber, invoice_id, true) },
          'subscription_canceled' => -> { SubscriptionMailer.subscription_canceled(test_subscriber, true) },
          'admin_subscription_canceled' => -> { SubscriptionMailer.admin_subscription_canceled(test_subscriber, true) },
          'subscription_reactivated' => -> { SubscriptionMailer.subscription_reactivated(test_subscriber, true) },
          'subscription_renewed' => -> { SubscriptionMailer.subscription_renewed(test_subscriber) },
          'scheduled_cancellation' => -> { SubscriptionMailer.scheduled_cancellation(test_subscriber, end_date, true) },
          'admin_scheduled_cancellation' => -> { SubscriptionMailer.admin_scheduled_cancellation(test_subscriber, end_date, true) },
          'status_change_to_active' => -> { SubscriptionMailer.status_change(test_subscriber, 'past_due', 'active', true) },
          'status_change_to_past_due' => -> { SubscriptionMailer.status_change(test_subscriber, 'active', 'past_due', true) },
          'status_change_to_unpaid' => -> { SubscriptionMailer.status_change(test_subscriber, 'active', 'unpaid', true) },
          'subscription_updated' => -> { SubscriptionMailer.subscription_updated(test_subscriber, 'sub_test123', true) },
          'invoice_notification_standard' => -> { SubscriptionMailer.invoice_notification(test_subscriber, invoice_id, :standard, true) },
          'invoice_notification_subscription_change' => -> { SubscriptionMailer.invoice_notification(test_subscriber, invoice_id, :subscription_change, true) },
          'upcoming_invoice' => -> { SubscriptionMailer.upcoming_invoice_notification(test_subscriber, invoice_id, true) },
          'invoice_overdue' => -> { SubscriptionMailer.invoice_overdue(test_subscriber, invoice_id, true) },
          'admin_new_subscription_notification' => -> { SubscriptionMailer.admin_new_subscription_notification(test_subscriber, nil, true, email) },
          'invoice_reminder' => -> { SubscriptionMailer.invoice_reminder(test_subscriber, invoice_id, true) },
          'welcome_email' => -> { SchoolMailer.welcome_email(User.new(name: 'Test User', email: email), 'example-pwd!', true) },
          'password_reset' => -> { SchoolMailer.password_reset(User.new(name: 'Test User', email: email), 'example-token!') },
        }
        # subscription_canceled

        # Send all email templates if "all" is selected, or just the specified template
        template = params[:template]

        if template == 'all'
          # Send all templates
          successful_emails = []
          failed_emails = []

          email_templates.each do |name, mailer_proc|
            mailer_proc.call.tap { |mail| mail.header['X-Test-Mode'] = 'true' }.deliver_now
            successful_emails << name
          rescue => e
            Rails.logger.error "Error sending #{name} email: #{e.message}"
            failed_emails << { name: name, error: e.message }
          end

          flash[:success] = "Successfully sent #{successful_emails.count} email templates to #{email}"

          flash[:warning] = "#{failed_emails.count} templates failed to send. See logs for details." if failed_emails.any?
        elsif email_templates.key?(template)
          # Send specific template
          begin
            email_templates[template].call.tap { |mail| mail.header['X-Test-Mode'] = 'true' }.deliver_now
            flash[:success] = "Successfully sent '#{template}' email template to #{email}"
          rescue => e
            Rails.logger.error "Error sending #{template} email: #{e.message}"
            flash[:error] = "Failed to send email: #{e.message}"
          end
        else
          flash[:error] = 'Invalid template selected'
        end
      else
        flash[:error] = 'Please enter a valid email address'
      end

      flash[:notice] = 'Test email(s) sent successfully'
      redirect_to test_emails_admin_subscription_dashboard_index_path
    end

    def sync_status
      @active_subscribers_count = Subscriber.where(subscription_status: %w[active active_until_period_end]).size

      @never_synced_count = Subscriber.where("active_products->>'synced_at' IS NULL").size

      @sync_errors = Subscriber.where("active_products->>'synced_at' IS NOT NULL").where("active_products->>'sync_error' IS NOT NULL").order("(active_products->>'synced_at')::timestamp DESC")
      @sync_recent = Subscriber.where("active_products->>'synced_at' IS NOT NULL").order("(active_products->>'synced_at')::timestamp DESC").limit(20)
    end

    def trigger_sync
      if params[:id].present?
        subscriber = Subscriber.find(params[:id])
        SubscriptionSyncJob.perform_later(subscriber.id)
        flash[:success] = "Sync job scheduled for subscriber #{subscriber.id}"
        redirect_to admin_subscription_dashboard_path(id: params[:id])
      else
        SubscriptionSyncJob.perform_later(nil, force: true)
        flash[:success] = 'Sync job scheduled for all subscribers'
        redirect_to sync_status_admin_subscription_dashboard_index_path
      end
    end

    def tracking_events
      subscriber = Subscriber.find_by(id: params[:id])
      raise ActiveRecord::RecordNotFound unless subscriber
      custom_cache.delete("subscription_tracking_events_#{subscriber.id}") if params[:force_recache].present? && params[:force_recache] == 'true'
      events = custom_cache.fetch("subscription_tracking_events_#{subscriber.id}", expires_in: 15.minutes) do
        resource = subscriber.subscriber
        valid_event_names = [
          'subscription_new',
          'subscription_create',
          'subscription_update',
          'subscription_show',
          'subscription_edit',
          'subscription_cancel',
          'subscription_cancelled',
          'subscription_reactivate'
        ]

        resource_filter = resource.is_a?(User) ? 'user_id' : 'school_id'

        event_labels_fmt = valid_event_names.map { |label| "'#{label}'" }.join(', ')
        query = <<-SQL
          SELECT
            event,
            timestamp,
            JSONExtractString(properties, 'school_id') as school_id,
            JSONExtractString(properties, 'user_id') as user_id,
            JSONExtractString(properties, 'data') as data
          FROM events
          WHERE
            event IN [#{event_labels_fmt}]
            AND (
              #{resource_filter} IS NOT NULL AND #{resource_filter} != '' AND #{resource_filter} = '#{resource.id}'
            )
          ORDER BY timestamp DESC
          LIMIT 100
        SQL
        data = TrackingService.query(query)
        keys = data['columns']
        values = data['results']
        results = values.map { |row| keys.zip(row).to_h }
        { cached_date: Time.current, events: results }
      end

      render json: events, status: :ok
    end

    private

    def calculate_size_distribution
      size_counts = {
        'Individual' => 0,
        'Individual Monthly' => 0,
        'Small School' => 0,
        'Medium School' => 0,
        'Large School' => 0,
        'Very Large School' => 0,
        'Unknown' => 0
      }

      # Count active subscribers by their actual subscription size from Stripe data
      Subscriber.includes(:subscriber).where(subscription_status: ['active', 'active_until_period_end']).each do |subscriber|
        active_products_data = subscriber.active_products || {}
        subscription_size = active_products_data['school_size']
        
        case subscription_size
        when 'individual'
          size_counts['Individual'] += 1
        when 'individual_monthly'
          size_counts['Individual Monthly'] += 1
        when 'small'
          size_counts['Small School'] += 1
        when 'medium'
          size_counts['Medium School'] += 1
        when 'large'
          size_counts['Large School'] += 1
        when 'very_large'
          size_counts['Very Large School'] += 1
        else
          # Fallback to subscriber type if no size data
          if subscriber.subscriber.is_a?(User)
            size_counts['Individual'] += 1
          elsif subscriber.subscriber.is_a?(School)
            size_counts['Small School'] += 1
          else
            size_counts['Unknown'] += 1
          end
        end
      end

      # Remove empty categories
      size_counts.reject { |_, count| count == 0 }
    end

    def calculate_subscription_metrics
      active_subscribers = Subscriber.where(subscription_status: ['active', 'active_until_period_end'])
      
      # Calculate Monthly Recurring Revenue using real Stripe data
      mrr = 0
      total_annual_revenue = 0
      
      active_subscribers.each do |subscriber|
        # Get the synced subscription data
        active_products_data = subscriber.active_products || {}
        
        # Extract amount and interval from synced data
        amount = active_products_data['amount']&.to_f || 0
        interval = active_products_data['interval'] # 'month' or 'year'
        
        if amount > 0
          case interval
          when 'month'
            mrr += amount
            total_annual_revenue += amount * 12
          when 'year'
            mrr += amount / 12.0
            total_annual_revenue += amount
          else
            # If no interval data, skip this subscriber
            Rails.logger.warn "No interval data for subscriber #{subscriber.id}"
          end
        end
      end

      # Calculate Lifetime Value (simplified as MRR * 36 months average retention)
      ltv = mrr * 36

      {
        monthly_recurring_revenue: "£#{format_currency(mrr)}",
        lifetime_value: "£#{format_currency(ltv)}"
      }
    end

    def format_currency(amount)
      amount.round.to_s.reverse.gsub(/(\d{3})(?=\d)/, '\\1,').reverse
    end
  end
end
