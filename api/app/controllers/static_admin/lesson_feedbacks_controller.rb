module StaticAdmin
  class LessonFeedbacksController < StaticAdminController
    include PaginationHelper

    admin_section :curriculum

    # GET /admin/lesson-feedback
    def index
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @lesson_feedbacks = NewPresentationFeedback
                            .where(associate_to: "template")
                            .left_joins(:user)
                            .includes(:user) 
                            .joins("LEFT JOIN lesson_templates ON lesson_templates.id = new_presentation_feedbacks.associate_to_id")
                            .select("new_presentation_feedbacks.*, lesson_templates.name AS lesson_template_name")

      
      @template_options = Lesson::Template
                            .where(id: @lesson_feedbacks.distinct.pluck(:associate_to_id))
                            .order(:name)
                            .pluck(:name, :id)

      if params[:lesson].present?
        @lesson_feedbacks = @lesson_feedbacks.where(associate_to_id: params[:lesson])
      end

      case params[:scope]
      when 'positive'
        @lesson_feedbacks = @lesson_feedbacks.where(rating: 1)
      when 'negative'
        @lesson_feedbacks = @lesson_feedbacks.where(rating: 0)
      end

      if params[:query].present?
        @lesson_feedbacks = @lesson_feedbacks.where(
          "new_presentation_feedbacks.feedback ILIKE :query OR users.name ILIKE :query OR users.email ILIKE :query",
          query: "%#{params[:query]}%"
        )
      end

      sort_column = sort == 'name' ? 'lesson_templates.name' : "new_presentation_feedbacks.#{sort}"
      @lesson_feedbacks = @lesson_feedbacks.order("#{sort_column} #{order}")
      @lesson_feedbacks = safe_paginate(@lesson_feedbacks, per_page: 50, page: params[:page])
    end

  end
end