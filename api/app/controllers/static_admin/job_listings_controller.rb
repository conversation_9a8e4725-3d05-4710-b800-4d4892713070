module StaticAdmin
  class StaticAdmin::JobListingsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_job_listing, only: [:show, :edit, :update, :destroy]
    
    admin_section :website

    # GET /admin/job_listings
    def index
      sortable_columns = ['title', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @job_listings = JobListing.all
      if params[:query].present?
        @job_listings = @job_listings.where("title ILIKE :query", query: "%#{params[:query]}%")
      end

      case params[:scope]
      when 'published'
        @job_listings = @job_listings.published
      end

      @job_listings = safe_paginate(@job_listings.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/job_listings/new
    def new
      @job_listing = JobListing.new
    end

    # POST /admin/job_listings
    def create
      @job_listing = JobListing.new(job_listing_params)

      if @job_listing.save
        redirect_to [:edit, :admin, @job_listing], notice: 'Job listing was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/job_listings/:id/edit
    def edit; end

    # PATCH/PUT /admin/job_listings/:id
    def update
      if @job_listing.update(job_listing_params)
        redirect_to [:edit, :admin, @job_listing], notice: 'Job listing was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/job_listings/:id
    def destroy
      @job_listing.destroy
      redirect_to admin_job_listings_url, notice: 'Job listing was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_job_listing
      @job_listing = JobListing.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def job_listing_params
      params.require(:job_listing).permit(:title, :published, :body, :length, :end_date, :salary)
    end

  end
end
