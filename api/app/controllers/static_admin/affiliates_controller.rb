module StaticAdmin
    class StaticAdmin::AffiliatesController < StaticAdminController
      include Pa<PERSON><PERSON>Helper
      before_action :set_affiliate, only: [:show, :edit, :update, :destroy]

      admin_section :marketing
  
      # GET /admin/affiliates
      def index
        sortable_columns = ['name', 'created_at']
        sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
        order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
  
        @affiliates = Affiliate.all
        if params[:query].present?
          @affiliates = @affiliates.where("name ILIKE :query", query: "%#{params[:query]}%")
        end
  
        @affiliates = safe_paginate(@affiliates.order("#{sort} #{order}"), page: params[:page])
      end
  
      # GET /admin/affiliates/new
      def new
        @affiliate = Affiliate.new
      end
  
      # POST /admin/affiliates
      def create
        @affiliate = Affiliate.new(affiliate_params)
  
        if @affiliate.save
          redirect_to [:edit, :admin, @affiliate], notice: 'Affiliate was successfully created.'
        else
          render :new
        end
      end
  
      # GET /admin/affiliates/:id/edit
      def edit; end
  
      # PATCH/PUT /admin/affiliates/:id
      def update
        if @affiliate.update(affiliate_params)
          redirect_to [:edit, :admin, @affiliate], notice: 'Affiliate was successfully updated.'
        else
          render :edit
        end
      end

      # GET /admin/affiliates/get_schools
      def get_schools
        render json: {
          schools: School.order('name asc').pluck(:name, :id)
        }
      end
  
      # DELETE /admin/affiliates/:id
      def destroy
        @affiliate.destroy
        redirect_to admin_affiliates_url, notice: 'Affiliate was successfully deleted.'
      end
  
      private
  
      # Use callbacks to share common setup or constraints between actions.
      def set_affiliate
        @affiliate = Affiliate.find(params[:id])
      end
  
      # Only allow a list of trusted parameters through.
      def affiliate_params
        params.require(:affiliate).permit(:name, school_ids: [])
      end
  
    end
  end
  