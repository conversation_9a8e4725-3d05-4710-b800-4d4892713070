module StaticAdmin
  class StaticAdmin::ImagesController < StaticAdminController
    include PaginationHelper
    before_action :set_image, only: [:show, :edit, :update, :reanalyze]

    admin_section :content
  
    def index
      @images = Image.all
      @images = @images.search(params[:query]) if params[:query].present?
      @images = safe_paginate(@images.order(created_at: :desc), page: params[:page], per_page: 30)
    end

    def show
      @usage_count = count_image_usage(@image.fileboy_image_id)
    end

    def edit
    end

    def update
      if @image.update(image_params)
        redirect_to admin_images_path, notice: 'Image updated successfully.'
      else
        render :edit
      end
    end

    def reanalyze
      @image.analyze_with_ai!
      redirect_to admin_images_path, notice: 'Image queued for re-analysis.'
    end

    private

    def set_image
      @image = Image.find(params[:id])
    end

    def image_params
      params.require(:image).permit(:title, :keywords)
    end

    def count_image_usage(fileboy_id)
      count = 0
      count += Lesson::Slide.where(fileboy_image_id: fileboy_id).count
      count += Lesson::Template.where(fileboy_image_id: fileboy_id).count
      count += Article.where(fileboy_image_id: fileboy_id).count
      count
    end
  end
end
