module StaticAdmin
  class StaticAdmin::LessonsController < StaticAdminController
    layout 'admin'
    include Fileboy
    include ActionView::Helpers::TextHelper
    require 'net/http'
    require 'uri'

    # GET /admin/lesson-templates
    def index; end

    def ai_lesson_importer; end

    def request_ai_lesson
      @lesson_id = params[:lesson_id]

      begin
        ai_lesson = get_ai_lesson(@lesson_id)
        parsed_lesson = parse_ai_lesson(ai_lesson)

        @lesson = parsed_lesson[:lesson]
        @keywords = parsed_lesson[:keyword_data]
        @tags = parsed_lesson[:tags]
        @intro_slide = parsed_lesson[:intro_slide]
        @slides = parsed_lesson[:slides]

        render 'import'
      rescue
        redirect_to ai_import_admin_lessons_path, alert: 'No Lesson Found'
      end
    end

    def import_ai_lesson
      @lesson_id = ai_lesson_params[:lesson_id]

      ai_lesson = get_ai_lesson(@lesson_id)
      parsed_lesson = parse_ai_lesson(ai_lesson)

      @lesson = parsed_lesson[:lesson]
      @lesson.machine_name = ai_lesson_params[:machine_name]

      resources = parsed_lesson[:resources]
      @keywords = parsed_lesson[:keyword_data]
      @tags = parsed_lesson[:tags]
      @intro_slide = parsed_lesson[:intro_slide]
      @slides = parsed_lesson[:slides]
      slide_keyword_relation = parsed_lesson[:slide_keyword_relation]

      ActiveRecord::Base.transaction do
        if @lesson.save
          lesson_image_id = upload_image_from_url(parsed_lesson[:lesson_image]) if parsed_lesson[:lesson_image].present?

          save_recommended_careers(resources[:careers])
          save_tags(@tags)
          save_slides(@intro_slide, lesson_image_id, @slides, @lesson.id)

          @lesson.update(fileboy_image_id: lesson_image_id)
          @lesson.update(keywords: save_keywords(@keywords, @lesson.id, slide_keyword_relation))

          redirect_to "/a/lesson-templates/#{@lesson.id}"
        else
          Rails.logger.error "Failed to save lesson: #{@lesson.errors.full_messages.join(", ")}"
          render 'import', :lesson_id => @lesson_id
        end
      end
    end

    private

    def get_ai_lesson id
      uri = URI.parse("https://www.developingexperts.com/ai/lessons/#{id}/presentation")      
      response = Net::HTTP.get_response(uri)

      puts response.body

      JSON.parse(response.body)
    end

    def parse_ai_lesson lesson
      resources = {
        careers: Career.where(id: (lesson['DeCareerIds'])),
        videos: Video.where(id: (lesson['DeVideoIds']))
      }

      new_lesson = Lesson::Template.new(
        name: lesson['Title'],
        objectives: lesson['LessonObjectives'],
        plan_national_curriculum: lesson['NationalCurriculum'],
        teacher_mastery: format_string_for_html(lesson['TeacherMastery']),

        use_2022_lesson_plan: true,
        intent: parse_intent(lesson),
        new_lesson_plan_resources: parse_resources(resources),
        implementation: parse_implementation(lesson),
        impact_assessment: parse_impact_assignment(lesson),
      )

      {
        lesson: new_lesson,
        resources: resources,
        keyword_data: parse_keywords(lesson['Keywords'], new_lesson.id),
        tags: parse_tags(lesson['Tags']),
        intro_slide: parse_intro_slide({ title: lesson['Title'], image: lesson['Image'], thumbnail: lesson['ImageThumb'] }, new_lesson.id, new_lesson.name),
        slides: parse_slides(lesson['LessonSlides'], new_lesson.id, new_lesson.name),
        slide_keyword_relation: get_keyword_info(lesson['LessonSlides']),
        lesson_image: lesson['Image']
      }
    end

    def parse_intent lesson
      lesson_objectives = lesson['LessonObjectives'].map { |str| "<li>#{str}</li>" }

      "<h3>Lesson Outcomes</h3>
            <ul>#{lesson_objectives.join('')}</ul>
            <h3>Lesson Outcomes</h3>
            <p>#{lesson['NationalCurriculum']}</p>
            "
    end

    def parse_resources resources
      videos = resources[:videos].map { |video| "<li>#{video.name}</li>" }
      careers = resources[:careers].map { |career| "<li>#{career.name}</li>" }

      resources = ""

      resources << "<h3>Videos</h3><ul>#{videos.join('')}</ul>" if videos.present?
      resources << "<h3>Careers</h3><ul>#{careers.join('')}</ul>" if careers.present?

      resources
    end

    def parse_implementation lesson
      str = ""
      str <<  "<h3>Starter</h3><p>#{lesson['StarterActivity']}</p>" if lesson['StarterActivity'].present?
      str <<  "<h3>Main Teaching</h3><p>#{lesson['PresentationExplanation']}</p>" if lesson['PresentationExplanation'].present?
      str <<  "<h3>Mission Assignment</h3><p>#{lesson['MissionAssignment']}</p>" if lesson['MissionAssignment'].present?

      str
    end

    def parse_impact_assignment lesson
      str = ""
      str <<  "<h3>Plenary</h3><p>#{format_string_for_html(simple_format(lesson['Plenary']))}</p>" if lesson['Plenary'].present?
      str <<  "<h3>Teacher Mastery</h3><p>#{format_string_for_html(simple_format(lesson['TeacherMastery']))}</p>" if lesson['TeacherMastery'].present?
      
      str
    end

    def parse_keywords keywords, lesson_id
      keywords.map do |keyword|
        new_keyword = Lesson::Keyword.new(name: keyword['Name'])

        new_keyword.body = keyword['Body'] unless new_keyword.body.present?
        new_keyword.image_url = keyword['Image']

        quiz_question = QuizOld::Question.new(lesson_template_id: lesson_id, body: keyword['Question'], answer: keyword['Answer'], lesson_keyword_id: new_keyword.id)

        { keyword: new_keyword, question: quiz_question }
      end
    end

    def parse_tags tags
      tags.map do |tag|
        CareerTag.find_or_create_by(name: tag)
      end
    end

    def parse_intro_slide slide, lesson_id, lesson_name
      new_slide = Lesson::Slide.new()

      new_slide.slide_type = 'intro'
      new_slide.intro_text = slide[:title]
      new_slide.weight = 0
      new_slide.image_url = slide[:image]
      new_slide.image_thumbnail_url = slide[:thumbnail]

      new_slide
    end

    def parse_slides slides, lesson_id, lesson_name
      slides.map do |slide|
        new_slide = Lesson::Slide.new()

        if slide['Type'] == 'keyword'
          new_slide.slide_type = 'keywords'
        elsif slide['Type'] == 'expertVideo'
          new_slide.slide_type = 'expert'
          new_slide.video_id = slide['DeVideoID']
        else
          new_slide.slide_type = slide['Type']
        end
        new_slide.body = slide['Body']

        if slide['Video'].present?
          new_slide.video_url = slide['Video']
          new_slide.slide_type = 'video'
          new_slide.video_id = slide['DeVideoID']
        end

        new_slide.weight = slide['Weight'] + 1
        new_slide.image_url = slide['Image']
        new_slide.image_thumbnail_url = slide['ImageThumb']

        new_slide
      end
    end

    def get_keyword_info slides
      filtered_slides = slides.reject { |slide| slide['Keyword'].strip.empty? }
      filtered_slides.map { |slide| { weight: slide['Weight'] + 1, keyword: slide['Keyword'] } }
    end

    def save_recommended_careers careers
      careers.each do |career|
        TemplateRecommendedCareer.create!(lesson_template_id: @lesson.id, career_id: career.id)
      end
    end

    def save_tags tags
      tags.each do |tag|
        CareerTagging.create(taggable_id: @lesson.id, taggable_type: 'Lesson::Template', career_tag_id: tag.id)
      end
    end

    def save_keywords keywords, lesson_id, slide_keyword_relation
      keywords.map do |data|
        keyword = data[:keyword]
        question = data[:question]

        keyword.template_id = lesson_id
        keyword.fileboy_image_id = upload_image_from_url(keyword.image_url) if keyword.image_url.present?
        keyword.save

        question.lesson_template_id = lesson_id
        question.lesson_keyword_id = keyword.id

        slide = slide_keyword_relation.find { |hash| hash[:keyword] == keyword.name }
        if slide.present?
          slide_id = Lesson::Template.find(lesson_id).slides.find_by(weight: slide[:weight]).id
          question.lesson_slide_id = slide_id
        end

        question.save

        data[:keyword]
      end
    end

    def save_slides intro_slide, intro_slide_image_id, slides, lesson_id
      intro_slide.template_id = lesson_id
      intro_slide.fileboy_image_id = intro_slide_image_id if intro_slide_image_id.present?
      intro_slide.save

      slides.each do |slide|
        slide.template_id = lesson_id
        slide.fileboy_image_id = upload_image_from_url(slide.image_url) if slide.image_url.present?

        if slide.video_id.present?
          slide.fileboy_video_id = get_video_fileboy_id(slide.video_id)
          slide.video_id = nil if slide.fileboy_video_id.present?
        end

        slide.save
      end
    end

    def format_string_for_html str
      formatted_str = str
      formatted_str = formatted_str.gsub(/\\n/, '<br>')
      formatted_str = formatted_str.gsub(/\*\*(.*?)\*\*/, '<strong>\1</strong>')

      formatted_str.html_safe
    end

    def ai_lesson_params
      params.require(:lesson_template).permit(:lesson_id, :machine_name)
    end

    def get_video_fileboy_id video_id
      uri = URI.parse("https://www.developingexperts.com/api/v2/videos/#{video_id}")      
      response = Net::HTTP.get_response(uri)
      parsed_response = JSON.parse(response.body)

      return parsed_response['external_id'] if parsed_response['source'] == 'fileboy'

      return nil
    end

  end
end
  