module StaticAdmin
  class StripeSubscriptionsController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_ai_subscription, only: %i[edit update destroy sync resume cancel force_cancel]

    admin_section :misc

    # GET /admin/ai-subscriptions
    def index
      sortable_columns = %w[status created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @subscriptions = StripeSubscription.includes([:user]).all.left_joins(:user)
      if params[:query].present?
        @subscriptions = @subscriptions.where('users.email ILIKE :query', query: "%#{params[:query]}%")
      end

      @subscriptions = safe_paginate(@subscriptions.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/ai-subscriptions/new
    def new
      @subscription = StripeSubscription.new
    end

    # POST /admin/ai-subscriptions
    def create
      user_email = params[:stripe_subscription][:user_email]
      user = User.find_by(email: user_email)
      if user.nil?
        @subscription = StripeSubscription.new(subscription_params)
        @subscription.errors.add(:user_email, 'not found')
        return render :new
      end
      params = subscription_params
      params[:user_id] = user.id
      @subscription = StripeSubscription.new(params)

      if @subscription.save
        redirect_to [:edit, :admin, @subscription], notice: 'Subscription was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/ai-subscriptions/:id/edit
    def edit; end

    # PATCH/PUT /admin/ai-subscriptions/:id
    def update
      if @subscription.update(subscription_params)
        redirect_to [:edit, :admin, @subscription], notice: 'Subscription was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/ai-subscriptions/:id
    def destroy
      begin
        @subscription.destroy
        redirect_to admin_stripe_subscriptions_path, notice: 'Subscription was successfully deleted.'
      rescue => e
        redirect_to [:edit, :admin, @subscription], alert: e.message
      end
    end

    def sync
      @subscription.sync_down(force: true)
      redirect_to [:edit, :admin, @subscription], notice: 'Subscription synced successfully.'
    end

    def resume
      @subscription.resume
      redirect_to [:edit, :admin, @subscription], notice: 'Subscription resumed successfully.'
    end

    def cancel
      @subscription.cancel
      redirect_to [:edit, :admin, @subscription], notice: 'Subscription prepared to cancel successfully.'
    end

    def force_cancel
      # This will immedatiely cancel the subscription on stripe, and cannot be undone.
      # once a subscription is in the "cancelled" state, it cannot be resumed.
      raise 'Subscription not found' unless @subscription.stripe_subscription_id.present?
      raise 'Subscription already cancelled' if @subscription.stripe_subscription&.status == 'cancelled'
      @subscription.cancel_now
      redirect_to [:edit, :admin, @subscription], notice: 'Subscription cancelled successfully.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_ai_subscription
      @subscription = StripeSubscription.find(params[:id])
      @stripe_subscription = @subscription.stripe_subscription
    end

    # Only allow a list of trusted parameters through.
    def subscription_params
      params.require(:stripe_subscription).permit(
        :user_id,
        :subscription_type,
        :free_subscription
      )
    end
  end
end
