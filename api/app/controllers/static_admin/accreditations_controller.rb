module StaticAdmin
  class StaticAdmin::AccreditationsController < StaticAdminController
    include PaginationHelper
    include Fileboy

    before_action :set_accreditation, only: [:show, :edit, :update, :destroy]

    admin_section :website

    # GET /admin/accreditations
    def index
      @accreditations = safe_paginate(Accreditation.all.order(weight: :asc), page: params[:page])
    end

    # GET /admin/accreditations/new
    def new
      @accreditation = Accreditation.new
    end

    # POST /admin/accreditations
    def create
      @accreditation = Accreditation.new(accreditation_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @accreditation.fileboy_image_id = fileboy_image_id if fileboy_image_id
      @accreditation.weight = Accreditation.maximum(:weight).to_i + 1

      if @accreditation.save
        redirect_to [:edit, :admin, @accreditation], notice: 'Accreditation was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/accreditations/:id/edit
    def edit; end

    # PATCH/PUT /admin/accreditations/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @accreditation.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @accreditation.update(accreditation_params)
        redirect_to [:edit, :admin, @accreditation], notice: 'Accreditation was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/accreditations/:id
    def destroy
      @accreditation.destroy
      redirect_to admin_accreditations_url, notice: 'Accreditation was successfully deleted.'
    end

    def update_weights
      params[:accreditation].each_with_index do |id, index|
        Accreditation.where(id: id).update_all(weight: index + 1)
      end
      head :ok
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_accreditation
      @accreditation = Accreditation.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def accreditation_params
      params.require(:accreditation).permit(:name)
    end
  end
end