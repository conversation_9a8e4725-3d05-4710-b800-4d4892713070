module StaticAdmin
    class StaticAdmin::FormsController < StaticAdminController
      include PaginationHelper
      include Fileboy
      before_action :set_form, only: [:show, :edit, :update, :destroy]
      before_action :set_school, only: [:new, :create, :edit, :update, :destroy]
      before_action :set_teachers, only: [:new, :create, :edit, :update]
      before_action :set_days, only: [:new, :create, :edit, :update]
  
      # GET /admin/forms
      def index
        sortable_columns = ['name', 'created_at']
        sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
        order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
  
        @forms = Form.all

        @forms = safe_paginate(@forms.order("#{sort} #{order}"), page: params[:page], per_page: 30)
      end
  
      # GET /admin/school/:school_id/forms/new
      def new
        @form = Form.new
      end
  
      # POST /admin/school/:school_id/forms
      def create
        @form = Form.new(form_params)

        @form.school = @school

        lesson_weekdays = (params[:form][:lesson_weekdays] || []).select(&:present?).map { |day| [day, 1] }.to_h
        @form.lesson_weekdays = lesson_weekdays

        if @form.save
          redirect_to classes_admin_static_school_path(@school), notice: 'Class was successfully created.'
        else
          render :new
        end
      end
  
      # GET /admin/school/:school_id/forms/:id/edit
      def edit; end
  
      # PATCH/PUT /admin/forms/:id
      def update
        lesson_weekdays = (params[:form][:lesson_weekdays] || []).select(&:present?).map { |day| [day, 1] }.to_h
        @form.lesson_weekdays = lesson_weekdays
        
        if @form.update(form_params)
          redirect_to classes_admin_static_school_path(@school), notice: 'Class was successfully updated.'
        else
          render :edit
        end
      end
  
      # DELETE /admin/forms/:id
      def destroy
        @form.destroy
        redirect_to classes_admin_static_school_path(@school), notice: 'Class was successfully deleted.'
      end
  
      private
  
      # Use callbacks to share common setup or constraints between actions.
      def set_form
        @form = Form.find(params[:id])
      end

      def set_school
        @school = School.find(params[:static_school_id])
      end

      def set_teachers
        @teachers = School.joins(:teachers).find(params[:static_school_id]).teachers
      end

      def set_days
        @days = [['Monday', '1'],
        ['Tuesday', '2'],
        ['Wednesday', '3'],
        ['Thursday', '4'],
        ['Friday', '5'],
        ['Saturday', '6'],
        ['Sunday', '7']].sort_by { |d| d[1] }
      end
  
      # Only allow a list of trusted parameters through.
      def form_params
        params.require(:form).permit(:name, :lesson_weekdays, :export_markbook_monthly, teacher_ids: [])
      end
  
    end
end