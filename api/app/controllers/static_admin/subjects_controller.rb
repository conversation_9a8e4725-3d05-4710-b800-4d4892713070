module StaticAdmin
  class StaticAdmin::SubjectsController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_subject, only: [:show, :edit, :update, :destroy]
    before_action :set_curricula, only: [:new, :create, :edit, :update]
    before_action :set_years, only: [:new, :edit]

    admin_section :curriculum

    # GET /admin/library-subjects
    def index
      @curricula = NewLibrary::Curriculum.all.order(:name).pluck(:id, :name)
      @selected_curriculum = NewLibrary::Curriculum.all.find_by(id: params[:curriculum_id]) if params[:curriculum_id].present?
      all_subjects = @selected_curriculum.present? ? @selected_curriculum.subjects : NewLibrary::Subject.all


      sortable_columns = ['name', 'curriculum', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'curriculum'

      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @subjects = all_subjects.includes([:curriculum, :years])
      if params[:query].present?
        @subjects = @subjects.where("new_library_subjects.name ILIKE :query", query: "%#{params[:query]}%")
      end

      if sort == 'curriculum'
        @subjects = @subjects.joins(:curriculum).order("new_library_curricula.name #{order}, new_library_subjects.name ASC")
      else
        @subjects = @subjects.order("#{sort} #{order}")
      end

      @subjects = safe_paginate(@subjects, page: params[:page])
    end

    # GET /admin/library-subjects/new
    def new
      @subject = NewLibrary::Subject.new
    end

    # POST /admin/library-subjects
    def create
      ActiveRecord::Base.transaction do
        @subject = NewLibrary::Subject.new(subject_params)

        year_ids = JSON.parse(params[:year_ids]).map(&:to_i)
        @subject.year_ids = year_ids

        year_ids.each_with_index do |id, i|
          NewLibrary::Year.find(id).update(weight: i)
        end

        if @subject.save
          redirect_to edit_admin_subject_path(@subject), notice: 'Subject was successfully created.'
        else
          render :new
        end
      end
    end

    # GET /admin/library-subjects/:id/edit
    def edit; end

    # PATCH/PUT /admin/library-subjects/:id
    def update
      ActiveRecord::Base.transaction do
        year_ids = JSON.parse(params[:year_ids]).map(&:to_i)
        @subject.year_ids = year_ids

        year_ids.each_with_index do |id, i|
          NewLibrary::Year.find(id).update(weight: i)
        end

        if @subject.update(subject_params)
          redirect_to edit_admin_subject_path(@subject), notice: 'Subject was successfully updated.'
        else
          render :edit
        end
      end
    end

    # DELETE /admin/library-subjects/:id
    def destroy
      @subject.destroy
      redirect_to admin_subjects_url, notice: 'Subject was successfully deleted.'
    end

    private

    def set_years
      @years = NewLibrary::Year
               .includes(:subject)
               .joins('LEFT OUTER JOIN new_library_subjects ON new_library_years.subject_id = new_library_subjects.id')
               .select('new_library_years.*, new_library_subjects.name AS subject_name')
               .order('subject_name ASC, new_library_years.name ASC')
    end

    def set_subject
      @subject = NewLibrary::Subject.find(params[:id])
    end

    def set_curricula
      @curricula = NewLibrary::Curriculum.all.order("name ASC")
    end

    def subject_params
      params.require(:new_library_subject).permit(:name, :curriculum_id)
    end

  end
end
