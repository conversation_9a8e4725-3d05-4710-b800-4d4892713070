# frozen_string_literal: true

module StaticAdmin
  class ToursController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_tour, only: %i[show edit update destroy update_scene]
    before_action :set_campaigns, only: %i[edit show]

    admin_section :content

    # GET /admin/tours
    def index
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @tours = Tour.left_joins(:organisation, :campaign).includes(%i[organisation campaign]).all
      @tours = @tours.where('tours.name ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      @tours = safe_paginate(@tours.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/tours/new
    def new
      @tour = Tour.new
    end

    # POST /admin/tours
    def create
      @tour = Tour.new(tour_params)
      if @tour.save
        redirect_to [:edit, :admin, @tour], notice: 'Tour was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/tours/:id/edit
    def edit; end

    def update_scene
      sceneKey = params[:sceneKey]
      sceneData = params[:tour]

      if params[:tour][:edit_raw] == '1'
        @tour.data[sceneKey] = JSON.parse(params[:raw])
      else
        @tour.data[sceneKey][:title] = sceneData[:title]
        @tour.data[sceneKey]['video']['videoSource'] = sceneData[:source]
        @tour.data[sceneKey]['video']['videoId'] = sceneData[:external_id]
        video = handle_video_for(sceneKey, sceneData[:source], sceneData[:external_id])
        @tour.data[sceneKey]['video']['videoRecordId'] = video.id
      end

      @tour.save

      redirect_to [:edit, :admin, @tour], notice: 'Scene updated.'
    end

    # PATCH/PUT /admin/tours/:id
    def update
      if @tour.fileboy_image_id != params[:fileboy_image_id] && params[:fileboy_image_id].present?
        fileboy_image_id = upload_image params[:fileboy_image_id]
        @tour.fileboy_image_id = fileboy_image_id if fileboy_image_id
      end
      if @tour.update(tour_params)
        redirect_to [:edit, :admin, @tour], notice: 'Tour was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/tours/:id
    def destroy
      @tour.destroy
      redirect_to admin_videos_url, notice: 'Tour was successfully deleted.'
    end

    # GET /admin/tours/build_sheet
    def build_sheet
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      tours = Tour.left_joins(:organisation, :campaign).all
      tours = tours.where('tours.name ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      tours = tours.order("#{sort} #{order}")

      Axlsx::Package.new do |p|
        p.workbook.add_worksheet(name: '360 Tours') do |sheet|
          bold = sheet.styles.add_style b: true
          top_row = ['Name', 'Available to pupils', 'Campaign', 'Organisation', 'Views', 'Created']
          sheet.add_row top_row, style: Array.new(top_row.count, bold)

          sheet.sheet_view.pane do |pane|
            pane.top_left_cell = 'B2'
            pane.state = :frozen_split
            pane.y_split = 1
            pane.x_split = 1
            pane.active_pane = :bottom_right
          end

          tours.each do |tour|
            sheet.add_row [
              tour.name,
              tour.available_to_pupils,
              tour.campaign&.name,
              tour.organisation&.name,
              tour.views,
              tour.created_at.strftime('%d %b %Y')
            ]
          end
        end

        timestamp = Time.now.strftime('%Y%m%d_%H%M%S')
        tempfile_path = "#{Rails.root}/tmp/360_tours_#{timestamp}.xlsx"

        p.serialize(tempfile_path)
        send_file(tempfile_path, filename: "360_tours_#{timestamp}.xlsx", type: 'application/vnd.ms-excel')
      end
    end

    def import
      @form = TourImportForm.new
    end

    def seed_tour
      data = params.require(:tour_import_form).permit(:tour_name, :organisation_name, :campaign_name, :data)
      @form = TourImportForm.new(data.to_h.merge({ fileboy_image: params.permit(:fileboy_image)[:fileboy_image] }))
      # Validate we have all required fields
      render :import and return unless @form.valid?

      # Parse & validate the data
      @form.parse_data
      render :import and return if @form.errors.any?

      # Upload the image & validate it uploaded
      @form.upload_fileboy_image
      render :import and return if @form.errors.any?

      # Actually create the Tour from parsed data
      @tour = Tour.seed_tour(
        @form.tour_data,
        **@form.to_seed_attributes
      )

      redirect_to edit_admin_tour_path(@tour)
    rescue => e
      Rails.logger.error(e)
      @form ||= TourImportForm.new
      @form.errors.add(:base, e.message)
      render :import
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_tour
      @tour = Tour.find(params[:id])
      @modalData = nil
    end

    # Only allow a list of trusted parameters through.
    def tour_params
      params.require(:tour).permit(:campaign_id, :name, :created_at, :updated_at, :available_to_pupils)
    end

    def set_campaigns
      @campaigns = Campaign.all
    end

    def handle_video_for(sceneName, video_source, external_video_id)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)
      video.name = sceneName if video.new_record? || video.name.blank?
      video.save if video.changed?
      video
    end
  end
end
