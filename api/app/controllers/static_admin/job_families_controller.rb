module StaticAdmin
  class StaticAdmin::JobFamiliesController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_job_family, only: [
      :edit, :update, :destroy,
      :careers,
      :myth_fact, :add_myth_fact, :update_myth_fact, :remove_myth_fact,
      :roles, :add_role, :update_role, :remove_role,
      :skills, :add_skill, :update_skill, :remove_skill,
      :progression, :add_progression_step, :update_progression_step, :remove_progression_step,
      :industries, :add_industry, :update_industry, :remove_industry,
      :workplaces, :add_workplace, :update_workplace, :remove_workplace
    ]

    admin_section :content

    # === CRUD ===
    def index
      @job_families = JobFamily.left_joins(:career_paths)
                                .select('job_families.*, COUNT(career_paths.id) AS career_paths_count')
                                .group('job_families.id')
      @job_families = @job_families.search(params[:query]) if params[:query].present?
      @job_families = safe_paginate(@job_families.order(created_at: :desc), page: params[:page], per_page: 30)
    end

     def new
      @job_family = JobFamily.new
    end

    def create
      @job_family = JobFamily.new(job_family_params)
      if @job_family.save
        redirect_to edit_admin_job_family_path(@job_family), notice: "Created"
      else
        render :new
      end
    end

    def edit; end

    def update
      if @job_family.update(job_family_params)
        redirect_to edit_admin_job_family_path(@job_family), notice: "Updated"
      else
        render :edit
      end
    end

    def destroy
      @job_family.destroy
      redirect_to admin_job_families_path, notice: "Deleted"
    end

    # === Careers ===
    def careers; end

    # === Myths & Facts ===
    def myth_fact; end

    def add_myth_fact
      @job_family.myths_facts ||= []
      @job_family.myths_facts << params.require(:myth_fact).permit(:myth, :fact).to_h
      save_and_redirect(:myth_fact)
    end

    def update_myth_fact
      index = params[:index].to_i
      @job_family.myths_facts[index] = params.require(:myth_fact).permit(:myth, :fact).to_h
      save_and_redirect(:myth_fact)
    end

    def remove_myth_fact
      @job_family.myths_facts.delete_at(params[:index].to_i)
      save_and_redirect(:myth_fact)
    end

    # === Roles ===
    def roles; end

    def add_role
      @job_family.roles ||= []
      @job_family.roles << role_params.to_h
      save_and_redirect(:roles)
    end

    def update_role
      @job_family.roles[params[:index].to_i] = role_params.to_h
      save_and_redirect(:roles)
    end

    def remove_role
      @job_family.roles.delete_at(params[:index].to_i)
      save_and_redirect(:roles)
    end

    # === Skills ===
    def skills; end

    def add_skill
      @job_family.skills ||= []
      @job_family.skills << skill_params.to_h
      save_and_redirect(:skills)
    end

    def update_skill
      @job_family.skills[params[:index].to_i] = skill_params.to_h
      save_and_redirect(:skills)
    end

    def remove_skill
      @job_family.skills.delete_at(params[:index].to_i)
      save_and_redirect(:skills)
    end

    # === Progression ===
    def progression; end

    def add_progression_step
      @job_family.progression ||= []
      @job_family.progression << progression_params.to_h
      save_and_redirect(:progression)
    end

    def update_progression_step
      @job_family.progression[params[:index].to_i] = progression_params.to_h
      save_and_redirect(:progression)
    end

    def remove_progression_step
      @job_family.progression.delete_at(params[:index].to_i)
      save_and_redirect(:progression)
    end

    # === Industries ===
    def industries; end

    def add_industry
      @job_family.industries ||= []
      @job_family.industries << params[:value]
      save_and_redirect(:industries)
    end

    def update_industry
      @job_family.industries[params[:index].to_i] = params[:value]
      save_and_redirect(:industries)
    end

    def remove_industry
      @job_family.industries.delete_at(params[:index].to_i)
      save_and_redirect(:industries)
    end

    # === Workplaces ===
    def workplaces; end

    def add_workplace
      @job_family.workplaces ||= []
      @job_family.workplaces << params[:value]
      save_and_redirect(:workplaces)
    end

    def update_workplace
      @job_family.workplaces[params[:index].to_i] = params[:value]
      save_and_redirect(:workplaces)
    end

    def remove_workplace
      @job_family.workplaces.delete_at(params[:index].to_i)
      save_and_redirect(:workplaces)
    end

    # === Shared ===
    private

    def set_job_family
      @job_family = JobFamily.find(params[:id])
    end

    def job_family_params
      params.require(:job_family).permit(:name, :slug, :description, :image, :fileboy_image_id)
    end

    def role_params
      params.require(:role).permit(:title, :summary, :icon, :salary_range, :level)
    end

    def skill_params
      params.require(:skill).permit(:category, :value)
    end

    def progression_params
      params.require(:progression).permit(:level, :title)
    end

    def save_and_redirect(tab)
      if @job_family.save
        redirect_to send("#{tab}_admin_job_family_path", @job_family), notice: "Updated successfully."
      else
        render tab
      end
    end
  end
end
