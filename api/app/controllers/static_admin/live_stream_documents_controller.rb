module StaticAdmin
  class LiveStreamDocumentsController < StaticAdminController
    include Fileboy
    before_action :set_live_stream
    before_action :set_live_stream_document, only: %i[edit update destroy]

    def new
      @document = @live_stream.live_stream_documents.new
    end

    def edit; end

    def create
      @document = @live_stream.live_stream_documents.new(live_stream_document_params)
      fileboy_id = upload_image params[:fileboy] if params[:fileboy].present?
      @document.fileboy_id = fileboy_id if fileboy_id
      authorize! :manage, @document
      if @document.save
        redirect_to documents_admin_live_stream_path(@live_stream), notice: 'Document created successfully.'
      else
        render :new
      end
    end

    def update
      authorize! :manage, @document
      fileboy_id = upload_image params[:fileboy] if params[:fileboy].present?
      data = live_stream_document_params
      data = data.merge(fileboy_id: fileboy_id) if fileboy_id
      if @document.update(data)
        redirect_to documents_admin_live_stream_path(@live_stream), notice: 'Document updated successfully.'
      else
        render :edit
      end
    end

    def destroy
      authorize! :manage, @document
      @document.destroy
      redirect_to documents_admin_live_stream_path(@live_stream), notice: 'Document deleted successfully.'
    end

    private

    def set_live_stream
      @live_stream = LiveStream.find(params[:live_stream_id])
    end

    def set_live_stream_document
      @document = @live_stream.live_stream_documents.find(params[:id])
    end

    def live_stream_document_params
      params.require(:live_stream_document).permit(:name, :fileboy_id, :published)
    end
  end
end
