module StaticAdmin
  class NewReferralsController < StaticAdminController
    include Pa<PERSON>ationHelper

    def index
      sortable_columns = %w[email created_at expires_at status]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @user_referrals = UserReferral.all

      # filter by status
      statuses = UserReferral.statuses
      @user_referrals = @user_referrals.where(status: params[:status]) if params[:status].present? && statuses.key?(params[:status])
      # filter by referrer
      @user_referrals = @user_referrals.left_joins(:referrer).where(referrer_id: params[:referrer_id]) if params[:referrer_id].present?
      # filter by email
      @user_referrals = @user_referrals.where('user_referrals.email ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      if params[:created_from].present?
        created_from = DateTime.parse(params[:created_from]) rescue nil
        @user_referrals = @user_referrals.where('user_referrals.created_at >= ?', created_from.beginning_of_day) if created_from
      end

      if params[:created_to].present?
        created_to = DateTime.parse(params[:created_to]) rescue nil
        @user_referrals = @user_referrals.where('user_referrals.created_at <= ?', created_to.end_of_day) if created_to
      end

      @user_referrals = @user_referrals.order(sort => order)
      @user_referrals = safe_paginate(@user_referrals, per_page: 50, page: params[:page])

      @referrer = User.find_by(id: params[:referrer_id]) if params[:referrer_id].present?
    end

    def search_users
      query = params[:query].strip
      @users = if query.present?
                 User.joins(:user_referrals)
                     .where('users.email ILIKE :query OR users.name ILIKE :query', query: "%#{query}%")
                     .order(:email)
                     .group('users.id')
                     .limit(10)
               else
                 []
               end

      fmt = @users.map do |user|
        {
          value: user.id,
          label: user.email,
          sub: user.name
        }
      end
      render json: fmt
    end

    def toggle_paid_at
      id = params.require(:id)
      filter_params = params.permit(:status, :referrer_id, :query, :page, :created_from, :created_to)
      return_to = admin_new_referrals_path(filter_params)

      referral = UserReferral.find_by(id: id)
      if referral.nil?
        flash[:error] = 'Referral not found.'
        redirect_to return_to
        return
      end

      if referral.paid_at
        referral.update!(paid_at: nil)
        flash[:success] = 'Referral marked as unpaid.'
      else
        referral.update!(paid_at: Time.current)
        flash[:success] = 'Referral marked as paid.'
      end
      redirect_to return_to
    end
  end
end
