module StaticAdmin
  class StaticAdmin::MotdsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_motd, only: [:show, :edit, :update, :destroy]
    before_action :set_initial_fields, only: [:new, :create, :edit, :update]

    admin_section :schools

    # GET /admin/motds
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @motds = Motd.all
      if params[:query].present?
        @motds = @motds.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @motds = safe_paginate(@motds.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/motds/new
    def new
      @motd = Motd.new
    end

    # POST /admin/motds
    def create
      @motd = Motd.new(motd_params)
      
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @motd.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @motd.save
        redirect_to [:edit, :admin, @motd], notice: 'Dashboard message was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/motds/:id/edit
    def edit; end

    # PATCH/PUT /admin/motds/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @motd.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @motd.update(motd_params)
        redirect_to [:edit, :admin, @motd], notice: 'Dashboard message was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/motds/:id
    def destroy
      @motd.destroy
      redirect_to admin_motds_url, notice: 'Dashboard message was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_motd
      @motd = Motd.find(params[:id])
    end

    def set_initial_fields
      @countries = Country.order("case when id = 1 then 0 else 1 end, name").pluck(:id, :name)
      @organisations = Organisation.order(:name).pluck(:id, :name)
    end

    # Only allow a list of trusted parameters through.
    def motd_params
      params.require(:motd).permit(:name, :body, :fileboy_image_id, :country_id, :display_tutors, :display_teachers, :display_admin_teachers, :organisation_id, :is_draft)
    end

  end
end
