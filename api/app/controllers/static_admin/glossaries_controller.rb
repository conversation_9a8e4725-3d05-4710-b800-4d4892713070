require 'nokogiri'

module StaticAdmin
  class StaticAdmin::GlossariesController < StaticAdminController
    helper ApplicationHelper
    include PaginationHelper
    include Fileboy

    before_action :set_glossary, only: [:show, :edit, :update, :destroy]

    admin_section :content

    # GET /admin/glossaries
    def index
      sortable_columns = ['name', 'short_description']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'name'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'asc'

      @glossaries = Glossary.all
      if params[:query].present?
        @glossaries = @glossaries.where("name ILIKE :query OR short_description ILIKE :query", query: "%#{params[:query]}%")
      end

      case params[:scope]
      when 'unpublished'
        @glossaries = @glossaries.unpublished
      when 'published'
        @glossaries = @glossaries.published
      when 'suggested'
        @glossaries = @glossaries.suggested
      end

      @glossaries = safe_paginate(@glossaries.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/glossaries/new
    def new
      @glossary = Glossary.new
    end

    # POST /admin/glossaries
    def create
      @glossary = Glossary.new(glossary_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @glossary.fileboy_image_id = fileboy_image_id if fileboy_image_id

      secondary_fileboy_image_id = upload_image params[:secondary_fileboy_image] if params[:secondary_fileboy_image].present?
      @glossary.secondary_fileboy_image_id = secondary_fileboy_image_id if secondary_fileboy_image_id

      if @glossary.save
        redirect_to [:edit, :admin, @glossary], notice: 'Glossary was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/glossaries/:id/edit
    def edit; end

    # PATCH/PUT /admin/glossaries/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @glossary.fileboy_image_id = fileboy_image_id if fileboy_image_id

      secondary_fileboy_image_id = upload_image params[:secondary_fileboy_image] if params[:secondary_fileboy_image].present?
      @glossary.secondary_fileboy_image_id = secondary_fileboy_image_id if secondary_fileboy_image_id

      if @glossary.update(glossary_params)
        redirect_to [:edit, :admin, @glossary], notice: 'Glossary was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/glossaries/:id
    def destroy
      @glossary.destroy
      redirect_to admin_glossaries_url, notice: 'Glossary was successfully deleted.'
    end

    def regenerate_audio
      body = request.body.read
      audio_content = JSON.parse(body)['audio']

      return unless audio_content.present?
      audio = Audio.find_by(content: audio_content)

      return unless audio.present?
      audio.save

      render json: { url: "https://www.developingexperts.com/file-cdn/files/get/#{audio.fileboy_audio_id}" }
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_glossary
      @glossary = Glossary.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def glossary_params
      params.require(:glossary).permit(:name, :short_description, :content, :published, :is_suggested, :usage, :image_caption, :secondary_content, :secondary_usage, :secondary_image_caption, :word_forms, :etymology, :task_question, :aqa_science_exam_question_answer, :slug, :fileboy_image_id, :secondary_fileboy_image_id)
    end
  end
end
