module StaticAdmin
  class StaticAdmin::OrganisationsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_organisation, only: %i[show edit update destroy dashboard dashboard_poll dashboard_top_careers]
    before_action :set_org_by_organisation_id, only: %i[dashboard_lessons dashboard_units dashboard_stats dashboard_schools]
    layout 'admin', except: %i[dashboard_lessons dashboard_units dashboard_stats dashboard_schools]
    before_action :set_careers, only: %i[new create edit update]

    admin_section :marketing

    # GET /admin/organisations
    def index
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @organisations = Organisation.all
      @organisations = @organisations.where('name ILIKE :query OR body ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      @organisations = @organisations.where(organisation_type: params[:scope]) if params[:scope].present?

      @organisations = safe_paginate(@organisations.order("#{sort} #{order}"), page: params[:page])

      @organisations_with_dashboard_data = Organisation.with_dashboard_data(@organisations.pluck(:id))
    end

    # GET /admin/organisations/new
    def new
      @organisation = Organisation.new
    end

    # POST /admin/organisations
    def create
      @organisation = Organisation.new(organisation_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @organisation.fileboy_image_id = fileboy_image_id if fileboy_image_id

      @organisation.update(white_label_content: params.dig(:white_label_content))
      @organisation.update(white_label_style: params.dig(:white_label_style))

      secondary_fileboy_image_id = upload_image params[:white_label_content_fileboyBackgroundImageId] if params[:white_label_content_fileboyBackgroundImageId].present?
      @organisation.white_label_content['fileboyBackgroundImageId'] = secondary_fileboy_image_id if secondary_fileboy_image_id

      tertiary_fileboy_image_id = upload_image params[:white_label_content_fileboySignInBackgroundImageId] if params[:white_label_content_fileboySignInBackgroundImageId].present?
      @organisation.white_label_content['fileboySignInBackgroundImageId'] = tertiary_fileboy_image_id if tertiary_fileboy_image_id

      @organisation.testimonial_video_id = handle_video_for(@organisation, params['organisation']['testimonial_video_source'], params['organisation']['external_testimonial_video_id'])
      @organisation.promotional_video_id = handle_video_for(@organisation, params['organisation']['promotional_video_source'], params['organisation']['external_promotional_video_id'])
      @organisation.expert_video_id = handle_video_for(@organisation, params['organisation']['expert_video_source'], params['organisation']['external_expert_video_id'])

      if @organisation.save
        redirect_to [:edit, :admin, @organisation], notice: 'Organisation was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/organisations/:id/edit
    def edit; end

    # PATCH/PUT /admin/organisations/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @organisation.fileboy_image_id = fileboy_image_id if fileboy_image_id

      @organisation.update(white_label_content: {}) if @organisation.white_label_content.nil?
      @organisation.update(white_label_style: params.dig(:white_label_style))

      secondary_fileboy_image_id = upload_image params[:white_label_content_fileboyBackgroundImageId] if params[:white_label_content_fileboyBackgroundImageId].present?
      @organisation.white_label_content['fileboyBackgroundImageId'] = secondary_fileboy_image_id if secondary_fileboy_image_id

      tertiary_fileboy_image_id = upload_image params[:white_label_content_fileboySignInBackgroundImageId] if params[:white_label_content_fileboySignInBackgroundImageId].present?
      @organisation.white_label_content['fileboySignInBackgroundImageId'] = tertiary_fileboy_image_id if tertiary_fileboy_image_id

      params[:organisation][:testimonial_video_id] = handle_video_for(@organisation, params['organisation']['testimonial_video_source'], params['organisation']['external_testimonial_video_id'])
      params[:organisation][:promotional_video_id] = handle_video_for(@organisation, params['organisation']['promotional_video_source'], params['organisation']['external_promotional_video_id'])
      params[:organisation][:expert_video_id] = handle_video_for(@organisation, params['organisation']['expert_video_source'], params['organisation']['external_expert_video_id'])

      if @organisation.update(organisation_params)
        redirect_to [:edit, :admin, @organisation], notice: 'Organisation was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/organisations/:id
    def destroy
      @organisation.destroy
      redirect_to admin_organisations_url, notice: 'Organisation was successfully deleted.'
    end

    def search
      organisations = Organisation.where('name ILIKE :query', query: "%#{params[:query]}%")
      fmt = organisations.map do |org|
        {
          value: org.id,
          label: org.name,
          sub: org.organisation_type.humanize
        }
      end
      render json: fmt
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_organisation
      @organisation = Organisation.find(params[:id])
    end

    def set_org_by_organisation_id
      @organisation = Organisation.find(params[:organisation_id])
    end

    def set_careers
      @careers = Career.pluck(:id, :name)
    end

    # Only allow a list of trusted parameters through.
    def organisation_params
      params.require(:organisation).permit(
        :organisation_type,
        :name,
        :alias,
        :body,
        :pupil_text,
        :fileboy_image_id,
        :promotional_video_id,
        :testimonial_video_id,
        :expert_video_id,
        :testimonial,
        :testimonial_by,
        :address_line_1,
        :address_line_2,
        :address_line_3,
        :town,
        :postcode,
        :county,
        :website,
        :phone_number,
        :fax,
        :lat,
        :lng,
        :use_white_labelling,
        :is_sponsor,
        :featured,
        :published,
        :testimonial_video_id,
        :promotional_video_id,
        :expert_video_id,
        :white_label_content_fileboyBackgroundImageId,
        :white_label_content_fileboySignInBackgroundImageId,
        white_label_style: %i[
          primary
          highlight
          secondary
          primaryAlt
          primaryLight
          secondaryLight
        ],
        white_label_content: %i[
          signUpAccount
          signUpDetails
          signUpTypeSelect
          fileboyBackgroundImageId
          fileboySignInBackgroundImageId
        ]
      )
    end

    def handle_video_for(model, video_source, external_video_id)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)
      video.name = model.name if video.new_record? || video.name.blank?
      video.save if video.changed?

      video.id
    end

    def set_dev
      @units = NewLibrary::Unit.where(id: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10])

      @lessons = Lesson::Template.where(id: [100, 200, 300, 400, 500, 600, 700])

      @schools = School.where.not(postcode: '').limit(14)
    end
  end
end
