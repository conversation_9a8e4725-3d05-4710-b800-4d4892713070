module StaticAdmin
  class StaticAdmin::CustomSignUpUrlsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_custom_sign_up_url, only: [:show, :edit, :update, :destroy]

    admin_section :marketing

    # GET /admin/custom_sign_up_urls
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @custom_sign_up_urls = CustomSignUpUrl.all
      if params[:query].present?
        @custom_sign_up_urls = @custom_sign_up_urls.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @custom_sign_up_urls = safe_paginate(@custom_sign_up_urls.order("#{sort} #{order}"), page: params[:page])

      @url_views = CustomSignUpUrlView.all.group(:custom_sign_up_url_id).count
      @url_uses = CustomSignUpUrlUse.all.group(:custom_sign_up_url_id).count
    end

    # GET /admin/custom_sign_up_urls/new
    def new
      @custom_sign_up_url = CustomSignUpUrl.new
    end

    # POST /admin/custom_sign_up_urls
    def create
      @custom_sign_up_url = CustomSignUpUrl.new(custom_sign_up_url_params)

      if @custom_sign_up_url.save
        redirect_to [:edit, :admin, @custom_sign_up_url], notice: 'Custom sign up URL was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/custom_sign_up_urls/:id/edit
    def edit; end

    # PATCH/PUT /admin/custom_sign_up_urls/:id
    def update
      if @custom_sign_up_url.update(custom_sign_up_url_params)
        redirect_to [:edit, :admin, @custom_sign_up_url], notice: 'Custom sign up URL was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/custom_sign_up_urls/:id
    def destroy
      @custom_sign_up_url.destroy
      redirect_to admin_custom_sign_up_urls_url, notice: 'Custom sign up URL was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_custom_sign_up_url
      @custom_sign_up_url = CustomSignUpUrl.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def custom_sign_up_url_params
      params.require(:custom_sign_up_url).permit(:name, :url, :published)
    end

  end
end
