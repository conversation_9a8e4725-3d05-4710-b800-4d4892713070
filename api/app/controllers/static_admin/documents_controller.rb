module StaticAdmin
  class StaticAdmin::DocumentsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_document, only: [:edit, :update, :reanalyze, :create_image]

    admin_section :content
  
    def index
      @documents = Document.includes(:documentable).all
      @documents = @documents.search(params[:query]) if params[:query].present?
      @documents = safe_paginate(@documents.order(created_at: :desc), page: params[:page], per_page: 30)
    end

    def edit; end

    def update
      if @document.update(document_params)
        redirect_to admin_documents_path, notice: 'Document updated successfully.'
      else
        render :edit
      end
    end

    def reanalyze
      @document.analyze_with_ai!
      redirect_to admin_documents_path, notice: 'Document queued for re-analysis.'
    end

    private

    def set_document
      @document = Document.find(params[:id])
    end

    def document_params
      params.require(:document).permit(:title, :keywords)
    end
  end
end
