# app/controllers/static_admin/mailchimp_controller.rb
module StaticAdmin
  class MailchimpController < StaticAdminController
    include PaginationHelper
    
    admin_section :marketing

    def index
      @stats = calculate_stats
      @recent_logs = MailchimpSyncLog.includes(:user)
                                    .recent
                                    .limit(50)
      
      # Filter logs if requested
      if params[:status].present?
        @recent_logs = @recent_logs.where(status: params[:status])
      end
      
      if params[:user_search].present?
        @recent_logs = @recent_logs.joins(:user)
                                  .where('users.name ILIKE ? OR users.email ILIKE ?', 
                                        "%#{params[:user_search]}%", 
                                        "%#{params[:user_search]}%")
      end

      @recent_logs = safe_paginate(@recent_logs, page: params[:page], per_page: 25)
      
      # Add field mapping data for sidebar
      @field_mappings = get_field_mappings
      @list_config = get_list_config
      @system_info = get_system_info
    end

    def sync_all
      count = User.where(type: ["Teacher", "IndividualUser"]).count
      
      MailchimpSyncJob.perform_later(force: true)
      
      flash[:notice] = "Queued sync for all #{count} eligible users. This may take several minutes."
      redirect_to admin_mailchimp_index_path
    end

    def retry_failed
      failed_user_ids = User.where(mailchimp_sync_status: 'failed').pluck(:id)
      
      if failed_user_ids.any?
        MailchimpSyncJob.perform_later(user_ids: failed_user_ids, force: true)
        flash[:notice] = "Queued retry for #{failed_user_ids.count} failed users."
      else
        flash[:info] = "No failed users to retry."
      end
      
      redirect_to admin_mailchimp_index_path
    end

    def sync_status
      render json: {
        queued_jobs: active_job_count,
        last_sync: last_successful_sync_time,
        stats: calculate_stats
      }
    end

    def preview_mapping
      @user = User.find(params[:id])
      @mapper = Mailchimp::UserMapper.new
      @mapped_data = @mapper.map_user_data(@user)
      
      # Get AI stats for display
      @ai_stats = Mailchimp::AiStatsService.fetch_for_users([@user]).first
      
      render json: {
        user: {
          id: @user.id,
          name: @user.name,
          email: @user.email,
          type: @user.type
        },
        ai_stats: @ai_stats&.to_h,
        mapped_data: @mapped_data,
        mailchimp_status: {
          last_sync: @user.mailchimp_last_sync,
          status: @user.mailchimp_sync_status,
          mailchimp_id: @user.mailchimp_id
        }
      }
    end

    private

    def get_field_mappings
      {
        user_attributes: Mailchimp::UserMapper.user_attribute_mappings,
        ai_attributes: Mailchimp::UserMapper.ai_attribute_mappings
      }
    end

    def calculate_stats
      total_users = User.where(type: ["Teacher", "IndividualUser"]).count
      synced_users = User.where(type: ["Teacher", "IndividualUser"])
                        .where.not(mailchimp_last_sync: nil).count
      failed_users = User.where(mailchimp_sync_status: 'failed').count
      pending_users = User.where(mailchimp_sync_status: 'pending').count
      
      {
        total_users: total_users,
        synced_users: synced_users,
        failed_users: failed_users,
        pending_users: pending_users,
        never_synced: total_users - synced_users,
        success_rate: total_users > 0 ? ((synced_users.to_f / total_users) * 100).round(1) : 0
      }
    end

    def active_job_count
      # This will depend on your job backend (Sidekiq, etc.)
      # For Sidekiq:
      if defined?(Sidekiq)
        Sidekiq::Queue.new.select { |job| job.klass == 'MailchimpSyncJob' }.count
      else
        "N/A"
      end
    rescue
      "N/A"
    end

    def last_successful_sync_time
      MailchimpSyncLog.successful.recent.first&.created_at
    end

    def get_list_config
      {
        teacher: {
          id: Mailchimp::ApiClient::LIST_IDS[:teacher],
          name: 'Teachers'
        },
        individual: {
          id: Mailchimp::ApiClient::LIST_IDS[:individual], 
          name: 'Individual Users'
        }
      }
    end

    def get_system_info
      {
        api_domain: ENV["MAILCHIMP_DOMAIN"],
        last_job_time: MailchimpSyncLog.where(trigger_type: 'scheduled').recent.first&.created_at
      }
    end
  end
end