module StaticAdmin
  class StaticAdmin::TeachersController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_teacher, only: [:show, :destroy, :reset_pw]
    before_action :set_school, only: [:new, :create, :destroy, :destroy_all]

    # GET /admin/teachers
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @teachers = Teacher.joins(:school)

      if params[:referral].present?
        @teachers = @teachers.where("referral_code ILIKE :query", query: "%#{params[:referral]}%")
      end

      if params[:name].present?
        @teachers = @teachers.where("users.name ILIKE :query", query: "%#{params[:name]}%")
      end

      if params[:school_name].present?
        @teachers = @teachers.where("schools.name ILIKE :query", query: "%#{params[:school_name]}%")
      end

      @teachers = safe_paginate(@teachers.order("#{sort} #{order}"), page: params[:page], per_page: 30)
    end

    # GET /admin/school/:school_id/teachers/new
    def new
      @teacher = Teacher.new
    end

    # POST /admin/school/:school_id/teachers
    def create
      @teacher = Teacher.new(teacher_params)

      @teacher.school = @school
      @teacher.uid = @teacher.email.downcase

      if @teacher.save
        redirect_to teachers_admin_static_school_path(@school), notice: 'Teacher was successfully created.'
      else
        render :new
      end
    end

    # DELETE /admin/teachers/:id
    def destroy
      @teacher.destroy
      redirect_to teachers_admin_static_school_path(@school), notice: 'Teacher was successfully deleted.'
    end

    # DELETE /admin/teachers
    def destroy_all
      ids = params[:ids]
      teachers = Teacher.where(id: ids)

      teachers.destroy_all

      redirect_to teachers_admin_static_school_path(@school), notice: 'Teachers successfully deleted.'
    end

    # PATCH /admin/teachers/reset
    def reset_pw_all
      ids = params[:ids]
      teachers = Teacher.where(id: ids)

      begin
        teachers.each do |teacher|
          teacher.send_password_reset_email(token: teacher.new_recovery_token)
        end

        render json: { success: true, notice: "Password reset requested for #{ids.count} users." }
      rescue
        render json: { success: false }
      end
    end

    # PATCH /admin/teachers/reset/:id
    def reset_pw
      @teacher.send_password_reset_email(token: @teacher.new_recovery_token)
      render json: { success: true, notice: 'Password reset requested.' }
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_teacher
      @teacher = Teacher.find(params[:id])
    end

    def set_school
      @school = School.find(params[:static_school_id])
    end

    # Only allow a list of trusted parameters through.
    def teacher_params
      params.require(:teacher).permit(:job_title, :name, :alias, :email, :dob, :gender, :working_days, :password, :is_school_admin, :use_new_presentation_player)
    end

  end
end
