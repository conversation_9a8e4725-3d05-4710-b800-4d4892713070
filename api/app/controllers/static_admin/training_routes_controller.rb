module StaticAdmin
  class StaticAdmin::TrainingRoutesController < StaticAdminController
    include Fileboy
    before_action :set_training_route, only: [:edit, :update, :destroy]

    # GET /admin/career/:career_id/training_routes
    def index; end

    # GET /admin/career/:career_id/training_routes/new
    def new
      @career = Career.find(params[:career_id])
      @training_route = TrainingRoute.new
    end

    # POST /admin/career/:career_id/training_routes
    def create
      @career = Career.find(params["career_id"])
      @training_route = @career.training_routes.build(training_route_params)

      @training_route.qualifications = sanitize_input(params["training_route"]["qualifications"])

      if @training_route.save
        redirect_to [:edit, :admin, @career], notice: 'Training route was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/career/:career_id/training_routes/:id/edit
    def edit; end

    # PATCH/PUT /admin/career/:career_id/training_routes/:id
    def update
      params[:training_route][:qualifications] = sanitize_input(params["training_route"]["qualifications"])

      if @training_route.update(training_route_params)
        redirect_to [:edit, :admin, @career], notice: 'Training route was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/career/:career_id/training_routes/:id
    def destroy
      @training_route.destroy
      
      redirect_to [:edit, :admin, @career], notice: 'Training route was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_training_route
      @career = Career.find(params[:career_id])
      @training_route = TrainingRoute.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def training_route_params
      params.require(:training_route).permit(:name, :route_type, :qualifications)
    end

    def sanitize_input(input)
      input.gsub(/[^[:print:]\n]/, '').gsub(/\n+\z/, '')
    end

  end
end
