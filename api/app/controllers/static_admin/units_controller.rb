module StaticAdmin
  class StaticAdmin::UnitsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_unit, only: [:show, :edit, :update, :destroy, :duplicate]

    admin_section :curriculum

    # GET /admin/library-units
    def index
      sortable_columns = ['name', 'created_at', 'curriculum_name', 'year_name', 'new_in_2024', 'featured_on_homepage']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @units = NewLibrary::Unit.joins(year: :curriculum).select(
        'new_library_units.*',
        'new_library_years.name as year_name',
        'new_library_curricula.name as curriculum_name',
      )

      if params[:query].present?
        @units = @units.where("new_library_units.name ILIKE :query", query: "%#{params[:query]}%")
      end

      @units = safe_paginate(@units.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/library-units/new
    def new
      @years = NewLibrary::Year.pluck(:name, :id)
      @lesson_templates = Lesson::Template.pluck(:id, :name, :machine_name, :fileboy_image_id)
                                          .map do |id, name, machine_name, img_id|
        { id: id, name: name, machine_name: machine_name, fileboy_image_id: img_id }
      end
      @unit = NewLibrary::Unit.new
    end

    # POST /admin/library-units
    def create
      @unit = NewLibrary::Unit.new(unit_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @unit.fileboy_image_id = fileboy_image_id if fileboy_image_id

      video_source = params["new_library_unit"]["video_source"]
      external_video_id = params["new_library_unit"]["external_video_id"]
      video_title = params["new_library_unit"]["jw_title"].blank? ? params["new_library_unit"]["name"] : params["new_library_unit"]["jw_title"]
      video_body = params["new_library_unit"]["jw_body"]

      @unit.video_id = handle_video_for(video_source, external_video_id, video_title, video_body)

      year = NewLibrary::Year.find(params["new_library_unit"]["year"].to_i)
      @unit.year = year if year.present?

      lesson_template_ids = JSON.parse(params[:lesson_template_ids]).map(&:to_i)
      @unit.lesson_template_ids = lesson_template_ids

      lesson_template_ids.each_with_index do |id, i|
        Lesson::Template.find(id).update(weight: i)
      end

      if @unit.save
        redirect_to edit_admin_unit_path(@unit), notice: 'Unit was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/library-units/:id/edit
    def edit
      @years = NewLibrary::Year.pluck(:name, :id)
      @lesson_templates = Lesson::Template.pluck(:id, :name, :machine_name, :fileboy_image_id)
                                          .map do |id, name, machine_name, img_id|
        { id: id, name: name, machine_name: machine_name, fileboy_image_id: img_id }
      end
    end

    # PATCH/PUT /admin/library-units/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @unit.fileboy_image_id = fileboy_image_id if fileboy_image_id

      video_source = params["new_library_unit"]["video_source"]
      external_video_id = params["new_library_unit"]["external_video_id"]
      video_title = params["new_library_unit"]["jw_title"].blank? ? params["new_library_unit"]["name"] : params["new_library_unit"]["jw_title"]
      video_body = params["new_library_unit"]["jw_body"]

      @unit.video_id = handle_video_for(video_source, external_video_id, video_title, video_body)

      year = NewLibrary::Year.find_by(id: params["new_library_unit"]["year"].to_i)
      @unit.year = year if year.present?

      lesson_template_ids = JSON.parse(params[:lesson_template_ids]).map(&:to_i)
      @unit.lesson_template_ids = lesson_template_ids

      lesson_template_ids.each_with_index do |id, i|
        Lesson::Template.find(id).update(weight: i)
      end

      if @unit.update(unit_params)
        redirect_to edit_admin_unit_path(@unit), notice: 'Unit was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/library-units/:id
    def destroy
      @unit.destroy
      redirect_to admin_units_url, notice: 'unit was successfully deleted.'
    end

    # POST /admin/library-units/:id/duplicate
    def duplicate
      @unit.duplicate
      redirect_to admin_units_path, notice: 'unit was successfully duplicated.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_unit
      @unit = NewLibrary::Unit.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def unit_params
      params.require(:new_library_unit).permit(:name, :summary, :fileboy_image_id, :weight, :details, :jw_title, :jw_body, :jw_id, :lesson_templates, :new_in_2024)
    end

    def handle_video_for(video_source, external_video_id, video_title, video_body)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)
      video.name = video_title if video.new_record? || video.name.blank?
      video.body = video_body if video_body.present?

      video.save! if video.changed?

      return video.id
    end

  end
end
