module StaticAdmin
  class StaticAdmin::CurriculaController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_curriculum, only: [:show, :edit, :update, :destroy]
    before_action :set_countries, only: [:new, :create, :edit, :update]

    admin_section :curriculum

    # GET /admin/library-curricula
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'

      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @curricula = NewLibrary::Curriculum.all.includes([:country])
      if params[:query].present?
        @curricula = @curricula.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      @curricula = safe_paginate(@curricula.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/library-curricula/new
    def new
      @curriculum = NewLibrary::Curriculum.new
    end

    # POST /admin/library-curricula
    def create
      @curriculum = NewLibrary::Curriculum.new(curriculum_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @curriculum.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @curriculum.save
        redirect_to edit_admin_curriculum_path(@curriculum), notice: 'Curriculum was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/library-curricula/:id/edit
    def edit; end

    # PATCH/PUT /admin/library-curricula/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @curriculum.fileboy_image_id = fileboy_image_id if fileboy_image_id

      if @curriculum.update(curriculum_params)
        redirect_to edit_admin_curriculum_path(@curriculum), notice: 'Curriculum was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/library-curricula/:id
    def destroy
      @curriculum.destroy
      redirect_to admin_curricula_url, notice: 'Curriculum was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_curriculum
      @curriculum = NewLibrary::Curriculum.find(params[:id])
    end

    def set_countries
      blacklist = %w[US]

      countries = Country.all.joins(:available_templates).group(:id).order("countries.machine_name = 'GB' desc", name: :asc)
      countries = countries.where.not('countries.machine_name IN (?)', blacklist)

      @countries = countries.pluck(:name, :id)
    end

    # Only allow a list of trusted parameters through.
    def curriculum_params
      params.require(:new_library_curriculum).permit(:name, :weight, :fileboy_image_id, :country_id, :published)
    end

  end
end
