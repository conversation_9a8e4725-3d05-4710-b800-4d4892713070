module StaticAdmin
  class DiscountCodesController < StaticAdminController
    before_action :set_discount_code, only: [:show, :edit, :update, :destroy, :toggle_active]

    def index
      @discount_codes = DiscountCode.all.order(created_at: :desc)
    end

    def show
      @redemptions = @discount_code.discount_code_redemptions.includes(:subscriber).order(created_at: :desc)
    end

    def new
      @discount_code = DiscountCode.new
    end

    def edit
    end

    def create
      # multiply amount by 100 when fixed amount
      if discount_code_params[:discount_type] == 'fixed_amount'
        params[:discount_code][:amount] = (discount_code_params[:amount].to_f * 100).to_i
      end

      @discount_code = DiscountCode.new(discount_code_params)

      if @discount_code.save
        flash[:success] = "Discount code was successfully created."
        redirect_to admin_discount_codes_path
      else
        flash.now[:error] = "There was a problem creating the discount code."
        render :new
      end
    rescue Stripe::StripeError => e
      flash.now[:error] = e.message
      render :new
    end

    def update
      # multiply amount by 100 when fixed amount
      if discount_code_params[:discount_type] == 'fixed_amount'
        params[:discount_code][:amount] = (discount_code_params[:amount].to_f * 100).to_i
      end

      if @discount_code.update(discount_code_params)
        flash[:success] = "Discount code was successfully updated."
        redirect_to admin_discount_code_path(@discount_code)
      else
        flash.now[:error] = "There was a problem updating the discount code."
        render :edit
      end
    end

    def destroy
      if @discount_code.discount_code_redemptions.exists?
        flash[:error] = "Cannot delete a discount code that has been redeemed."
        redirect_to admin_discount_code_path(@discount_code)
      else
        @discount_code.destroy
        flash[:success] = "Discount code was successfully deleted."
        redirect_to admin_discount_codes_path
      end
    end
    
    def toggle_active
      @discount_code.update(active: !@discount_code.active)
      flash[:success] = "Discount code #{@discount_code.active? ? 'activated' : 'deactivated'} successfully."
      redirect_to admin_discount_code_path(@discount_code)
    end

    private
    
    def set_discount_code
      @discount_code = DiscountCode.find(params[:id])
    end

    def discount_code_params
      params.require(:discount_code).permit(
        :code, 
        :discount_type, 
        :amount, 
        :description, 
        :expires_at, 
        :max_redemptions, 
        :active
      )
    end
  end
end
