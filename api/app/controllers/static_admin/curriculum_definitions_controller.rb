module StaticAdmin
  class StaticAdmin::CurriculumDefinitionsController < StaticAdminController
    include <PERSON><PERSON><PERSON><PERSON>elper
    before_action :set_curriculum_definition, only: [:show, :edit, :update, :destroy]

    admin_section :curriculum

    # GET /admin/curriculum_definitions
    def index
      sortable_columns = ['title', 'year', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @curriculum_definitions = CurriculumDefinition.all
      if params[:query].present?
        @curriculum_definitions = @curriculum_definitions.where("title ILIKE :query", query: "%#{params[:query]}%")
      end

      @curriculum_definitions = safe_paginate(@curriculum_definitions.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/curriculum_definitions/new
    def new
      @curriculum_definition = CurriculumDefinition.new
    end

    # POST /admin/curriculum_definitions
    def create
      @curriculum_definition = CurriculumDefinition.new(curriculum_definition_params)

      if @curriculum_definition.save
        redirect_to [:edit, :admin, @curriculum_definition], notice: 'Curriculum definition was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/curriculum_definitions/:id/edit
    def edit; end

    # PATCH/PUT /admin/curriculum_definitions/:id
    def update
      if @curriculum_definition.update(curriculum_definition_params)
        redirect_to [:edit, :admin, @curriculum_definition], notice: 'Curriculum definition was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/curriculum_definitions/:id
    def destroy
      @curriculum_definition.destroy
      redirect_to admin_curriculum_definitions_url, notice: 'Curriculum definition was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_curriculum_definition
      @curriculum_definition = CurriculumDefinition.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def curriculum_definition_params
      params.require(:curriculum_definition).permit(:year, :title, :body, :subject)
    end

  end
end
