# app/controllers/static_admin/event_registrants_controller.rb
module StaticAdmin
  class EventRegistrantsController < StaticAdminController
    before_action :set_event_registration
    before_action :set_event_registrant, only: [:show, :destroy, :resync_mailchimp]

    def destroy
      @event_registrant.destroy
      redirect_to registrants_admin_event_registration_path(@event_registration),
                 notice: 'Registration was successfully deleted.'
    end

    def resync_mailchimp
      begin
        Mailchimp::EventRegistrantSyncService.new.sync_registrant(@event_registrant)
        flash[:notice] = 'Successfully resynced registrant to Mailchimp.'
      rescue => e
        flash[:alert] = "Failed to sync registrant: #{e.message}"
      end
      
      redirect_to registrants_admin_event_registration_path(@event_registration)
    end

    def create
      @event_registrant = @event_registration.event_registrants.build(event_registrant_params)
      
      if @event_registrant.save
        flash[:notice] = "#{@event_registrant.name} successfully registered for #{@event_registration.name}"
        redirect_to admin_event_registration_path(@event_registration)
      else
        flash[:alert] = "Registration failed: #{@event_registrant.errors.full_messages.join(', ')}"
        redirect_to admin_event_registration_path(@event_registration)
      end
    end

    private

    def set_event_registration
      @event_registration = EventRegistration.find(params[:event_registration_id])
    end

    def set_event_registrant
      @event_registrant = @event_registration.event_registrants.find(params[:id])
    end

    def event_registrant_params
      permitted = params.require(:event_registrant).permit(:name, :email)
      
      # Add registration_data if present
      if params[:registration_data].present?
        permitted[:registration_data] = params[:registration_data].permit!.to_h
      end
      
      permitted
    end
  end
end