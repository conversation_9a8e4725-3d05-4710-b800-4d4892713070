module StaticAdmin
  class StaticAdmin::EnquirySkillsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_enquiry_skill, only: [:show, :edit, :update, :destroy]

    admin_section :curriculum

    # GET /admin/enquiry_skills
    def index
      sortable_columns = ['title', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @enquiry_skills = ScientificEnquiryType.all
      if params[:query].present?
        @enquiry_skills = @enquiry_skills.where("title ILIKE :query", query: "%#{params[:query]}%")
      end

      @enquiry_skills = safe_paginate(@enquiry_skills.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/enquiry_skills/new
    def new
      @enquiry_skill = ScientificEnquiryType.new
    end

    # POST /admin/enquiry_skills
    def create
      @enquiry_skill = ScientificEnquiryType.new(enquiry_skill_params)
      
      fileboy_icon_id = upload_image params[:fileboy_icon] if params[:fileboy_icon].present?
      @enquiry_skill.fileboy_icon_id = fileboy_icon_id if fileboy_icon_id

      if @enquiry_skill.save
        redirect_to [:edit, :admin, @enquiry_skill], notice: 'Enquiry type was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/enquiry_skills/:id/edit
    def edit; end

    # PATCH/PUT /admin/enquiry_skills/:id
    def update
      fileboy_icon_id = upload_image params[:fileboy_icon] if params[:fileboy_icon].present?
      @enquiry_skill.fileboy_icon_id = fileboy_icon_id if fileboy_icon_id

      if @enquiry_skill.update(enquiry_skill_params)
        redirect_to [:edit, :admin, @enquiry_skill], notice: 'Enquiry type was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/enquiry_skills/:id
    def destroy
      @enquiry_skill.destroy
      redirect_to admin_scientific_enquiry_types_url, notice: 'Enquiry type was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_enquiry_skill
      @enquiry_skill = ScientificEnquiryType.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def enquiry_skill_params
      params.require(:enquiry_skill).permit(:title, :body, :fileboy_image_id)
    end

  end
end
