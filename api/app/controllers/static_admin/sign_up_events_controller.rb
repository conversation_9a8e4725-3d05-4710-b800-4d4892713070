module StaticAdmin
  class StaticAdmin::SignUpEventsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_sign_up_event, only: [:show, :edit, :update, :destroy]

    admin_section :marketing

    # GET /admin/sign_up_events
    def index
      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'

      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @sign_up_events = SignUpEvent.all
      if params[:query].present?
        @sign_up_events = @sign_up_events.where("name ILIKE :query", query: "%#{params[:query]}%")
      end
      @sign_up_events = safe_paginate(@sign_up_events.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/sign_up_events/new
    def new
      @sign_up_event = SignUpEvent.new
    end

    # POST /admin/sign_up_events
    def create

      @sign_up_event = SignUpEvent.new(sign_up_event_params)

      if @sign_up_event.save
        redirect_to [:edit, :admin, @sign_up_event], notice: 'Sign up event was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/sign_up_events/:id/edit
    def edit; end

    # PATCH/PUT /admin/sign_up_events/:id
    def update
      if @sign_up_event.update(sign_up_event_params)
        redirect_to [:edit, :admin, @sign_up_event], notice: 'Sign up event was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/sign_up_events/:id
    def destroy
      @sign_up_event.destroy
      redirect_to admin_sign_up_events_url, notice: 'Sign up event was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_sign_up_event
      @sign_up_event = SignUpEvent.find(params[:id])
    end

    # Only allow a list of trusted parameters through.
    def sign_up_event_params
      params.require(:sign_up_event).permit(:name, :published)
    end

  end
end
