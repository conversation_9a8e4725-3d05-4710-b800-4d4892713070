module StaticAdmin
  class StaticAdmin::FlaggedImagesController < StaticAdminController
    include Fileboy

    admin_section :misc

    # GET /admin/flagged-images
    def index
      sortable_columns = ['image_id', 'type', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @flagged_images = FlaggedImage.all
      if params[:query].present?
        @flagged_images = @flagged_images.where("image_id ILIKE :query", query: "%#{params[:query]}%")
      end

      @flagged_images = @flagged_images.order("#{sort} #{order}")
    end

    # GET /admin/flagged-images/new
    def new
      @flagged_image = FlaggedImage.new
    end

    # POST /admin/flagged-images
    def create
      @flagged_image = FlaggedImage.new(flagged_image_params)

      if @flagged_image.save
        redirect_to admin_flagged_images_path, notice: 'Flagged image was successfully created.'
      else
        render :new
      end
    end

    # DELETE /admin/flagged-images/:id
    def destroy
      @flagged_image = FlaggedImage.find(params[:id])
      record_to_restore = @flagged_image.flaggable

      if record_to_restore && @flagged_image.image_path.present?
      top_level_key = @flagged_image.image_path.first
      json_data = record_to_restore.public_send(top_level_key)

      path_within_json = @flagged_image.image_path.drop(1)

      restored_data = dig_and_set(json_data, path_within_json, @flagged_image.image_id)

      record_to_restore.public_send("#{top_level_key}=", restored_data)
      
      record_to_restore.save!
    end

      @flagged_image.destroy
      redirect_to admin_flagged_images_url, status: :see_other, notice: 'Flagged image was successfully deleted.'
    end

    private

    def flagged_image_params
      params.require(:flagged_image).permit(:image_id, :type, :reason)
    end

    def dig_and_set(obj, path, value)
      if path.length == 1
        parent = obj
      else
        parent = obj.dig(*path[0..-2])
      end
      if parent.is_a?(Hash) || parent.is_a?(Array)
        parent[path.last] = value
      end

      obj
    end
  end
end
