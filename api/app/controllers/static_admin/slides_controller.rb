# app/controllers/static_admin/slides_controller.rb
module StaticAdmin
  class SlidesController < StaticAdminController
    before_action :set_lesson_template
    before_action :set_slide, only: %i[update destroy]

    # POST /admin/templates/:lesson_template_id/slides
    def create
      @slide = Lesson::Slide.new(slide_params)
      @slide.template = @lesson_template

      if params['slide']['slide_type'] == 'timer'
        handle_video_for(@slide, 'fileboy', params['slide']['timer_video_id'], "#{@lesson_template.name} - #{@slide.slide_type}")
      else
        handle_video_for(@slide, params['slide']['video_source'], params['slide']['external_video_id'], "#{@lesson_template.name} - #{@slide.slide_type}")
      end

      if @slide.save
        query_params = params['reopen_draw'].present? ? { slide_id: @slide.id } : {}
        redirect_to presentation_admin_lesson_template_path(@lesson_template, **query_params), notice: 'Slide was successfully created.'
      else
        # Show error message
        error_message = @slide.errors.full_messages.join(', ')
        redirect_to presentation_admin_lesson_template_path(@lesson_template), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to presentation_admin_lesson_template_path(@lesson_template),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    rescue ActiveRecord::RecordInvalid => e
      if e.message.include?('External ')
        redirect_to presentation_admin_lesson_template_path(@lesson_template),
                    alert: "Error: #{e.message.gsub('Validation failed: External ', '')}"
      else
        redirect_to presentation_admin_lesson_template_path(@lesson_template),
                    alert: 'Error: Invalid data supplied - Please check all fields.'
      end
    end

    # PUT/PATCH /admin/templates/:lesson_template_id/slides/:id
    def update
      if params['slide']['slide_type'] == 'timer'
        handle_video_for(@slide, 'fileboy', params['slide']['timer_video_id'], "#{@lesson_template.name} - #{@slide.slide_type}")
      else
        handle_video_for(@slide, params['slide']['video_source'], params['slide']['external_video_id'], "#{@lesson_template.name} - #{@slide.slide_type}")
      end

      if @slide.update(slide_params)
        query_params = params['reopen_draw'].present? ? { slide_id: @slide.id } : {}
        redirect_to presentation_admin_lesson_template_path(@lesson_template, **query_params), notice: 'Slide was successfully updated.'
      else
        error_message = @slide.errors.full_messages.join(', ')
        redirect_to presentation_admin_lesson_template_path(@lesson_template, slide_id: @slide.id), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to presentation_admin_lesson_template_path(@lesson_template, slide_id: @slide.id),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    rescue ActiveRecord::RecordInvalid => e
      if e.message.include?('External ')
        redirect_to presentation_admin_lesson_template_path(@lesson_template, slide_id: @slide.id),
                    alert: "Error: #{e.message.gsub('Validation failed: External ', '')}"
      else
        redirect_to presentation_admin_lesson_template_path(@lesson_template, slide_id: @slide.id),
                    alert: 'Error: Invalid data supplied - Please check all fields.'
      end
    end

    # DELETE /admin/templates/:lesson_template_id/slides/:id
    def destroy
      @slide.destroy
      redirect_to presentation_admin_lesson_template_path(@lesson_template), notice: 'Slide was successfully deleted.'
    end

    # PUT /admin/templates/:lesson_template_id/slides/reorder
    def reorder
      # Handle the reordering of slides
      slide_ids = params[:slide_ids]

      if slide_ids.present?
        # Get the intro and outro slides (if they exist)
        intro_slide = @lesson_template.slides.find_by(slide_type: 'intro')
        outro_slide = @lesson_template.slides.find_by(slide_type: 'outro')

        # Update each slide's weight based on its position in the array,
        # accounting for the intro slide (weight 1) if it exists
        start_weight = intro_slide.present? ? 2 : 1

        ActiveRecord::Base.transaction do
          # Ensure intro slide stays first if it exists
          intro_slide.update(weight: 1) if intro_slide.present?

          # Update the weights of the regular slides
          slide_ids.each_with_index do |id, index|
            Lesson::Slide.where(id: id).update_all(weight: index + start_weight)
          end

          # Ensure outro slide stays last if it exists
          outro_slide.update(weight: slide_ids.length + start_weight) if outro_slide.present?
        end

        head :ok
      else
        head :bad_request
      end
    end

    # POST /admin/templates/:lesson_template_id/slides/generate_quiz_question
    def generate_quiz_question
      data = params.permit(:question_type)
      q_type = data[:question_type]
      default_data = QuipQuestion::DEFAULT_QUESTION_DATA[q_type]

      unless default_data.present?
        render json: { error: "Invalid question type #{q_type}", question_id: nil }
        return
      end
      quiz = QuipQuiz.create!(
        name: "Presentation slide question for #{@lesson_template.name}",
        user: current_user,
        user_generated: !!@lesson_template.user_generated,
        published: true,
        # lesson_template: DO NOT ASSIGN
      )

      question = QuipQuestion.create!(
        question_type: q_type,
        data_json: default_data,
        quip_quiz_id: quiz.id,
        weight: 0,
      )

      render json: { error: nil, question_id: question.id }
    end

    def quip_question_details
      data = params.permit(:question_id, :slide_id)
      render json: nil and return unless data[:question_id]
      question = QuipQuestion.find_by(id: data[:question_id])
      render json: nil and return unless question
      
      related_slides = Lesson::Slide.where(quip_question_id: data[:question_id])
      related_slides = related_slides.where.not(id: data[:slide_id]) if data[:slide_id].present?

      render json: {
        quip_quiz_id: question&.quip_quiz&.id,
        type: question.question_type.titleize,
        prompt: question.data_json['prompt'],
        count_references: related_slides.count,
        question_id: question.id,
      }
    end

    private

    def set_lesson_template
      @lesson_template = Lesson::Template.find(params[:lesson_template_id])
    end

    def set_slide
      @slide = @lesson_template.slides.find(params[:id])
    end

    def slide_params
      # Extract the base parameters that are common to all slide types
      permitted_params = %i[slide_type name weight]

      # Add type-specific parameters based on the slide_type
      slide_type = params.dig(:slide, :slide_type)

      # list of all attributes that can be updated here
      # this list is used to clear any params that are not permitted
      allowed_attributes_list = %i[
        body
        footer
        image_style_type
        iframe_src
        fileboy_video_id
        video_url
        fileboy_image_id
        tour_id
        autoFocusVideoId
        quip_question_id
        loop_video
        data
      ]

      case slide_type
      when 'text', 'investigation', 'progress_check', 'rocket_thinking'
        # Text-based slides with images
        permitted_params += %i[body footer fileboy_image_id image_style_type]
      when 'video', 'song', 'expert', 'mission_assignment'
        # Video slides
        permitted_params += %i[body footer fileboy_video_id video_url loop_video]
      when 'timer'
        permitted_params += %i[body footer]
      when 'phet'
        # PHET simulation slides
        permitted_params += %i[body footer iframe_src fileboy_image_id]
      when 'tour'
        # Tour slides
        permitted_params += %i[body footer tour_id autoFocusVideoId]
      when 'quip_question'
        # Quiz question slides
        permitted_params += %i[body footer quip_question_id]
      when 'keywords', 'previous_keywords', 'quiz'
        # These types don't need additional fields
        # No additional params
        permitted_params += %i[body footer]
      when 'homework'
        permitted_params += %i[body footer homework_id homework_task_id]
      when 'dynamic'
        # Permit the 'data' JSON field and its expected structure.
        # For arrays of objects (like 'panels'), you permit a hash where keys are attributes.
        permitted_params += [
          data: [
            :top_text,
            :bottom_text,
            :thumbnail,
            :max_columns,
            :background_color,
            :background_image,
            :image_style_type,
            { panels: [
              :media_type,
              :media_padding,
              # Image fields
              :image_url,           # Stays: fileboy_image_id
              :image_display_style, # New: 'cover' or 'contain'
              :label,
              # Video fields
              :video_source,        # New: 'youtube', 'vimeo', 'internal_fileboy_id'
              :external_video_id,   # New: for youtube/vimeo
              # Text fields
              :text,
              :text_color
              # Old panel video fields like :video_url (if it was a direct URL)
              # or :video_id (if its meaning was ambiguous) can be removed
              # if replaced by the new structure.
            ] }
          ]
        ]
      else
        # For any other slide types
        permitted_params += %i[body footer fileboy_image_id]
      end

      # determine which params are not permitted, data is an object so this is handling the object key as well
      clear_params = allowed_attributes_list.reject do |key|
        permitted_params.include?(key) || permitted_params.any? { |p| p.is_a?(Hash) && p.key?(key) }
      end

      # unset anything that is not permitted and then permit it so that it will actually be updated
      clear_params.each do |param|
        params[:slide][param] = nil
        permitted_params << param unless permitted_params.include?(param)
      end

      # Require the slide parameter and permit only the appropriate params for this slide type
      params.require(:slide).permit(*permitted_params)
    end

    def handle_video_for(model, video_source, external_video_id, name)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)

      video.name = name if video.new_record? || video.name.blank?
      video.save! if video.changed?

      model.video = video
    end
  end
end
