module StaticAdmin
  class WondeImportsController < StaticAdminController
    include PaginationHelper
    
    # GET /admin/videos
    def index
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @records = WondeImport.all
      @records = @records
                 .order("#{sort} #{order}")

      @records = safe_paginate(@records, page: params[:page], per_page: 50)
    end

    def todo
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
      # filter to no Wonde import since 2nd September
      @schools = schools
                 .where('(SELECT COUNT(wonde_imports.id) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ?) = 0', '2024-09-02')
                 .order("#{sort} #{order}")

      @schools = @schools.where('name ilike ?', "%#{params[:query]}%") if params[:query].present?
      @schools = safe_paginate(@schools, page: params[:page], per_page: 50)

      @active_link = 'todo'
      render 'todo'
    end

    def active
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
      # Wonde import since 2nd September
      # has live classes
      # all classes have assigned pupils
      @schools = schools
                 .where('(SELECT COUNT(wonde_imports.id) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ?) > 0', '2024-09-02')
                 .where('(SELECT FIRST(wonde_imports.import_status) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ? GROUP BY wonde_imports.created_at ORDER BY created_at DESC LIMIT 1) NOT IN (?)', '2024-09-02', [WondeImport.import_statuses['imported'], WondeImport.import_statuses['cancelled']])
                 .group('schools.id')
                 .order("#{sort} #{order}")

      @schools = @schools.where('name ilike ?', "%#{params[:query]}%") if params[:query].present?
      @schools = safe_paginate(@schools, page: params[:page], per_page: 50)

      @active_link = 'active'
      render 'active'
    end

    def done
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
      # Wonde import since 2nd September (schools)
      # has live classes // not sure what this means?
      # all classes have assigned pupils
      sql_query = "
        SELECT schools.id
        FROM schools
        WHERE ((
          (
            SELECT COUNT(forms.id) FROM forms
            WHERE forms.school_id = schools.id
            AND forms.wonde_id IS NOT NULL AND forms.wonde_id != ''
            AND NOT forms.deleted
            AND (
              (
                (
                  SELECT COUNT(enrollments.id) FROM enrollments
                  JOIN users ON enrollments.user_id = users.id
                  WHERE NOT enrollments.deleted AND enrollments.form_id = forms.id
                  AND users.type = 'Pupil'
                ) > 0
                OR (
                  SELECT
                  COUNT(enrollments.id)
                  FROM enrollments
                  JOIN users ON enrollments.user_id = users.id
                  WHERE NOT enrollments.deleted AND enrollments.form_id = forms.id
                  AND users.type = 'Teacher'
                ) > 0
              )
            ) AND (
              (
                SELECT COUNT(forms.id)
                FROM forms
                WHERE forms.school_id = schools.id
                AND forms.wonde_id IS NOT NULL AND forms.wonde_id != ''
                AND NOT forms.deleted
                AND (
                  SELECT
                  COUNT(enrollments.id)
                  FROM enrollments
                  JOIN users ON enrollments.user_id = users.id
                  WHERE NOT enrollments.deleted
                  AND enrollments.form_id = forms.id
                  AND users.type = 'Pupil'
                ) = 0
              ) != (
                SELECT
                COUNT(forms.id)
                FROM forms
                WHERE forms.school_id = schools.id
                AND NOT forms.deleted
                AND forms.wonde_id IS NOT NULL
                AND forms.wonde_id != ''
              )
              OR (
                SELECT COUNT(forms.id)
                FROM forms
                WHERE forms.school_id = schools.id
                AND forms.wonde_id IS NOT NULL AND forms.wonde_id != ''
                AND NOT forms.deleted
                AND (
                  SELECT
                  COUNT(enrollments.id)
                  FROM enrollments
                  JOIN users ON enrollments.user_id = users.id
                  WHERE NOT enrollments.deleted
                  AND enrollments.form_id = forms.id
                  AND users.type = 'Teacher'
                ) = 0
              ) != (
                SELECT
                COUNT(forms.id)
                FROM forms
                WHERE forms.school_id = schools.id
                AND NOT forms.deleted
                AND forms.wonde_id IS NOT NULL
                AND forms.wonde_id != ''
              )
            )
          )
        ) > 0)
        AND schools.id IN (#{schools.pluck(:id).join(',')})
      "
      # get the ids for these schools but then refetch using rails so we can do cool rails things with the records
      school_ids = ActiveRecord::Base.connection.execute(sql_query).values.flatten

      # filter by ids, then do the date filter etc
      @schools = schools
                 .where(id: school_ids)
                 .where('(SELECT COUNT(wonde_imports.id) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ?) > 0', '2024-09-02')
                 .where('(SELECT FIRST(wonde_imports.import_status) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ? GROUP BY wonde_imports.created_at ORDER BY created_at DESC LIMIT 1) IN (?)', '2024-09-02', [WondeImport.import_statuses['imported']])
                 .order("#{sort} #{order}")

      @schools = @schools.where('name ilike ?', "%#{params[:query]}%") if params[:query].present?
      @schools = safe_paginate(@schools, page: params[:page], per_page: 50)

      @active_link = 'done'
      render 'done'
    end

    def problems
      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
      # Wonde import since 2nd September (schools)
      # has live classes // not sure what this means?
      # all classes have assigned pupils
      sql_query = "
        SELECT schools.id
        FROM schools
        WHERE ((
          SELECT COUNT(forms.id)
          FROM forms
          WHERE forms.school_id = schools.id
          AND forms.wonde_id IS NOT NULL AND forms.wonde_id != ''
          AND NOT forms.deleted
          AND (
            SELECT
              COUNT(enrollments.id)
              FROM enrollments
              JOIN users ON enrollments.user_id = users.id
              WHERE NOT enrollments.deleted
              AND enrollments.form_id = forms.id
              AND users.type = 'Pupil'
          ) = 0
        ) = (
          SELECT
            COUNT(forms.id)
            FROM forms
            WHERE forms.school_id = schools.id
            AND NOT forms.deleted
            AND forms.wonde_id IS NOT NULL
            AND forms.wonde_id != ''
          )
        OR (
          SELECT COUNT(forms.id)
          FROM forms
          WHERE forms.school_id = schools.id
          AND forms.wonde_id IS NOT NULL AND forms.wonde_id != ''
          AND NOT forms.deleted
          AND (
            SELECT
              COUNT(enrollments.id)
              FROM enrollments
              JOIN users ON enrollments.user_id = users.id
              WHERE NOT enrollments.deleted
              AND enrollments.form_id = forms.id
              AND users.type = 'Teacher'
          ) = 0
        ) = (
          SELECT
            COUNT(forms.id)
            FROM forms
            WHERE forms.school_id = schools.id
            AND NOT forms.deleted
            AND forms.wonde_id IS NOT NULL
            AND forms.wonde_id != ''
          )
        OR (SELECT FIRST(wonde_imports.import_status) FROM wonde_imports WHERE wonde_imports.school_id = schools.id GROUP BY wonde_imports.created_at ORDER BY created_at DESC LIMIT 1) IN (#{WondeImport.import_statuses['cancelled']})
        )
        AND schools.id IN (#{schools.pluck(:id).join(',')})
      "
      # get the ids for these schools but then refetch using rails so we can do cool rails things with the records
      school_ids = ActiveRecord::Base.connection.execute(sql_query).values.flatten

      # filter by ids, then do the date filter etc
      @schools = schools
                 .where(id: school_ids)
                 .where('(SELECT COUNT(wonde_imports.id) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ?) > 0', '2024-09-02')
                 .where('(SELECT FIRST(wonde_imports.import_status) FROM wonde_imports WHERE wonde_imports.school_id = schools.id AND wonde_imports.created_at > ? GROUP BY wonde_imports.created_at ORDER BY created_at DESC LIMIT 1) IN (?)', '2024-09-02', [WondeImport.import_statuses['imported'], WondeImport.import_statuses['cancelled']])
                 .order("#{sort} #{order}")

      @schools = @schools.where('name ilike ?', "%#{params[:query]}%") if params[:query].present?
      @schools = safe_paginate(@schools, page: params[:page], per_page: 50)

      @active_link = 'problems'
      render 'problems'
    end

    def start_import
      ids = params[:ids]

      schools = School.where(id: ids)
      schools.each do |school|
        import_id = WondeManager.sync_data(school, true, disable_error_email: true)
        WondeImport.find(import_id)&.save_import_data if import_id
      end

      respond_to do |format|
        format.html do
          render json: 'Imported'
        end
        format.js do
          render json: { status: 'ok' }
        end
      end
    end

    private


    def schools
      School
        .generic
        .where("schools.wonde_id IS NOT NULL AND schools.wonde_id != ''")
        .where(wonde_request_status: :active)
    end

  end
end
