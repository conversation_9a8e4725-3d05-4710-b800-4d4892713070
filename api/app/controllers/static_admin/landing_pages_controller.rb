module StaticAdmin
  class StaticAdmin::LandingPagesController < StaticAdminController
    before_action :set_landing_page, only: [:show, :edit, :update, :destroy, :generate_content, :publish, :unpublish, :preview, :generation_status]
    
    def index
      @landing_pages = LandingPage.order(created_at: :desc)
      
      # Apply scope-based filtering from tabs
      case params[:scope]
      when 'published'
        @landing_pages = @landing_pages.where(published: true)
      when 'drafts'
        @landing_pages = @landing_pages.where(published: false)
      when 'recent'
        @landing_pages = @landing_pages.where(created_at: 1.week.ago..Time.current)
      # 'nil' or any other value shows all pages
      end
      
      # Apply search query
      if params[:query].present?
        @landing_pages = @landing_pages.where(
          "title ILIKE ? OR topic ILIKE ? OR subject ILIKE ?", 
          "%#{params[:query]}%", "%#{params[:query]}%", "%#{params[:query]}%"
        )
      end
      
      # Apply additional filters
      @landing_pages = @landing_pages.by_key_stage(params[:key_stage]) if params[:key_stage].present?
      @landing_pages = @landing_pages.by_subject(params[:subject]) if params[:subject].present?
      @landing_pages = @landing_pages.by_resource_type(params[:resource_type]) if params[:resource_type].present?
      
      # Apply sorting
      if params[:sort].present?
        order_direction = params[:order] == 'desc' ? :desc : :asc
        case params[:sort]
        when 'title'
          @landing_pages = @landing_pages.order(title: order_direction)
        when 'created_at'
          @landing_pages = @landing_pages.order(created_at: order_direction)
        end
      end
      
      # Apply pagination
      @landing_pages = @landing_pages.page(params[:page])
    end
    
    def show
    end
    
    def new
      @landing_page = LandingPage.new
      @generator_form = LandingPageGeneratorForm.new
    end
    
    def create
      @generator_form = LandingPageGeneratorForm.new(generator_params)
      
      if @generator_form.valid?
        @landing_page = LandingPage.create!(
          key_stage: @generator_form.key_stage,
          subject: @generator_form.subject,
          topic: @generator_form.topic,
          resource_type: @generator_form.resource_type,
          title: generate_temp_title(@generator_form),
          meta_description: generate_temp_meta_description(@generator_form),
          content: '<p>Content is being generated...</p>',
          teaching_context: build_teaching_context(@generator_form),
          published: false
        )
        
        # Generate session ID and start job
        session_id = SecureRandom.hex(16)
        session[:landing_page_generation_id] = session_id
        
        LandingPageGenerationJob.perform_later(
          @landing_page.id, 
          @generator_form.attributes.compact,
          session_id
        )
        
        redirect_to admin_landing_page_path(@landing_page), notice: 'Landing page is being generated...'
      else
        render :new
      end
    end
    
    def edit
    end
    
    def update
      if teaching_context_params.any?
        current_context = @landing_page.teaching_context || {}
        @landing_page.teaching_context = current_context.merge(teaching_context_params)
      end
      
      if @landing_page.update(landing_page_params)
        redirect_to admin_landing_page_path(@landing_page), notice: 'Landing page updated successfully!'
      else
        render :edit
      end
    end

    def search_units
      query = params[:q]
      return render json: [] if query.blank? || query.length < 2
      
      units = NewLibrary::Unit.joins(:year)
                             .joins(year: :curriculum)
                             .where("new_library_units.name ILIKE ?", "%#{query}%")
                             .limit(20)
                             .includes(year: :curriculum)
      
      units_data = units.map do |unit|
        {
          id: unit.id,
          name: unit.name,
          url: unit_library_units_path(unit),
          year: unit.year&.name,
          curriculum: unit.year&.curriculum&.name,
          lesson_count: unit.lesson_templates.count,
          relevance_score: 0 # Default for manually added
        }
      end
      
      render json: units_data
    end

    def search_lessons
      query = params[:q]
      return render json: [] if query.blank? || query.length < 2
      
      lessons = Lesson::Template.joins(:new_library_units)
                               .joins(new_library_units: { year: :curriculum })
                               .where("lesson_templates.name ILIKE ?", "%#{query}%")
                               .limit(20)
                               .includes(new_library_units: { year: :curriculum })
                               .distinct
      
      lessons_data = lessons.map do |lesson|
        {
          id: lesson.id,
          name: lesson.name,
          url: static_missions_path(lesson),
          units: lesson.new_library_units.pluck(:name),
          year: lesson.new_library_units.first&.year&.name,
          curriculum: lesson.new_library_units.first&.year&.curriculum&.name,
          relevance_score: 0 # Default for manually added
        }
      end
      
      render json: lessons_data
    end
    
    def destroy
      @landing_page.update(deleted_at: Time.current)
      redirect_to admin_landing_pages_path, notice: 'Landing page deleted successfully!'
    end
    
    def generate_content
      generation_params = {
        key_stage: @landing_page.key_stage,
        subject: @landing_page.subject,
        topic: @landing_page.topic,
        resource_type: @landing_page.resource_type,
        career_theme: @landing_page.career_theme,
        geographic_focus: @landing_page.geographic_focus,
        curriculum_alignment: @landing_page.curriculum_alignment
      }.compact
      
      # Generate session ID and start job
      session_id = SecureRandom.hex(16)
      session[:landing_page_generation_id] = session_id
      
      LandingPageGenerationJob.perform_later(@landing_page.id, generation_params, session_id)
      
      redirect_to admin_landing_page_path(@landing_page), notice: 'Content is being regenerated...'
    end
    
    def publish
      @landing_page.update(published: true, published_at: Time.current)
      redirect_to admin_landing_page_path(@landing_page), notice: 'Landing page published!'
    end
    
    def unpublish
      @landing_page.update(published: false, published_at: nil)
      redirect_to admin_landing_page_path(@landing_page), notice: 'Landing page unpublished!'
    end
    
    def preview
      if params[:generator_form].present?
        generator = LandingPageGeneratorService.new(generator_params)
        @preview_page = generator.preview
        render :preview_modal, layout: false
      else
        render :preview_modal, layout: false
      end
    end
  

    def generation_status
      session_id = session[:landing_page_generation_id]
      
      if session_id.blank?
        render json: { status: 'completed', completed: true }
        return
      end
      
      cached_status = Rails.cache.read("landing_page_generation_status_#{session_id}")
      
      if cached_status.nil?
        # No cache entry means either completed or expired
        render json: { status: 'completed', completed: true }
      elsif cached_status == 'processing'
        render json: { status: 'processing', completed: false }
      else
        # Parse the JSON result
        result = JSON.parse(cached_status)
        completed = result['status'] != 'processing'
        
        # Clear session if completed
        session.delete(:landing_page_generation_id) if completed
        
        render json: { 
          status: result['status'], 
          completed: completed,
          errors: result['errors']
        }
      end
    end
    
    private
    
    def set_landing_page
      @landing_page = LandingPage.friendly.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      if @current_user&.admin?
        @landing_page = LandingPage.friendly.find(params[:id])
        flash.now[:warning] = "This page is not yet published and is only visible to admins."
      else
        raise ActiveRecord::RecordNotFound
      end
    end
    
    def landing_page_params
      params.require(:landing_page).permit(
        :title, :meta_description, :content, :published, :key_stage, :subject, :topic,
        :resource_type,
        curriculum_codes: [], 
        careers_data: [:title, :description, :category],
        unit_links: [:id, :name, :url, :year, :curriculum, :lesson_count, :relevance_score],
        lesson_links: [:id, :name, :url, :year, :curriculum, :units, :relevance_score]
      )
    end
    
    def teaching_context_params
      permitted = params.require(:landing_page).permit(:career_theme, :geographic_focus, :curriculum_alignment)
      # Remove empty values manually
      permitted.to_h.reject { |k, v| v.blank? }
    end
    
    def generator_params
      params.require(:landing_page_generator_form).permit(
        :key_stage, :subject, :topic, :resource_type,
        :career_theme, :curriculum_alignment, :geographic_focus
      )
    end

    def generate_temp_title(form)
      "#{form.key_stage} #{form.subject} #{form.topic.titleize} Resources"
    end
    
    def generate_temp_meta_description(form)
      base = "Explore #{form.key_stage} #{form.subject} #{form.topic.downcase} resources. "
      base += "Quality #{form.resource_type&.humanize&.downcase || 'educational materials'} "
      base += "aligned with UK National Curriculum. Expert-led content connecting classroom learning to real-world STEM careers. "
      base += "Transform your teaching today."
      
      # Ensure it meets the 150-160 character requirement
      if base.length < 150
        base += " Download comprehensive teaching resources and lesson plans designed by education specialists."
      elsif base.length > 160
        base = base.truncate(157) + "..."
      end
      
      base
    end
    
    def build_teaching_context(form)
      {
        key_stage: form.key_stage,
        subject: form.subject,
        topic: form.topic,
        resource_type: form.resource_type,
        career_theme: form.career_theme,
        geographic_focus: form.geographic_focus,
        curriculum_alignment: form.curriculum_alignment
      }.compact
    end
  end
end