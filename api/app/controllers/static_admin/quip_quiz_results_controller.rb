module StaticAdmin
  class QuipQuizResultsController < StaticAdminController
    include PaginationHelper

    admin_section :content

    def index
      sortable_columns = %w[created_at quip_quizzes.name schools.name users.name lesson_templates.name]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @quip_quiz_results = QuipQuizResult.all
                                         .left_joins(:lesson_template, :quip_quiz)
                                         .joins('LEFT JOIN users ON (users.id = quip_quiz_results.pupil_id OR users.id = quip_quiz_results.user_id)')
                                         .joins('LEFT JOIN schools ON schools.id = users.school_id')

      query_fields = %w[quiz_name school_name user_name template_name]
      query_fields.each do |field|
        next unless params[field].present?
        # produces a has like {v0: '%a', v1: '%b%'}
        value_hash = {}
        values = params[field].split(',').map(&:strip).each.with_index { |value, index| value_hash["v#{index}".to_sym] = "%#{value}%" }
        # fields to compare for assoc models
        matches = {
          'quiz_name' => %w[quip_quizzes.name],
          'school_name' => %w[schools.name],
          'user_name' => %w[users.name],
          'template_name' => %w[lesson_templates.name lesson_templates.machine_name],
        }[field]
        # produces a query like 'quip_quizzes.name ilike :v0 OR users.name ilike :v0 ... etc
        query = matches.map { |m| values.map.with_index { |_, index| "#{m} ILIKE :v#{index}" }.join(' OR ') }.join(' OR ')
        # destructure everything back into a rails where to prevent sql injection
        @quip_quiz_results = @quip_quiz_results.where(query, **value_hash)
      end

      @quip_quiz_results = @quip_quiz_results.where('quip_quiz_results.created_at <= ?', Date.parse(params[:created_before]).end_of_day) if params[:created_before].present?
      @quip_quiz_results = @quip_quiz_results.where('quip_quiz_results.created_at >= ?', Date.parse(params[:created_after]).beginning_of_day) if params[:created_after].present?

      @quip_quiz_results = safe_paginate(@quip_quiz_results.order("#{sort} #{order}"), page: params[:page])
    end
  end
end
