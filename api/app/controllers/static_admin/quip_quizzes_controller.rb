module StaticAdmin
  class QuipQuizzesController < StaticAdminController
    include PaginationHelper

    admin_section :content
    
    def index
      @quizzes = QuipQuiz.platform_quizzes

      sortable_columns = %w[name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      if params[:query].present?
        @quizzes = @quizzes.where('quip_quizzes.name ILIKE :query', query: "%#{params[:query]}%")
      end

      @quizzes = safe_paginate(@quizzes.order("#{sort} #{order}"), page: params[:page])
    end

    def build_placeholder_quiz
      @flow = Flow.find(params[:flow_id])
      quiz = QuipQuiz.create!(
        name: "Flow Step question for #{@flow.name}",
        user: current_user,
        user_generated: false,
        published: true
      )

      q_type = 'multi_choice'
      default_data = QuipQuestion::DEFAULT_QUESTION_DATA[q_type]
      QuipQuestion.create!(
        question_type: q_type,
        data_json: default_data,
        quip_quiz_id: quiz.id,
        weight: 0
      )

      render json: {
        id: quiz.id,
        name: quiz.name,
      }
    end

    def quiz_details
      @quiz = QuipQuiz.find(params[:id])
      render json: {
        id: @quiz.id,
        name: @quiz.name,
        question_count: @quiz.quip_questions.count,
      }
    end
  end
end
