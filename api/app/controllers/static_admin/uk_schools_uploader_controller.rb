require 'fileutils'

module StaticAdmin
  class UkSchoolsUploaderController < StaticAdminController
    layout 'admin'

    admin_section :misc

    # GET /admin/uk-schools-uploader
    def index; end

    # POST /admin/uk-schools-uploader/upload
    def upload
      return unless params[:file].present?
      csv_file = Rails.root.join('tmp', 'uk_school_upload', params[:file].original_filename)
      FileUtils.mkdir_p(File.dirname(csv_file))

      File.open(csv_file, 'wb') do |f|
        content = params[:file].read.force_encoding('ISO-8859-1').encode('UTF-8', invalid: :replace, undef: :replace, replace: '')
        f.write(content)
      end

      validation = validate_csv_headers(csv_file)

      if validation[:valid]
        job = UkSchoolsImportJob.perform_now(csv_file.to_s)
        render json: { job_id: job.job_id }, status: :accepted
      else
        File.delete(csv_file) if File.exist?(csv_file)
        render json: { error: validation[:message] }, status: :unprocessable_entity
      end
    end

    # GET /admin/uk-schools-uploader/import_progress/:job_id
    def import_progress
      job_id = params[:job_id]
      progress = Rails.cache.read("uk_schools_import_progress_#{job_id}")
      # abort jobs if they are taking too long and making no progress
      if progress
        start_time = progress[:start_time] || Time.now
        if Time.now - start_time > 1.minute && progress[:percent] == 0
          UkSchoolsImportJob.cancel(job_id)
          progress[:complete] = true
          progress[:success] = false
        end
        return render json: progress
      end

      render json: progress || { percent: 0, progress: '0 / ?', success: true, complete: false, start_time: Time.now }
    end

    private

    EXPECTED_HEADERS = [
      'EstablishmentName',
      'Postcode',
      'LA (name)',
      'URN',
      'TelephoneNum',
      'County (name)',
      'Street',
      'Address3',
      'Town',
      'SchoolWebsite',
      'StatutoryLowAge',
      'StatutoryHighAge',
      'NumberOfPupils',
      'OpenDate',
      'PhaseOfEducation (name)',
      'GOR (name)',
    ]

    def validate_csv_headers(file_path)
      headers = nil

      CSV.foreach(file_path, headers: true).with_index do |row, _index|
        headers = row.headers.map(&:strip)
        break
      end

      return { valid: false, message: 'The CSV is empty.' } if headers.nil?

      missing_headers = EXPECTED_HEADERS - headers

      if missing_headers.empty?
        { valid: true, message: 'Headers are valid.' }
      else
        { valid: false, message: "Missing headers: #{missing_headers.join(', ')}" }
      end
    rescue Errno::ENOENT
      { valid: false, message: 'File not found.' }
    rescue CSV::MalformedCSVError => e
      { valid: false, message: "Malformed CSV: #{e.message}" }
    end
  end
end
