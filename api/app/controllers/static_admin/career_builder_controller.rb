module StaticAdmin
  class StaticAdmin::CareerBuilderController < StaticAdminController
    include Pa<PERSON><PERSON>Helper
    before_action :set_career_path, only: [:edit, :update, :destroy, :bulk_update_job_family]

    admin_section :content

    def index
      redirect_to career_builder_index_path unless current_user&.admin?

      sortable_columns = ['career_name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @all_careers = CareerPath.includes(:job_family)
      if params[:search].present?
        @all_careers = @all_careers.where("career_name ILIKE :search", search: "%#{params[:search]}%")
      end

      @all_careers = safe_paginate(@all_careers.order("#{sort} #{order}"), page: params[:page])
    end

    def edit
      @related_career_paths = CareerPath.where.not(id: @career_path.id).where(
        career_name: @career_path.career_name,
        status: 'completed'
      ).where.not(education_level: nil).by_education_level
    end

    def bulk_update_job_family
      job_family_id = params.require(:job_family_id)

      records = @related_career_paths = CareerPath.where(
        career_name: @career_path.career_name,
        status: 'completed'
      ).where.not(education_level: nil)

      if records.update_all(job_family_id: job_family_id)
        redirect_to edit_admin_career_builder_path(@career_path), notice: "Job family updated for #{records.count} careers successfully."
      else
        redirect_to edit_admin_career_builder_path(@career_path), alert: 'Failed to update job family for careers.'
      end
    end

    def update
      # split tags to array
      params[:career_path][:info][:tags] = params[:career_path][:info][:tags].split(',').map(&:strip) if params[:career_path][:info][:tags].is_a?(String)

      # convert nested hash to array
      params[:career_path][:info][:stats] = params[:career_path][:info][:stats].values
      params[:career_path][:info][:stats] = params[:career_path][:info][:stats].reject { |stat| stat["title"].blank? || stat["text"].blank? }

      params[:career_path][:info][:relatedCareers] = params[:career_path][:info][:relatedCareers].values
      params[:career_path][:info][:relatedCareers] = params[:career_path][:info][:relatedCareers].reject { |career| career["name"].blank? }

      params[:career_path][:info][:further_careers] = params[:career_path][:info][:further_careers].values
      params[:career_path][:info][:further_careers] = params[:career_path][:info][:further_careers].reject { |career| career["name"].blank? }

      params[:career_path][:career_path][:careerPath].values.each do |stage|
        stage["steps"] = stage["steps"].values
      end

      params[:career_path][:career_path][:careerPath] = params[:career_path][:career_path][:careerPath].values

      params[:career_path][:career_path][:career] = params[:career_path][:career_name]

      params[:career_path][:video_ids] = params[:career_path][:video_ids].values.reject{ |x| x == '' }.compact.map(&:to_i)

      assign_image_urls

      if @career_path.update(career_path_params)
        redirect_to edit_admin_career_builder_path(params[:id]), notice: 'Career path was successfully updated.'
      else
        render :edit
      end
    end

    def destroy
      @career_path.destroy
      redirect_to admin_career_builder_index_url, notice: 'Career path was successfully deleted.'
    end

    # GET /admin/career_builder/:id/logs
    def logs
      @career_path = CareerPath.find(params[:id])

      @logs = @career_path.career_path_logs
    end

    def dump
      id = params[:id]

      @log = UserCareerPath.joins(:career_path).find_by(log_uuid: id) || CareerSuggestion.find_by(log_uuid: id) 
      @events = ""
    end

    private

    def set_career_path
      @career_path = CareerPath.find(params[:id])
    end

    def upload_career_path_image data
      fileboy_image_id = upload_image data[:fileboy_image]
      data[:image] = "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:_x200" if fileboy_image_id

      return data
    end

    def assign_image_from_fileboy_id(obj)
      # if there was no fileboy id AND we have a pexels assign, don't overwrite the pexels image as it would wipe the images on the front.
      return if obj[:fileboy_image_id].blank? && obj[:image].present? && !obj[:image].include?('www.developingexperts.com/file-cdn/')
      obj[:image] = ImageHelpers.image_url(obj[:fileboy_image_id], { format: 'webp', resize: '300x_', quality: 75 })
    end

    def assign_image_urls
      assign_image_from_fileboy_id(params[:career_path][:career_path])
      params[:career_path][:career_path][:careerPath].each do |stage|
        assign_image_from_fileboy_id(stage)
        stage[:steps].each { |step| assign_image_from_fileboy_id(step) }
      end

      params[:career_path][:info][:relatedCareers].each { |step| assign_image_from_fileboy_id(step) }
      params[:career_path][:info][:further_careers].each { |step| assign_image_from_fileboy_id(step) }
    end

    def career_path_params
      params.require(:career_path).permit(
        :career_name,
        :age,
        video_ids: [],
        career_path: [
          :fileboy_image_id,
          :career,
          :image,
          :description,
          {
            careerPath: [
              :title,
              :emoji,
              :keyword,
              {
                steps: [
                  :fileboy_image_id,
                  :image,
                  :title,
                  :description,
                  :furtherInfo
                ]
              }
            ]
          }
        ],
        info: [
          :about,
          { tags: [] },
          {
            stats: [
              :title,
              :text
            ]
          },
          {
            relatedCareers: [
              :fileboy_image_id,
              :image,
              :name,
              :description
            ],
            further_careers: [
              :fileboy_image_id,
              :image,
              :name,
              :description
            ]
          }
        ]
      )
    end

  end
end
