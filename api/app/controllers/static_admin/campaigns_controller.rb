module StaticAdmin
  class CampaignsController < StaticAdminController
    before_action :set_campaign, only: %i[
      show edit kpi cache_kpi update impressions impressions_top_careers impressions_lessons 
      impressions_units impressions_schools videos tours clear_impressions_cache clear_kpi_cache
      units
  ]
    before_action :set_school_types, only: [:show]

    admin_section :website

    def index
      @campaigns = Campaign.all.order(name: :asc)
    end

    def new
      @campaign = Campaign.new
    end

    def create
      @campaign = Campaign.new(campaign_params)

      if @campaign.save
        redirect_to admin_campaign_path(@campaign), notice: 'Campaign created successfully.'
      else
        flash.now[:alert] = 'Failed to create campaign.'
        render :new
      end
    end

    def show
      all_schools_data = School.generic.where.not(postcode: [nil, '']).pluck(
        :id, :postcode, :category, :uk_school_id, :latitude, :longitude, :hubspot_subscription_status, :free_subscription
      )

      geocode_and_update_schools(all_schools_data)

      @schools = serialize_schools_from_data(all_schools_data)
      @subscribed_schools = @schools.select do |school|
        school[:hubspot_subscription_status] == School::HS_SUBSCRIPTION_STATUS[:subscribed] || school[:free_subscription]
      end

      @pupil_count = User.where(school: @campaign.related_schools, type: 'Pupil').count
      @lesson_count = @campaign.campaign_units_lesson_count

      unit_recommendations_source = find_unit_recommendations_source

      schools = @campaign.related_schools
      units = @campaign.related_units

      unit_stats = unit_recommendations_source.each_with_object({}) do |unit, hash|
        unit_schools = unit.schools

        hash[unit.id] = {
          lesson_count: NewLibrary::Unit
            .where(id: unit.id).joins(lesson_templates: { lessons: :school })
            .where(id: units, schools: { id: schools })
            .count('DISTINCT lesson_lessons.id'),
          pupil_count: Pupil.where(school: unit_schools).count,
          school_count: unit_schools.count
        }
      end

      campaign_related_word_names = @campaign.related_words.map(&:name)
      attached_unit_ids = @campaign.new_library_units.map(&:id)
      @returning_current_units = @campaign.number_of_units == @campaign.campaign_units.size
      @postcodes = (@campaign.postcodes.split(',').map(&:strip).reject(&:blank?) || []).map(&:upcase)

      @unit_recommendations = unit_recommendations_source.map.with_index do |unit, i|
        stats = unit_stats[unit.id] || {}
        {
          id: unit.id,
          name: unit.name,
          fileboy_image_id: unit.fileboy_image_id,
          potential_unit: i >= (@campaign.number_of_units || 1),
          tags: unit.related_words.map(&:name) & campaign_related_word_names,
          pupil_count: stats[:pupil_count] || 0,
          school_count: stats[:school_count] || 0,
          lesson_count: stats[:lesson_count] || 0,
          lesson_templates: unit.lesson_templates.order(:id).first(3).map { |lt| { id: lt.id, name: lt.name } },
          is_attached: attached_unit_ids.include?(unit.id)
        }
      end
    end

    def edit; end

    def update
      if @campaign.update(campaign_params)
        redirect_to edit_admin_campaign_path(@campaign), notice: 'Campaign updated successfully.'
      else
        flash.now[:alert] = 'Failed to update campaign.'
        render :edit
      end
    end

    def kpi
      return unless @campaign.kpi_stats_json.present?

      key = Campaign.filter_key(parse_cache_kpi_params)

      return unless key

      @all_stats = key ? (@campaign.kpi_stats_json.presence || {}).dig(key, 'data') : nil

      @engagement_stat = @all_stats&.find { |x| x['event_label'] == 'school engagement' }
      @unit_engagement_stat = @all_stats&.find { |x| x['event_label'] == 'school unit engagement' }
      @general_stats = @all_stats&.reject { |x| x['event_label'] == 'school engagement' || x['event_label'].include?('campaign') }&.sort_by { |x| x['event_label'] }
      @campaign_stats = @all_stats&.select { |x| x['event_label'].include?('campaign') }&.sort_by { |x| x['event_label'] }
    end

    def cache_kpi
      result = @campaign.cache_posthog_stats(parse_cache_kpi_params, force: params[:force] == '1')

      notice = result[:from_cache].present? ? 'Loaded KPI data from cache.' : 'Cached KPI data successfully.'
      redirect_to kpi_admin_campaign_path(@campaign, parse_cache_kpi_params), notice: notice
    rescue => e
      Rails.logger.error("Error caching KPI data for campaign ID #{@campaign.id}: #{e.message}\n\n#{e.backtrace.join("\n")}")
      redirect_to kpi_admin_campaign_path(@campaign), alert: 'Failed to cache KPI data.'
    end

    def videos; end

    def tours
      @tours = Tour.accessible_by(current_ability)
                    .left_joins(:campaign, :organisation)
                    .where(campaign_id: @campaign.id)
                    .includes(:careers, campaign_units: :new_library_units)
    end

    def resources; end

    # GET /impressions
    def impressions; end

    # GET /top_careers
    def impressions_top_careers
      @organisation = @campaign.organisation

      job_family = params[:family]
      age = params[:age]
      region = params[:region]
      gender = params[:gender]
      ethnicity = params[:ethnicity]

      job_family_or_nil = job_family.present? && job_family != 'all' ? job_family : nil
      age_or_nil = age.present? && age != 'all' ? age : nil
      region_or_nil = region.present? && region != 'all' ? region : nil
      gender_or_nil = gender.present? && gender != 'all' ? gender : nil
      ethnicity_or_nil = ethnicity.present? && ethnicity != 'all' ? ethnicity : nil

      # {"school_id"=>"24223", "career_id"=>"7", "career_name"=>"Doctor", "career_job_family"=>"Healthcare", "month"=>"2025-04", "school_name"=>"SB School - Thailand", "school_postcode"=>"", "job_family"=>"", "dob"=>"", "gender"=>"", "ethnicity"=>"", "region"=>"", "user_id"=>"617932", "engagement"=>1}
      results = @organisation.kpi_stats_by_type('career', { "age": age_or_nil, "region": region_or_nil, "gender": gender_or_nil, "ethnicity": ethnicity_or_nil })

      career_ids = results.select { |r| r['career_type'] == 'Career' }.map { |r| r['career_id'] }.uniq
      career_path_ids_from_careers = CareerPath.where(career_id: career_ids).pluck(:id)
      career_path_ids = results.select { |r| r['career_type'] == 'Career Path' }.map { |r| r['career_id'] }.uniq
      all_job_families = JobFamily.joins(:career_paths).where(career_paths: { id: (career_path_ids_from_careers + career_path_ids).uniq })

      all_job_families.each do |jf|
        # get the ids for the possible records
        career_ids = jf.career_paths.pluck(:career_id).uniq
        career_path_ids = jf.career_paths.pluck(:id).uniq

        # fix the missing data for job family names
        results.each do |r|
          type = r['career_type']
          next if r['career_job_family'].present?
          next unless type == 'Career' && career_ids.include?(r['career_id'].to_i) || type == 'Career Path' && career_path_ids.include?(r['career_id'].to_i)
          r['career_job_family'] = jf.name
        end
      end


      # Build families summary
      families = results
                .reject { |c| c['career_job_family'].blank? }
                .group_by { |c| c['career_job_family'] }
                .map do |name, group|
        TopCareerFamily.new(
          name: name,
          no_of_careers: group.count,
          no_of_impressions: group.sum { |r| r['engagement'].to_i }
        )
      end
        .compact
        .sort_by { |f| -f.no_of_impressions }

      results = results.select { |result| result['career_job_family'] == job_family_or_nil } if job_family_or_nil.present?

      # Top careers
      all_time_views = results.select do |r|
        r['career_id'].present? && r['career_type'] == 'Career' 
      end.group_by { |r| r['career_id'] }.map do |career_id, group|
        {
          id: career_id,
          name: group[0]['career_name'],
          family: group[0]['career_job_family'],
          views: group.sum { |r| r['engagement'].to_i }
        }
      end.sort_by { |c| -c[:views] }

      careers = all_time_views.map do |career|
        TopCareer.new(
          name: career[:name],
          views: career[:views],
          family: career[:family]
        )
      end.compact

      # Regional views
      regional_views = results
                      .select { |r| r['region'].present? && r['month'].present? }
                      .group_by { |r| [r['region'], r['month']] }
                      .map do |(region, month), group|
        RegionalView.new(
          region: region,
          views: group.sum { |r| r['engagement'].to_i },
          created_at: month
        )
      end
                      .sort_by { |x| -x.views }

      # Job family views
      job_family_views = results
                        .select { |r| r['career_job_family'].present? && r['month'].present? }
                        .map do |r|
        {
          name: r['career_job_family'],
          date: r['month'],
          age: r['dob'].present? ? compute_age_at_date(Date.parse(r['dob']), Date.parse("#{r['month']}-01")) : '',
          region: r['region'],
          gender: r['gender'],
          ethnicity: map_ethnicity(r['ethnicity']),
          views: r['engagement'].to_i
        }
      end

      # Views for selected job family (if applicable)
      careers_for_selected_family = if job_family_or_nil
        results
          .select { |r| r['month'].present? }
          .group_by { |r| [r['career_job_family'], r['month']] }
          .map do |(family_name, month), group|
            {
              name: family_name,
              views: group.sum { |r| r['engagement'].to_i },
              created_at: month
            }
          end
          .sort_by { |x| -x[:views] }
      end

      render json: {
        families: families,
        careers: careers,
        regionalViews: regional_views,
        jobFamilyViews: job_family_views,
        selectedJobFamily: job_family_or_nil,
        careersForSelectedJobFamily: careers_for_selected_family
      }
    end


    def impressions_lessons
      @organisation = @campaign.organisation
      (0..59).map { |i| (Date.today << i).strftime('%Y-%m') }.reverse

      age = params[:age]
      region = params[:region]
      gender = params[:gender]
      ethnicity = params[:ethnicity]

      age_or_nil = age.present? && age != 'all' ? age : nil
      region_or_nil = region.present? && region != 'all' ? region : nil
      gender_or_nil = gender.present? && gender != 'all' ? gender : nil
      ethnicity_or_nil = ethnicity.present? && ethnicity != 'all' ? ethnicity : nil

      # {"dob"=>"", "month"=>"2024-05", "gender"=>"", "region"=>"", "user_id"=>"89972", "ethnicity"=>"", "school_id"=>"1", "engagement"=>1, "school_name"=>"16-19 Abingdon", "school_postcode"=>"NR18 0LD", "lesson_template_id"=>"2931"}
      results = @organisation.kpi_stats_by_type('lesson', { "age": age_or_nil, "region": region_or_nil, "gender": gender_or_nil, "ethnicity": ethnicity_or_nil })
      dates = results.map { |result| result['month'] }.uniq.sort.reverse

      # { lesson_id: number; user_id: number, created_at: string, age: string, ethnicity: string; gender: string, region: string }[]
      views = results.map do |result|
        {
          template_id: result['lesson_template_id'],
          age: result['dob'].present? ? compute_age_at_date(Date.parse(result['dob']), Date.parse("#{result['month']}-01")) : '',
          region: result['region'],
          gender: result['gender'],
          ethnicity: map_ethnicity(result['ethnicity']),
          created_at: result['month']
        }
      end

      all_time_views = results.group_by { |result| result['lesson_template_id'] }.map do |lesson_template_id, lesson_results|
        {
          template_id: lesson_template_id,
          views: lesson_results.sum { |r| r['engagement'].to_i },
        }
      end.sort_by { |lesson| -lesson[:views] }

      top_10_ids = all_time_views.map { |lesson| lesson[:template_id] }.first(10)

      # { name: string; fileboy_id: string; views: number }[]
      lessons = Lesson::Template.where(id: top_10_ids).map do |lesson|
        result = all_time_views.find { |lesson_result| lesson_result[:template_id].to_i == lesson.id }
        next unless result
        TopLesson.new(
          id: lesson.id,
          fileboy_id: lesson.fileboy_image_id,
          name: lesson.name,
          views: result[:views]
        )
      end.compact.sort_by { |lesson| -lesson.views }

      # { date: string, name: string, views: number }[]
      top_lesson_views_over_time = dates.flat_map do |date|
        lessons.first(5).map do |lesson|
          {
            date: date,
            name: lesson.name,
            views: results.select { |result| result['month'] == date && result['lesson_template_id'].to_i == lesson.id }.size
          }
        end
      end

      render json: {
        lessons: lessons,
        views: views,
        topLessonViewsOverTime: top_lesson_views_over_time
      }
    end

    def impressions_units
      @organisation = @campaign.organisation
      (0..59).map { |i| (Date.today << i).strftime('%Y-%m') }.reverse

      age = params[:age]
      region = params[:region]
      gender = params[:gender]
      ethnicity = params[:ethnicity]

      age_or_nil = age.present? && age != 'all' ? age : nil
      region_or_nil = region.present? && region != 'all' ? region : nil
      gender_or_nil = gender.present? && gender != 'all' ? gender : nil
      ethnicity_or_nil = ethnicity.present? && ethnicity != 'all' ? ethnicity : nil

      # {"school_id"=>"20241", "unit_id"=>"55", "month"=>"2024-07", "school_name"=>"Colby Primary School", "school_postcode"=>"", "dob"=>"", "gender"=>"male", "ethnicity"=>"", "region"=>"north_west", "user_id"=>"620345", "engagement"=>1}
      results = @organisation.kpi_stats_by_type('unit', { "age": age_or_nil, "region": region_or_nil, "gender": gender_or_nil, "ethnicity": ethnicity_or_nil })
      dates = results.map { |result| result['month'] }.uniq.sort.reverse

      # { unit_id: number; created_at: string, age: string, ethnicity: string; gender: string, region: string }[]
      views = results.map do |result|
        {
          unit_id: result['unit_id'],
          age: result['dob'].present? ? compute_age_at_date(Date.parse(result['dob']), Date.parse("#{result['month']}-01")) : '',
          region: result['region'],
          gender: result['gender'],
          ethnicity: map_ethnicity(result['ethnicity']),
          created_at: result['month']
        }
      end

      all_time_views = results.group_by { |result| result['unit_id'] }.map do |unit_id, unit_results|
        {
          unit_id: unit_id,
          views: unit_results.sum { |r| r['engagement'].to_i },
        }
      end.sort_by { |lesson| -lesson[:views] }

      top_10_ids = all_time_views.map { |unit| unit[:unit_id] }.first(10)

      # { name: string; fileboy_id: string; views: number }[]
      units = NewLibrary::Unit.where(id: top_10_ids).map do |unit|
        result = all_time_views.find { |unit_result| unit_result[:unit_id].to_i == unit.id }
        next unless result
        TopUnit.new(
          id: unit.id,
          fileboy_id: unit.fileboy_image_id,
          name: unit.name,
          views: result[:views]
        )
      end.compact.sort_by { |unit| -unit.views }

      # { date: string, name: string, views: number }[]
      top_unit_views_over_time = dates.flat_map do |date|
        units.first(5).map do |unit|
          {
            date: date,
            name: unit.name,
            views: results.select { |result| result['month'] == date && result['unit_id'].to_i == unit.id }.size
          }
        end
      end

      render json: {
        units: units,
        views: views,
        topUnitViewsOverTime: top_unit_views_over_time
      }
    end

    def impressions_schools
      @organisation = @campaign.organisation
      age = params[:age]
      gender = params[:gender]
      ethnicity = params[:ethnicity]

      age_or_nil = age.present? && age != 'all' ? age : nil
      gender_or_nil = gender.present? && gender != 'all' ? gender : nil
      ethnicity_or_nil = ethnicity.present? && ethnicity != 'all' ? ethnicity : nil

      # {"school_id"=>"30984", "school_name"=>"Leighton Primary School", "school_postcode"=>"PE2 5PL", "region"=>"east_of_england", "engagement"=>1}
      results = @organisation.kpi_stats_by_type('school', { "age": age_or_nil, "gender": gender_or_nil, "ethnicity": ethnicity_or_nil })

      # { name: string; views: number }[]
      regional_views = results
                       .select { |result| result['region'].present? }
                       .group_by { |result| result['region'] }
                       .map do |region, region_results|
        {
          name: region,
          views: region_results.sum { |r| r['engagement'].to_i }
        }
      end.sort_by { |region| -region[:views] }

      all_time_views = results
                       .group_by { |result| result['school_id'] }
                       .map do |school_id, school_results|
        {
          school_id: school_id,
          views: school_results.sum { |r| r['engagement'].to_i },
        }
      end.sort_by { |school| -school[:views] }

      top_20_ids = all_time_views.map { |school| school[:school_id] }.first(20)

      # { name: string; fileboy_id: string; views: number; latitude?: number; longitude?: number }[]
      schools = School.where(id: top_20_ids).map do |school|
        result = all_time_views.find { |school_result| school_result[:school_id].to_i == school.id }
        next unless result
        {
          name: school.name,
          fileboy_id: school.fileboy_image_id,
          latitude: school.latitude,
          longitude: school.longitude,
          postcode: school.postcode,
          views: result[:views]
        }
      end.sort_by { |school| -school[:views] }

      render json: {
        schools: schools,
        regionalViews: regional_views
      }
    end

    def clear_impressions_cache
      @campaign.organisation.update(kpi_stats_cache: nil)
      redirect_to impressions_admin_campaign_path(@campaign), notice: 'Impressions stats cache cleared successfully.'
    end

    def clear_kpi_cache
      @campaign.update(kpi_stats_json: nil)
      redirect_to kpi_admin_campaign_path(@campaign), notice: 'KPI stats cache cleared successfully.'
    end

    def units; end

    private

    def parse_cache_kpi_params
      data = params.permit(:hide_anonymous, :region, :gender, :ethnicity, :age, :old_kpi)
      data.reject { |_k, v| v == '*' }
    end

    def set_campaign
      @campaign = Campaign.find(params[:id])
    end

    def set_school_types
      @school_types = [
        %w[primary_school red],
        %w[secondary_school blue],
        %w[special_school green],
        %w[all_through orange],
        %w[international purple],
        %w[home_school_category violet],
        %w[mat blueviolet],
      ]
    end

    def find_unit_recommendations_source
      base_scope = NewLibrary::Unit.includes(:related_words, :lesson_templates)

      if @campaign.number_of_units == @campaign.campaign_units.size
        base_scope.where(id: @campaign.new_library_units.map(&:id))
      else
        base_scope.where(id: @campaign.related_units.map(&:id)).limit((@campaign.number_of_units || 1) + 2)
      end
    end

    def serialize_schools_from_data(data)
      data.map do |id, postcode, category, uk_school_id, latitude, longitude, hs_status, free_sub|
        {
          id: id,
          postcode: postcode,
          category: category,
          uk_school_id: uk_school_id,
          lat: latitude,
          lng: longitude,
          hubspot_subscription_status: hs_status,
          free_subscription: free_sub
        }
      end
    end

    def geocode_and_update_schools(schools_data)
      schools_to_geocode = schools_data.select do |data|
        postcode = data[1]
        latitude = data[4]
        longitude = data[5]
        postcode.present? && (latitude.nil? || longitude.nil?)
      end

      return if schools_to_geocode.empty?

      schools_by_postcode = schools_to_geocode.group_by { |data| data[1] }
                                              .transform_values { |v| v.map { |data| data[0] } }

      postcodes_to_fetch = schools_by_postcode.keys

      postcodes_to_fetch.each_slice(100) do |batch|
        uri = URI('https://api.postcodes.io/postcodes?filter=longitude,latitude')
        http = Net::HTTP.new(uri.host, uri.port)
        http.use_ssl = true

        request = Net::HTTP::Post.new(uri.request_uri, 'Content-Type' => 'application/json')
        request.body = { 'postcodes' => batch }.to_json

        response = http.request(request)

        next unless response.is_a?(Net::HTTPSuccess)

        api_results = JSON.parse(response.body)['result']

        updates = []
        api_results.each do |res|
          next if res['result'].nil?

          postcode = res['query']
          latitude = res['result']['latitude']
          longitude = res['result']['longitude']
          school_ids = schools_by_postcode[postcode]
          updates << { ids: school_ids, lat: latitude, lon: longitude } if school_ids.present? && latitude && longitude
        end

        updates.each do |update|
          School.where(id: update[:ids]).update_all(
            latitude: update[:lat],
            longitude: update[:lon],
            updated_at: Time.current
          )
        end
      rescue => e
        Rails.logger.error("Failed to geocode batch of postcodes: #{e.message}")
        next
      end
    end

    def map_ethnicity(value)
      if value.to_s.match?(/^\d+$/)
        User.ethnicities.key(value.to_i) || value
      else
        value
      end
    end

    def compute_age_at_date(dob, date)
      return '' unless dob.present?

      age_number = date.year - dob.year

      case age_number
      when 1..4
        'eyfs'
      when 5..11
        'ks1_2'
      when 12..14
        'ks3'
      when 15..17
        'ks4'
      else
        ''
      end
    end

    def campaign_params
      data = params.require(:campaign).permit(
        :name,
        :campaign_page_link,
        :organisation_id,
        :postcodes,
        :contract_expiry_date,
        :contact_name,
        :contact_number,
        :contact_email,
        :interest_tags,
      )
      data[:interest_tags] = data[:interest_tags].split(',').map(&:strip).reject(&:blank?) if data[:interest_tags].present?
      data
    end
  end
end

class TopLesson
  attr_accessor :id, :fileboy_id, :name, :views

  def initialize(id:, fileboy_id:, name:, views:)
    @id = id
    @fileboy_id = fileboy_id
    @name = name
    @views = views
  end
end

class TopUnit
  attr_accessor :id, :fileboy_id, :name, :views

  def initialize(id:, fileboy_id:, name:, views:)
    @id = id
    @fileboy_id = fileboy_id
    @name = name
    @views = views
  end
end

class LessonView
  attr_accessor :template_id, :created_at, :age, :gender, :ethnicity, :region

  def initialize(template_id:, created_at:, age:, gender:, ethnicity:, region:)
    @template_id = template_id
    @created_at = created_at
    @age = age
    @gender = gender
    @ethnicity = ethnicity
    @region = region
  end
end

class UnitView
  attr_accessor :unit_id, :created_at, :age, :gender, :ethnicity, :region

  def initialize(unit_id:, created_at:, age:, gender:, ethnicity:, region:)
    @unit_id = unit_id
    @created_at = created_at
    @age = age
    @gender = gender
    @ethnicity = ethnicity
    @region = region
  end
end

class TopCareer
  attr_accessor :name, :family, :views, :views_male, :views_female, :views_other

  def initialize(name:, views:, family:)
    @name = name
    @views = views
    @family = family
  end
end

class JobFamilyView
  attr_accessor :family, :created_at, :age, :gender, :ethnicity, :region

  def initialize(family:, created_at:, age:, gender:, ethnicity:, region:)
    @family = family
    @created_at = created_at
    @gender = gender
    @ethnicity = ethnicity
    @region = region
  end
end

class TopCareerFamily
  attr_accessor :name, :no_of_careers, :no_of_impressions

  def initialize(name:, no_of_careers:, no_of_impressions:)
    @name = name
    @no_of_careers = no_of_careers
    @no_of_impressions = no_of_impressions
  end

  def views
    @no_of_impressions
  end

  def views=(value)
    @no_of_impressions = value
  end
end

class RegionalView
  attr_accessor :region, :views, :created_at

  def initialize(region:, views:, created_at:)
    @region = region
    @views = views
    @created_at = created_at
  end
end
