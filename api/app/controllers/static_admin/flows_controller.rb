module StaticAdmin
  class FlowsController < StaticAdminController
    include ApplicationHelper
    before_action :check_access
    before_action :set_flow, only: [:show, :edit, :update, :destroy, :publish, :steps, :enrollments, :documents, :summary]

    def check_access
      redirect_to universal_dashboard_link unless current_user.beta_feature_enabled?('flows')
    end

    # GET /admin/flows
    def index
      @flows = Flow.includes(:job_family)
                   .order(created_at: :desc)
    end

    # GET /admin/flows/new
    def new
      @flow = Flow.new
    end

    # POST /admin/flows
    def create
      @flow = Flow.new(flow_params)
      @flow.user = current_user

      if params[:flow][:source].present? && params[:flow][:external_id].present?
        handle_video_for(@flow, params[:flow][:source], params[:flow][:external_id])
      else
        @flow.video = nil
      end

      if @flow.save
        redirect_to edit_admin_flow_path(@flow), notice: 'Flow was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/flows/:id/edit
    def edit
    end

    # PATCH/PUT /admin/flows/:id
    def update
      data = flow_params

      if params[:flow][:source].present? && params[:flow][:external_id].present?
        handle_video_for(@flow, params[:flow][:source], params[:flow][:external_id])
      else
        @flow.video = nil
      end

      @flow.assign_attributes(data)
      if @flow.save
        redirect_to edit_admin_flow_path(@flow), notice: 'Flow was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/flows/:id
    def destroy
      @flow.destroy
      redirect_to admin_flows_path, notice: 'Flow was successfully deleted.'
    end

    # POST /admin/flows/:id/publish
    def publish
      @flow.update(published: !@flow.published)
      status = @flow.published? ? 'published' : 'unpublished'
      redirect_to edit_admin_flow_path(@flow), notice: "Flow was successfully #{status}."
    end

    # GET /admin/flows/:id/steps
    def steps
      @flow_steps = @flow.flow_steps.includes(:video, :quiz).order(:weight)
    end

    # GET /admin/flows/:id/enrollments
    def enrollments
      @enrollments = @flow.flow_enrollments
                           .includes(:user)
                           .order(enrolled_at: :desc)
                           .paginate(page: params[:page], per_page: 25)
    end

    # GET /admin/flows/:id/documents
    def documents
      @flow_documents = @flow.flow_documents.ordered
                             .paginate(page: params[:page], per_page: 25)
    end

    # GET /admin/flows/:id/summary/:enrollment_id
    def summary
      @flow_steps = @flow.flow_steps.order(:weight)
      @enrollment = FlowEnrollment.find(params[:enrollment_id])
    end

    private

    def set_flow
      @flow = Flow.find(params[:id])
    end

    def flow_params
      params.require(:flow).permit(
        :name,
        :description,
        :learning_objectives,
        :job_family_id,
        :difficulty_level,
        :estimated_duration_minutes,
        :fileboy_image_id,
        :published
      )
    end

    def handle_video_for(model, video_source, external_video_id)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)

      video.name = "Video for Flow #{model.name}" if video.new_record? || video.name.blank?
      video.save! if video.changed?

      model.video = video
    end
  end
end
