module StaticAdmin
  class StaticAdmin::CareersController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_career, only: [:show, :edit, :update, :destroy]
    before_action :set_initial_fields, only: [:new, :create]

    admin_section :content

    # GET /admin/careers
    def index
      sortable_columns = ['name', 'family', 'work_location']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'name'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'asc'

      @careers = Career.all
      if params[:query].present?
        @careers = @careers.where("name ILIKE :query", query: "%#{params[:query]}%")
      end

      if params[:tags].present?

        tags = params[:tags].split(',')
        @careers = @careers.joins(:tags).where(tags: { name: tags }).distinct
      end

      @careers = safe_paginate(@careers.order("#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/careers/new
    def new
      @career = Career.new
    end

    # POST /admin/careers
    def create
      @career = Career.new(career_params)

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @career.fileboy_image_id = fileboy_image_id if fileboy_image_id

      @career.video_id = handle_video_for(@career, params["career"]["video_source"], params["career"]["external_video_id"])

      @career.competencies_technical = sanitize_input(params["career"]["competencies_technical"])
      @career.competencies_non_technical = sanitize_input(params["career"]["competencies_non_technical"])
      @career.work_location = sanitize_input(params["career"]["work_location"])
      @career.sectors = sanitize_input(params["career"]["sectors"])
      @career.keywords = sanitize_input(params["career"]["keywords"])

      update_career_tags(@career, params["career"]["career_tags"])

      tour_ids = params[:tour_ids]
      tour_ids = JSON.parse(tour_ids)
      @career.tour_ids = tour_ids

      if @career.save
        redirect_to [:edit, :admin, @career], notice: 'Career was successfully created.'
      else
        render :new
      end

    end

    # GET /admin/careers/:id/edit
    def edit; end

    # PATCH/PUT /admin/careers/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      params[:career][:fileboy_image_id] = fileboy_image_id if fileboy_image_id

      params[:career][:video_id] = handle_video_for(@career, params["career"]["video_source"], params["career"]["external_video_id"])

      params[:career][:competencies_technical] = sanitize_input(params["career"]["competencies_technical"])
      params[:career][:competencies_non_technical] = sanitize_input(params["career"]["competencies_non_technical"])
      params[:career][:work_location] = sanitize_input(params["career"]["work_location"])
      params[:career][:sectors] = sanitize_input(params["career"]["sectors"])
      params[:career][:keywords] = sanitize_input(params[:career][:keywords])

      update_career_tags(@career, params["career"]["career_tags"])

      tour_ids = params[:tour_ids]
      tour_ids = JSON.parse(tour_ids)
      @career.tour_ids = tour_ids

      if @career.update(career_params)
        redirect_to [:edit, :admin, @career], notice: 'Career was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/careers/:id
    def destroy
      @career.destroy
      redirect_to admin_careers_url, notice: 'Career was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_career
      id = params[:id]
      @career = Career.find(id)
      @show_training_routes = true
      @training_routes = TrainingRoute.where(career_id: id)
      @organisations = Organisation.all.order(name: :asc)
      @tours = Tour.all.order(name: :asc)
    end

    def set_initial_fields
      @show_training_routes = false
      @training_routes = []
      @organisations = Organisation.all.order(name: :asc)
      @tours = Tour.all.order(name: :asc)
    end

    # Only allow a list of trusted parameters through.
    def career_params
      params.require(:career).permit(
        :name,
        :body,
        :fileboy_image_id,
        :competencies_technical,
        :competencies_non_technical,
        :work_location,
        :family,
        :sectors,
        :keywords,
        :definition,
        :salary_from,
        :salary_to,
        :tour_id,
        :organisation_id,
        :video_id,
        :complete,
        :tag_list,
      )
    end

    def handle_video_for(model, video_source, external_video_id)
      return unless video_source.present? && external_video_id.present?

      video = Video.find_or_initialize_by(source: video_source, external_id: external_video_id)
      video.name = model.name if video.new_record? || video.name.blank?
      video.save if video.changed?

      return video.id
    end

    def sanitize_input(input)
      input.gsub(/[^[:print:]\n]/, '').gsub(/\n+\z/, '')
    end

    def update_career_tags(career, tag_list)
      new_tag_names = tag_list.split(';').map(&:strip)

      new_tags = new_tag_names.map do |tag_name|
        CareerTag.find_or_initialize_by(name: tag_name)
      end

      career.career_tags.replace(new_tags)
    end
  end
end
