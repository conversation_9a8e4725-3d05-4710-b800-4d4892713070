module StaticAdmin
  class StaticAdmin::YearsController < StaticAdminController
    include PaginationHelper
    include Fileboy
    before_action :set_year, only: [:show, :edit, :update, :destroy]
    before_action :set_units, only: [:new, :create, :edit, :update]
    before_action :set_subjects, only: [:new, :create, :edit, :update]

    admin_section :curriculum

    # GET /admin/library-years
    def index
      all_years = NewLibrary::Year.all
      if params[:subject_name].present?
        all_years = all_years.joins(:subject).where('new_library_subjects.name = ?', params[:subject_name])
      end
      if params[:curriculum_id].present?
        all_years = all_years.joins(subject: :curriculum).where('new_library_curricula.id = ?', params[:curriculum_id])
      end

      @curricula = NewLibrary::Curriculum.all.order(:name).pluck(:id, :name)
      @subjects = NewLibrary::Subject.all.order(:name).pluck(:name).uniq

      sortable_columns = ['name', 'created_at']
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @years = all_years.includes(subject: :curriculum)
      if params[:query].present?
        @years = @years.where("new_library_years.name ILIKE :query", query: "%#{params[:query]}%")
      end

      @years = safe_paginate(@years.order("new_library_years.#{sort} #{order}"), page: params[:page])
    end

    # GET /admin/library-years/new
    def new
      @year = NewLibrary::Year.new
    end

    # POST /admin/library-years
    def create
      @year = NewLibrary::Year.new(year_params)
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @year.fileboy_image_id = fileboy_image_id if fileboy_image_id

      unit_ids = JSON.parse(params[:unit_ids]).map(&:to_i)
      @year.unit_ids = unit_ids

      unit_ids.each_with_index do |id, i|
        NewLibrary::Unit.find(id).update(weight: i)
      end

      if @year.save
        redirect_to edit_admin_year_path(@year), notice: 'Year was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/library-years/:id/edit
    def edit; end

    # PATCH/PUT /admin/library-years/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @year.fileboy_image_id = fileboy_image_id if fileboy_image_id

      unit_ids = JSON.parse(params[:unit_ids]).map(&:to_i)
      @year.unit_ids = unit_ids

      unit_ids.each_with_index do |id, i|
        NewLibrary::Unit.find(id).update(weight: i)
      end

      if @year.update(year_params)
        redirect_to edit_admin_year_path(@year), notice: 'Year was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/library-years/:id
    def destroy
      @year.destroy
      redirect_to admin_years_url, notice: 'Year was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def set_year
      @year = NewLibrary::Year.find(params[:id])
      @current_units = @year.units.order(:weight).to_a.map do |unit|
        unit.as_json.merge(lesson_codes: unit.lesson_codes)
      end
    end

    def set_units
      @units = NewLibrary::Unit.joins('LEFT OUTER JOIN new_library_years ON new_library_units.year_id = new_library_years.id')
                                .joins(:lesson_templates)
                                .select('new_library_units.*, new_library_years.name AS year_name, STRING_AGG(DISTINCT lesson_templates.machine_name, \', \') AS lesson_codes')
                                .group('new_library_units.id, new_library_years.name').order('year_name ASC').order('name ASC')
      @year_select = NewLibrary::Year.order('name ASC').pluck(:name, :id)
    end

    def set_subjects
      @subjects = NewLibrary::Subject.includes(:curriculum).all.order('name ASC')
    end

    # Only allow a list of trusted parameters through.
    def year_params
      params.require(:new_library_year).permit(:name, :fileboy_image_id, :max_age, :min_age, :subject_id, :tags_string)
    end
  end
end
