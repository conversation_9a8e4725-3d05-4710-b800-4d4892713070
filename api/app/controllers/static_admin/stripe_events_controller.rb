module StaticAdmin
  class StripeEventsController < StaticAdminController
    include <PERSON><PERSON><PERSON>Helper

    def index
      @event_types = StripeEvent.select(:event_type).distinct.pluck(:event_type).compact

      # Filter by type if provided
      @events = if params[:event_type].present?
                  StripeEvent.by_type(params[:event_type])
                else
                  StripeEvent.all
                end

      customer_id = params[:customer_id]
      if customer_id.present?
        # If customer_id is provided, filter events by that customer
        # check both "id" and "customer" fields in event_data
        # Filter events where event_data->>'id' or event_data->>'customer' matches customer_id
        @events = @events.where(
          "event_data->>'id' = :customer_id OR event_data->>'customer' = :customer_id",
          customer_id: customer_id
        )
      end

      if params[:from_date].present?
        # Filter events from a specific date
        from_date = DateTime.parse(params[:from_date])
        @events = @events.where('created_at >= ?', from_date)
      end

      if params[:to_date].present?
        # Filter events to a specific date
        to_date = DateTime.parse(params[:to_date])
        @events = @events.where('created_at <= ?', to_date)
      end

      # Filter by status if provided
      if params[:status] == 'error'
        @events = @events.with_errors
      elsif params[:status] == 'success'
        @events = @events.successful
      elsif params[:status] == 'critical'
        @events = @events.critical.with_errors
      elsif params[:status] == 'pending_retry'
        @events = @events.pending_retry
      end

      # Order and paginate
      @events = safe_paginate(@events.order(created_at: :desc), page: params[:page], per_page: 20)
      
      # Stats for dashboard
      @stats = StripeEvent.status_counts
    end

    def show
      @event = StripeEvent.find(params[:id])

      # Try to find the associated subscriber
      find_subscriber_for_event(@event)

      # Generate intervention steps for failed events
      @intervention_steps = generate_intervention_steps(@event) if @event.error.present?
    end

    def retry
      @event = StripeEvent.find(params[:id])

      if @event.error.blank?
        flash[:notice] = 'This event has already been processed successfully.'
        redirect_to admin_stripe_event_path(@event)
        return
      end

      # Queue the retry job
      StripeWebhookRetryJob.perform_later(@event.id)

      flash[:success] = 'Event queued for retry. Please check back in a few moments.'
      redirect_to admin_stripe_event_path(@event)
    end

    def retry_all_critical
      critical_events = StripeEvent.critical.with_errors

      count = 0
      critical_events.find_each do |event|
        StripeWebhookRetryJob.perform_later(event.id)
        count += 1
      end

      flash[:success] = "Scheduled #{count} critical webhook events for retry"
      redirect_to admin_stripe_events_path
    end

    def destroy
      @event = StripeEvent.find(params[:id])
      @event.destroy

      flash[:success] = 'Event deleted successfully'
      redirect_to admin_stripe_events_path
    end

    def clear_old_events
      # Keep recent events (last 90 days) and events with errors
      cutoff_date = 90.days.ago
      old_events = StripeEvent.successful.where('created_at < ?', cutoff_date)
      count = old_events.count

      old_events.destroy_all

      flash[:success] = "Cleared #{count} old events"
      redirect_to admin_stripe_events_path
    end

    def clear_resolved
      # Find events that have been retried successfully
      resolved_events = StripeEvent.where(error: nil)
                                   .where('retry_count > 0')
                                   .where('created_at < ?', 30.days.ago)

      count = resolved_events.count
      resolved_events.destroy_all

      flash[:success] = "Cleared #{count} resolved webhook events"
      redirect_to admin_stripe_events_path
    end

    def report
      # Generate a simple report directly in the controller
      @report = "Stripe Webhook Report\n"
      @report += "===================\n\n"
      @report += "Generated at: #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}\n\n"

      # Basic statistics
      @report += "Overall Statistics:\n"
      @report += "-------------------\n"
      @report += "Total events: #{StripeEvent.count}\n"
      @report += "Error events: #{StripeEvent.with_errors.count}\n"
      @report += "Recent events (24h): #{StripeEvent.where('created_at > ?', 24.hours.ago).count}\n"
      @report += "Recent errors (24h): #{StripeEvent.with_errors.where('created_at > ?', 24.hours.ago).count}\n\n"

      # Latest errors
      @report += "Latest Errors:\n"
      @report += "-------------\n"
      StripeEvent.with_errors.order(created_at: :desc).limit(5).each do |event|
        @report += "#{event.created_at.strftime('%Y-%m-%d %H:%M:%S')} - #{event.event_type}\n"
        @report += "Error: #{event.error.to_s.truncate(100)}\n\n"
      end

      # Only render HTML for now
      respond_to do |format|
        format.html # report.html.erb
      end
    end

    def manual_intervention_guide
      @event = StripeEvent.find(params[:id])
      @intervention_steps = generate_intervention_steps(@event)
    end

    private

    def find_subscriber_for_event(event)
      # If it's a subscription event, try to find the subscriber
      if event.event_type&.include?('subscription') && event.event_data.present?
        subscription_id = event.event_data['id']
        @subscriber = Subscriber.find_by(stripe_subscription_id: subscription_id) if subscription_id.present?
      end

      # If it's a customer event, try to find the subscriber
      if @subscriber.nil? && event.event_data.present?
        customer_id = event.event_data['customer']
        @subscriber = Subscriber.find_by(stripe_customer_id: customer_id) if customer_id.present?
      end

      # If it's an invoice event, try to find the subscriber
      return unless @subscriber.nil? && event.event_type&.include?('invoice') && event.event_data.present?
      customer_id = event.event_data['customer']
      return unless customer_id.present?
      @subscriber = Subscriber.find_by(stripe_customer_id: customer_id)
    end

    def generate_intervention_steps(event)
      # Generate step-by-step instructions based on event type
      case event.event_type
      when 'invoice.payment_succeeded'
        [
          '1. Verify that the payment was actually received in Stripe Dashboard',
          '2. Check if the subscriber record exists in your database',
          "3. Ensure the subscription status is correctly set to 'active'",
          '4. If status is incorrect, manually update it using the Subscribers admin panel',
          '5. Verify that the payment success email was sent to the customer',
          '6. If needed, trigger a manual email notification to the customer'
        ]
      when 'invoice.payment_failed'
        [
          '1. Check if the customer has an active payment method in Stripe Dashboard',
          '2. Verify if the customer has sufficient funds',
          '3. Contact the customer to update their payment method',
          '4. Once updated, retry the webhook or create a new invoice'
        ]
      when 'customer.subscription.deleted'
        [
          '1. Check if this was an intentional cancellation in the Stripe Dashboard',
          '2. If unintentional, contact the customer to confirm',
          '3. Create a new subscription for the customer if needed',
          '4. Update local subscriber record with the new subscription ID'
        ]
      when 'checkout.session.completed'
        [
          '1. Check if the checkout session exists in Stripe',
          '2. Verify if a subscription was created for the customer',
          '3. If the subscription exists, manually update the subscriber record',
          '4. If no subscription was created, contact the customer to retry the payment'
        ]
      when 'customer.subscription.updated'
        [
          '1. Check the current subscription status in Stripe',
          '2. Verify if our local record matches the Stripe status',
          "3. If they don't match, manually update the subscriber status",
          '4. Check if there were plan changes that need to be reflected'
        ]
      else
        ['Please contact the development team for assistance with this webhook type.']
      end
    end
  end
end
