module StaticAdmin
  class StaticAdmin::DashboardController < StaticAdminController
    def index
      # lesson_views_by_campaign = ::PosthogDataService.fetch_lesson_template_views_by_campaign(46, 90)
    end

    def fetch_schools
      @schools = {
        subscribed: School.generic.subscribed.count,
        active: School.generic.active.count,
        total: School.generic.count
      }

      render :schools, layout: nil
    end

    def fetch_teachers
      @teachers = {
        active: Teacher.active.count,
        total: Teacher.count
      }

      render :teachers, layout: nil
    end

    def fetch_pupils
      @pupils = {
        active: Pupil.active.count,
        total: Pupil.count
      }

      render :pupils, layout: nil
    end
  end
end
