module StaticAdmin
  class FlowDocumentsController < StaticAdminController
    before_action :set_flow
    before_action :set_flow_document, only: [:edit, :update, :destroy]

    # GET /admin/flows/:flow_id/documents/new
    def new
      @flow_document = @flow.flow_documents.build
    end

    # POST /admin/flows/:flow_id/documents
    def create
      @flow_document = @flow.flow_documents.build(flow_document_params)

      if @flow_document.save
        redirect_to documents_admin_flow_path(@flow), notice: 'Document was successfully added.'
      else
        error_message = @flow_document.errors.full_messages.join(', ')
        redirect_to documents_admin_flow_path(@flow), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to documents_admin_flow_path(@flow),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # GET /admin/flows/:flow_id/documents/:id/edit
    def edit
    end

    # PATCH/PUT /admin/flows/:flow_id/documents/:id
    def update
      if @flow_document.update(flow_document_params)
        redirect_to documents_admin_flow_path(@flow), notice: 'Document was successfully updated.'
      else
        error_message = @flow_document.errors.full_messages.join(', ')
        redirect_to documents_admin_flow_path(@flow), alert: error_message
      end
    rescue ActionController::ParameterMissing
      redirect_to documents_admin_flow_path(@flow),
                  alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end

    # DELETE /admin/flows/:flow_id/documents/:id
    def destroy
      @flow_document.destroy
      redirect_to documents_admin_flow_path(@flow), notice: 'Document was successfully deleted.'
    end

    # PUT /admin/flows/:flow_id/documents/reorder
    def reorder
      document_ids = params[:document_ids]

      if document_ids.present?
        start_weight = 0

        ActiveRecord::Base.transaction do
          document_ids.each_with_index do |id, index|
            FlowDocument.where(id: id).update_all(weight: index + start_weight)
          end
        end

        head :ok
      else
        head :bad_request
      end
    end

    private

    def set_flow
      @flow = Flow.find(params[:flow_id])
    end

    def set_flow_document
      @flow_document = @flow.flow_documents.find(params[:id])
    end

    def flow_document_params
      permitted = params.require(:flow_document).permit(
        :title,
        :description,
        :fileboy_id,
        :document_type,
        :weight
      )

      permitted
    end
  end
end
