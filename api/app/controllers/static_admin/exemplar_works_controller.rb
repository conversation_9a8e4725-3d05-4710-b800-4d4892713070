module StaticAdmin
  class StaticAdmin::ExemplarWorksController < StaticAdminController
    before_action :set_exemplar_work, only: %i[show edit update destroy]
    before_action :set_units_and_lessons, only: %i[new create edit update]

    admin_section :content

    # GET /admin/exemplar-works
    def index
      works = ExemplarWork.joins(:user)
                          .left_joins(:new_library_unit, :lesson_template, user: :school)
                          .joins(Arel.sql("LEFT JOIN users AS approved_by_users ON approved_by_users.id = exemplar_works.approved_by_id"))
                          .select(
                            "exemplar_works.*, 
                            users.name AS user_name,
                            schools.name AS school_name,
                            new_library_units.name AS new_library_unit_name, 
                            lesson_templates.name AS lesson_template_name,
                            approved_by_users.name AS approved_by_name"
                          )
                          .ransack(
                            display_name_or_title_or_user_name_i_cont: params[:query],
                            status_eq: params[:scope]
                          )

      sortable_columns = %w[title created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @exemplar_works = paginate works.result.order("#{sort} #{order}")
    end

    # GET /admin/exemplar-works/new
    def new
      @exemplar_work = ExemplarWork.new()
    end

    # POST /admin/exemplar-works
    def create
      @exemplar_work = ExemplarWork.new(exemplar_work_params.merge(user_id: @current_user.id))

      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @exemplar_work.fileboy_id = fileboy_image_id if fileboy_image_id

      @exemplar_work.approved_by = exemplar_work_params[:status] == "approved" ? @current_user : nil

      if @exemplar_work.save
        redirect_to [:edit, :admin, @exemplar_work], notice: 'Exemplar work was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/exemplar-works/:id
    def show; end

    # PATCH /admin/exemplar-works/:id
    def update
      fileboy_image_id = upload_image params[:fileboy_image] if params[:fileboy_image].present?
      @exemplar_work.fileboy_id = fileboy_image_id if fileboy_image_id

      @exemplar_work.approved_by = exemplar_work_params[:status] == "approved" ? @current_user : nil

      if @exemplar_work.update(exemplar_work_params)
        user = @exemplar_work.user
        if user&.teacher? && user.allows_notification?(:exemplar_work) && @exemplar_work.status == "approved"
          SchoolMailer.exemplar_work_approved(user, [@exemplar_work]).deliver_now
        end

        redirect_to [:edit, :admin, @exemplar_work], notice: 'Exemplar work was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/exemplar-works/:id
    def destroy
      @exemplar_work.destroy
      redirect_to admin_exemplar_works_url, notice: 'Exemplar work was successfully deleted.'
    end

    private

    def exemplar_work_params
      params.require(:exemplar_work)
            .permit(:display_name, :status, :title, :body, :fileboy_id, :new_library_unit_id, :lesson_template_id)
    end

    def set_exemplar_work
      @exemplar_work = ExemplarWork.find(params[:id])
    end

    def set_units_and_lessons
      @units = NewLibrary::Unit.accessible_by(current_ability)
                                .joins(:year)
                                .joins("LEFT JOIN new_library_curricula ON new_library_years.curriculum_id = new_library_curricula.id")
                                .pluck("new_library_units.id, new_library_units.name, new_library_years.name, new_library_curricula.name")
                                .map do |id, unit_name, year_name, curriculum_name|
                                  extra_details = [year_name, curriculum_name].compact.join(" - ")
                                  ["#{[unit_name, extra_details].compact.join(': ')}", id]
                                end

      @lessons = Lesson::Template.accessible_by(current_ability)
                                  .where(user_generated: false)
                                  .pluck(:id, :name, :machine_name)
                                  .map do |id, name, machine_name|
                                    [[name, machine_name].compact.join(" - "), id]
                                  end
    end

  end
end