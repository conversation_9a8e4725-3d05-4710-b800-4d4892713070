module StaticAdmin
  class StaticAdmin::LibraryDocumentsController < StaticAdminController
    include Fileboy
    before_action :get_model, only: [:new]
    before_action :set_document, only: [:edit, :update, :destroy]

    # GET /admin/:model/:model_id/documents
    def index; end

    # GET /admin/:model/:model_id/documents/new
    def new
      @document = NewLibrary::Document.new
    end

    # POST /admin/:model/:model_id/documents
    def create
      case
      when params.key?(:year_id)
        id = params[:year_id]
        @model = NewLibrary::Year.find(id)
        @back_path = edit_admin_year_path(id: id)
      when params.key?(:unit_id)
        id = params[:unit_id]
        @model = NewLibrary::Unit.find(id)
        @back_path = edit_admin_unit_path(id: id)
      when params.key?(:video_id)
        id = params[:video_id]
        @model = Video.find(id)
        @back_path = edit_admin_video_path(id: id)
      end

      @document = @model.documents.build(document_params)

      fileboy_file_id = upload_image params[:fileboy] if params[:fileboy].present?
      @document.fileboy_file_id = fileboy_file_id if fileboy_file_id

      if @document.save
        redirect_to @back_path, notice: 'Document was successfully created.'
      else
        render :new
      end
    end

    # GET /admin/:model/:model_id/documents/:id/edit
    def edit; end

    # PATCH/PUT /admin/:model/:model_id/documents/:id
    def update
      fileboy_file_id = upload_image params[:fileboy] if params[:fileboy].present?
      @document.fileboy_file_id = fileboy_file_id if fileboy_file_id

      if @document.update(document_params)
        redirect_to @back_path, notice: 'Document was successfully updated.'
      else
        render :edit
      end
    end

    # DELETE /admin/:model/:model_id/documents/:id
    def destroy
      @document.destroy

      redirect_to @back_path, notice: 'Document was successfully deleted.'
    end

    private

    # Use callbacks to share common setup or constraints between actions.
    def get_model
      case
      when params.key?(:year_id)
        id = params[:year_id]
        @model = NewLibrary::Year.find(id)
        @back_path = edit_admin_year_path(id: id)
        @save_path = admin_year_library_documents_path(@model)
        @resource = 'Year'
      when params.key?(:unit_id)
        id = params[:unit_id]
        @model = NewLibrary::Unit.find(id)
        @back_path = edit_admin_unit_path(id: id)
        @save_path = admin_unit_library_documents_path(@model)
        @resource = 'Unit'
      when params.key?(:video_id)
        id = params[:video_id]
        @model = Video.find(id)
        @back_path = edit_admin_video_path(id: id)
        @save_path = admin_video_library_documents_path(@model)
        @resource = 'Video'
      end
    end

    def set_document
      get_model
      @document = NewLibrary::Document.find(params[:id])
      case
      when params.key?(:year_id)
        @delete_path = admin_year_library_document_path(@model, @document)
        @save_path = admin_year_library_document_path(@model, @document)
      when params.key?(:unit_id)
        @delete_path = admin_unit_library_document_path(@model, @document)
        @save_path = admin_unit_library_document_path(@model, @document)
      when params.key?(:video_id)
        @delete_path = admin_video_library_document_path(@model, @document)
        @save_path = admin_video_library_document_path(@model, @document)
      end
    end

    # Only allow a list of trusted parameters through.
    def document_params
      params.require(:new_library_document).permit(:name, :fileboy)
    end

  end
end
  