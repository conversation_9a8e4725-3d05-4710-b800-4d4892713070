module StaticAdmin
  class StaticAdmin::MarketingLinksController < StaticAdminController
    admin_section :marketing
    
    def new
      # This will render the form for generating tracking links
    end

    def create
      @marketing_identifier = params[:marketing_identifier]
      @link_destination = params[:link_destination]
      
      if @marketing_identifier.present? && @link_destination.present?
        # Process the link destination to handle full URLs
        processed_destination = process_link_destination(@link_destination)
        
        # Generate the tracking link using URL encoding for the destination
        @tracking_link = marketing_tracking_link_url(
          marketing_identifier: @marketing_identifier,
          link_destination: CGI.escape(processed_destination)
        )
        
        # Store the original for display
        @original_destination = @link_destination
        @processed_destination = processed_destination
        
        # Render the form again with the generated link
        render :new
      else
        flash.now[:error] = "Please fill in both fields"
        render :new
      end
    end

    private

    def process_link_destination(url)
      # If it's a full URL, extract just the path and query string
      if url.match?(/\Ahttps?:\/\//)
        uri = URI.parse(url)
        # Return path + query string + fragment
        result = uri.path
        result += "?#{uri.query}" if uri.query.present?
        result += "##{uri.fragment}" if uri.fragment.present?
        result
      else
        # If it's already a relative path, ensure it starts with /
        url.start_with?('/') ? url : "/#{url}"
      end
    rescue URI::InvalidURIError
      # If URL parsing fails, return as-is
      url
    end
  end
end