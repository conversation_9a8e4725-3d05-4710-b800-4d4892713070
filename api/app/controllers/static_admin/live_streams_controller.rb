module StaticAdmin
  class LiveStreamsController < StaticAdminController
    before_action :set_live_stream, only: %i[edit update destroy preview documents messages submit_reply delete]

    admin_section :content

    def index
      sortable_columns = %w[title videos2.name published start_time end_time created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'
      @live_streams = LiveStream.accessible_by(current_ability).includes(:video)

      # Apply search query
      @live_streams = @live_streams.where('title ILIKE :query OR url ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      @live_streams = @live_streams.order("#{sort} #{order}")
      @live_streams = @live_streams.page(params[:page])
    end

    def new
      @live_stream = LiveStream.new
    end

    def edit; end

    def preview
      pinned = params[:pinned] == 'true'
      @messages = @live_stream.live_stream_messages.accessible_by(current_ability).where(is_reply: false).order(created_at: :asc)
      @messages = @messages.where(pinned: true) if pinned
    end

    def documents; end
    def delete; end

    def destroy
      @live_stream.destroy
      redirect_to admin_live_streams_path, notice: 'Live Stream deleted'
    end

    def create
      @live_stream = LiveStream.new(live_stream_params)
      authorize! :manage, @live_stream
      if @live_stream.save
        redirect_to edit_admin_live_stream_path(@live_stream), notice: 'Live stream created successfully.'
      else
        render :new
      end
    end

    def update
      stream = LiveStream.find(params.require(:id))
      authorize! :manage, stream
      if stream.update(live_stream_params)
        redirect_to edit_admin_live_stream_path(stream), notice: 'Live stream updated successfully.'
      else
        render :edit
      end
    end

    def show
      stream = LiveStream.find(params.require(:id))
      authorize! :read, stream
      render json: stream.as_json.merge({ documents: stream.live_stream_documents.accessible_by(current_ability) })
    end

def messages
  pinned = params[:pinned] == 'true'
  messages_query = @live_stream.live_stream_messages
    .accessible_by(current_ability)
    .where(is_reply: false)
    .includes([live_stream_messages: :user], :user)
    .order(created_at: :asc)
  
  messages_query = messages_query.where(pinned: true) if pinned
  
  render json: {
    messages: messages_query.map do |message|
      {
        id: message.id,
        message: message.message,
        created_at: message.created_at.strftime("%d/%m/%Y %H:%M"),
        user_name: message.user&.name.presence || 'Anonymous',
        pinned: message.pinned,
        replies_count: message.live_stream_messages.count,
        replies: message.live_stream_messages.includes(:user).order(created_at: :asc).map do |reply|
          {
            id: reply.id,
            message: reply.message,
            created_at: reply.created_at.strftime("%d/%m/%Y %H:%M"),
            user_name: reply.user&.name.presence || 'Anonymous'
          }
        end
      }
    end,
    total_count: messages_query.count
  }
end

    def submit_reply
      data = params.permit(:live_stream_message_id, :message)
      live_stream_message_id = data[:live_stream_message_id]
      message = data[:message]

      new_message = LiveStreamMessage.new(
        live_stream: @live_stream,
        live_stream_message_id: live_stream_message_id,
        message: message,
        user: current_user,
        is_reply: true
      )
      authorize! :create, new_message
      if new_message.save
        redirect_to preview_admin_live_stream_path(@live_stream), notice: 'Reply submitted successfully.'
      else
        redirect_to preview_admin_live_stream_path(@live_stream), alert: new_message.errors.full_messages.to_sentence
      end
    end

    def pin_message
      stream = LiveStream.find(params.require(:id))
      authorize! :manage, stream
      message = stream.live_stream_messages.find(params.require(:message_id))
      authorize! :manage, message
      message.update(pinned: !message.pinned)
      redirect_back fallback_location: edit_admin_live_stream_path(stream)
    end

    private

    def set_live_stream
      @live_stream = LiveStream.find(params[:id])
    end

    def live_stream_params
      params.require(:live_stream).permit(:url, :title, :published, :start_time, :end_time, :fileboy_image_id, :include_live_chat, :category, :video_id, school_ids: [])
    end
  end
end
