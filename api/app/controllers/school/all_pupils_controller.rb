class School::AllPupilsController < SchoolController
  before_action :authenticate_admin_teacher
  before_action :set_pupil, only: %i[show edit update destroy]

  def index

    collection = current_school.pupils

    if params[:teacher_id]
      collection = current_school.teachers.find(params[:teacher_id]).pupils.group(:id)
    end

    if params[:form_id]
      collection = Form.find(params[:form_id]).pupils
    end

    if params[:not_in_form]
      collection = current_school.pupils.left_joins(:enrollments).group(:id).having('COUNT(enrollments.id) < 1')
    end

    list_with_response collection.select(:id, :identifier, :name, :created_at, :last_sign_in_at, :last_activity_at,:school_id, :points, :dob, :gender, :ethnicity, :location),
                        params,
                        allowed_scopes: pupils_scopes,
                        search_params: ['users.name', "identifier"],
                        default_sort: { name: :asc }
  end

  def create
    pupil = Pupil.new(pupil_params.merge(school_id: current_user.school_id))

    if pupil.save
      render json: { saved: true, record: pupil }
    else
      render json: { saved: false, errors: pupil.errors.full_messages }
    end
  end

  def show
    render json: @pupil.as_json.merge(
      form_ids: @pupil.form_ids,
      latest_quiz_results: @pupil.latest_quiz_results
    )
  end

  def update
    save_with_response @pupil, pupil_params
  end

  def destroy
    destroy_record @pupil
  end

  def mass_destroy
    ids = params.permit(ids: [])[:ids]
    pupils = current_school.pupils.where(id: ids)
    mass_destroy_records(pupils)
  end

  private

  def set_pupil
    @pupil = current_school.pupils.find(params[:id])
  end

  def pupils_scopes
    %w[all]
  end

  def pupil_params
    params.permit(
      :name,
      :identifier,
      :dob,
      :gender,
      :ethnicity,
      :location,
      form_ids: [],
    )
  end

end
