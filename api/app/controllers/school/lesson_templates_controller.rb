class School::Lesson<PERSON><PERSON>platesController < SchoolController
  before_action :set_lesson_template, only: %i[show load_rocket_words_quiz update_pupil_results presentation]

  def show
    render json: @lesson_template.data_for_show
  end

  def get_form_ids_for_templates
    if current_user.is_school_admin
      form_ids = current_user.school.forms.ids
    else
      form_ids = current_user.forms.ids
    end

    render json: Form.where(id: form_ids).joins(:lessons).where(lesson_lessons: { template_id: params[:template_ids] }).ids
  end

  def presentation
    TrackingPresentationView.create!({
      lesson_template: @lesson_template,
      lesson: @lesson_template.lesson_for_user(current_user),
      user: current_user
    })
    render json: @lesson_template.presentation_data(current_user, current_locale)
  end

  private

  def set_lesson_template
    @lesson_template = (
      current_user.accessible_lesson_templates.or(Lesson::Template.where(id: current_user.school.user_lesson_templates.ids))
    ).find(params[:id])
  end

  def params_with_image
    params_image = params['image']
    params_sponsor_logo = params['sponsor_logo']
    update_params = new_library_lesson_template_params
    update_params['image'] = params_image unless params_image.is_a? String
    update_params['sponsor_logo'] = params_sponsor_logo unless params_sponsor_logo.is_a? String
    update_params['plan_learning_outcomes_attributes'] = update_params.delete('plan_learning_outcomes')
    update_params
  end
end
