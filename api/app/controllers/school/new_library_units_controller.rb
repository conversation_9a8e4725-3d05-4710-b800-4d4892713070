class School::NewLibraryUnitsController < SchoolController
  before_action :set_unit, only: %i[show forms_assigned]

  def show
    render json: @unit.data_for_show(current_user)
  end

  def forms_assigned
    current_user_template_ids = current_user.accessible_lesson_templates.ids
    available_template_ids = @unit.lesson_templates
                              .available
                              .joins(:available_countries)
                              .group(:id)
                              .where(countries: { id: current_user.country.id })
                              .where(id: current_user_template_ids)
                              .pluck(:id)
                              .sort


    forms = current_user.is_school_admin ? current_school.forms : current_user.forms

    forms = forms
      .joins(:lessons)
      .order('forms.name')
      .group(:id)
      .select('forms.id, forms.name, array_agg(lesson_lessons.template_id) as all_template_ids')

    forms = forms.select do |form|
      available_template_ids.any? && (available_template_ids - form['all_template_ids']).empty?
    end.map do |form|
      { id: form.id, name: form.name }
    end

    render json: { records: forms }
  end

  private

  def set_unit
    @unit = NewLibrary::Unit.find(params[:id])
  end
end
