class School::All<PERSON><PERSON>ers<PERSON>ontroller < SchoolController
  before_action :authenticate_admin_teacher
  before_action :set_teacher, only: %i[show update edit destroy]

  def index
    collection = current_school.teachers

    if (pupil_id = params[:pupil_id])
      collection = current_school.pupils.find(pupil_id).teachers
    end

    if (class_id = params[:class_id])
      collection = current_school.all_forms.find(class_id).teachers
    end

    list_with_response collection, params, search_params: %w[name email], serialized: true, default_sort: { name: :asc }
  end

  def create
    teacher = Teacher.new
    teacher.should_validate_domain = true
    return render json: { errors: ['Individual account already exists'] }, status: :unprocessable_entity if IndividualUser.find_by(email: teachers_params[:email].downcase)
    save_with_response teacher, teachers_params.merge(school_id: current_user.school_id)
  end

  def update
    @teacher.should_validate_domain = true
    save_with_response @teacher, teachers_params
  end

  def show
    render json: @teacher.as_json.merge({form_ids: @teacher.form_ids})
  end

  def destroy
    destroy_record @teacher
  end

  private

  def set_teacher
    @teacher = current_school.teachers.find(params[:id])
  end

  def teachers_params
    params.permit(:name, :email, :password, :is_school_admin, :gender, :dob, :working_days, :alias, :job_title, :science_lead, form_ids: [])
  end
end
