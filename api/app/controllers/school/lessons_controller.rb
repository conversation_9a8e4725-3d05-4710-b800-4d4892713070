class School::<PERSON>ons<PERSON>ontroller < SchoolController
  def index
    collection = current_school ? current_school.lessons : Lesson::Lesson.none

    if (pupil_id = params[:pupil_id])
      collection = current_school.pupils.find(pupil_id).lessons
    end

    if (class_id = params[:class_id])
      collection = current_school.forms.find(class_id).lessons
    end

    collection = collection
      .joins(:form)
      .group(:id)
      .including_done_attr
      .select('FIRST(forms.name) AS form_name')

    opts = {}
    sort = opts[:default_sort] || { time: :asc }
    processed_collection = collection

    if (template_id = params[:template_id])
      processed_collection = processed_collection.where(template_id: template_id)
    end

    if (teacher_id = params[:teacher_id])
      forms = current_school.teachers.find(teacher_id).forms
      processed_collection = processed_collection.where(form: forms)
    end

    if params[:query] && params[:query].present?
      processed_collection = processed_collection.joins(:template).where("lesson_templates.name ILIKE ?", "%#{params[:query]}%")
    end

    allowed_scopes ||= []

    if params[:scope] && allowed_scopes && allowed_scopes.include?(params[:scope])
      processed_collection = processed_collection.send(params[:scope])
    end

    if params[:skip_pagination]
      render json: { records: processed_collection, meta: {} }
    else
      pagy, records = pagy(processed_collection.order(sort))
      render json: { records: records, meta: pagy }
    end
  end

  def show
    lesson = current_school.lessons.find(params[:id])
    render json: lesson.as_json.merge(form_name: lesson.form.name, done: lesson.done)
  end

  def remove_from_form
    lesson = current_school.lessons.find(params[:id])

    begin
      lesson.form.remove_lesson_template_ids([lesson.template_id])

      render json: { saved: true }
    rescue => e
      render json: { saved: false, errors: [e] }
    end
  end

  def reschedule
    lesson = current_school.lessons.not_done.find(params[:id])

    begin
      lesson.update!(time: params[:time])

      render json: { saved: true }
    rescue => e
      render json: { saved: false, errors: [e] }
    end
  end
end
