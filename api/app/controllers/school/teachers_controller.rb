class School::TeachersController < SchoolController
  before_action :set_teacher, only: %i[show update edit destroy]

  def index
    collection = current_school.teachers

    if pupil_id = params[:pupil_id]
      collection = current_school.pupils.find(pupil_id).teachers.group(:id)
    end

    if class_id = params[:form_id]
      collection = current_school.all_forms.find(class_id).teachers
    end

    list_with_response collection, params, search_params: %w[users.name users.email], serialized: true, default_sort: { name: :asc }
  end

  def update
    @teacher.should_validate_domain = true
    save_with_response @teacher, teachers_params
  end

  def show
    render json: @teacher.as_json.merge({form_ids: @teacher.form_ids})
  end

  private

  def set_teacher
    @teacher = current_school.teachers.find(params[:id])
  end

  def teachers_params
    params.permit(:name, :email, :password, :gender, :ethnicity, :dob, :working_days, :alias, :job_title, :science_lead, form_ids: [])
  end
end
