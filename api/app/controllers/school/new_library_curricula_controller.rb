class School::NewLibraryCurriculaController < SchoolController
  before_action :set_curriculum, only: %i[show edit update destroy]

  def index
    collection = NewLibrary::Curriculum
                   .joins(lesson_templates: :available_countries)
                   .group(:id)
                   .where(
                     country: current_user.country,
                     lesson_templates: { available: true },
                     countries: { id: current_user.country.id },
                   )

    list_with_response(
      collection,
      params,
      search_params: %w[name],
      allowed_scopes: %w[published],
      default_sort: { name: :asc },
    )
  end

  def show
    cached = custom_cache.fetch("curriculum_show_#{params[:id]}", expires_in: 1.day) do
      years = @curriculum.years
                         .joins(lesson_templates: :available_countries)
                         .group(:id)
                         .where(lesson_templates: { available: true }, countries: { id: current_user&.country&.id || 1 })
                         .ordered
      @curriculum.as_json.merge(years: years)
    end
    render json: cached
  end

  private

  def set_curriculum
    @curriculum = NewLibrary::Curriculum.find(params[:id])
  end
end
