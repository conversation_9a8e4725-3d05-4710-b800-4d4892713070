class School::XlsImportsController < SchoolController
  before_action :set_xls_import, only: %i[show update]

  def index
    list_with_response(
      XlsImport.where(school: current_school),
      params,
      default_sort: { created_at: :desc }
    )
  end

  def show
    render json: @xls_import
  end

  def create
    save_with_response XlsImport.new, xls_import_params
  end

  def update
    save_with_response @xls_import, xls_import_params
  end

  private

  def set_xls_import
    @xls_import = XlsImport.where(school: current_school).find(params[:id])
  end

  def xls_import_params
    params
      .permit(:fileboy_file_id, :run_import, :ignore_warnings, :build_lessons, data: {})
      .merge(school_id: current_school.id, locale: current_locale, user: current_user)
  end
end
