class School::FormsController < SchoolController
  before_action :set_form, only: [:show, :edit, :build_lessons, :pupil_letter_data_for_form]

  def index
    if params[:show_all]
      collection = current_user.forms
    else
      collection = current_user.forms.current_school_year
    end
    if (teacher_id = params[:teacher_ids])
      collection = collection.for_users(teacher_id["0"])
    end
    if (pupil_id = params[:pupil_ids])
      collection = current_school.pupils.find(pupil_id["0"]).forms
    end
    if (pupil_id = params[:pupil_id])
      collection = current_school.pupils.find(pupil_id).forms
    end

    list_with_response collection, params, search_params: %w[name], default_sort: { name: :asc }
  end

  def show
    render json: @form.as_json.merge(
      template_ids: @form.template_ids,
      done_template_ids: @form.done_template_ids,
      not_done_template_ids: @form.not_done_template_ids,
    )
  end

  def class_graph_data
    @form = current_school.forms.find(params[:form_id])
    render json: @form.class_graph_data
  end

  def forms_for_current_user
    forms = current_user.forms
    if current_user.is_school_admin?
      forms = current_user.school.forms
    end

    render json: { records: forms }
  end

  def build_lessons
    lesson_template_ids = params["lesson_template_ids"]
    lesson_start_date = params["lesson_start_date"]
    lesson_weekdays = params["lesson_weekdays"]

    @form.update(
      lesson_start_date:lesson_start_date,
      lesson_weekdays: lesson_weekdays,
    )

    @form.add_accessible_lesson_template_ids(current_user, current_school, lesson_template_ids)

    render json: { saved: true }
  rescue => e
    render json: { saved: false, errors: [e] }
  end

  def pupil_letter_data_for_form
    data = @form.pupils.pluck_to_hash(:name, :identifier, :school_id, :id).map do |pupilData|
      pupilData["qr_code"] = Pupil.find(pupilData[:id]).qr_signin_link
      pupilData
    end
    render json: data
  end

  private

  def set_form
    @form = current_user.forms.find(params[:id])
  end
end
