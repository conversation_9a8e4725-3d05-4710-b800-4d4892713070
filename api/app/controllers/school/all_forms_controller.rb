class School::AllFormsController < SchoolController
  before_action :set_form, only: [:show, :edit, :update, :destroy, :add_pupils, :remove_pupils, :build_lessons, :pupil_letter_data_for_form]

  def index
    if params[:show_all]
      collection = current_school.forms
    else
      collection = current_school.forms.current_school_year
    end
    if (teacher_id = params[:teacher_ids])
      collection = collection.for_users(teacher_id["0"])
    end
    if (pupil_id = params[:pupil_ids])
      collection = current_school.pupils.find(pupil_id["0"]).forms
    end
    if (pupil_id = params[:pupil_id])
      collection = current_school.pupils.find(pupil_id).forms
    end

    list_with_response collection, params, search_params: %w[name], default_sort: { name: :asc }
  end

  def show
    render json: @form
  end

  def forms_for_school
    render json: { records: current_school.forms.pluck_to_hash(:id, :name) }
  end

  def create
    save_with_response Form.new, form_params
  end

  def update
    save_with_response @form, form_params
  end

  def destroy
    destroy_record @form
  end

  def add_pupils
    if @form.pupil_ids += params[:pupil_ids]
      render json: { saved: true }
    else
      render json: { saved: false }
    end
  end

  def remove_pupils
    if @form.pupil_ids -= params[:pupil_ids]
      render json: { saved: true }
    else
      render json: { saved: false }
    end
  end

  def build_lessons
    begin
      lesson_template_ids = params["lesson_template_ids"]
      lesson_start_date = params["lesson_start_date"]
      lesson_weekdays = params["lesson_weekdays"]

      @form.update(
        lesson_start_date: lesson_start_date,
        lesson_weekdays: lesson_weekdays,
      )

      @form.add_accessible_lesson_template_ids(current_user, current_school, lesson_template_ids)

      render json: { saved: true }

      rescue => e
        render json: { saved: false, errors: [e] }
      end
  end

  def pupil_letter_data_for_form
    data = @form.pupils.pluck_to_hash(:name, :identifier, :school_id, :id).map do |pupilData|
      pupilData["qr_code"] = Pupil.find(pupilData[:id]).qr_signin_link
      pupilData
    end
    render json: data
  end

  private

  def set_form
    @form = current_school.forms.find(params[:id])
  end

  def form_params
    params.permit(:name, teacher_ids: [], pupil_ids: [], lesson_weekdays: {}).merge(school_id: current_school.id)
  end
end
