class School::DashboardController < SchoolController
  def show
    render json: {
      next_lesson: current_user.lessons.ordered.upcoming.first&.as_json(methods: :template),
      previous_lesson: current_user.lessons.reversed.past.first&.as_json(methods: :template),
      pupils: current_user.pupils.group(:id).pluck_to_hash(:id, :name),
      motd: Motd.for_dashboard(params["currentLocale"], current_user.is_school_admin ? :for_admin_teachers : :for_teachers, current_school).first(3),
      school_admins: current_user.school.teachers.where(is_school_admin: true).select(:name, :email, :type, :deleted)
    }
  end

  def get_sign_in_token_for_pupil
    pupil = current_school.pupils.find(params[:id])
    render json: {
      pupil_token: pupil.generate_sign_in_token,
      return_token: current_user.generate_sign_in_token,
    }
  end
end
