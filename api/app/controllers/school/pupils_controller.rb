class School::<PERSON><PERSON>ils<PERSON><PERSON>roller < SchoolController
  before_action :set_pupil, only: %i[show edit update destroy lessons]

  def index
    collection = current_user.pupils.group(:id)

    if params[:form_id]
      collection = current_user.forms.find(params[:form_id]).pupils
    end

    list_with_response collection.select(:id, :identifier, :name, :created_at, :last_sign_in_at, :last_activity_at, :school_id, :points),
                        params,
                        allowed_scopes: pupils_scopes,
                        search_params: ['users.name', "identifier"],
                        default_sort: { name: :asc }
  end


  def show
    render json: @pupil.as_json.merge(
      form_ids: @pupil.form_ids,
      latest_quiz_results: @pupil.latest_quiz_results,
      qr_code: params[:withQr] ? @pupil.qr_signin_link : nil
    )
  end

  def update
    save_with_response @pupil, pupil_params
  end

  private

  def set_pupil
    @pupil = current_user.pupils.find(params[:id])
  end

  def pupils_scopes
    %w[all]
  end

  def pupil_params
    params.permit(
      :name,
      :identifier,
      :dob,
      :gender,
      form_ids: [],
    )
  end
end
