module StaticPupil
  class LessonsController < StaticPupilController
    before_action :set_lesson, only: %i[lesson_show rocket_word_quiz assessment_quiz word_search lesson_plan]

    def lesson_index
      lessons = @current_pupil.lessons.accessible_by(current_ability).includes([:template])

      subject_id = params[:subject_id]
      form_id = params[:form_id]

      @subjects = []

      filtered_form_unit_id = params[:form_unit_id]

      if (filtered_form_unit_id.present?)
        lessons = lessons.where(form_unit_id: filtered_form_unit_id)
        @filtered_form_unit = FormUnit.find_by(id: filtered_form_unit_id)
      end

      if (form_id.present?)
        lessons = lessons.where(form_id: form_id)
        @filtered_form = Form.find_by(id: form_id)
      end

      if (subject_id.present?)
        @filtered_subject = NewLibrary::Subject.find_by(id: subject_id)
      end

      @lesson_units = lessons.group_by(&:form_unit_id).map do |form_unit_id, lessons|
        form_unit = FormUnit.find_by(id: form_unit_id) if form_unit_id.present?

        next if subject_id.present? && form_unit.nil?

        # Compute the unit data
        unit_data = { name: 'No Unit', year: '', subject: '' }
        if form_unit.present?

          subject = form_unit.new_library_unit.year.subject
          @subjects << subject

          next if subject_id.present? && subject.id != subject_id.to_i

          unit_data = {
            year: form_unit.new_library_unit.year.name,
            unit: {
              name: form_unit.name,
              id: form_unit.id,
            },
            subject: {
              name: subject.name,
              id: subject.id
            },
            form: {
              name: form_unit.form.name,
              id: form_unit.form.id
            },
          }
        end

        # Compute the lesson data
        lesson_data = lessons.map do |lesson|
          lesson_datum = lesson.attributes.symbolize_keys
          lesson_datum[:fileboy_image_id] = lesson.template.fileboy_image_id
          lesson_datum[:name] = lesson.template.name
          lesson_datum[:progress] = {
            presentation_viewed: !!@current_pupil.tracking_presentation_views.find_by(lesson_id: lesson.id),
            rocket_word_quiz: !!@current_pupil.tracking_rocket_words.find_by(lesson_id: lesson.id),
            summative_quiz: !!@current_pupil.tracking_summative_quizzes.find_by(lesson_id: lesson.id),
            tracking_word_search: !!@current_pupil.tracking_word_searches.find_by(lesson_id: lesson.id)
          }
          lesson_datum
        end

        [unit_data, lesson_data]
      end
      .reject { |k, v| k.nil? }


      # Unique subjects
      @subjects = @subjects.uniq
    end

    def unit_index
      curricula = current_user.school.new_library_curriculum || NewLibrary::Curriculum.first
      @units = curricula.units
    end

    def unit_show
      @unit = NewLibrary::Unit.find(params[:unit_id])
    end

    def lesson_show
      @progress = [
        {
          key: 'presentation_viewed',
          complete: !!@current_pupil&.tracking_presentation_views&.find_by(lesson_id: @lesson.id),
          path: static_presentation_path(@lesson_template, @lesson.id, user_id: @current_pupil.id, return_to: request.fullpath),
          body: {
            complete: "You've watched the presentation",
            todo: "You haven't watched the presentation",
          }
        },
        {
          key: 'rocket_word_quiz',
          complete: !!@current_pupil&.tracking_rocket_words&.find_by(lesson_id: @lesson.id),
          path: pupil_lesson_show_rocket_word_quiz_path(@lesson, user_id: @current_pupil.id, return_to: request.fullpath),
          body: {
            complete: "You've completed the rocket word quiz",
            todo: "You haven't completed the rocket word quiz",
          }
        },
        {
          key: 'summative_quiz',
          complete: !!@current_pupil&.tracking_summative_quizzes&.find_by(lesson_id: @lesson.id),
          path: pupil_lesson_show_assessment_quiz_path(@lesson, user_id: @current_pupil.id, return_to: request.fullpath),
          body: {
            complete: "You've completed the summative word quiz",
            todo: "You haven't completed the summative word quiz",
          }
        },
        {
          key: 'tracking_word_search',
          complete: !!@current_pupil&.tracking_word_searches&.find_by(lesson_id: @lesson.id),
          path: pupil_lesson_show_word_search_path(@lesson, user_id: @current_pupil.id, return_to: request.fullpath),
          body: {
            complete: "You've completed the word search",
            todo: "You haven't completed the word search",
          }
        },
      ].map do |data|
        data.merge({ name: data[:key].humanize })
      end

      @progress_pct = ((100.to_f / @progress.length) * @progress.count { |p| p[:complete] }).round(2)

      @links = []
      if @lesson_template.film_link || @lesson_template.film_fileboy_video_id || @lesson_template.film_video_id
        @links << {
          link: @lesson_template.film_link,
          fileboy_video_id: @lesson_template.film_fileboy_video_id,
          name: 'Career Film',
          video_id: @lesson_template.film_video_id,
          resource: 'Career',
          id: 'career-links',
        }
      end

      if @lesson_template.employer_link || @lesson_template.employer_fileboy_video_id || @lesson_template.employer_video_id
        @links << {
          link: @lesson_template.employer_link,
          fileboy_video_id: @lesson_template.employer_fileboy_video_id,
          name: 'Employer Link',
          video_id: @lesson_template.employer_video_id,
          resource: 'Employer',
          id: 'employer-links',
        }
      end

      if @lesson_template.post_16_link || @lesson_template.post_16_fileboy_video_id || @lesson_template.post16_video_id
        @links << {
          link: @lesson_template.post_16_link,
          fileboy_video_id: @lesson_template.post_16_fileboy_video_id,
          name: 'Post 16 course',
          video_id: @lesson_template.post16_video_id,
          resource: 'Post 16 Course',
          id: 'post-16-links',
        }
      end

      # Add a type so we can just check the type in the view
      @links = @links.map { |link|
        link.merge({ type: !link[:fileboy_video_id].present? && !link[:video_id].present? ? "link" : link[:video_id].present? ? "campaign" : "video" })
      }

      @videos = []
      slide = @lesson_template.slides.find_by(slide_type: 'expert')
      if slide&.video_url || slide&.fileboy_video_id || slide&.video_id
        @videos << {
          name: 'Expert film',
          fileboy_video_id: slide.fileboy_video_id,
          video_url: slide.video_url,
          video_id: slide.video_id,
          slide_id: slide.id,
          type: 'expert',
        }
      end

      slide = @lesson_template.slides.find_by(slide_type: 'mission_assignment')
      if slide&.video_url || slide&.fileboy_video_id || slide&.video_id
        @videos << {
          name: 'Mission assignment film',
          fileboy_video_id: slide.fileboy_video_id,
          video_url: slide.video_url,
          video_id: slide.video_id,
          slide_id: slide.id,
          type: 'mission_assignment',
        }
      end

      @recommended_careers = @lesson_template.recommended_careers
      if @recommended_careers.empty?
        @recommended_careers = custom_cache.fetch("recommended_careers_#{@lesson_template.id}", expires_in: 7.days) do
          @lesson_template.generate_recommended_careers
        end
      end

      TrackingService.track_lesson_template_view(@current_pupil, @lesson_template, { lesson_id: @lesson.id })

      render 'static/lesson_templates/show_pupil'
    end

    def rocket_word_quiz
      @quiz = @lesson_template.rocket_word_quiz
      @submit_path = rocket_word_quizzes_path(template_id: @lesson_template.id, lesson_id: @lesson.id, user_id: @current_pupil.id)

      @page_title = 'Rocket Word Quiz'
      render 'static/lesson_templates/quiz'
    end

    def assessment_quiz
      unless @lesson_template.quip_quiz.present?
        return redirect_to pupil_lesson_show_path(@lesson)
      end

      quiz_record = @lesson_template.quip_quiz
      @quiz = quiz_record.format_for_static
      @submit_path = template_quizzes_path(template_id: @lesson_template.id, lesson_id: @lesson.id, user_id: @current_pupil.id)

      TrackingService.track_quiz_view(quiz_record.id, @current_pupil, {
        quiz_type: 'DE Quiz',
        lesson_template_id: @lesson_template.id,
        lesson_id: @lesson.id,
      })

      @page_title = 'Assessment Quiz'
      render 'static/lesson_templates/quiz'
    end

    def word_search
      @user_id = @current_pupil&.id
      @template = @lesson_template
      @lesson_id = @lesson.id

      @words = @template.keywords.pluck(:name)
      @best_times = {
        overall: @template.best_word_search_result,
        personal: @current_pupil.word_search_results.where(lesson_template_id: @template.id).minimum(:time_taken)
      }

      TrackingService.track_word_search_view(@template, @current_pupil, { lesson_id: @lesson_id })

      render 'static_game/lesson_template_word_search'
    end

    def lesson_plan
      @assessment_fields = [
        {
          assessment: 'Questions to Ask During the Lesson',
          value: @lesson_template.plan.assessment_questions,
        },
        { assessment: 'Mark Allocation', value: @lesson_template.plan.assessment_marks },
        {
          assessment: 'Choral Response Questions/ Phrase Suggestions',
          value: @lesson_template.plan.assessment_phrases,
        },
        { assessment: 'Teacher Mastery', value: @lesson_template.teacher_mastery },
      ].select { |x| x[:value].present? }

      @curriculum_fields = [
        { curriculum: 'National Curriculum', value: @lesson_template.plan.national_curriculum },
        {
          curriculum: 'Curriculum Of Excellence',
          value: @lesson_template.plan.curriculum_of_excellence,
        },
        {
          curriculum: 'International Baccalaureate',
          value: @lesson_template.plan.international_baccalaureate,
        },
        { curriculum: 'Early Years Framework', value: @lesson_template.plan.early_years_framework },
        {
          curriculum: 'Enquiry Skills and Approaches',
          value: @lesson_template.plan.scientific_enquiry_type,
        },
        {
          curriculum: 'Working Scientifically Skills',
          value: @lesson_template.plan.working_scientifically_skills,
        },
        {
          curriculum: 'Century Skills For Life',
          value: @lesson_template.plan.century_skills_for_life,
        },
        {
          curriculum: 'CrossCurriculum Opportunities',
          value: @lesson_template.plan.cross_curriculum_opportunities,
        },
        { curriculum: 'CBSE', value: @lesson_template.plan.cbse },
        {
          curriculum: 'Kingdom Of Saudi Arabia',
          value: @lesson_template.plan.kingdom_of_saudi_arabia,
        },
        {
          curriculum: 'Chinese Compulsory Education Primary School Science',
          value: @lesson_template.plan.chinese_compulsory_education_primary_school_science,
        },
      ].select { |x| x[:value].present? }

      TrackingService.track_lesson_plan_view(@current_pupil, @lesson_template)

      render 'static/lesson_templates/lesson_plan'
    end

    private

    def set_lesson
      @lesson = Lesson::Lesson.accessible_by(current_ability).find(params[:lesson_id])
      @lesson_template = @lesson.template
      set_curriculum(@lesson_template, @current_user.school)
    end

    def set_curriculum(lesson_template, curriculum)
      if lesson_template.new_library_units.empty?
        return
      end

      if lesson_template.new_library_units.count == 1
        unit = lesson_template.new_library_units.first
        @curriculum = unit.year&.curriculum
        return
      end

      # here on we use the source template as a user generated shouldn't have a unit of it's own anyway
      # - the template default unit link if it has one
      source_template = lesson_template.source_template || lesson_template
      if source_template.new_library_units.count == 1
        unit = source_template.new_library_units.first
        @curriculum = unit.year&.curriculum
        return
      end

      # - If more than one then your schools curriculums unit (if it has one)
      unit = curriculum ? source_template.new_library_units.joins(year: :curriculum).where(year: { new_library_curricula: { id: curriculum.id } }).first : nil
      if unit.present?
        @curriculum = unit.year&.curriculum
        return
      end

      # - If not then just to the first
      unit = source_template.new_library_units.first
      @curriculum = unit.year&.curriculum
    end
  end
end
