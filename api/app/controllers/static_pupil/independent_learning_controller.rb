module StaticPupil
  class IndependentLearningController < StaticPupilController
    include <PERSON><PERSON>

    def index
      homeworks = @current_pupil&.homework.includes(:tasks, lesson: :template)

      @active_homeworks = homeworks.published.incomplete_for(@current_pupil)
      @completed_homeworks = homeworks.published.complete_for(@current_pupil)
    end

    def show
      @homework = @current_pupil.homework.find(params[:id])

      @lesson_documents = if @homework.show_lesson_files
        docs = @homework&.lesson&.template&.documents
        docs.respond_to?(:where) ? docs.where(for_pupil: true) : []
      else
        []
      end

      @remaining_tasks = @homework.tasks.count - HomeworkTaskSubmission.where(homework_task_id: @homework.task_ids, user_id: @current_pupil.id).where.not(complete_at: nil).count
    end

    def task_show
      @homework = @current_pupil.homework.find(params[:id])
      @task = @homework.tasks.find(params[:task_id])

      # check if pupil has completed previous tasks - otherwise redirect back to show page
      submitted_tasks = HomeworkTaskSubmission.where(homework_task_id: @homework.task_ids, user_id: @current_pupil.id)
      task_index = @homework.tasks.ordered.find_index(@task)

      if task_index > submitted_tasks.count
        redirect_to "/pupil/independent-learning/#{@homework.id}" and return
      end

      @submission = @task.submissions.find_by(user_id: @current_pupil.id).presence || HomeworkTaskSubmission.new()
    end

    def quiz_show
      task = HomeworkTask.find(params[:id])
      @quiz = {}

      if task.task_type == 'lesson_quiz'
        @quiz = task.homework.lesson.template.quip_quiz.format_for_static
      elsif task.task_type == 'quiz'
        @quiz = parse_custom_quiz(task.quiz_question_data)
      end

      @submit_path = "/pupil/independent-learning/#{task.homework.id}/task/#{task.id}?user_id=#{@current_pupil&.id}"

      render 'static/quip_quiz/show', layout: false
    end

    def pupil_submit
      homework = @current_pupil.homework.find(params[:id])
      total_pupils = homework.pupils.count
      submitted_count_before = homework.submissions.where.not(submitted_at: nil).count
      submissions = homework.submissions.where(user_id: @current_pupil.id).where.not(complete_at: nil)

      submissions.update_all(submitted_at: DateTime.now)

      teacher = homework&.created_by
      if teacher.present? && teacher.allows_notification?(:independent_learning)
        if total_pupils > 1 && submitted_count_before == 0
          SchoolMailer.homework_submission(homework, "first").deliver_now
        end

        submitted_pupil_ids = homework.submissions.where.not(submitted_at: nil).distinct.pluck(:user_id)
        all_pupil_ids = homework.pupils.pluck(:id)

        if submitted_pupil_ids.any? && submitted_pupil_ids.sort == all_pupil_ids.sort
          SchoolMailer.homework_submission(homework, "last").deliver_now
        end
      end

      redirect_to pupil_independent_learning_path, notice: 'Independent Learning submitted'
    end

    def task_submit
      @submission = HomeworkTaskSubmission.where(user_id: @current_pupil.id, homework_task_id: params[:task_id]).first_or_initialize
      homework_task = HomeworkTask.find(params[:task_id])

      if params[:results].present?
        @results = JSON.parse(params[:results])
        score = @results.sum { |item| item.dig('result', 'score').to_i }
        total = @results.sum { |item| item.dig('result', 'total').to_i }
        QuipQuizResult.create(
          pupil: @current_pupil,
          user: @current_pupil,
          result_type: :old_homework_task,
          score: score,
          total: total,
          results_json: { data: @results },
          lesson_template_id: homework_task&.homework&.lesson&.template_id
        )

        @submission.quiz_result = parse_quiz_results(@submission.id, params[:results])
        # convert score into value / 100
        result = @submission.quiz_result['finalResult']
        score = result['score'].to_f
        total = result['total'].to_f
        @submission.score = ((score / total) * 100).round(2)
        @submission.complete_at = DateTime.now

        time_taken = 0
        if params[:start_time].present?
          start_time = Time.parse(params[:start_time])
          end_time = Time.now()
          time_taken = end_time - start_time
        end

        lesson_id = homework_task&.homework&.lesson&.id
        quiz_id = homework_task.task_type == "lesson_quiz" ?
                    homework_task&.homework&.lesson&.template&.quip_quiz&.id :
                    "lesson_#{lesson_id}_custom_quiz"
        TrackingService.track_quip_quiz_completion(@current_pupil, homework_task&.homework&.lesson&.template, {
          quiz_id: quiz_id,
          time_taken: time_taken,
          answers_json: JSON.parse(params[:results]),
          score: score,
          total_score: total,
          lesson_id: lesson_id,
          result_type: :old_homework_task
        })
      else
        @submission.update(homework_task_submission_params)
        handle_files_for(@submission, [params[:homework_task_submission][:files]]) if params[:homework_task_submission][:files].present?
        @submission.complete_at = DateTime.now
      end

      if @submission.save
        if request.headers["HTTP_HX_REQUEST"]
          response.set_header("HX-Location", "/pupil/independent-learning/#{params[:id]}")
          head :ok
          flash[:notice] = 'Task submitted successfully.'
        else
          redirect_to "/pupil/independent-learning/#{params[:id]}", notice: 'Task submitted successfully.'
        end
      end
    end

    private

    def handle_files_for(model, files)
      return unless files.present? && !files.empty?

      model.files.destroy_all

      files.each do |file|
        fileboy_id = upload_image file
        model.files << HomeworkFile.new(name: file.original_filename, fileboy_id: fileboy_id, homework_task_submission: model)
      end
    end

    def fetch_from_quip(key)
      uri = URI("https://quip-api.herokuapp.com/quizzes/by-quiz-key/#{key}")

      req = Net::HTTP::Post.new(uri, { "content-type": 'application/json', "x-quip-public": 'cadbc93c-bb2f-4159-a105-0b11135ec826' })
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = (uri.scheme == 'https')
      res = http.request(req)

      JSON.parse(res.body)['data']
    end

    def parse_quiz_results index, results
      parsed_results = JSON.parse(results)
      parsed = Hash.new()
      parsed[:index] = index

      parsed[:results] = []
      parsed[:questions] = []

      parsed_results.each do |result|
        parsed[:results] << {
          type: result['question']['type'],
          score: result['result']['score'],
          prompt: result['question']['dataJson']['prompt'],
          options: result['question']['dataJson']['options'],
          isCorrect: result['result']['score'] == result['result']['total'],
          givenAnswer: result['result']['answer'],
          correctScore: result['result']['total'],
          validResponse: result['question']['dataJson']['validResponse']
        }

        parsed[:questions] << {
          key: result['question']['dataJson']['key'],
          type: result['question']['type'],
          prompt: result['question']['dataJson']['prompt'],
          options: result['question']['dataJson']['options'],
          limitChoices: result['question']['dataJson']['limitChoices'],
          validResponse: result['question']['dataJson']['validResponse']
        }
      end

      parsed[:finalResult] = {
        score: parsed_results.sum { |r| r['result']['score'].to_i },
        total: parsed_results.sum { |r| r['result']['total'].to_i }
      }

      parsed
    end

    def parse_custom_quiz quiz
      parsed = {}
      parsed['id'] = 1
      parsed['name'] = quiz['title']
      parsed['options'] = {}
      parsed['options']['showAnswersBetweenQuestions'] = true

      parsed['questions'] = []

      quiz['questions'].each_with_index do |question, i|
        options = question['choices']

        if options.present?
          options = question['choices'].map do |hash|
            hash.transform_keys { |k| k == 'key' ? 'value' : k }
          end
        end

        case question['type']
        when 'multi-choice'
          answers = []

          new_format = options[0].keys.include? 'correct'
          # if old format, reformat to have 'correct' key on options
          unless new_format
            options = options.map do |option|
              option['correct'] = option['value'] == question['correct']
              option
            end
          end

          # set answers based on correct value, which should always be present after parsing
          # new format already has correct key setup
          options.each do |option|
            answers << option['value'] if option['correct']
          end

          parsed['questions'] << {
            'id': i.to_s,
            'type': 'multi-choice',
            'weight': i,
            'dataJson': {
              'key': question['key'].to_s,
              'prompt': question['prompt'],
              'options': options,
              'limitChoices': { 'max': answers.length, 'min': answers.length },
              'validResponse': answers
            }
          }
        when 'text'
          parsed['questions'] << {
            'id': i.to_s,
            'type': 'free-text',
            'weight': i,
            'dataJson': {
              'key': question['key'].to_s,
              'prompt': question['prompt'],
              'options': options,
              'limitChoices': { 'max': 1, 'min': 1 },
              'validResponse': [question['correct']]
            }
          }
        when 'sort'
          valid_response = []
          options = options.map do |option|
            # if we want to use key for the value, it can't start with a number because javascript ids
            key = option['key'] || option['value']
            key = "i-#{key}" if key.is_a?(Numeric)
            valid_response << key
            { key: key, 'label': option['label'], 'value': key }
          end

          parsed['questions'] << {
            'id': i.to_s,
            'type': 'sort-list',
            'weight': i,
            'dataJson': {
              'key': question['key'].to_s,
              'prompt': question['prompt'],
              'options': options,
              'limitChoices': { 'max': 1, 'min': 1 },
              'validResponse': valid_response
            }
          }
        when 'fill-in-blanks'
          valid_response = {}
          options = {}
          question['options'].each do |key, opts|
            valid_response[key] = opts.find { |value| question['answers'][key] == value['key'] }['key']
            options[key] = opts.map { |value| { 'label': value['label'], 'value': value['key'] } }
          end

          template = question['template']
          template = template.gsub!(/\{(\w+)\}/) { |match| "{{#{$1}}}" }


          parsed['questions'] << {
            'id': i.to_s,
            'type': 'fill-in-blanks',
            'weight': i,
            'dataJson': {
              'key': question['key'].to_s,
              'prompt': question['prompt'],
              'template': template,
              'options': options,
              'validResponse': valid_response
            }
          }
        when 'image-bucket'
          options = []
          question['buckets'].each do |bucket|
            bucket['options'].each do |option|
              options << {
                'label': option['label'],
                'value': option['label'],
                'bucket': bucket['key'],
                'fileboyId': option['imageId']
              }
            end
          end
          parsed['questions'] << {
            'id': i.to_s,
            'type': 'image-bucket',
            'weight': i,
            'dataJson': {
              'key': question['key'].to_s,
              'prompt': question['prompt'],
              'buckets': question['buckets'].map { |bucket| { 'key': bucket['key'], 'name': bucket['label'] } },
              'options': options,
            }
          }
        else
          raise "Unknown question type: #{question['type']}"
        end
      end

      parsed.with_indifferent_access
    end

    def homework_task_submission_params
      params.require(:homework_task_submission).permit(:body, :score, :notes)
    end
  end
end
