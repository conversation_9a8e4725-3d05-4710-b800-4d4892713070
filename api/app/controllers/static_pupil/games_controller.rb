module StaticPupil
  class GamesController < StaticPupilController
    before_action :runLobbyJob, only: [:fetch_word_searches]
    before_action :set_lobby, only: [:blast_words_show, :blast_words_leaderboard, :show_lobby, :join_lobby, :leave_lobby, :increment_score, :dev_start_blast_words]

    def index; end

    def blast_words; end

    def fetch_word_searches
      @lobbies = WordSearchLobby
                  .where('"startsAt" > ?', DateTime.now)
                  .accessible_by(current_ability)
                  .order(created_at: :desc)
                  .limit(3)
      
      render :word_searches, layout: nil
    end

    def blast_words_show
      if @lobby[:startsAt] < DateTime.now && @lobby.user_ids.include?(@current_pupil.id) && @lobby.word_search_lobby_users.find_by(user_id: @current_pupil.id)&.finishedAt.nil?
        @words = @lobby.word_search_lobby_users.find_by(user_id: @current_pupil.id).words
        @best_time = WordSearchLobbyUser
                    .joins(:word_search_lobby)
                    .where(user: @current_pupil)
                    .minimum("date_part('epoch', \"finishedAt\" - \"startsAt\")")
        @game_in_progress = true
  
        render :blast_words_show and return
      else
        render :blast_words_lobby and return
      end
  
    end
  
    def blast_words_leaderboard
      # Handle case where lobby might not be set properly
      unless @lobby && @current_pupil
        raise "Lobby or current pupil not set"
      end
      
      lobby_user = @lobby.word_search_lobby_users.find_by(user_id: @current_pupil.id)
      
      # If the user doesn't exist in the lobby redirect them back to the lobby
      if lobby_user.nil?
        render layout: false and return
      end
      
      @game_in_progress = game_in_progress?(lobby_user)
      @user_finished = lobby_user&.finishedAt.present?
      
      render layout: false
    end
  
    def join_lobby
      unless @lobby.word_search_lobby_users.exists?(user: @current_pupil)
        lobby_user = @lobby.word_search_lobby_users.create(user: @current_pupil)
        lobby_user.generate_words 5
      end
  
      @lobby.delete_cache
      render json: @lobby.generate_cache
    end
  
    def leave_lobby
      @lobby.word_search_lobby_users.delete(@lobby.word_search_lobby_users.where(user: @current_pupil))
  
      @lobby.delete_cache
      render json: @lobby.generate_cache
    end
  
    def increment_score
      lobby_user = @lobby.word_search_lobby_users.find_by(user_id: @current_pupil.id)
      return if lobby_user.nil?
  
      lobby_user.score += 1
  
      if lobby_user.score >= lobby_user.words.length
        lobby_user.finishedAt = Time.zone.now
        UserRankEvent.add_points(lobby_user.user, lobby_user.score * 2, "blast_words")
      end
  
      if (lobby_user.save)
        render json: lobby_user
      end
    end
  
    def dev_start_blast_words
      return unless Rails.env.development?
  
      unless @lobby.word_search_lobby_users.exists?(user: @current_pupil)
        lobby_user = @lobby.word_search_lobby_users.create(user: @current_pupil)
        lobby_user.generate_words 5
      end
  
      @lobby.delete_cache
      @lobby.update(startsAt: Time.now)
  
      render json: @lobby.generate_cache
    end

    private

    def game_in_progress?(lobby_user)
      return false unless @lobby && @current_pupil
      
      @lobby[:startsAt] < DateTime.now && 
        @lobby.user_ids.include?(@current_pupil.id) && 
        lobby_user.present? && 
        lobby_user.finishedAt.nil?
    end

    # This function should run all of the periodic tasks for managing the lobbies.
    def runLobbyJob
      # ensure a pending lobby exists
      hasPendingLobby = WordSearchLobby.exists?(['"startsAt" > ?', DateTime.now])
      if (!hasPendingLobby)
        WordSearchLobby.create! startsAt: 10.minutes.from_now
      end

      # remove lobbies which have started and have no players
      staleLobbies = WordSearchLobby
                      .where('"startsAt" <= NOW()') # already started
                      .includes(:word_search_lobby_users).where(word_search_lobby_users: { id: nil }) # no users

      timedOutLobbies = WordSearchLobby
                          .where('"startsAt" <= ?', Time.now - 10.minutes) # 10 minutes old
                          .includes(:word_search_lobby_users)
                          .where.not(word_search_lobby_users: { id: nil })
                          .where(word_search_lobby_users: { finishedAt: nil }) # has a user without a finished at time

      timedOutLobbies.each do |lobby|
        if lobby.word_search_lobby_users.any?
          lobby
            .word_search_lobby_users
            .includes(:user)
            .where(word_search_lobby_users: { finishedAt: nil })
            .update_all({ finishedAt: Time.now })
        end
      end

      # get lobbies where all users have completed the game
      # and the lobby was created recently (so we don't get a huge backlog that will make this task)
      # take a long time to run
      WordSearchLobby
        .where('"startsAt" <= ?', Time.now - 10.minutes) # at least 10 minutes old
        .where('created_at > ?', 1.day.ago) # created in the last day
        .includes(:word_search_lobby_users)
        .where.not(word_search_lobby_users: { id: nil }) # has users
        .where.not(word_search_lobby_users: { finishedAt: nil }) # all users have finished
        .destroy_all

      staleLobbies.destroy_all
    end
    
    def set_lobby
      @lobby = WordSearchLobby.find_by(id: params[:lobby_id])
  
      redirect_to params[:return_to].presence || '/pupil/games/blast-words' and return if @lobby.nil?
    end

  end
end
