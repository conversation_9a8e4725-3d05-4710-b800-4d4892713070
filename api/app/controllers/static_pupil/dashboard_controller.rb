module StaticPupil
  class DashboardController < StaticPupilController

    def index
      if @current_pupil.present?
        @homeworks = @current_pupil&.homework.published.filter { |homework| homework.date_due >= Date.current && homework.tasks.first.submissions.find_by(user_id: @current_pupil.id)&.submitted_at.nil? }
      else
        render "no_content"
      end
    end

    def return_to_dashboard
      session[:pupil_id] = nil
      redirect_to '/accounts/dashboard'
    end

    def set_pupil
      if current_user.present? && current_user.teacher?
        allowed_pupils = current_user.pupils
        pupil = allowed_pupils.find_by(id: params[:pupil_id])
        if pupil.present?
          session[:pupil_id] = pupil.id
          redirect_to "/pupil"
        else
          redirect_to root_path
        end
      else
        redirect_to root_path
      end
    end
  end
end
