
module StaticPupil
  class AccountController < StaticPupilController
    include Pa<PERSON>ationHelper
    before_action :set_selects, only: %i[index update]

    def index; end

    def update
      params[:pupil][:alias] = "#{params[:pupil][:color]} #{params[:pupil][:animal]}".strip

      if @current_pupil.update(pupil_params)
        redirect_to pupil_account_path, notice: 'Info was successfully updated.'
      else
        render :index
      end
    end

    def presentation_settings
      @presentation_settings = @current_pupil.presentation_settings || {}
    end

    def update_presentation_settings
      @user = @current_pupil
      settings = params.require(:presentation_settings).permit(:font_size, :font_family, :letter_spacing, :text_case, :color_scheme, :narration_voice, :narration_auto_play, :sidebar_side, :sidebar_visible, :top_bottom_text_visible)
      @user.presentation_settings = (@user.presentation_settings || {}).merge(settings.to_h)
      respond_to do |format|
        format.json do
          if @user.save
            render json: { success: true, message: 'Presentation settings updated.' }
          else
            render json: { success: false, errors: @user.errors.full_messages }, status: :unprocessable_entity
          end
        end
        format.html do
          if @user.save
            redirect_to pupil_account_presentation_settings_path, notice: 'Presentation settings updated.'
          else
            flash.now[:error] = 'Failed to update settings.'
            @presentation_settings = @user.presentation_settings
            render :presentation_settings
          end
        end
      end
    end

    def progress
      @average_quiz_score = 0
      quizzes = @current_pupil.tracking_summative_quizzes

      if quizzes.present?
        total_scores = quizzes.reduce(0) { |total, result| total + result.score }
        total_totals = quizzes.reduce(0) { |total, result| total + result.total_score }

        @average_quiz_score = (total_scores.to_f / total_totals.to_f * 100).round()
      end

      @presentations_viewed = @current_pupil.tracking_presentation_views.distinct.count(:lesson_template_id)
      @lessons_viewed = @current_pupil.tracking_lesson_template_views.distinct.count(:lesson_template_id)
      @films_viewed = @current_pupil.tracking_films.distinct.count(:lesson_template_id)

      @top_word_searches = @current_pupil.tracking_word_searches.includes([:lesson_template]).where.not(lesson_template: nil).order('time_taken asc').limit(5)
      @top_rocket_words = @current_pupil.tracking_rocket_words.includes([:lesson_template]).where.not(lesson_template: nil).order('time_taken asc').limit(5)
    end

    def history
      @events = safe_paginate(UserRankEvent.where(user_id: @current_pupil.id).order('created_at desc'), page: params[:page], per_page: 100)
    end

    private

    def set_selects
      @colors = [
        "Beige",
        "Blue",
        "Brown",
        "Gold",
        "Green",
        "Grey",
        "Indigo",
        "Orange",
        "Pink",
        "Purple",
        "Red",
        "Silver",
        "Violet",
        "White",
        "Yellow",
      ]

      @animals = [
        "Ant",
        "Bear",
        "Bee",
        "Bird",
        "Butterfly",
        "Camel",
        "Cat",
        "Caterpillar",
        "Chicken",
        "Clownfish",
        "Cow",
        "Crab",
        "Crocodile",
        "Deer",
        "Dinosaur",
        "Dog",
        "Dolphin",
        "Donkey",
        "Dragon",
        "Duck",
        "Elephant",
        "Fish",
        "Frog",
        "Giraffe",
        "Goat",
        "Hamster",
        "Hedgehog",
        "Hippo",
        "Horse",
        "Jellyfish",
        "Kangaroo",
        "Ladybird",
        "Lion",
        "Mole",
        "Monkey",
        "Mouse",
        "Octopus",
        "Ostrich",
        "Owl",
        "Panda",
        "Peacock",
        "Penguin",
        "Pig",
        "Pigeon",
        "Pony",
        "Puppy",
        "Rabbit",
        "Rat",
        "Rhinoceros",
        "Seahorse",
        "Sheep",
        "Snake",
        "Spider",
        "Starfish",
        "Stingray",
        "Tiger",
        "Toad",
        "Turkey",
        "Turtle",
        "Unicorn",
        "Whale",
        "Worm",
        "Zebra",
      ]
    end

    def pupil_params
      params.require(:pupil).permit(:name, :dob, :gender, :ethnicity, :alias)
    end
  end
end
