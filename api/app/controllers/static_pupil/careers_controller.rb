module StaticPupil
  class CareersController < StaticPupilController
    def index
      @de_resources = [
        {
          title: 'AI Career Search',
          body: 'Use our AI careers tool to link the content your pupils enjoy in lessons to potential careers.',
          image_fileboy_id: 'f241ec60-a5d9-4f09-9a7f-b3c01a6abb5e',
          path: '/career-builder'
        },
        {
          title: '360 Tours',
          body: 'Explore career opportunities in 360',
          image_fileboy_id: 'e131068e-e8cc-4f0a-9376-c285aa0c9d50',
          path: '/pupil/careers/tours'
        }
      ]

      @partner_resources = [
        {
          title: '<PERSON>l',
          body: 'Building a lasting legacy',
          image_fileboy_id: '5c412373-2371-4ccc-90ef-8b1ecd879da8',
          path: '/morgan-sindall'
        },
        {
          title: 'Microsoft',
          body: "Click here to access Microsoft's Career Resources and Courses.",
          image_fileboy_id: 'ab5c101f-2ea6-45bf-8491-cd8f403d93e1',
          path: 'https://www.linkedin.com/learning/search?entityType=VIDEO~LEARNING_PATH&keywords=Curriculum%20Vitae%20(CV)'
        },
      ]
    end

    def tours_index
      @tours = Tour.order(created_at: :desc)
    end
  end
end