class Admin::TeachersController < AdminController
  before_action :set_teacher, only: %i[show update edit destroy send_welcome_email]

  def index
    collection = Teacher.all
    if (pupil_id = params[:pupil_id])
      collection = Pupil.find(pupil_id).teachers
    end

    if (class_id = params[:class_id])
      collection = Form.find(class_id).teachers
    end

    if referral_code = params[:referral_code]
      collection = collection
        .joins("LEFT JOIN users ref_users ON users.referred_from_user_id = ref_users.id")
        .where("ref_users.referral_code ILIKE :q", { q: "%#{params[:query]}%" })
    end

    collection = collection.joins(:school).where.not({ schools: { deleted: true }})

    list_with_response collection,
                        params,
                        allowed_scopes: %w[all],
                        search_params: ["users.name", "email"],
                        serialized: true,
                        default_sort: { name: :asc }
  end

  def deleted_teachers
    collection = User.unscoped.where(deleted: true).joins(:school)
    list_with_response collection,
                        params,
                        search_params: ["users.name", "email"],
                        default_sort: { name: :asc }
  end

  def create
    save_with_response Teacher.new, teachers_params
  end

  def update
    save_with_response @teacher, teachers_params
  end

  def show
    render json: @teacher.as_json.merge({form_ids: @teacher.form_ids, school: {
      id: @teacher.school.id,
      name: @teacher.school.name,
      type: @teacher.school.school_type
    }})
  end

  def destroy_all
    Teacher.where(id: params[:ids]).destroy_all
    render json: { success: true }
  end

  def destroy
    destroy_record @teacher
  end

  def permanently_delete
    teachers = User.unscoped.where(deleted: true, id: params[:ids])
    teachers.each do |teacher|
      PresentationProgress.unscoped.where(user: teacher).each { |e| e.delete }
      Enrollment.unscoped.where(user: teacher).each { |e| e.delete }
      View.unscoped.where(user: teacher).each { |e| e.delete }
      UserTemplate.unscoped.where(user: teacher).each { |e| e.delete }
      teacher.delete
    end
    render json: { success: true }
  end

  def send_welcome_email
    @teacher.send_welcome_email
    render json: { success: true }
  end

  private

  def set_teacher
    @teacher = Teacher.find(params[:id])
  end

  def teachers_params
    params.permit(
      :name,
      :email,
      :password,
      :school_id,
      :gender,
      :dob,
      :working_days,
      :alias,
      :job_title,
      :is_school_admin,
      :can_create_lessons,
      form_ids: []
    )
  end
end
