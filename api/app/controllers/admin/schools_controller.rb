class Admin::SchoolsController < AdminController
  before_action :set_school, only: %i[show update edit send_welcome_emails destroy assign_templates_for_forms delete_forms send_multiple_welcome_emails]

  def index
    collection = School.generic
    collection = collection.where(country_id: params[:country_id]) if params[:country_id].present?
    collection = collection.where(region: params[:region]) if params[:region].present? && params[:country_id].to_i == 1

    if params[:query].present?
      terms = params[:query].strip.split(/\s+/)
      fields = ['schools.name', 'schools.postcode', 'uk_schools.name', 'uk_schools.postcode']
      condition = terms.map do |term|
        condition = fields.map { |field| "#{field} ILIKE #{escape "%#{term}%"}" }.join(' OR ')
        "(#{condition})"
      end.join(' AND ')
      collection = collection.left_joins(:uk_school).where(condition)
      params[:query] = nil
    end

    list_with_response(
      collection,
      params,
      search_params: ['schools.name'],
      allowed_scopes: %w[all in_trial restricted subscribed],
      default_sort: { name: :asc },
      serialized: true
    )
  end

  def for_select
    records = School.generic.order('lower(trim(name)) ASC').select(:id, :name)
    render json: JSONString.generate(schools: sql(records))
  end

  def create
    ActiveRecord::Base.transaction do
      school = School.generic.create!(schools_params)
      school.created_by = current_user
      school.created_by_email = current_user&.email
      create_teacher_for_school(school) if teacher_params_present?

      render json: { saved: true, record: school }, status: :created
    end
  rescue ActiveRecord::RecordInvalid => e
    # This will catch validation failures for both School and Teacher
    render json: { saved: false, errors: e.record.errors.full_messages }, status: :unprocessable_entity
  rescue => e
    render json: { saved: false, errors: [e.message] }, status: :unprocessable_entity
  end

  def update
    save_with_response @school, schools_params
  end

  def show
    render json: @school.as_json
  end

  def teachers_for_school
    @school = School.find_by(id: params['school_id'])

    render json: { records: @school.teachers }
  end

  def forms_for_school
    @school = School.find_by(id: params['school_id'])

    render json: { records: @school.forms }
  end

  def send_welcome_emails
    @school.teachers.each do |t|
      t.send_welcome_email
      sleep(0.5)
    end
    @school.complete_checklist_item(:send_welcome_email)
    @school.save
    render json: { sent: true }
  end

  def destroy
    @school.teachers.destroy_all
    destroy_record @school
  end

  def summary_stats
    schools = School.generic
    pupils = Pupil.all.includes(:school).where(schools: { school_type: :generic })
    teachers = Teacher.all.includes(:school).where(schools: { school_type: :generic }).where('schools.category != ? OR schools.category IS NULL', School.categories[:home_school_category])
    tutors = Teacher.all.includes(:school).where(schools: { school_type: :generic }).where(schools: { category: :home_school_category })

    render json: { stats: [
      [schools.count, 'Schools', schools.where('created_at > ?', 1.week.ago).count],
      [pupils.count, 'Pupils', pupils.where('users.created_at > ?', 1.week.ago).count],
      [teachers.count, 'Teachers', teachers.where('users.created_at > ?', 1.week.ago).count],
      [tutors.count, 'Tutors', tutors.where('users.created_at > ?', 1.week.ago).count]
    ] }
  end

  def assign_templates_for_forms
    template_ids = Array.wrap(params[:template_ids]).map(&:to_i)
    form_ids = Array.wrap(params[:form_ids]).map(&:to_i)

    unit = params[:unit_id].present? ? NewLibrary::Unit.find_by(id: params[:unit_id]) : nil

    teacher = @school.teachers.first

    assigned_template_ids = []

    begin
      @school.forms.where(id: form_ids).each do |form|
        assigned_template_ids = form.add_accessible_lesson_template_ids(teacher, @school, template_ids)

        form.form_units.create!(new_library_unit_id: unit.id) if unit && form.form_units.where(new_library_unit_id: unit.id).count == 0
      end

      render json: { saved: true, template_ids: assigned_template_ids }
    rescue => e
      render json: { saved: false, errors: [e] }
    end
  end

  def send_multiple_welcome_emails
    @school.teachers.where(id: params[:ids]).each do |t|
      t.send_welcome_email
      sleep(0.5)
    end
    render json: { sent: true }
  end

  private

  def create_teacher_for_school(school)
    Teacher.create!(
      email: params[:teacher_email],
      name: params[:teacher_name],
      password: params[:teacher_password],
      is_school_admin: true,
      science_lead: true,
      school: school
    )
  end

  def teacher_params_present?
    params[:teacher_email].present?
  end

  def set_school
    @school = School.generic.find(params[:id])
  end

  def schools_params
    params.permit(
      :name,
      :approaching_renewal,
      :country_id,
      :actual_country_id,
      :subscription_start_date,
      :subscription_end_date,
      :year_start,
      :term_length,
      :fileboy_image_id,
      :postcode,
      :school_type,
      :category,
      :uk_school_id,
      :subscription_externally_covered,
      :organisation_id,
      :wonde_id,
      :wonde_enabled,
      :sync_wonde_weekly,
      :wonde_request_status,
      :show_lesson_plan_pupil_area,
      :trial_start_date,
      :trial_end_date,
      :is_example_school,
      :lead_source,
      :sign_up_event_id,
      :creation_email,
      :hubspot_renewal_date,
      :approved_domains,
      :renewal_month,
      :memo,
      :can_view_glossary,
      :new_library_curriculum_id,
      sponsor_ids: [],
      science_leader_ids: [],
      admin_science_leader_ids: []
    )
  end
end
