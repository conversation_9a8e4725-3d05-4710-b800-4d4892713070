class Admin::<PERSON>on<PERSON><PERSON>platesController < AdminController
  before_action :set_lesson_template, only: %i[
    show edit update destroy duplicate load_rocket_words_quiz download_content upload_content file_links presentation
  ]
  skip_before_action :authenticate_admin, only: :download_content

  def index
    collection = Lesson::Template.all.where(user_generated: false)

    list_with_response(collection, params, {
      allowed_scopes: %w[all awaiting_translation available],
      search_params: [:name, :machine_name, "replace(machine_name, '_', '')"],
      serialized: true,
      default_sort: { machine_name: :asc },
    })
  end

  def update
    if @lesson_template.update!(params_with_image)
      render json: { saved: true, record: @lesson_template }
    else
      render json: { saved: false, errors: @lesson_template.errors.full_messages }
    end
  end

  def create
    @lesson_template = Lesson::Template.new(params_with_image)

    if @lesson_template.save
      @lesson_template.create_default_presentation
      render json: { saved: true, record: @lesson_template }
    else
      render json: { saved: false, errors: @lesson_template.errors.full_messages }
    end
  end

  def duplicate
    if (new_template_id = @lesson_template.duplicate)
      render json: {
        saved: true, record: new_template_id
      }
    else
      render json: {
        saved: false
      }
    end

  end

  def show
    render json: @lesson_template.data_for_show
  end

  def destroy
    destroy_record @lesson_template
  end

  def load_rocket_words_quiz
    render json: @lesson_template.quiz_questions_formatted_for_quip
  end

  def presentation
    render json: @lesson_template.presentation_data(current_user, current_locale)
  end

  def get_tag_suggestions
    @lesson_template = Lesson::Template.find(params[:id])
    render json: { words: @lesson_template.extracted_keywords }
  end

  private

  def set_lesson_template
    @lesson_template = Lesson::Template.find(params[:id])
  end

  def params_with_image
    update_params = new_library_lesson_template_params
    update_params['plan_learning_outcomes_attributes'] = update_params.delete('plan_learning_outcomes')
    update_params
  end

  def new_library_lesson_template_params
    ActionController::Parameters.new(
      JSON.parse(params.require(:lesson_templates))
    )
                                .permit(
                                  :name,
                                  :unit_ids,
                                  :machine_name,
                                  :resources,
                                  :sentence_structure,
                                  :activities,
                                  :assessment,
                                  :learning_outcomes,
                                  :teacher_mastery,
                                  :pupil_mastery,
                                  :previous_rocket_word_narration,
                                  :demo,
                                  :anonymous,
                                  :available,
                                  :film_link,
                                  :employer_link,
                                  :post_16_link,
                                  :film_jw_id,
                                  :employer_jw_id,
                                  :post_16_jw_id,
                                  :use_new,
                                  :plan_national_curriculum,
                                  :plan_curriculum_of_excellence,
                                  :plan_international_baccalaureate,
                                  :plan_early_years_framework,
                                  :plan_cbse,
                                  :plan_scientific_enquiry_type,
                                  :plan_working_scientifically_skills,
                                  :plan_century_skills_for_life,
                                  :plan_cross_curriculum_opportunities,
                                  :plan_next_generation_science_standards,
                                  :plan_kingdom_of_saudi_arabia,
                                  :plan_chinese_compulsory_education_primary_school_science,
                                  :plan_assessment_questions,
                                  :plan_assessment_marks,
                                  :plan_assessment_phrases,
                                  :teacher_mastery,
                                  :previous_rocket_word_narration,
                                  :quip_quiz_key,
                                  :country_id,
                                  :fileboy_image_id,
                                  :fileboy_sponsor_logo_id,
                                  :film_fileboy_video_id,
                                  :employer_fileboy_video_id,
                                  :post_16_fileboy_video_id,
                                  :employer_video_id,
                                  :post16_video_id,
                                  :film_video_id,
                                  :use_new_presentation,
                                  :user_generated,
                                  :primary_new_library_unit_id,
                                  :disable_viewing,
                                  :viewable_only,
                                  scientific_enquiry_type_ids: [],
                                  recommended_template_ids: [],
                                  recommended_by_template_ids: [],
                                  author_ids: [],
                                  objectives: [],
                                  plan_objectives: [],
                                  plan_learning_outcomes: [:id, :_destroy, :body, :higher, :middle, :lower, :weight],
                                  plan_activities: [:name, :method, :resources, :required_resources],
                                  available_country_ids: [],
                                  career_tag_ids: [],
                                  recommended_career_ids: [],
                                )
  end
end
