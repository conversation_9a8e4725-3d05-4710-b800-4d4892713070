class Admin::NewLibraryUnitsController < AdminController
  before_action :set_unit,
                only: %i[update_template_order]

  def update_template_order
    params[:lesson_template_ids].each_with_index do |id, i|
      @unit.lesson_templates.find(id).update(weight: i)
    end
    render json: { saved: true }
  end

  private

  def set_unit
    @unit = NewLibrary::Unit.find(params[:id])
  end

  def new_library_unit_params
    params.permit(
      :name,
      :summary,
      :weight,
      :details,
      :jw_id,
      :jw_title,
      :jw_body,
      :year_id,
      :fileboy_image_id,
      :fileboy_video_id,
      :new_in_2024,
      :featured_on_homepage,
      lesson_template_ids: []
    )
  end
end
