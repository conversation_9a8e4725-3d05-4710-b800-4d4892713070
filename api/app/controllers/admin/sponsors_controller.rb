class Admin::SponsorsController < AdminController
  before_action :set_sponsor, only: %i[show edit update destroy]

  def index
    list_with_response Sponsor.all,
                       params,
                       search_params: %w[name],
                       default_sort: { name: :asc }
  end
  def create
    save_with_response Sponsor.new, sponsor_params
  end

  def for_select
    records = Sponsor.order("lower(trim(name)) ASC").select(:id, :name)
    render json: JSONString.generate(sponsors: sql(records))
  end

  private

  def set_sponsor
    @sponsor = Sponsor.find(params[:id])
  end

  def sponsor_params
    params.permit(:name, :body, :fileboy_image_id, school_ids: [])
  end

  def sponsors_scopes
    %w[all]
  end
end
