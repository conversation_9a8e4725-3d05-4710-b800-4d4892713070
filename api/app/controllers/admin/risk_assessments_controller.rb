class Admin::RiskAssessmentsController < AdminController
  before_action :set_risk_assessment, only: %i[show edit update destroy]

  def index
    list_with_response Lesson::RiskAssessment.all, params, search_params: %w[name], default_sort: { name: :asc }
  end

  def update
    save_with_response @risk_assessment, risk_assessment_params
  end

  def create
    save_with_response Lesson::RiskAssessment.new, risk_assessment_params
  end

  def show
    render json: @risk_assessment
  end

  def destroy
    destroy_record @risk_assessment
  end

  private

  def set_risk_assessment
    @risk_assessment = Lesson::RiskAssessment.find(params[:id])
  end

  def risk_assessment_params
    params.permit(:name, :safety_points, :response, :likelihood)
  end
end
