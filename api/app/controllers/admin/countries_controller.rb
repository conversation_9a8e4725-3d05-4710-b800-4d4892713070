class Admin::CountriesController < AdminController
  def for_select_list

    curriculumOnly = request.params[:curriculumOnly]

    countries = Country.all
    
    if (curriculumOnly)
      countries = countries.joins(:available_templates).group(:id)
    end

    countries = countries.order(name: :asc).pluck(:name, :id).map {|name, id| { label: name, value: id }}
    render json: countries
  end
end
