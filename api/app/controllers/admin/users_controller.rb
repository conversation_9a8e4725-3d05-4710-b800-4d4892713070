class Admin::UsersController < AdminController
   def block
    user = User.find(params[:id])
    user.block!
    render json: { ok: true }
  end

  def unblock
    user = User.find(params[:id])
    user.unblock!
    render json: { ok: true }
  end

  private

  def user_params
    ActionController::Parameters.new(JSON.parse(params.require(:users))).permit(
      :name,
      :email,
      :password,
      :fileboy_image_id
    )
  end
end
