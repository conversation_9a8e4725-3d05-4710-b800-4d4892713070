class Admin::LessonDocumentsController < AdminController
  before_action :set_document, only: %i[show edit update destroy]

  def index
    collection = Lesson::Document.all
    if (template_id = params[:template_id])
      collection = Lesson::Template.find(template_id).documents
    end
    list_with_response collection, params, default_sort: { created_at: :desc }
  end

  def show
    render json: @document
  end

  def update
    save_with_response @document, document_params
  end

  def create
    save_with_response Lesson::Document.new, document_params
  end

  def destroy
    destroy_record @document
  end

  private

  def set_document
    @document = Lesson::Document.find(params[:id])
  end

  def document_params
    ActionController::Parameters.new(JSON.parse(params.require(:lesson_documents))).permit(
      :name,
      :skip_translation,
      :for_pupil,
      :template_id,
      :fileboy_file_id
    )
  end
end
