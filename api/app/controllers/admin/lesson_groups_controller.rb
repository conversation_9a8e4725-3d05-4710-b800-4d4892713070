class Admin::LessonGroupsController < AdminController
  def index
    collection = Lesson::Group
    collecton = collection.joins(:templates).where(lesson_templates: { user_generated: params[:user_generated] === "true" }).group(:id)

    list_with_response(collecton, params, {
      search_params: [
        "lesson_templates.name",
        "lesson_templates.machine_name",
        "replace(lesson_templates.machine_name, '_', '')",
      ],
      default_sort: { machine_name: :asc },
      serialized: true,
    })
  end

end
