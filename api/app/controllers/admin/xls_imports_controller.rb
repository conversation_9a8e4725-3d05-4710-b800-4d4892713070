class Admin::XlsImportsController < AdminController
  before_action :set_xls_import, only: %i[show update]

  def index
    list_with_response(
      XlsImport
        .joins(:school)
        .left_joins(:user)
        .select('xls_imports.*, schools.name AS school_name, schools.school_type AS school_type, users.email AS user_email'),
      params,
      search_params: %w[xls_imports.file_name schools.name users.email],
      default_sort: { created_at: :desc },
    )
  end

  def show
    render json: @xls_import.as_json.merge(
      school_name: @xls_import.school.name,
      user_name: @xls_import.user&.email,
    )
  end

  def create
    save_with_response XlsImport.new, xls_import_params
  end

  def update
    save_with_response @xls_import, xls_import_params
  end

  private

  def set_xls_import
    @xls_import = XlsImport.find(params[:id])
  end

  def xls_import_params
    params
      .permit(:school_id, :fileboy_file_id, :run_import, :ignore_warnings, :build_lessons, data: {})
      .merge(locale: current_locale, user: current_user)
  end
end
