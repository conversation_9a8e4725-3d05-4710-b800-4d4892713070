# frozen_string_literal: true

require 'date'
class Admin::ReportsController < AdminController
  def download_teacher_reports
    admin_only = !!reports_params[:admin_only]
    from = reports_params[:from].present? ? Date.parse(reports_params[:from]) : nil
    to = reports_params[:to].present? ? Date.parse(reports_params[:to]) : nil

    collection = Teacher.joins(:school).order(created_at: :desc).select(
      'users.name AS "Name"',
      'users.email AS "Email"',
      'schools.name AS "School Name"',
      'schools.lead_source AS "Lead Source"',
      'users.created_at AS "Created At"'
    )

    collection = collection.where('users.created_at >= ?', from) if from

    collection = collection.where('users.created_at <= ?', to) if to

    collection = collection.where(is_school_admin: true) if admin_only

    filename = "#{admin_only ? 'AdminTeacher' : 'Teacher'}Report_#{Date.today}"
    filename += "__#{from || 'inception'}-#{to || 'present'}" if from || to

    render json: { csv: collection.to_csv, filename: filename }
  end

  def download_school_reports
    from = reports_params[:from]
    to = reports_params[:to]

    collection = School.generic
    collection = collection.where(country_id: params[:country_id]) if params[:country_id].present?
    collection = collection.where(region: params[:region]) if params[:region].present? && params[:country_id].to_i == 1
    collection = collection.where('schools.created_at >= ? AND schools.created_at <= ?', from, to)

    sql_query = <<-SQL
      SELECT
        name,
        (SELECT count(id) FROM users WHERE users.school_id = schools.id AND type = 'Teacher') AS teacher_count,
        (SELECT count(id) FROM users WHERE users.school_id = schools.id AND type = 'Pupil') AS pupil_count,
        (SELECT count(id) FROM xls_imports WHERE xls_imports.school_id = schools.id) AS import_count,
        (to_char(created_at, 'DD/MM/YYYY hh:mm:ss')) as record_created_at,
        postcode,
        (SELECT STRING_AGG(email, ', ') FROM users WHERE users.school_id = schools.id AND is_school_admin) AS admin_teachers,
        lead_source,
        (SELECT name FROM countries WHERE countries.id = schools.country_id) AS country_name
      FROM schools WHERE id IN (#{collection.ids.join(', ')}) ORDER BY created_at DESC
    SQL

    collection = JSONString.generate(sql(sql_query))
    filename = "SchoolReport_#{Date.today}__#{Date.parse(from)}-#{Date.parse(to)}"

    render json: {
      csv: CSV.generate do |csv|
        csv << ['Name', '# Teachers', '# Pupils', 'Used Importer', 'Created At', 'Postcode', 'Admin Teachers', 'Lead Source', 'Country']
        collection.each do |record|
          csv << [
            record['name'],
            record['teacher_count'],
            record['pupil_count'],
            record['import_count'] > 0,
            record['record_created_at'],
            record['postcode'],
            record['admin_teachers'],
            record['lead_source'],
            record['country_name'],
          ]
        end
      end,
      filename: filename,
    }
  end

  def download_champions_reports
    from = reports_params[:from].present? ? Date.parse(reports_params[:from]) : nil
    to = reports_params[:to].present? ? Date.parse(reports_params[:to]) : nil

    collection = School.generic

    if from
      collection = collection.where('quiz_attempts.created_at >= ?', from)
      collection = collection.where('tracking_presentation_views.created_at >= ?', from)
    end

    if to
      collection = collection.where('quiz_attempts.created_at < ?', to)
      collection = collection.where('tracking_presentation_views.created_at < ?', to)
    end

    collection = collection
                 .left_joins(users: %i[quiz_attempts tracking_presentation_views])
                 .group('schools.id')
                 .select(
                   'schools.id',
                   'schools.name',
                   'round(coalesce(AVG(time_viewed), 0)) AS time_viewed',
                   'coalesce(COUNT(distinct(quiz_attempts.id)), 0) AS quizzes'
                 )

    csv = sql(<<-SQL, collection).to_csv
      SELECT
        id AS "School ID",
        name AS "School Name",
        time_viewed AS "Average Presentation Time (seconds)",
        quizzes AS "Quizzes Taken"
      FROM (?) AS data
      ORDER BY (time_viewed / MAX(time_viewed) OVER () + quizzes / MAX(quizzes) OVER ()) / 2 DESC
    SQL

    filename = "ChampionsReport_#{Date.today}"
    filename += "__#{from || 'inception'}-#{to || 'present'}" if from || to

    render json: { csv: csv, filename: filename }
  end

  def download_new_school_reports
    from = reports_params[:from]
    to = reports_params[:to]

    collection = School.generic
    collection = collection.where(country_id: params[:country_id]) if params[:country_id].present?
    collection = collection.where(region: params[:region]) if params[:region].present? && params[:country_id].to_i == 1
    collection = collection.where('schools.created_at >= ? AND schools.created_at <= ?', from, to)

    sql_query = <<-SQL
      SELECT
        name,
        (SELECT count(id) FROM users WHERE users.school_id = schools.id AND type = 'Teacher') AS teacher_count,
        (SELECT count(id) FROM forms WHERE forms.school_id = schools.id) AS class_count,
        justify_interval(date_trunc('day', AGE(created_at)::interval)) as account_age,
        (to_char(created_at, 'DD/MM/YYYY hh:mm:ss')) as record_created_at,
        (CASE
            WHEN schools.finance_name IS NOT NULL AND
                 schools.finance_name != '' AND
                 schools.finance_email IS NOT NULL AND
                 schools.finance_email != '' THEN
              true
            WHEN schools.subscription_externally_covered OR
                 schools.annual_subscription
            THEN
              true
            ELSE
              false
        END) as has_billing
      FROM schools WHERE id IN (#{collection.ids.join(', ')}) ORDER BY created_at DESC
    SQL

    collection = JSONString.generate(sql(sql_query))
    filename = "SchoolReport_#{Date.today}__#{Date.parse(from)}-#{Date.parse(to)}"

    render json: {
      csv: CSV.generate do |csv|
        csv << ['Name', '# Teachers', '# Classes', 'Account Age', 'Created At', 'Has Billing']
        collection.each do |record|
          csv << [
            record['name'],
            record['teacher_count'],
            record['class_count'],
            record['account_age'].gsub('mons ', 'months ').gsub('mon ', 'month '),
            record['record_created_at'],
            record['has_billing'],
          ]
        end
      end,
      filename: filename,
    }
  end

  def download_schools_subscribed
    from = reports_params[:from]
    to = reports_params[:to]
    onlyActive = params[:onlyShowActive] == 'true'
    activityFrom = (params[:activityFrom].present? ? Date.parse(params[:activityFrom]) : 30.days.ago.to_datetime).beginning_of_day
    activityTo = (params[:activityTo].present? ? Date.parse(params[:activityTo]) : DateTime.now).end_of_day
    sub_state = params[:isSubscribed]

    collection = School.generic
    collection = collection.where(country_id: params[:country_id]) if params[:country_id].present?
    collection = collection.where(region: params[:region]) if params[:region].present? && params[:country_id].to_i == 1
    collection = collection.where('schools.created_at >= ?', from) if from.present?
    collection = collection.where('schools.created_at <= ?', to) if to.present?
    collection = collection.where('schools.hubspot_subscription_status = ?', sub_state) if sub_state.present? && sub_state != 'all'
    collection = collection.joins(:teachers).group('schools.id').order('schools.name')
    if onlyActive
      collection = collection.where("
        EXISTS(
          SELECT id
          FROM users
          WHERE users.type = 'Teacher'
          AND users.school_id = schools.id
          AND EXISTS(
            SELECT active_days.id
            FROM active_days
            WHERE active_days.user_id = users.id
            AND active_days.date >= :rangeStart
            AND active_days.date <= :rangeEnd
          )
        )
        ",
                                    rangeStart: activityFrom,
                                    rangeEnd: activityTo)
    end

    filename = "SchoolsSubscribed_#{Date.today}"

    render json: {
      csv: CSV.generate do |csv|
        csv << [
          'ID',
          'Name',
          'Admin Email',
          'Admin name',
          'Subscribed',
          'Subscription Status',
          'School created at',
          '# Teachers',
          '# Active teachers'
        ]
        collection.each do |record|
          active_teachers_count = record.teachers.where("
              EXISTS(
                SELECT active_days.id
                FROM active_days
                WHERE active_days.user_id = users.id
                AND active_days.date >= :rangeStart
                AND active_days.date <= :rangeEnd
              )",
                                                        rangeStart: activityFrom,
                                                        rangeEnd: activityTo).count
          csv << [
            record.id,
            record.name,
            record.main_teacher.email,
            record.main_teacher.name,
            record.subscribed?,
            record.hubspot_subscription_status,
            record.created_at,
            record.teachers.count,
            active_teachers_count,
          ]
        end
      end,
      filename: filename,
    }
  end

  def custom_lessons_report
    templates = Lesson::Template.where(user_generated: true)
    # count of users/schools who have made templates (unique)
    unique_users = templates.pluck(:user_id).compact.uniq
    unique_schools = templates.joins(:user).pluck(:school_id).compact.uniq

    week_ago = (DateTime.now - 1.week).beginning_of_day
    templates_last_week = templates.where('lesson_templates.created_at >= ?', week_ago)
    month_ago = (DateTime.now - 1.month).beginning_of_day
    templates_last_month = templates.where('lesson_templates.created_at >= ?', month_ago)
    year_ago = (DateTime.now - 1.year).beginning_of_day
    templates_last_year = templates.where('lesson_templates.created_at >= ?', year_ago)

    published_templates_week = templates_last_week.where(available: true)
    published_templates_month = templates_last_month.where(available: true)
    published_templates_year = templates_last_year.where(available: true)
    published_templates_all_time = templates.where(available: true)

    # count of custom templates created - 1 week
    # count of custom templates created - 1 month
    # count of custom templates created - this year
    # count of custom templates created - all time

    csv_lines = [
      ['User Lesson Template Stats', ''],
      ['Users:', 'Unique Users', 'Unique Schools', DateTime.now.strftime('%d/%m/%Y')],
      ['# of Unique: ', unique_users.count, unique_schools.count],
      [],
      ['All Templates:', 'Templates (7 days)', 'Templates (1 month)', 'Templates (1 year)', 'Templates (all time)'],
      ['From: ', week_ago.strftime('%d/%m/%Y'), month_ago.strftime('%d/%m/%Y'), year_ago.strftime('%d/%m/%Y')],
      ['# of Templates: ', templates_last_week.count, templates_last_month.count, templates_last_year.count, templates.count],
      [],
      ['Published Templates:', 'Templates (7 days)', 'Templates (1 month)', 'Templates (1 year)', 'Templates (all time)'],
      ['From: ', week_ago.strftime('%d/%m/%Y'), month_ago.strftime('%d/%m/%Y'), year_ago.strftime('%d/%m/%Y')],
      [
        '# of Templates: ',
        published_templates_week.count,
        published_templates_month.count,
        published_templates_year.count,
        published_templates_all_time.count
      ],
      [],
      [],
      ['Schools:', 'School ID', 'School Name', 'Template Count'],
      *School
        .where(id: unique_schools)
        .joins(:user_lesson_templates)
        .group('schools.id')
        .pluck(:id, :name, 'count(lesson_templates.id) as template_count')
        .map { |school| ['', *school] },
      [],
      ['Duplicated Lessons:', 'Template ID', 'Template Name', 'Template Machine Name', 'Duplicated Count'],
      *Lesson::Template
        .joins(:user_templates)
        .group('lesson_templates.id')
        .pluck(:id, :name, :machine_name, 'count(lesson_templates.id) as duplicated_count')
        .map { |school| ['', *school] },
    ]

    # count of custom templates - published
    # list of schools who have made custom templates
    # list of templates duplicated from lessons

    render json: {
      csv: CSV.generate do |csv|
        csv_lines.each do |line|
          csv << line
        end
      end,
      filename: "#{Date.today}_custom_lessons_report",
    }
  end

  def subscription_wonde_status_report
    query = <<-SQL
      SELECT
        schools.id as "ID",
        schools.name as "Name",
        schools.hubspot_subscription_status as "Subscription Status",
        schools.wonde_id as "Wonde ID",
        (SELECT wonde_imports.created_at
          FROM wonde_imports
          WHERE wonde_imports.school_id = schools.id
          ORDER BY wonde_imports.created_at DESC
          LIMIT 1
        ) as "Last Import",
        (SELECT wonde_imports.import_status
          FROM wonde_imports
          WHERE wonde_imports.school_id = schools.id
          ORDER BY wonde_imports.created_at DESC
          LIMIT 1
        ) as "Import Status",
        (SELECT
          COUNT(DISTINCT(users.id))
          FROM users
          WHERE users.school_id = schools.id
            AND users.type = 'Teacher'
            AND EXISTS(
              SELECT active_days.id
              FROM active_days
              WHERE active_days.user_id = users.id
              AND active_days.date >= \'#{60.days.ago.beginning_of_day}\'
            )
        ) as "Active Teachers",
        (SELECT
          COUNT(DISTINCT(users.id))
          FROM users
          WHERE users.school_id = schools.id
            AND users.type = 'Pupil'
            AND EXISTS(
              SELECT active_days.id
              FROM active_days
              WHERE active_days.user_id = users.id
              AND active_days.date >= \'#{60.days.ago.beginning_of_day}\'
            )
        ) as "Active Pupils"
      FROM schools
      LEFT JOIN users ON users.school_id = schools.id
      LEFT JOIN active_days ON active_days.user_id = users.id
      LEFT JOIN wonde_imports ON wonde_imports.school_id = schools.id
      -- generic schools that are not home school category
      WHERE schools.school_type = 0 AND schools.category != 5
      GROUP BY schools.id
    SQL

    result = ApplicationRecord.connection.exec_query(query)

    render json: {
      csv: CSV.generate do |csv|
        csv << [
          'ID',
          'Name',
          'Subscription Status',
          'Wonde ID',
          'Last Import',
          'Import Status',
          'Active Teachers',
          'Active Pupils'
        ]
        result.each do |row|
          csv << [
            row['ID'],
            row['Name'],
            row['Subscription Status'],
            row['Wonde ID'],
            row['Last Import'],
            WondeImport.import_statuses.key(row['Import Status']),
            row['Active Teachers'],
            row['Active Pupils']
          ]
        end
      end,
      filename: "#{Date.today}_school_subscription_wonde_status_report",
    }
  end

  def teacher_activity_by_units
    unit_ids = params['unit_ids'].values.map(&:to_i)

    query = <<-SQL
      SELECT
        u.id as unit_id,
        u.name as unit_name,
        users.id as user_id,
        users.name as user_name,
        users.email as user_email,
        schools.id as school_id,
        schools.name as school_name,
        y.id as year_id,
        y.name as year_name,
        c.id as curriculum_id,
        c.name as curriculum_name
      FROM new_library_units u
        JOIN new_library_unit_templates ut ON ut.unit_id = u.id
        JOIN lesson_templates t ON ut.template_id = t.id
        JOIN lesson_lessons l on l.template_id = t.id
        JOIN forms f on l.form_id = f.id
        JOIN enrollments e ON e.form_id = f.id
        JOIN users ON e.user_id = users.id
        JOIN schools on users.school_id = schools.id
        LEFT JOIN new_library_years y ON u.year_id = y.id
        LEFT JOIN new_library_curricula c ON y.curriculum_id = c.id
      WHERE u.id IN ( #{unit_ids.join(', ')} )
        AND NOT schools.deleted
        AND NOT users.deleted
        AND NOT f.deleted
        AND NOT l.deleted
        AND NOT t.deleted
        AND l.time < NOW()
        AND users.type = 'Teacher'
      GROUP BY u.id, users.id, schools.id, y.id, c.id
      ORDER BY u.id asc, schools.id asc, users.id asc
    SQL
    result = ApplicationRecord.connection.exec_query(query)

    render json: {
      csv: CSV.generate do |csv|
        csv << [
          'Unit ID',
          'Unit Name',
          'Year ID',
          'Year Name',
          'Curriculum ID',
          'Curriculum Name',
          'School ID',
          'School Name',
          'Teacher ID',
          'Teacher Name',
          'Teacher Email',
        ]
        result.each do |row|
          csv << [
            row['unit_id'],
            row['unit_name'],
            row['year_id'],
            row['year_name'],
            row['curriculum_id'],
            row['curriculum_name'],
            row['school_id'],
            row['school_name'],
            row['user_id'],
            row['user_name'],
            row['user_email'],
          ]
        end
      end,
      filename: "#{Date.today}_teacher_unit_activity_report",
    }
  end

  private

  def reports_params
    params.permit(:from, :to, :admin_only, :country_id, :region)
  end
end
