class Admin::TemplateAssessmentsController < AdminController
  before_action :set_template_assessment, only: %i[show edit update destroy]

  def index
    collection = Lesson::TemplateAssessment.all
    if (template_id = params[:template_id])
      collection = Lesson::Template.find(template_id).template_assessments
    end
    list_with_response collection.joins(:risk_assessment),
                       params, search_params: %w[lesson_risk_assessments.name], default_sort: { created_at: :desc }
  end

  def update
    save_with_response @template_assessment, template_assessment_params
  end

  def create
    save_with_response Lesson::TemplateAssessment.new, template_assessment_params
  end

  private

  def set_template_assessment
    @template_assessment = Lesson::TemplateAssessment.find(params[:id])
  end

  def template_assessment_params
    ActionController::Parameters.new(
      JSON.parse(params.require(:template_assessments))
    )
      .permit(:likelihood, :risk_assessment_id, :severity, :template_id)
  end
end
