# frozen_string_literal: true
class Admin::AdminsController < AdminController
  before_action :set_admin, only: %i[show edit update destroy]

  def index
    list_with_response User.admin,
                       params,
                       search_params: %w[name email],
                       default_sort: { name: :asc }
  end

  def update
    save_with_response @admin, admin_params
  end

  def create
    save_with_response User.admin.new, admin_params
  end

  def show
    render json: @admin
  end

  def destroy
    destroy_record @admin
  end

  private

  def set_admin
    @admin = User.admin.find(params[:id])
  end

  def admin_params
    params.permit(:name, :email, :password, :can_view_school_data)
  end
end
