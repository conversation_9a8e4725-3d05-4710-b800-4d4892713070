class Admin::AuthorsController < AdminController
  before_action :set_author, only: %i[show edit update destroy]

  def index
    list_with_response Author.all.where(user: nil, organisation: nil),
                       params,
                       search_params: %w[name],
                       default_sort: { name: :asc }
  end

  def show
    render json: <AUTHOR>

  def update
    save_with_response @author, author_params
  end

  def create
    save_with_response Author.new, author_params
  end

  def destroy
    destroy_record <AUTHOR>

  private

  def set_author
    <AUTHOR> Author.find(params[:id])
  end

  def author_params
    params.permit(:name, :body, :title, :summary, :jw_id, :image_alt, :draft, :fileboy_image_id)
  end

  def authors_scopes
    %w[all published drafts]
  end
end
