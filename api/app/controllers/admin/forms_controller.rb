class Admin::FormsController < AdminController
  before_action :set_form, only: [:show, :update, :edit, :add_pupils, :remove_pupils, :destroy, :build_lessons]

  def index
    if params[:show_all]
      collection = Form.all
    else
      collection = Form.current_school_year
    end
    if (teacher_id = params[:teacher_ids])
      collection = collection.for_users(teacher_id["0"])
    end
    if (pupil_id = params[:pupil_ids])
      collection = Pupil.find(pupil_id["0"]).forms
    end
    if (pupil_id = params[:pupil_id])
      collection = Pupil.find(pupil_id).forms
    end

    collection = collection.joins(:school).where.not(schools: { deleted: true })

    list_with_response collection, params, search_params: ["forms.name"], default_sort: { name: :asc }
  end

  def create
    save_with_response Form.new, form_params
  end

  def update
    save_with_response @form, form_params
  end

  def show
    render json: @form
  end

  def add_pupils
    if @form.pupil_ids += params[:pupil_ids]
      render json: { saved: true }
    else
      render json: { saved: false }
    end
  end

  def remove_pupils
    if @form.pupil_ids -= params[:pupil_ids]
      render json: { saved: true }
    else
      render json: { saved: false }
    end
  end

  def destroy
    destroy_record @form
  end

  def mass_destroy
    ids = params.permit(ids: [])[:ids]
    forms = Form.where(id: ids)
    mass_destroy_records(forms)
  end

  def build_lessons
    begin
      lesson_template_ids = params["lesson_template_ids"]
      lesson_start_date = params["lesson_start_date"]
      lesson_weekdays = params["lesson_weekdays"]

      @form.update(
        lesson_start_date: lesson_start_date,
        lesson_weekdays: lesson_weekdays,
      )

      @form.add_lesson_template_ids(lesson_template_ids)

      render json: { saved: true }

    rescue => e
      render json: { saved: false, errors: [e] }
    end
  end

  def class_graph_data
    @form = current_school.forms.find(params[:form_id])
    render json: @form.class_graph_data
  end

  private

  def set_form
    @form = Form.find(params[:id])
  end

  def form_params
    params.permit(:name, :school_id, teacher_ids: [], pupil_ids: [], lesson_weekdays: {})
  end
end
