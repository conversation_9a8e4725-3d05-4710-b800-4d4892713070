class Admin::KeywordsController < AdminController
  before_action :set_keyword, only: %i[show edit update destroy]

  def index
    collection = Lesson::Keyword.all
    if (template_id = params[:template_id])
      collection = Lesson::Template.find(template_id).keywords
    end
    list_with_response collection, params, default_sort: { name: :asc }
  end

  def show
    render json: @keyword
  end

  def update
    save_with_response @keyword, keyword_params
  end

  def create
    save_with_response Lesson::Keyword.new, keyword_params
  end

  def destroy
    destroy_record @keyword
  end

  def update_order
    params[:keyword_ids].each_with_index do |id, i|
      Lesson::Keyword.find(id).update(weight: i)
    end
    render json: { saved: true }
  end

  private

  def set_keyword
    @keyword = Lesson::Keyword.find(params[:id])
  end

  def keyword_params
    ActionController::Parameters
      .new(JSON.parse(params.require(:keywords)))
      .permit(
        :template_id,
        :name,
        :body,
        :weight,
        :fileboy_image_id,
        :image_url,
        quiz_question_attributes: [
          :id,
          :body,
          :lesson_template_id,
          :answer,
          :lesson_slide_id,
          :use_data,
          :question_body,
          :question_video_url,
          :question_fileboy_image_id,
        ]
      )
  end
end
