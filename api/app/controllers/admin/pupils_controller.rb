class Admin::PupilsController < AdminController
  before_action :set_pupil, only: %i[show edit update destroy lessons :destroy_mark]

  def index
    collection = Pupil.all

    if (class_id = params[:class_id])
      collection = Form.find(class_id).pupils
      if params[:add_pupils].present?
        add = params[:add_pupils]
        form = Form.find(class_id)
        collection = (add == "true" ? form.unassigned_pupils : form.pupils)
      end
    end

    if (teacher_id = params[:teacher_id])
      collection = Teacher.find(teacher_id).pupils.group(:id)
    end

    if params[:form_id]
      collection = Form.find(params[:form_id]).pupils
    end

    if params[:not_in_form].present?
      collection = Form.find(params[:not_in_form]).school.pupils.left_joins(:enrollments).group(:id).having('COUNT(enrollments.id) < 1')
    end

    collection = collection.joins(:school).where.not({ schools: { deleted: true }})

    list_with_response collection,
                        params,
                        allowed_scopes: pupils_scopes,
                        # users.name over name is to expliclity fix 'PG::AmbiguousColumn - ERROR:  column reference "name" is ambiguous'
                        search_params: ["users.name", "identifier"],
                        default_sort: { name: :asc }
  end

  def create
    pupil = Pupil.new(pupil_params)
    if pupil.save
      render json: { saved: true, record: pupil }
    else
      render json: { saved: false, errors: pupil.errors.full_messages }
    end
  end

  def show
    render json: @pupil.as_json.merge(form_ids: @pupil.form_ids, school: {
      id: @pupil.school.id,
      name: @pupil.school.name,
      type: @pupil.school.school_type
    })
  end

  def update
    save_with_response @pupil, pupil_params
  end

  def destroy
    destroy_record @pupil
  end

  def mass_destroy
    ids = params.permit(ids: [])[:ids]
    pupils = Pupil.where(id: ids)
    mass_destroy_records(pupils)
  end

  private

  def set_pupil
    @pupil = Pupil.find(params[:id])
  end

  def pupils_scopes
    %w[all home_school school]
  end

  def pupil_params
    params.permit(
      :name,
      :identifier,
      :dob,
      :gender,
      :school_id,
      :ethnicity,
      :location,
      form_ids: [],
    )
  end
end
