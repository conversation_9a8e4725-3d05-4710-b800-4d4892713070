class Admin::LessonsController < AdminController
  def index
    collection = Lesson::Lesson.all

    if (pupil_id = params[:pupil_id])
      collection = Pupil.find(pupil_id).lessons
    end

    if (class_id = params[:class_id])
      collection = Form.find(class_id).lessons
    end

    collection = collection
      .joins(:form)
      .group(:id)
      .including_done_attr
      .select('FIRST(forms.name) AS form_name')

    opts = {}
    school_id = params[:school_id]
    sort = opts[:default_sort] || { time: :asc }
    processed_collection = collection

    if params[:query] && params[:query].present?
      processed_collection = processed_collection.joins(:template).where("lesson_templates.name ILIKE ?", "%#{params[:query]}%")
    end

    allowed_scopes ||= []

    if params[:scope] && allowed_scopes && allowed_scopes.include?(params[:scope])
      processed_collection = processed_collection.send(params[:scope])
    end

    if school_id
      processed_collection = processed_collection.where(school_id: school_id)
    end

    if params[:skip_pagination]
      render json: { records: processed_collection, meta: {} }
    else
      pagy, records = pagy(processed_collection.order(sort))
      render json: { records: records, meta: pagy }
    end
  end

  def show
    render json: Lesson::Lesson.find(params[:id]).as_json(methods: [:done])
  end

  def lesson_pupil_marks
    lesson = Lesson::Lesson.find(params[:id])
    pupils_with_lesson = lesson.pupils.map do |pupil|
      result = pupil.mark_for_lesson(lesson.template.id)
      quiz_results = pupil.quiz_results_for(lesson)
      word_search_results = pupil.word_search_results.where(lesson_template_id: lesson.template.id)
      quip_quiz_results = pupil.quip_quiz_results.where(lesson_template_id: lesson.template.id)
      {
        id: pupil.id,
        name: pupil.name,
        lesson_result: result,
        quiz_result: quiz_results,
        quip_quiz_results: quip_quiz_results,
        word_search_results: word_search_results
      }
    end
    render json: lesson.as_json.merge(pupils: pupils_with_lesson, name: lesson.template.name)
  end

  def destroy_lesson_result_for_pupil
    lesson = Lesson::Lesson.find(params[:id])
    params[:pupil_ids].map do |pupil_id|
      pupil = Pupil.find(pupil_id)
      pupil.quiz_attempts.where(lesson_template_id: lesson.template.id).delete_all()
      pupil.word_search_results.where(lesson_template_id: lesson.template.id).delete_all()
      pupil.quip_quiz_results.where(lesson_template_id: lesson.template.id).delete_all()
      pupil.lesson_results.where(template_id: lesson.template.id).delete_all()
    end
    render json: { complete: true }
  end

  def remove_from_form
    school = School.find_by(id: params[:school_id])
    lesson = school.lessons.find(params[:id])

    begin
      lesson.form.remove_lesson_template_ids([lesson.template_id])

      render json: { saved: true }
    rescue => e
      render json: { saved: false, errors: [e] }
    end
  end

  def reschedule
    school = School.find_by(id: params[:school_id])
    lesson = school.lessons.not_done.find(params[:id])

    begin
      lesson.update!(time: params[:time])

      render json: { saved: true }
    rescue => e
      render json: { saved: false, errors: [e] }
    end
  end
end
