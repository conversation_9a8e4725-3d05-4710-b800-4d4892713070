class Admin::SlidesController < AdminController
  before_action :set_slide, only: %i[show edit update destroy]

  def index
    collection = Lesson::Slide.all
    if (template_id = params[:template_id])
      collection = Lesson::Template.find(template_id).slides
    end
    list_with_response collection,
                       params, serialized: true, default_sort: { weight: :asc }
  end

  def show
    render json: @slide
  end

  def update
    save_with_response @slide, slide_params
  end

  def create
    save_with_response Lesson::Slide.new, slide_params
  end

  def update_order
    params[:slide_ids].each_with_index do |id, i|
      Lesson::Slide.find(id).update(weight: i)
    end
    render json: { saved: true }
  end

  def destroy
    destroy_record @slide
  end

  private

  def set_slide
    @slide = Lesson::Slide.find(params[:id])
  end

  def slide_params
    ActionController::Parameters.new(JSON.parse(params.require(:slides))).permit(:slide_type, :body, :template_id, :iframe_src, :jw_id, :image_style_type, :intro_text, :skip_translation, :narration_video, :fileboy_image_id, :fileboy_vtt_id, :fileboy_video_id, :video_url, :video_id, :tour_id, :autoFocusVideoId, :end_of_unit_assessment, :quip_question_id, :loop_video, :image_url)
  end
end
