class Admin::RewardsController < AdminController
  before_action :set_reward, only: %i[show edit update destroy]

  def index
    collection = Reward.all
    list_with_response collection, params, default_sort: { created_at: :desc }
  end

  def create
    save_with_response Reward.new, rewards_params
  end

  def update
    save_with_response @reward, rewards_params
  end

  def show
    render json: @reward
  end

  def destroy
    destroy_record @reward
  end

  private

  def set_reward
    @reward = Reward.find(params[:id])
  end

  def rewards_params
    params.permit(:reward_type, :rank_id, :fileboy_image_id)
  end
end
