module StaticUser
  class AccountsController < StaticUserController
    before_action :set_user, only: %i[show update]

    def show; end

    def update
      if @user.update(user_params)
        redirect_to user_account_path(@user), notice: 'Account updated successfully'
      else
        flash.now[:error] = @user.errors.full_messages.to_sentence
        render :show
      end
    end

    def close_account
      throw 'Must be signed in to close account' unless @current_user.present?
      throw 'Only individual user accounts can be closed' unless @current_user.individual_user?
      # if we have a legacy ai subscription, try cancel it. handle errors if it fails but don't block
      user_info = { id: @current_user.id, type: @current_user.type, school_id: @current_user.school_id }
      if @current_user.ai_subscription&.active? || @current_user.curriculum_subscription&.active? || @current_user.active_subscription
        throw "Cannot close account with active subscription, please cancel your subscription first and then close your account"
      end

      sign_out
      redirect_to root_path, notice: 'Account closed'

      @current_user.destroy
    rescue => e
      alert_failed_close_account(@current_user, e.message)
      puts e.message
      ErrorLog.create(error: {error: e.message}.to_s, user_info: user_info)
      redirect_to user_account_path(@current_user), notice: 'Failed to close account'
    end

    def current_account_redirect
      redirect_to '/accounts/dashboard' unless @current_user.present?
      redirect_to user_account_path(@current_user)
    end

    def feature_preview
      @user = @current_user
      @beta_features = User::BETA_FEATURES
    end

    def toggle_beta_feature
      feature = params[:feature]
      enable = params[:enable] == true
      if User::BETA_FEATURES.key?(feature)
        if enable
          @current_user.enable_beta_feature!(feature)
        else
          @current_user.disable_beta_feature!(feature)
        end
        render json: { success: true }
      else
        render json: { success: false, error: 'Unknown feature' }, status: :unprocessable_entity
      end
    end

    # Presentation Settings Page
    def presentation_settings
      @user = @current_user
      @presentation_settings = @user.presentation_settings || {}
    end

    def update_presentation_settings
      @user = @current_user
      settings = params.require(:presentation_settings).permit(:font_size, :font_family, :letter_spacing, :text_case, :color_scheme, :narration_voice, :narration_auto_play, :sidebar_side, :sidebar_visible, :top_bottom_text_visible)
      @user.presentation_settings = (@user.presentation_settings || {}).merge(settings.to_h)
      respond_to do |format|
        format.json do
          if @user.save
            render json: { success: true, message: 'Presentation settings updated.' }
          else
            render json: { success: false, errors: @user.errors.full_messages }, status: :unprocessable_entity
          end
        end
        format.html do
          if @user.save
            redirect_to presentation_settings_user_account_path(@user), notice: 'Presentation settings updated.'
          else
            flash.now[:error] = 'Failed to update settings.'
            @presentation_settings = @user.presentation_settings
            render :presentation_settings
          end
        end
      end
    end

    # Communication Preferences Page
    def communication_preferences
      @user = @current_user
      @communication_preferences = User::COMMUNICATION_PREFERENCES
    end

    def toggle_communication_preference
      preference = params[:feature]
      enable = params[:enable] == true
      if User::COMMUNICATION_PREFERENCES.key?(preference)
        if enable
          @current_user.enable_communication_preference!(preference)
        else
          @current_user.disable_communication_preference!(preference)
        end
        render json: { success: true }
      else
        render json: { success: false, error: 'Unknown preference' }, status: :unprocessable_entity
      end
    end

    private

    def alert_failed_close_account(user, error_message)
      AdminMailer.failed_account_closure(user, error_message).deliver_now
    end

    def set_user
      @user = @current_user
    end

    def user_params
      param_data = if params[:teacher].present?
                     params.require(:teacher)
                   else
                     params.require(:individual_user)
                   end
      param_data.permit(
        :name,
        :email,
        :phone,
        :job_title,
        :password,
        :dob,
        :gender,
        :ethnicity
      ).tap do |data|
        data.delete(:password) if data[:password].blank?
      end
    end
  end
end
