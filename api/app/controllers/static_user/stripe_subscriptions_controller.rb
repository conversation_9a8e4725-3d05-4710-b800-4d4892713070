module StaticUser
  class StripeSubscriptionsController < StaticUserController
    def subscriptions
      # only redirect if param && user has the ai sub already
      if params[:signup_redirect].present? && current_user.subscribed_to_service?(:ai)
        redirect_to '/ai'
      end
      @subscriptions = StripeSubscription.subscriptions(current_user)&.map do |item|
        data_item = item&.items&.data&.first
        next unless data_item
        sub = current_user.stripe_subscriptions.find_by(stripe_subscription_id: item.id)
        {
          id: item.id,
          subscription_type: sub&.subscription_type,
          subscription_id: sub&.id,
          name: data_item.plan.nickname,
          recurring: data_item.price.recurring.interval,
          start: Time.at(item.current_period_start),
          end: Time.at(item.current_period_end),
          status: item.status,
          price_pennies: data_item.price.unit_amount,
          formatted_price: StripeSubscription.format_currency(data_item.price.unit_amount, data_item.price.currency),
          cancel_at: item.cancel_at.present? ? Time.at(item.cancel_at) : nil,
          cancel_at_period_end: item.cancel_at_period_end,
          cancelled_at: item.canceled_at.present? ? Time.at(item.canceled_at) : nil,
        }
      end || []

      @has_subscription = @subscriptions.any?

      @invoice_data = StripeSubscription.invoices(current_user, starting_after: params[:after], ending_before: params[:before])
      @invoices = @invoice_data&.data&.map do |item|
        {
          name: item&.lines&.data&.first&.plan&.nickname || "",
          id: item.id,
          amount_paid: item.amount_paid,
          amount_due: item.amount_due,
          amount_remaining: item.amount_remaining,
          attempt_count: item.attempt_count,
          due_date: item.due_date.present? ? Time.at(item.due_date) : nil,
          currency: item.currency,
          status: item.status,
          period_start: Time.at(item.period_start),
          period_end: Time.at(item.period_end),
          hosted_invoice: item.hosted_invoice_url,
          pdf_download: item.invoice_pdf,
        }
      end || []
    end

    # cancel a active subscriptions
    def cancel
      subscription = current_user.stripe_subscriptions.find(params[:id])
      subscription.cancel # Cancel on stripe
      redirect_to subscriptions_user_stripe_subscriptions_path, notice: 'Subscription cancelled successfully'
    end
  end
end
