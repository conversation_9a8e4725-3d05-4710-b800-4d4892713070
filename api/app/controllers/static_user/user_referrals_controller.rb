# app/controllers/static_user/user_referrals_controller.rb
module StaticUser
  class UserReferralsController < StaticUserController
    include BreadcrumbsHelper
    before_action :set_user_referral, only: [:show, :destroy, :resend]
    before_action :set_base_breadcrumbs
    
    def index
      @pending_referrals = current_user.user_referrals.pending
      @accepted_referrals = current_user.user_referrals.accepted + current_user.user_referrals.completed
      @expired_referrals = current_user.user_referrals.expired
      
      @total_earnings = current_user.total_referral_earnings
      @pending_earnings = current_user.pending_referral_earnings
      @paid_earnings = current_user.paid_referral_earnings
    end
    
    def show
    end
    
    def new
      add_breadcrumb 'Refer a Friend', user_referrals_path
      @user_referral = current_user.user_referrals.build
    end
    
    def create
      add_breadcrumb 'Refer a Friend', user_referrals_path
      data = user_referral_params
      data[:reward_amount] = UserReferral::BASE_REFERRAL_AMOUNT
      @user_referral = current_user.user_referrals.build(data)
      @user_referral.invited_at = Time.current

      user_exists = User.find_by(email: @user_referral.email)
      if user_exists
        flash[:error] = 'Error: User with this email already exists.'
        render :new and return
      end

      referral_exists = UserReferral.exists?(email: @user_referral.email)
      if referral_exists
        flash[:error] = 'Error: Referral for this email already exists.'
        render :new and return
      end

      if @user_referral.save
        UserReferralMailer.invitation_email(@user_referral).deliver_now
        redirect_to user_referrals_path, notice: 'Referral sent successfully!'
      else
        render :new
      end
    rescue ActionController::ParameterMissing
      redirect_to user_referrals_path, alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    end
    
    def destroy
      @user_referral.update!(status: :cancelled)
      redirect_to user_referrals_path, notice: 'Referral cancelled.'
    end
    
    def resend
      if @user_referral.pending? && @user_referral.expires_at > Time.current
        UserReferralMailer.invitation_email(@user_referral).deliver_now
        redirect_to user_referrals_path, notice: 'Referral resent!'
      else
        redirect_to user_referrals_path, alert: 'Cannot resend this referral.'
      end
    end

    def update_referral_code
      data = params.require(:user).permit(:referral_code)
      code = data[:referral_code].strip.upcase
      if code.blank?
        redirect_to user_referrals_path, alert: 'Referral code cannot be blank.'
        return
      end
      code_in_use = User.where.not(id: current_user.id).find_by(referral_code: code)
      if code_in_use
        redirect_to user_referrals_path, alert: 'This referral code is already in use. Please choose a different one.'
        return
      end
      current_user.update!(referral_code: code)
      redirect_to user_referrals_path, notice: 'Referral code updated successfully!'
    rescue ActiveRecord::RecordInvalid => e
      redirect_to user_referrals_path, alert: "Error updating referral code: #{e.message}"
    rescue ActionController::ParameterMissing
      redirect_to user_referrals_path, alert: 'Error: Parameter missing - Please make sure all required fields are filled in.'
    rescue => e
      redirect_to user_referrals_path, alert: "An unexpected error occurred: #{e.message}"
    end

    private

    def set_user_referral
      @user_referral = current_user.user_referrals.find(params[:id])
    end
    
    def user_referral_params
      params.require(:user_referral).permit(:email)
    end

    def set_base_breadcrumbs
      add_breadcrumb "Dashboard", school_static_dashboard_path
      add_breadcrumb 'Referrals', user_referrals_path
    end
  end
end
