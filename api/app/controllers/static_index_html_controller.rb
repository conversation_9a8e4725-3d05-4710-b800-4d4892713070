# frozen_string_literal: true

I18N_DATA = JSON.load(File.open(Rails.root + "lib/i18n_data/en.json"))

BASE_WEBSITE_PATH = Rails.root + 'app/website'

class StaticIndexHtmlController < ApplicationController
  api_version 1

  def get
    # Serve files matching the name (static files)
    if (File.file?("#{BASE_WEBSITE_PATH}#{request.path}"))

      # "disposition: :inline" causes rails to send the file to be displayed
      # the default is to make the browser download the file instead
      send_file "#{BASE_WEBSITE_PATH}#{request.path}", disposition: :inline

    # fallback to the index
    else
      serve_index_html_file
    end
  end

  private

  def serve_index_html_file
    html = File.read("#{BASE_WEBSITE_PATH}/index.html")
    html = html.gsub("{{ DATA }}", I18N_DATA.to_json)
    html = html.gsub("{{ INJECTED_ENV }}", get_env.to_json)
    render html: html.html_safe
  end


  def get_env
    {
      QUIP_PUBLIC_KEY: "cadbc93c-bb2f-4159-a105-0b11135ec826",
      QUIP_URL: "https://quip-website.herokuapp.com",
      QUIP_PROXIED_API_URL: "https://www.developingexperts.com/api/v2",
      QUIP_API_URL: "https://quip-api.herokuapp.com/",
      QUIP_FILEBOY_PUBLIC_KEY: "undefined",
      CHINA: false,
      USE_NEW_AREAS: true,
      QUIP_WEBSITE_URL: "https://quip-website.herokuapp.com",
      NODE_ENV: Rails.env.development? ? "development" : "production",
    }
  end
end
