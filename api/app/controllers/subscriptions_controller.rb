# frozen_string_literal: true
require Rails.root.join('lib', 'utils', 'error_logger')
class SubscriptionsController < WebApplicationController
  include Warden<PERSON>ontroller
  include ErrorLogger

  before_action :authenticate_user!
  before_action :check_stripe_configuration, except: [:success]
  before_action :set_current_user

  layout 'subscription'

  def new
    capture_posthog_marketing_event('subscription_new')
    # Check if user already has an active subscription
    # First check for a personal subscription
    set_subscribable(true) # Force user
    has_personal_subscription = check_for_active_subscription(@subscribable)

    # Then check for a school subscription if applicable
    has_school_subscription = false
    if current_user.school.present? && !has_personal_subscription
      set_subscribable(false) # Use school

      has_school_subscription = check_for_active_subscription(@subscribable)
    end

    # If user has any active subscription, redirect to subscription page
    if has_school_subscription || has_personal_subscription
      flash[:notice] = 'You already have an active subscription. If you wish to make changes, please use the edit option.'
      redirect_to subscription_path
      return
    end

    @products = StripeProducts::PRODUCTS
    @sizes = StripeProducts::SIZES

    # Preselect Science product and appropriate size
    @current_products = { science: true }
    @current_size = determine_appropriate_size
    @current_interval = :annual

    # Set a flag for the view to know if this is a user with school
    @has_school = current_user.school.present?

    # Prefill address fields for school subscriptions
    if @has_school && @current_size != :individual
      school = current_user.school
      @address = OpenStruct.new(
        address_line1: school.default_billing_address_line_1,
        address_line2: school.default_billing_address_line_2,
        city: school.default_billing_city,
        state: school.default_billing_state,
        postal_code: school.default_billing_postal_code,
        country: school.default_billing_country&.machine_name || 'GB',
      )
    else
      # restore address from the subscribable if one existed
      @address = @subscribable.subscriber if @subscribable.subscriber.present?
    end

    TrackingService.track_subscription_event('subscription_new', current_user, { 
      message: 'Subscription new page loaded',
      has_school: @has_school,
      initial_size: @current_size,
    })
  rescue => e
    Rails.logger.error "Error in subscription new: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_new')
    flash[:error] = 'An error occurred while preparing the subscription form. Please try again or contact support.'
    redirect_to root_path
  end

  def create
    capture_posthog_marketing_event('subscription_create')

    product_keys = params[:products]&.select { |_, v| v == '1' }&.keys&.map(&:to_sym) || [] # Ensure it's an array
    size_param = params[:size]&.to_sym # This is the base size, e.g. :individual
    interval_param = params[:billing_interval]&.to_sym # NEW: e.g. :annual, :monthly
    discount_code = params[:validated_discount_code]

    # Determine the final plan key (e.g., :individual_monthly, :small)
    final_plan_key = determine_final_plan_key(size_param, interval_param)

    Rails.logger.info "Create - Products: #{product_keys}, Size Param: #{size_param}, Interval: #{interval_param}, Final Plan Key: #{final_plan_key}, Discount: #{discount_code}"

    # Validate user inputs before proceeding
    if product_keys.empty? || size_param.blank? # size_param (base size) must be present
      flash[:error] = 'Please select at least one product and a size.'
      TrackingService.track_subscription_event('subscription_create', current_user, {
        message: 'Invalid subscription creation parameters',
        product_keys: product_keys,
        size_param: size_param,
        interval_param: interval_param,
        discount_code: discount_code
      })
      re_render_new_for_create_errors(product_keys, size_param, interval_param); return
    end

    # Validate the selected products
    product_keys.each do |key|
      unless StripeProducts::PRODUCTS.key?(key)
        flash[:error] = 'Invalid product selection.'; re_render_new_for_create_errors(product_keys, size_param, interval_param); return
      end
    end

    # Validate the final_plan_key against StripeProducts::BASE_PRICES
    unless final_plan_key && StripeProducts::BASE_PRICES.key?(final_plan_key)
      flash[:error] = 'Invalid plan configuration. Please select a valid size and billing interval if applicable.'
      TrackingService.track_subscription_event('subscription_create', current_user, {
        message: 'Invalid final plan key',
        final_plan_key: final_plan_key,
        product_keys: product_keys,
        size_param: size_param,
        interval_param: interval_param,
        discount_code: discount_code
      })
      re_render_new_for_create_errors(product_keys, size_param, interval_param); return
    end

    # Determine subscribable based on the actual plan type (individual or school)
    is_personal_plan_type = (StripeProducts.get_base_size_from_plan_key(final_plan_key) == :individual)
    set_subscribable(is_personal_plan_type)

    subscriber = @subscribable.get_or_create_subscriber

    # These methods need to correctly determine if they should run based on final_plan_key
    # or @subscribable. Assuming they are adapted or @subscribable check is sufficient.
    # Minimal change here is to pass final_plan_key if they are updated to use it.
    update_finance_contact_if_needed(final_plan_key)
    update_po_number_if_needed(final_plan_key)


    subscriber.set_creator(current_user) if @subscribable.is_a?(School) # Keep this as is

    # Update address fields
    unless subscriber.update(params.permit(:address_line1, :address_line2, :city, :state, :postal_code, :country))
      flash[:error] = "Address update failed: #{subscriber.errors.full_messages.join(', ')}"
      TrackingService.track_subscription_event('subscription_create', current_user, {
        message: 'Address update failed',
        final_plan_key: final_plan_key,
        product_keys: product_keys,
        size_param: size_param,
        interval_param: interval_param,
        discount_code: discount_code
      })
      re_render_new_for_create_errors(product_keys, size_param, interval_param); return
    end

    service = SubscriptionService.new(@subscribable)
    # Condition uses is_personal_plan_type
    if is_personal_plan_type
      # Pass final_plan_key to service
      result = service.create_subscription(product_keys, final_plan_key, success_subscription_url, discount_code)

      if result[:checkout_url]
        session[:pending_subscription_product_keys] = product_keys.map(&:to_s)
        # Store final_plan_key in session with a new key name for clarity
        session[:pending_subscription_plan_key] = final_plan_key.to_s
        session[:pending_subscription_subscriber_id] = @subscribable.id
        session[:pending_subscription_subscriber_type] = @subscribable.class.name
        session[:pending_subscription_discount_code] = discount_code

        Rails.logger.info "Storing in session - plan_key: #{session[:pending_subscription_plan_key]}"
        TrackingService.track_subscription_event('subscription_create', current_user, {
          message: 'Checkout session created successfully',
          final_plan_key: final_plan_key,
          product_keys: product_keys,
          size_param: size_param,
          interval_param: interval_param,
          discount_code: discount_code
        })
        redirect_to result[:checkout_url], allow_other_host: true # Added allow_other_host
      else
        flash[:error] = result[:error] || 'Failed to create checkout session.'
        TrackingService.track_subscription_event('subscription_create', current_user, {
          message: 'Error creating checkout session',
          final_plan_key: final_plan_key,
          product_keys: product_keys,
          size_param: size_param,
          interval_param: interval_param,
          discount_code: discount_code
        })
        re_render_new_for_create_errors(product_keys, size_param, interval_param)
      end
    else # School plan
      # Pass final_plan_key to service
      result = service.create_subscription(product_keys, final_plan_key, nil, discount_code)
      if result[:subscription]
        flash[:success] = if result[:invoice]
                            'Subscription created successfully. An invoice has been generated and can be accessed below.'
                          else
                            'Subscription created successfully.'
                          end

        TrackingService.track_subscription_event('subscription_create', current_user, {
          message: 'School Subscription was created successfully',
          final_plan_key: final_plan_key,
          product_keys: product_keys,
          size_param: size_param,
          interval_param: interval_param,
          discount_code: discount_code
        })
        redirect_to subscription_path
      else
          flash[:error] = result[:error] || 'Failed to create school subscription.'
          TrackingService.track_subscription_event('subscription_create', current_user, {
            message: 'Error creating school subscription',
            final_plan_key: final_plan_key,
            product_keys: product_keys,
            size_param: size_param,
            interval_param: interval_param,
            discount_code: discount_code
          })
          re_render_new_for_create_errors(product_keys, size_param, interval_param)
      end
    end
  rescue ArgumentError => e
    flash[:error] = "Invalid subscription parameters: #{e.message}"
    Rails.logger.error "Subscription creation argument error: #{e.message}"
    handle_error_log(e, 'subscription_create')
    re_render_new_for_create_errors(params[:products]&.keys&.map(&:to_sym), params[:size]&.to_sym, params[:billing_interval]&.to_sym)
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = "Validation error: #{e.message}"
    Rails.logger.error "Subscription database validation error: #{e.message}"
    handle_error_log(e, 'subscription_create')
    re_render_new_for_create_errors(params[:products]&.keys&.map(&:to_sym), params[:size]&.to_sym, params[:billing_interval]&.to_sym)
  rescue => e
    flash[:error] = "Error: #{e.message}"
    Rails.logger.error "Subscription error: #{e.message}\n#{e.backtrace.join("\n")}"
    handle_error_log(e, 'subscription_create')
    re_render_new_for_create_errors(params[:products]&.keys&.map(&:to_sym), params[:size]&.to_sym, params[:billing_interval]&.to_sym)
  end

  # Handle Stripe checkout success
  def success
    capture_posthog_marketing_event('subscription_success')
    begin
      # Check if this is a redirect from Stripe checkout
      if params[:session_id].present?
        # Retrieve subscription information from session as individual variables
        subscriber_type = session[:pending_subscription_subscriber_type]
        subscriber_id = session[:pending_subscription_subscriber_id]

        # Debug logging
        Rails.logger.info "Retrieved from session - subscriber_type: #{subscriber_type}"
        Rails.logger.info "Retrieved from session - subscriber_id: #{subscriber_id}"

        if subscriber_type.present? && subscriber_id.present?
          begin
            # Find the subscriber from the stored information
            subscriber_class = subscriber_type.constantize
            subscriber = subscriber_class.find(subscriber_id)

            # Create service to handle checkout completion
            service = SubscriptionService.new(subscriber)

            # Complete the checkout process
            result = service.complete_checkout(params[:session_id])

            if result[:success]
              flash[:success] = 'Your subscription has been created successfully!'
              # Clear the pending subscription from session
              session.delete(:pending_subscription_product_keys)
              session.delete(:pending_subscription_plan_key)
              session.delete(:pending_subscription_subscriber_id)
              session.delete(:pending_subscription_subscriber_type)
              session.delete(:pending_subscription_discount_code)
            else
              flash[:error] = "There was an issue finalizing your subscription: #{result[:error]}"
            end
          rescue NameError => e
            Rails.logger.error "Invalid subscriber type in session: #{e.message}"
            flash[:error] = 'Invalid subscription information was found. Please contact support.'
          rescue ActiveRecord::RecordNotFound => e
            Rails.logger.error "Subscriber not found: #{e.message}"
            flash[:error] = 'Your account information could not be found. Please contact support.'
          rescue => e
            Rails.logger.error "Error in checkout success handler: #{e.message}"
            Rails.logger.error e.backtrace.join("\n")
            flash[:error] = "An error occurred while processing your subscription: #{e.message}"
          end
        else
          Rails.logger.error 'Missing subscription data in session'
          Rails.logger.error "subscriber_type: #{subscriber_type.inspect}"
          Rails.logger.error "subscriber_id: #{subscriber_id.inspect}"
          flash[:error] = 'Your subscription information could not be found. Please contact support.'
        end
      elsif params[:canceled]
        flash[:notice] = 'Your subscription checkout was cancelled. You have not been charged.'
      else
        flash[:notice] = 'No checkout session found.'
      end
    rescue => e
      Rails.logger.error "Unexpected error in subscription success: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      flash[:error] = 'An unexpected error occurred. Please check if your subscription was created successfully or contact support.'
    end

    # Redirect to subscription page
    redirect_to subscription_path
  end

  def update
    capture_posthog_marketing_event('subscription_update')
    find_active_subscription # Sets @subscribable

    unless @subscribable.subscriber&.stripe_subscription_id.present?
      flash[:error] = 'No subscription found to update.'; redirect_to new_subscription_path and return
    end

    product_keys = params[:products]&.select { |_, v| v == '1' }&.keys&.map(&:to_sym) || []
    # size_key from params[:size] and interval from params[:billing_interval] SHOULD NOT be used to change the plan.
    # The plan type (Individual/School) and interval (Annual/Monthly) are fixed after creation.
    discount_code = params[:validated_discount_code]

    if product_keys.empty?
      flash[:error] = 'Please select at least one product.'; redirect_to edit_subscription_path and return
    end
    product_keys.each do |key|
      unless StripeProducts::PRODUCTS.key?(key)
        flash[:error] = 'Invalid product selection.'; redirect_to edit_subscription_path and return
      end
    end

    service = SubscriptionService.new(@subscribable)
    # IMPORTANT: Get the *existing* plan key. It cannot be changed here.
    # SubscriptionService needs a method to provide this.
    existing_plan_key = service.get_current_plan_key_from_active_sub # Placeholder for actual implementation

    unless existing_plan_key
      flash[:error] = 'Could not determine current plan. Update failed.'; redirect_to edit_subscription_path and return
    end

    # These methods need to know not to change plan type from their params if they use them.
    # Pass existing_plan_key so they can correctly assess if PO/Finance is applicable.
    update_finance_contact_if_needed(existing_plan_key)
    update_po_number_if_needed(existing_plan_key)

    if @subscribable.subscriber.present?
      # Use a specific permit method for address params
      if @subscribable.subscriber.update(address_params_for_subscriber)
        service.update_stripe_customer_address if @subscribable.subscriber.previous_changes.slice(*address_params_for_subscriber.keys).any?
      else
        flash[:error] = "Address update failed: #{@subscribable.subscriber.errors.full_messages.join(', ')}"
        TrackingService.track_subscription_event('subscription_update', current_user, {
          message: 'Address update failed',
        })
        redirect_to edit_subscription_path and return
      end
    end

    is_individual_base_plan = (StripeProducts.get_base_size_from_plan_key(existing_plan_key) == :individual)
    # CHANGED: Pass existing_plan_key to the service.
    result = service.update_subscription(product_keys, existing_plan_key, discount_code, invoice_immediately: is_individual_base_plan)

    if result[:subscription] # Assuming service returns :success for clarity
      flash[:success] = 'Your subscription has been updated successfully.'
    else
      flash[:error] = result[:error] || 'There was an issue updating your subscription.' # Use service error if available
    end
    TrackingService.track_subscription_event('subscription_update', current_user, {
      message: flash[:success] || flash[:error],
      stripe_subscription_id: @subscribable.subscriber&.stripe_subscription_id,
      product_keys: product_keys,
      discount_code: discount_code
    })

    redirect_to subscription_path
  rescue ActiveRecord::RecordInvalid => e
    flash[:error] = "Validation error: #{e.message}"
    Rails.logger.error "Update AR error: #{e.message}"
    handle_error_log(e, 'subscription_update')
    redirect_to edit_subscription_path
  rescue ArgumentError => e
    flash[:error] = "Invalid parameters: #{e.message}"
    Rails.logger.error "Update Arg error: #{e.message}"
    handle_error_log(e, 'subscription_update')
    redirect_to edit_subscription_path
  rescue => e
    flash[:error] = "Error: #{e.message}"
    Rails.logger.error "Update error: #{e.message}\n#{e.backtrace.join("\n")}";
    handle_error_log(e, 'subscription_update')
    redirect_to edit_subscription_path
  end

  def show
    capture_posthog_marketing_event('subscription_show')
    # First try to find a personal subscription
    set_subscribable(true) # Force user

    has_personal_subscription = check_for_active_subscription(@subscribable)
    user_has_any_free = @subscribable.subscriber&.has_any_free_subscription?
    school_has_any_free = false

    # If the user does not have a paid subscription or a partial free, check the schools state
    if !has_personal_subscription && current_user.school.present?
      set_subscribable(false) # Use school

      # If they have a school sub OR a partial free school sub
      has_school_subscription = check_for_active_subscription(@subscribable)
      school_has_any_free = @subscribable.subscriber&.has_any_free_subscription?

      # revert to user if there is no school subscription or free partial
      set_subscribable(true) unless has_school_subscription || school_has_any_free
    end

    # after passing school check, check if user or school is set for personal
    @is_personal_subscription = @subscribable.is_a?(User)

    # If there is no subscriber or there is no free partial or sub ID, we can send straight to new
    has_free_partial = user_has_any_free || school_has_any_free
    if @subscribable.subscriber.nil? || (!has_free_partial && @subscribable.subscriber.stripe_subscription_id.nil?)
      TrackingService.track_subscription_event('subscription_show', current_user, {
        message: 'User has no active subscription',
        has_subscriber: @subscribable.subscriber.present?,
        has_stripe_subscription: @subscribable.subscriber&.stripe_subscription_id.present?,
      })
      flash[:notice] = "You don't have an active subscription yet."
      redirect_to new_subscription_path
      return
    end

    # Continue with regular paid subscription logic...
    service = SubscriptionService.new(@subscribable)
    @subscription = service.get_subscription_details

    # If there is no subscription but there is a free partial, default the subscription data values
    # and send to show.
    @has_free_subscriptions = has_free_partial
    if @subscription.nil? && @has_free_subscriptions
      @invoice_history = []
      @open_invoices = []
      @upcoming_invoice = nil
      @cancellation_details = nil
      TrackingService.track_subscription_event('subscription_show', current_user, {
        message: "User has no active subscription but does have free partial",
      })
      render :show
      return
    end

    # If there is no subscription then there was an issue loading it - so error and redirect
    if @subscription.nil?
      flash[:error] = 'There was an error retrieving your subscription. Please try again later.'
      Rails.logger.error "Failed to retrieve subscription #{@subscribable.subscriber.stripe_subscription_id} for subscriber #{@subscribable.subscriber.id}" if @subscribable.subscriber&.stripe_subscription_id
      TrackingService.track_subscription_event('subscription_show', current_user, {
        message: "Subscription was missing but expected, user was redirected to root",
      })
      redirect_to root_path
      return
    end

    # Load details from the subscription
    all_invoices = service.get_all_customer_invoices

    # Get all invoices for this customer
    @invoice_history = all_invoices.select { |inv| %w[open paid].include?(inv.status) }
    # Find any open invoices that need payment
    @open_invoices = @invoice_history.select { |inv| inv.status == 'open' }
    # Get the upcoming invoice
    @upcoming_invoice = all_invoices.find { |inv| inv.billing_reason == 'upcoming' }
    # Get cancellation details if already scheduled
    @cancellation_details = service.get_cancellation_details if @subscription

    TrackingService.track_subscription_event('subscription_show', current_user, {
      message: "Rendered show page with subscription details",
    })
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error in subscription show: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_update')

    flash[:error] = 'There was an error communicating with our payment processor. Please try again later.'
    redirect_to root_path
  rescue => e
    Rails.logger.error "Error in subscription show: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_update')

    flash[:error] = 'An unexpected error occurred. Please try again later or contact support.'
    redirect_to root_path
  end

  def edit
    capture_posthog_marketing_event('subscription_edit')
    # First try to find a personal subscription
    set_subscribable(true) # Force user

    has_personal_subscription = check_for_active_subscription(@subscribable)

    # If no personal subscription and user has a school, check for school subscription
    if !has_personal_subscription && current_user.school.present?
      set_subscribable(false) # Use school

      has_school_subscription = check_for_active_subscription(@subscribable)

      # If no school subscription either, revert to user
      set_subscribable(true) unless has_school_subscription
    end

    if @subscribable.subscriber.nil? || @subscribable.subscriber.stripe_subscription_id.nil?
      flash[:notice] = "You don't have an active subscription to edit."
      TrackingService.track_subscription_event('subscription_edit', current_user, {
        message: 'User has no active subscription to edit',
        has_subscriber: @subscribable.subscriber.present?,
        has_stripe_subscription: @subscribable.subscriber&.stripe_subscription_id.present?,
      })
      redirect_to new_subscription_path
      return
    end

    service = SubscriptionService.new(@subscribable)
    @subscription = service.get_subscription_details

    if @subscription.nil?
      flash[:error] = 'There was an error retrieving your subscription. Please try again later.'
      TrackingService.track_subscription_event('subscription_edit', current_user, {
        message: 'Subscription details could not be retrieved',
        has_subscriber: @subscribable.subscriber.present?,
        has_stripe_subscription: @subscribable.subscriber&.stripe_subscription_id.present?,
      })
      redirect_to subscription_path
      return
    end

    # Check if there is a pending cancellation - don't allow editing in this case
    if @subscription.cancel_at_period_end
      flash[:notice] = 'Your subscription is scheduled for cancellation. Please reactivate it first if you wish to make changes.'
      TrackingService.track_subscription_event('subscription_edit', current_user, {
        message: 'Subscription is scheduled for cancellation',
        has_subscriber: @subscribable.subscriber.present?,
        has_stripe_subscription: @subscribable.subscriber&.stripe_subscription_id.present?,
      })
      redirect_to subscription_path
      return
    end

    # Get address information
    @address = @subscribable.subscriber if @subscribable.subscriber.present?

    # IMPORTANT: SubscriptionService needs a method to reliably get the plan_key
    # (e.g., :individual, :individual_monthly, :small) from the Stripe subscription object.
    current_actual_plan_key = service.get_current_plan_key_from_details(@subscription) 

    unless current_actual_plan_key && StripeProducts::BASE_PRICES.key?(current_actual_plan_key)
      flash[:error] = 'Could not determine current plan details. Cannot edit.'
      Rails.logger.error "EDIT: Failed to determine current_actual_plan_key for subscription #{@subscription.id}, tried with #{current_actual_plan_key}"
      TrackingService.track_subscription_event('subscription_edit', current_user, {
        message: 'Could not determine current plan key',
        has_subscriber: @subscribable.subscriber.present?,
        has_stripe_subscription: @subscribable.subscriber&.stripe_subscription_id.present?,
      })
      redirect_to subscription_path and return
    end

    @current_interval = StripeProducts.is_monthly_plan?(current_actual_plan_key) ? :monthly : :annual

    # Get currently subscribed products
    @current_products = {}
    @current_size = nil

    @subscription.items.data.each do |item|
      next unless item.price&.product
      product_key = find_product_key_by_stripe_product_id(item.price.product)
      @current_products[product_key] = true if product_key

      # Try to determine current size from price nickname
      next unless item.price.nickname
      # Sort the keys by length so that in cases of keys with the same name, it correctly matches
      # e.g large school & very large school
      StripeProducts::SIZES.sort_by { |k, v| -v[:name].length }.each do |size_key, size_info|
        if item.price.nickname.include?(size_info[:name])
          @current_size = size_key
          break
        end
      end
    end

    # Add a flag to indicate if this is a personal or school subscription
    @is_personal_subscription = @subscribable.is_a?(User)

    # Set a flag for the view to know if this is a user with school
    @has_school = current_user.school.present?

    @products = StripeProducts::PRODUCTS
    @sizes = StripeProducts::SIZES

    TrackingService.track_subscription_event('subscription_edit', current_user, {
      message: 'Subscription edit page loaded successfully',
      current_plan_key: current_actual_plan_key,
      current_interval: @current_interval,
      current_products: @current_products,
      current_size: @current_size,
      has_school: @has_school,
      has_free_subscriptions: @subscribable.subscriber&.has_any_free_subscription?,
      is_personal_subscription: @is_personal_subscription,
    })
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error in subscription edit: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_edit')

    flash[:error] = 'There was an error communicating with our payment processor. Please try again later.'
    redirect_to subscription_path
  rescue => e
    Rails.logger.error "Error in subscription edit: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_edit')

    flash[:error] = 'An unexpected error occurred. Please try again later or contact support.'
    redirect_to subscription_path
  end

  # GET /subscriptions/cancel
  def cancel
    capture_posthog_marketing_event('subscription_cancel')
    TrackingService.track_subscription_event('subscription_cancel', current_user)
    # Since cancel is related to an existing subscription, find what subscription the user has
    find_active_subscription

    if @subscribable.subscriber.nil? || @subscribable.subscriber.stripe_subscription_id.nil?
      flash[:error] = 'No active subscription found'
      redirect_to subscription_path
      return
    end

    service = SubscriptionService.new(@subscribable)
    @subscription = service.get_subscription_details

    if @subscription.nil?
      flash[:error] = 'Unable to retrieve subscription details'
      redirect_to subscription_path
      return
    end

    # Get current subscription products and size
    @current_products = []
    @current_size = nil

    @subscription.items.data.each do |item|
      @current_products << item.price.product.name if item.price&.product
    end

    # Add a flag to indicate if this is a personal or school subscription
    @is_personal_subscription = @subscribable.is_a?(User)

    # Get cancellation details if already scheduled
    @cancellation_details = service.get_cancellation_details
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error in subscription cancel page: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_cancel')

    flash[:error] = 'There was an error communicating with our payment processor. Please try again later.'
    redirect_to subscription_path
  rescue => e
    Rails.logger.error "Error in subscription cancel page: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_cancel')

    flash[:error] = 'An unexpected error occurred. Please try again later or contact support.'
    redirect_to subscription_path
  end

  # POST /subscriptions/process_cancellation
  def process_cancellation
    capture_posthog_marketing_event('subscription_process_cancellation')
    begin
      # Since process_cancellation is related to an existing subscription, find what subscription the user has
      find_active_subscription

      if @subscribable.subscriber.nil? || @subscribable.subscriber.stripe_subscription_id.nil?
        flash[:error] = 'No active subscription found'
        TrackingService.track_subscription_event('subscription_cancelled', current_user, {
          message: 'No active subscription found for cancellation',
        })
        redirect_to subscription_path
        return
      end

      # Get cancellation parameters
      cancel_at_period_end = params[:cancel_option] == 'end_of_period'
      cancellation_reason = params[:cancellation_reason]

      service = SubscriptionService.new(@subscribable)

      result = service.cancel_subscription(cancel_at_period_end, cancellation_reason)

      if result
        flash[:success] = if cancel_at_period_end
                            'Your subscription will remain active until the end of the current billing period, after which it will be cancelled.'
                          else
                            'Your subscription has been cancelled immediately. Any applicable refunds will be processed.'
                          end
      else
        flash[:error] = 'There was an error canceling your subscription. Please try again or contact support.'
      end
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error in subscription cancellation: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      handle_error_log(e, 'subscription_process_cancellation')

      error_message = case e.code
                      when 'resource_missing'
                        'The subscription could not be found. It may have already been cancelled.'
                      when 'subscription_already_canceled'
                        'This subscription has already been cancelled.'
                      else
                        "Error canceling subscription: #{e.message}"
                      end

      flash[:error] = error_message
    rescue => e
      Rails.logger.error "Error in subscription cancellation: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      handle_error_log(e, 'subscription_process_cancellation')

      flash[:error] = 'An unexpected error occurred while canceling your subscription. Please try again later or contact support.'
    end
    TrackingService.track_subscription_event('subscription_cancelled', current_user, {
      message: flash[:success] || flash[:error],
      cancel_at_period_end: cancel_at_period_end,
      cancellation_reason: cancellation_reason,
      stripe_subscription_id: @subscribable.subscriber&.stripe_subscription_id
    })
    redirect_to subscription_path
  end

  # POST /subscriptions/reactivate
  def reactivate
    capture_posthog_marketing_event('subscription_reactivate')
    begin
      # Find active subscription
      find_active_subscription

      if @subscribable.subscriber.nil? || @subscribable.subscriber.stripe_subscription_id.nil?
        flash[:error] = 'No subscription found to reactivate'
        TrackingService.track_subscription_event('subscription_reactivate', current_user, {
          message: 'No active subscription found for reactivation',
        })
        redirect_to subscription_path
        return
      end

      service = SubscriptionService.new(@subscribable)

      result = service.reactivate_subscription

      if result
        flash[:success] = 'Your subscription has been reactivated successfully.'
      else
        flash[:error] = 'There was an error reactivating your subscription. Please try again or contact support.'
      end
    rescue Stripe::StripeError => e
      Rails.logger.error "Stripe error in subscription reactivation: #{e.message}"
      Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
      Rails.logger.error e.backtrace.join("\n")
      handle_error_log(e, 'subscription_reactivate')

      error_message = case e.code
                      when 'resource_missing'
                        'The subscription could not be found. It may have already been cancelled.'
                      when 'subscription_already_canceled'
                        'This subscription has already been cancelled and cannot be reactivated.'
                      else
                        "Error reactivating subscription: #{e.message}"
                      end

      flash[:error] = error_message
    rescue => e
      Rails.logger.error "Error in subscription reactivation: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      handle_error_log(e, 'subscription_reactivate')

      flash[:error] = 'An unexpected error occurred while reactivating your subscription. Please try again later or contact support.'
    end

    TrackingService.track_subscription_event('subscription_reactivate', current_user, {
      message: flash[:success] || flash[:error],
      stripe_subscription_id: @subscribable.subscriber&.stripe_subscription_id
    })

    redirect_to subscription_path
  end

  # GET /subscription/customer_portal
  def customer_portal
    capture_posthog_marketing_event('subscription_customer_portal')
    # Find the active subscription
    find_active_subscription

    if @subscribable.subscriber.nil? || @subscribable.subscriber.stripe_customer_id.nil?
      flash[:notice] = "You don't have an active subscription yet."
      redirect_to new_subscription_path
      return
    end

    service = SubscriptionService.new(@subscribable)

    # Create a return URL to this application after managing billing
    return_url = subscription_url

    # Create a Stripe Customer Portal session
    result = service.create_customer_portal_session(return_url)

    if result[:success]
      # Redirect to the Stripe Customer Portal
      redirect_to result[:url], allow_other_host: true
    else
      flash[:error] = "Unable to access the billing portal: #{result[:error]}"
      redirect_to subscription_path
    end
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error creating customer portal session: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_customer_portal')

    flash[:error] = 'Unable to access the billing portal. Please try again later or contact support.'
    redirect_to subscription_path
  rescue => e
    Rails.logger.error "Error in customer portal redirection: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_customer_portal')

    flash[:error] = 'An unexpected error occurred while accessing the billing portal. Please try again later.'
    redirect_to subscription_path
  end

  def validate_discount
    capture_posthog_marketing_event('subscription_validate_discount')
    discount_code = params[:discount_code]

    if discount_code.blank?
      render json: { valid: false, message: 'Please enter a discount code' }
      return
    end

    # Find the active subscription
    find_active_subscription

    # Validate the discount code
    discount_service = DiscountService.new(@subscribable)
    result = discount_service.validate_discount_code(discount_code)

    render json: result
  rescue ActiveRecord::RecordNotFound => e
    Rails.logger.error "Database error in discount validation: #{e.message}"
    render json: { valid: false, message: 'An error occurred while validating the discount code' }
  rescue => e
    Rails.logger.error "Error in discount code validation: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    handle_error_log(e, 'subscription_validate_discount')
    render json: { valid: false, message: 'An unexpected error occurred. Please try again later.' }
  end

  def update_po_number
    begin
      po_number = params[:po_number]

      # Find the active subscription
      find_active_subscription

      if @subscribable.subscriber.nil?
        flash[:error] = 'No subscription found.'
        redirect_to subscription_path
        return
      end

      @subscribable.subscriber.update(po_number: po_number)

      service = SubscriptionService.new(@subscribable)

      if service.update_po_number(po_number)
        flash[:success] = 'Purchase Order number has been updated successfully.'
      else
        flash[:error] = 'Unable to update Purchase Order number. Please try again.'
      end
    rescue => e
      Rails.logger.error "Error updating PO number: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      handle_error_log(e, 'subscription_update_po_number')
      flash[:error] = 'An error occurred while updating the Purchase Order number.'
    end

    redirect_to subscription_path
  end

  private

  def check_stripe_configuration
    unless Rails.configuration.stripe[:publishable_key].present? &&
           Rails.configuration.stripe[:secret_key].present?
      Rails.logger.error 'Stripe configuration missing: publishable_key or secret_key not set'
      flash[:error] = 'Payment system is not properly configured. Please contact support.'
      redirect_to root_path
    end
  end

  def find_active_subscription
    # First try to find a personal subscription
    set_subscribable(true) # Force user
    has_personal_subscription = check_for_active_subscription(@subscribable)

    # If no personal subscription and user has a school, check for school subscription
    if !has_personal_subscription && current_user.school.present?
      set_subscribable(false) # Use school
      has_school_subscription = check_for_active_subscription(@subscribable)

      # If no school subscription either, revert to user
      set_subscribable(true) unless has_school_subscription
    end
  rescue => e
    Rails.logger.error "Error finding active subscription: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    # Set default to user as fallback
    set_subscribable(true)
  end

  def check_for_active_subscription(subscribable)
    return false unless subscribable && subscribable.subscriber && subscribable.subscriber.stripe_subscription_id.present?

    service = SubscriptionService.new(subscribable)
    subscription = service.get_subscription_details

    subscription && %w[active active_until_period_end past_due].include?(subscription.status)
  rescue Stripe::StripeError => e
    Rails.logger.error "Stripe error checking subscription status: #{e.message}"
    Rails.logger.error "Error type: #{e.class.name}, Stripe error code: #{e.code}"
    # Return false as a safe default if we can't verify
    false
  rescue => e
    Rails.logger.error "Error checking subscription status: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    # Return false as a safe default if we can't verify
    false
  end

  def determine_appropriate_size
    # For individual users (no school), default to individual subscription
    return :individual if @subscribable.is_a?(User) && @subscribable.school.nil?

    # For school users, determine size based on number of pupils if available
    if @subscribable.is_a?(School) || (@subscribable.is_a?(User) && @subscribable.school.present?)
      school = @subscribable.is_a?(School) ? @subscribable : @subscribable.school

      # Check if school is linked to a uk_school with pupil data
      if school.respond_to?(:uk_school) && school.uk_school.present? && school.uk_school.respond_to?(:number_of_pupils)
        pupils = school.uk_school.number_of_pupils

        # Select size tier based on number of pupils
        if pupils.present? && pupils > 0
          # Iterate through SIZES in order, assuming they are ordered by max_users ascending
          # Skip :individual as it's handled separately or not applicable for schools here
          StripeProducts::SIZES.each do |size_key, size_info|
            next if size_key == :individual # Skip individual size for school logic
            # If max_users is nil, it's the largest tier (e.g., very_large)
            return size_key if size_info[:max_users].nil? || pupils <= size_info[:max_users]
          end
          # Fallback to the largest size if pupils exceed all defined max_users
          # This assumes the last non-nil max_users tier is the one before "very_large" or equivalent
          # Or, if all have max_users, and it's larger than all, it should ideally pick the largest.
          largest_school_size = StripeProducts::SIZES.select { |_, info| info[:max_users].nil? }.keys.first
          return largest_school_size if largest_school_size
        end
      end
    end

    @using_default_size = true
    # Default to small if we couldn't determine the appropriate size or pupils is 0 or not present
    :small
  rescue => e
    Rails.logger.error "Error determining appropriate size: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    # Default to small as a safe fallback
    :small
  end

  def update_po_number_if_needed(final_plan_key = nil)
    return unless @subscribable.subscriber.present? && params.key?(:po_number) # Check if po_number was submitted

    is_school_plan = final_plan_key ? (StripeProducts.get_base_size_from_plan_key(final_plan_key) != :individual) : @subscribable.is_a?(School)
    po_val_from_params = params[:po_number]

    if !is_school_plan # Personal plan
      # Clear PO if it's a personal plan and PO was somehow set
      if @subscribable.subscriber.po_number.present?
        @subscribable.subscriber.update(po_number: nil)
        # service.update_po_number('') # If Stripe needs update
      end
    elsif is_school_plan # School plan
      # Update PO number only if it has changed
      if @subscribable.subscriber.po_number != po_val_from_params
        @subscribable.subscriber.update(po_number: po_val_from_params)
        # Ensure Stripe is updated immediately if local save is valid and PO changed
        SubscriptionService.new(@subscribable).update_po_number(po_val_from_params) if @subscribable.subscriber.valid?
      end
    end
  end

  def update_finance_contact_if_needed(final_plan_key = nil) # Make plan_key optional for calls from update where it might be complex to get
    is_school_plan = final_plan_key ? (StripeProducts.get_base_size_from_plan_key(final_plan_key) != :individual) : @subscribable.is_a?(School)
    return unless @subscribable.is_a?(School) && is_school_plan

    if params[:finance_name].present? || params[:finance_email].present?
      @subscribable.update(
        finance_name: params[:finance_name],
        finance_email: params[:finance_email]
      )
    end
  end

  def render_new_subscription
    @products = StripeProducts::PRODUCTS
    @sizes = StripeProducts::SIZES

    # Preselect Science product and Small School size
    @current_products = { science: true }
    @current_size = :small

    render :new
  end

  def set_subscribable(force_user = false)
    @subscribable = if current_user.school.present? && !force_user
                      current_user.school
                    else
                      current_user
                    end
  end

  def set_current_user
    @current_user = current_user
  end

  def determine_final_plan_key(size_param, interval_param)
    return nil unless size_param # Base size must be present

    if size_param == :individual
      # For individual, interval_param determines if it's monthly or annual. Default to annual if interval is not 'monthly'.
      interval_param == :monthly ? :individual_monthly : :individual
    else
      # For school sizes (:small, :medium, etc.), interval is implicitly annual.
      # Ensure it's a valid school size defined in StripeProducts::BASE_PRICES.
      StripeProducts::BASE_PRICES.key?(size_param) ? size_param : nil
    end
  end

  def re_render_new_for_create_errors(product_keys, size_param, interval_param)
    @current_products = product_keys.index_with { |_k| true } if product_keys.present?
    @current_size = size_param
    @current_interval = interval_param if size_param == :individual # Preserve interval if individual size was selected
    @has_school = current_user.school.present?
    @products = StripeProducts::PRODUCTS
    @sizes = StripeProducts::SIZES # For the primary size selector
    # Ensure @address is repopulated from params if available, or prefilled
    @address = OpenStruct.new(params.permit(:address_line1, :address_line2, :city, :state, :postal_code, :country)) if params[:address_line1].present?
    @address ||= prefill_address_for_new_subscription(@current_size || determine_appropriate_size) # Original prefill logic
    render :new, status: :unprocessable_entity
  end

  def address_params_for_subscriber
    params.permit(:address_line1, :address_line2, :city, :state, :postal_code, :country)
  end

  def find_product_key_by_stripe_product_id(stripe_product_obj_or_id)
    return nil unless stripe_product_obj_or_id
    # Handles both Stripe::Product object and string ID
    stripe_id = stripe_product_obj_or_id.is_a?(String) ? stripe_product_obj_or_id : stripe_product_obj_or_id.id

    # This assumes you have a way to map Stripe Product IDs back to your internal keys.
    # Using StripeProductPrice with size 'base' is one convention.
    record = StripeProductPrice.find_by(live_product_id: stripe_id, size: 'base') ||
             StripeProductPrice.find_by(test_product_id: stripe_id, size: 'base')
    record&.product&.to_sym
  end

  def prefill_address_for_new_subscription(current_selected_size_for_prefill)
    return nil unless current_user.school.present? && current_selected_size_for_prefill != :individual

    school = current_user.school
    OpenStruct.new(
      address_line1: school.default_billing_address_line_1,
      address_line2: school.default_billing_address_line_2,
      city: school.default_billing_city,
      state: school.default_billing_state,
      postal_code: school.default_billing_postal_code,
      country: school.default_billing_country&.machine_name || 'GB'
    )
  end

  def handle_error_log(error, location)
    log_error(error, "SubscriptionsController##{location}", current_user)
  end
end
