module Static
  class LessonTemplatesController < StaticController
    layout :dynamic_layout
    before_action :set_template, only: %i[show lesson_plan rocket_word_quiz assessment_quiz word_search json]
    before_action :set_related_lessons, only: %i[show]

    def show
      if @current_user.present? && @template_versions.present? && params['no-redirect'] != '1'
        default_template = @template_versions.detect { |t| t[:is_default] && t[:is_available] }
        return redirect_to static_missions_path(id: default_template[:id]) if default_template && default_template[:id] != @lesson_template.id
      end

      @videos = []
      if restricted_view
        @videos << { name: 'Expert film' }
        @videos << { name: 'Mission assignment film' }
      else
        slide = @lesson_template.slides.find_by(slide_type: 'expert')
        if slide&.video_url || slide&.fileboy_video_id || slide&.video_id
          @videos << {
            name: 'Expert film',
            fileboy_video_id: slide.fileboy_video_id,
            video_url: slide.video_url,
            video_id: slide.video_id,
            slide_id: slide.id,
            type: 'expert',
          }
        end

        slide = @lesson_template.slides.find_by(slide_type: 'mission_assignment')
        if slide&.video_url || slide&.fileboy_video_id || slide&.video_id
          @videos << {
            name: 'Mission assignment film',
            fileboy_video_id: slide.fileboy_video_id,
            video_url: slide.video_url,
            video_id: slide.video_id,
            slide_id: slide.id,
            type: 'mission_assignment',
          }
        end
      end

      @recommended_careers = @lesson_template.recommended_careers
      if @recommended_careers.empty?
        @recommended_careers = custom_cache.fetch("recommended_careers_#{@lesson_template.id}", expires_in: 7.days) do
          @lesson_template.generate_recommended_careers
        end
      end

      TrackingService.track_lesson_template_view(@current_user, @lesson_template)

      return render :show_available unless restricted_view
      render :show_restricted
    end

    def lesson
      @lesson = Lesson::Lesson.accessible_by(current_ability).find(params[:id])
      @template = @lesson.template
    end

    def school_lesson_show
      @lesson = Lesson::Lesson.accessible_by(current_ability).find(params[:id])
      @lesson_template = @lesson.template
      @template_versions = nil
      show
    end

    def rocket_word_quiz
      @quiz = @lesson_template.rocket_word_quiz
      @submit_path = rocket_word_quizzes_path(template_id: @lesson_template.id, lesson_id: nil, user_id: @current_user&.id)

      @page_title = 'Rocket Word Quiz'
      render 'static/lesson_templates/quiz'
    end

    def assessment_quiz
      unless @lesson_template.quip_quiz.present?
        return redirect_to static_missions_path(@lesson_template, "no-redirect": 1)
      end

      begin
        quiz_record = @lesson_template.quip_quiz
        @quiz = quiz_record.format_for_static

        TrackingService.track_quiz_view(quiz_record.id.to_s, @current_user, {
          quiz_type: 'DE Quiz',
          lesson_template_id: @lesson_template.id,
        })

        @submit_path = template_quizzes_path(template_id: @lesson_template.id, lesson_id: nil, user_id: @current_user&.id)
        @page_title = 'Assessment Quiz'
        render 'static/lesson_templates/quiz'
      rescue => e
        Rails.logger.error("Error fetching quiz: #{e.message}\n#{e.backtrace.join("\n")}")
        redirect_to static_missions_path(id: @lesson_template.id)
      end
    end

    def word_search
      @user_id = @current_user&.id
      @template = @lesson_template
      @lesson_id = nil

      @words = @template.keywords.pluck(:name)
      @best_times = {
        overall: @template.best_word_search_result,
        personal: @current_user.present? ? @current_user.word_search_results.where(lesson_template_id: @template.id).minimum(:time_taken) : nil,
      }

      TrackingService.track_word_search_view(@template, @current_user, { lesson_id: @lesson_id })

      render 'static_game/lesson_template_word_search'
    end

    def lesson_plan
      @assessment_fields = [
        {
          assessment: 'Questions to Ask During the Lesson',
          value: @lesson_template.plan.assessment_questions,
        },
        { assessment: 'Mark Allocation', value: @lesson_template.plan.assessment_marks },
        {
          assessment: 'Choral Response Questions/ Phrase Suggestions',
          value: @lesson_template.plan.assessment_phrases,
        },
        { assessment: 'Teacher Mastery', value: @lesson_template.teacher_mastery },
      ].select { |x| x[:value].present? }

      @curriculum_fields = [
        { curriculum: 'National Curriculum', value: @lesson_template.plan.national_curriculum },
        {
          curriculum: 'Curriculum Of Excellence',
          value: @lesson_template.plan.curriculum_of_excellence,
        },
        {
          curriculum: 'International Baccalaureate',
          value: @lesson_template.plan.international_baccalaureate,
        },
        { curriculum: 'Early Years Framework', value: @lesson_template.plan.early_years_framework },
        {
          curriculum: 'Enquiry Skills and Approaches',
          value: @lesson_template.plan.scientific_enquiry_type,
        },
        {
          curriculum: 'Working Scientifically Skills',
          value: @lesson_template.plan.working_scientifically_skills,
        },
        {
          curriculum: 'Century Skills For Life',
          value: @lesson_template.plan.century_skills_for_life,
        },
        {
          curriculum: 'CrossCurriculum Opportunities',
          value: @lesson_template.plan.cross_curriculum_opportunities,
        },
        { curriculum: 'CBSE', value: @lesson_template.plan.cbse },
        {
          curriculum: 'Kingdom Of Saudi Arabia',
          value: @lesson_template.plan.kingdom_of_saudi_arabia,
        },
        {
          curriculum: 'Chinese Compulsory Education Primary School Science',
          value: @lesson_template.plan.chinese_compulsory_education_primary_school_science,
        },
      ].select { |x| x[:value].present? }

      TrackingService.track_lesson_plan_view(current_user, @lesson_template) if current_user.present?
    end

    def json
      render :json => @lesson_template.json_data
    end

    def ai_query
      @lesson_template = Lesson::Template.accessible_by(current_ability).find(params[:id])
      query = params[:query]&.strip
      
      if query.blank?
        return render json: { 
          success: false, 
          error: "Please provide a question about the lesson." 
        }, status: 400
      end

      begin
        query_service = LessonTemplateQueryService.new(@lesson_template)
        response = query_service.query(query)
        
        render json: { 
          success: true, 
          response: response,
          query: query 
        }
      rescue => e
        Rails.logger.error("AI Query Error: #{e.message}\n#{e.backtrace.join("\n")}")
        
        error_message = case e
        when AiClient::ApiError
          "I'm having trouble processing your question right now. Please try again in a moment."
        when AiClient::ParseError
          "I had trouble understanding the response. Please try rephrasing your question."
        when AiClient::ValidationError
          "There was an issue with your question. Please check and try again."
        else
          "Something went wrong while processing your question. Please try again."
        end
        
        render json: { 
          success: false, 
          error: error_message 
        }, status: 500
      end
    end

    private

    def set_related_lessons
      original_template = @lesson_template.source_template || @lesson_template
      user_templates = original_template.user_templates.includes([:user]).accessible_by(current_ability)

      @template_versions = []
      @original_is_default = true
      if @current_user.present?
        @template_versions << {
          id: original_template.id,
          name: original_template.name,
          user: { name: 'Developing Experts' },
          is_default: user_templates.where(is_default: true).none?,
          is_source: true,
          is_available: original_template.available,
        }

        user_templates.map do |user_template|
          @template_versions << {
            id: user_template.id,
            name: user_template.name,
            user: user_template.user ? { name: user_template.user.name } : nil,
            is_default: user_template.is_default,
            is_source: false,
            is_available: user_template.available,
          }
        end
        @original_is_default = !@template_versions.detect { |t| t[:is_default] && !t[:is_source] } && @lesson_template.id == @template_versions.detect { |t| t[:is_source] }&.dig(:id)
      end
    end

    def set_template
      @lesson_template = Lesson::Template.accessible_by(current_ability).find(params[:id])
      unit_from_params = params[:unit_id]
      curriculum = current_user&.school&.new_library_curriculum

      set_return_path(@lesson_template, curriculum, unit_from_params)
    end

    def set_return_path(lesson_template, curriculum, unit_from_params)
      # - if only one unit, use that
      @return_path = { label: '', to: '' }

      template_or_source_template = lesson_template.source_template || lesson_template

      unit = unit_from_params ? NewLibrary::Unit.find_by(id: unit_from_params) : nil

      puts "UNIT FROM PARAMS: #{unit_from_params}, UNIT: #{unit.inspect}"
      puts "UNIT FROM PARAMS: #{unit_from_params}, UNIT: #{unit.inspect}"
      puts "UNIT FROM PARAMS: #{unit_from_params}, UNIT: #{unit.inspect}"
      puts "UNIT FROM PARAMS: #{unit_from_params}, UNIT: #{unit.inspect}"

      if template_or_source_template.new_library_units.count == 1
        unit ||= template_or_source_template.new_library_units.first
        @return_path[:label] = unit.name
        @return_path[:to] = "/unit-library/units/#{unit.id}"
        @curriculum = unit.year&.curriculum
        return
      end

      # - If more than one then your schools curriculums unit (if it has one)
      unit ||= curriculum ? template_or_source_template.new_library_units.joins(year: :curriculum).where(year: { new_library_curricula: { id: curriculum.id } }).first : nil
      if unit.present?
        @return_path[:label] = unit.name
        @return_path[:to] = "/unit-library/units/#{unit.id}"
        @curriculum = unit.year&.curriculum
        return
      end

      if template_or_source_template.new_library_units.empty?
        @return_path = nil
        return
      end

      # - If not then just to the first
      unit ||= template_or_source_template.new_library_units.first
      @return_path[:label] = unit.name
      @return_path[:to] = "/unit-library/units/#{unit.id}"
      @curriculum = unit.year&.curriculum

      # - If nothing to link to, then clear the value so it can check for presence.
      @return_path = nil unless @return_path[:to].present?
    end

    def restricted_view
      # if there is no user & it's demo or anon then it's a "free preview"
      return false if !@current_user && (@lesson_template.demo || @lesson_template.anonymous)

      # if there is no user, then its restricted
      return true unless @current_user.present?

      # check user access
      !@current_user.has_full_access_to_template?(@lesson_template)
    end

    def dynamic_layout
      return 'school' if current_user&.type == 'Teacher'
      'static'
    end
  end
end
