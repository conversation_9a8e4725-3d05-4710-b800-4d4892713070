module Static
  class SessionAccountsController < StaticApplicationController
    include ApplicationHelper
    include BreadcrumbsHelper

    layout 'session_accounts'
    before_action :region_check, only: %i[new user user_submit school school_validate school_details create home international]
    before_action :set_base_breadcrumbs, only: %i[school_details]

    def accounts_sign_in
      if params[:type].present? && params[:identifier].present? && params[:school].present? && params[:handle] == 'qr'
        identifier = params[:identifier]
        school_id = params[:school]

        school = School.find_by(id: school_id)
        return unless school
        pupil = school.pupils.find_by(identifier: identifier) if school.present?
        return unless pupil

        sign_in(pupil, skip_authentication: true)
        return redirect_to '/accounts/dashboard'
      end

      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?

      # Fetch schools with unrestricted HubSpot subscription status
      unrestricted_school_ids = School.generic
          .where.not(hubspot_subscription_status: School::HS_SUBSCRIPTION_STATUS[:restricted])
          .pluck(:id)

      # Fetch schools with active or free Stripe subscriptions
      stripe_subscribed_school_ids = School.generic
          .joins(teachers: :stripe_subscriptions)
          .where("stripe_subscriptions.free_subscription OR
                  (stripe_cache IS NOT NULL AND stripe_cache->>'status' = 'active')")
          .pluck(:id)

      school_ids = (unrestricted_school_ids + stripe_subscribed_school_ids).uniq
      @schools = School.where(id: school_ids).pluck(:id, :name, :postcode)
    end

    def accounts_sign_in_submit
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?

      @type = params[:type] # pupil | teacher

      handle_error = Proc.new do |key, message|
        @schools = School.generic.pluck(:id, :name, :postcode)
        flash[key] = message
        render 'accounts_sign_in'
        return
      end

      return handle_error.call(:error, 'There was a problem signing you in') if @type != 'pupil' && @type != 'teacher'
      begin
        if @type == 'pupil'
          @school_id = params[:school_id]
          @pupil_code = params[:pupil_code]
          return handle_error.call(:pupil_error, 'Missing school or pupil code') unless @school_id.present? && @pupil_code.present?

          user = Pupil.find_by(identifier: @pupil_code, school_id: @school_id)
          return handle_error.call(:pupil_error, 'Invalid school or pupil code') unless user.present?

          sign_in(user, skip_authentication: true)
          return handle_error.call(:pupil_error, 'Invalid school or pupil code') unless user == current_user
        elsif @type == 'teacher'
          @email = params[:email]
          password = params[:password]
          return handle_error.call(:teacher_error, 'Missing email or password') unless @email.present? && password.present?

          user = User.find_by(uid: @email.downcase, provider: 'email')
          return handle_error.call(:teacher_error, 'Invalid email or password') unless user.present?
          return handle_error.call(:teacher_error, 'This account has been deactivated. Please contact support.') if user.is_blocked

          if user.require_password_reset
            send_password_reset_for_required(user)
            return handle_error.call(:teacher_error, "Password reset is required")
          end

          sign_in(user, password)
          return handle_error.call(:teacher_error, 'Invalid email or password') unless user == current_user
        end
        if params[:ai].present? && @type == 'teacher'
          redirect_to subscriptions_user_stripe_subscriptions_path(signup_redirect: 1)
        else
          if session[:user_return_to]
            redirect_path = session[:user_return_to]
            session[:user_return_to] = nil
            redirect_to redirect_path
          else
            redirect_to universal_dashboard_link(user)
          end
        end
      rescue => e
        Rails.logger.error(e)
        if e.is_a? BCrypt::Errors::InvalidHash
          handle_error.call(:error, 'Your password needs to be updated for you to sign in. Please do so by clicking the "Forgot your password" link below.')
        else
          handle_error.call(:error, 'There was a problem signing you in')
        end
      end
    end

    def custom_signup_url
      url = URI.parse(request.url).path.gsub(%r{^/sign-up/}, '')
      record = CustomSignUpUrl.find_by(url: url, published: true)
      record.custom_signup_url_views.create(user: current_user) if record.present?
      redirect_to("/accounts/new#{record ? "?suid=#{record&.id}" : ''}")
    end

    def new_accounts
      redirect_to '/accounts/dashboard' if current_user.present? && !params[:oid]
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
    end

    def new
      track_signup_url_view(params, current_user) if params[:suid].present?
      if current_user.present? && !params[:oid]
        redirect_to '/accounts/dashboard' 
        return
      end
      render layout: 'static'
    end

    # GET /accounts/new/user
    def user
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      @user = IndividualUser.new

      # Pre-fill email from referral invitation
      if session[:invited_email].present?
        @user.email = session[:invited_email]
      end
    end

    # POST /accounts/new/user
    def user_submit
      data = user_form_params
      name = [data[:first_name], data[:last_name]].join(' ')
      @user = IndividualUser.new({
                                   name: name,
                                   email: data[:email],
                                   password: data[:password],
                                   job_title: data[:job_title],
                                   phone: data[:phone],
                                 })

      if @user.save
        sign_in(@user, skip_authentication: true)

        # Complete user referral if present
        complete_user_referral(@user) if session[:user_referral_token].present?
        referral_code = params.require(:individual_user).permit(:referral_code)[:referral_code]
        if referral_code.present? && !session[:user_referral_token].present? && !session[:school_invite_token].present?
          register_user_referral(@user, referral_code)
        end

        track_signup_url(params, @user)
        if params[:ai].present?
          redirect_to subscriptions_user_stripe_subscriptions_path(signup_redirect: 1)
        else
          redirect_to '/accounts/dashboard', notice: 'Account created.', suid: params[:suid], ai: params[:ai]
        end
      else
        render 'static/session_accounts/user', error: 'There was a problem creating your account', suid: params[:suid], ai: params[:ai]
      end
    end

    # GET /accounts/new/school
    def school
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      @school = School.new
    end

    # POST /accounts/new/school
    def school_validate
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
    
      # Load the UK School by ID from form params1
      school_params = params.require(:school).permit(:uk_school_id)
      uk_school = school_params[:uk_school_id].present? ? UkSchool.find_by_id(school_params[:uk_school_id]) : nil

      @school = School.new
      unless uk_school.present?
        @school.errors.add(:uk_school_id, 'Please select a school')
        puts @school&.errors&.messages
        @uk_schools = UkSchool.pluck(:id, :name, :postcode)
        render 'static/session_accounts/school'
        return
      end
      @school.uk_school_id = uk_school.id

      # find associated schools to the uk school
      @conflict_school = School.find_by_uk_school_id(uk_school.id)

      # validate the uk school
      # to re-use the conflicted school, the uk school must only have 1 associated school,
      # and that school must not be subscribed or have had recent activity
      if @conflict_school && (uk_school.schools.count > 1 || @conflict_school.subscribed? || @conflict_school.has_had_recent_activity?)
        @school.errors.add(:uk_school_id, 'School already in use')
        # update the conflicted count
        uk_school&.update(conflict_sign_ups_count: uk_school.conflict_sign_ups_count + 1)
        # return back to show the conflict modal
        @uk_schools = UkSchool.pluck(:id, :name, :postcode)
        render 'static/session_accounts/school'
        return
      end

      # is a valid school :thumbsup:
      redirect_to accounts_new_school_details_url(oid: @org&.id, uk_school_id: uk_school.id, suid: params[:suid], ai: params[:ai])
    end

    # MARK: POST invite
    # POST /accounts/new/request_school_invite
    def request_school_invite
      @uk_schools = UkSchool.all

      school_id = params.require(:school_id)
      name = params.require(:name)
      email = params.require(:email)
      school = School.generic.find(school_id)

      return unless school.main_teacher&.is_school_admin

      SchoolMailer.school_invite_request(name, email, school).deliver_now

      @school = School.new
      @enquiry_submitted = true

      redirect_to accounts_new_school_url(oid: @org&.id, enquiry_submitted: true, suid: params[:suid], ai: params[:ai])
    end

    # MARK: GET School Details
    # GET /accounts/news/school_details
    def school_details
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      uk_school_id = params[:uk_school_id]
      @school = School.new(uk_school_id: uk_school_id, country: Country.find(1))
      uk_school = UkSchool.find_by(id: uk_school_id)
      @user = @school.teachers.new

      # Pre-fill email from school invitation
      if session[:invited_email].present?
        @user.email = session[:invited_email]
      end
      @countries = Country.all

      add_breadcrumb "New School", accounts_new_school_path
      add_breadcrumb uk_school ? uk_school.name : 'Complete form', :accounts_new_school_details
    end

    # MARK: POST Create
    # POST /accounts/news/school_details
    def create
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      @school = School.new(actual_country_id: 1)

      @user = Teacher.new(
        name: "#{user_params[:first_name]} #{user_params[:last_name]}",
        email: user_params[:email],
        password: user_params[:password],
        job_title: user_params[:job_title],
        phone: user_params[:phone],
        is_school_admin: true,
        type: 'Teacher',
        science_lead: true
      )

      # validate country select list fields
      %i[country category lead_source].each do |field|
        @school.errors.add(field, 'is required') if all_params[field].blank?
      end

      # validate sign up event if lead source is tradeshow
      @school.errors.add(:sign_up_event_id, 'is required') if school_params[:lead_source] == 'tradeshow' && !school_params[:sign_up_event_id].present?
      # validate lead source other if lead source is other
      @school.errors.add(:lead_source_other, 'is required') if school_params[:lead_source] == 'other' && !all_params[:lead_source_other].present?

      lead_source = if school_params[:lead_source] == 'other'
                      all_params[:lead_source_other]
                    else
                      school_params[:lead_source]
                    end

      # UK school conflict handling
      # while we validated this in the previous step, we need to re-validate here incase someone
      # messed with the passed id - also need to run the clear content logics etc

      uk_school_id = school_params[:uk_school_id]
      uk_school = uk_school_id.present? ? UkSchool.find_by_id(uk_school_id) : nil
      unless uk_school.present?
        puts 'UK SCHOOL not found'
        redirect_to accounts_new_school_url(oid: @org&.id, suid: params[:suid], ai: params[:ai]) and return
      end

      # find assocated schools to the uk school
      conflict_school = School.find_by_uk_school_id(uk_school_id)

      # validate the uk school
      # to re-use the conflicted school, the uk school must only have 1 associated school,
      # and that school must not be subscribed or have had recent activity
      if conflict_school && (uk_school.schools.count > 1 || conflict_school.subscribed? || conflict_school.has_had_recent_activity?)
        # We shouldn't hit this assuming that nothing was changed ~ if we do, then the uk school
        # id was changed so throw error and yeet them back to the school page
        puts 'UK SCHOOL conflict'
        redirect_to accounts_new_school_url(oid: @org&.id, suid: params[:suid], ai: params[:ai]) and return
      end

      if conflict_school.present?
        # clear the content as we need to re-use this school
        conflict_school.clear_content
        # move the errors from @school on to the conflict school
        conflict_school.errors.merge!(@school.errors)
        # assign the conflict school to @school
        @school = conflict_school

        # resetting school attributes
        @school.assign_attributes(
          social: nil,
          wonde_id: nil,
          trial_lessons_started: false,
          trial_handouts_viewed: false,
          trial_videos_watched: false,
          trial_library_browsed: false,
          onboarding_teacher_data_stage: false,
          onboarding_teacher_communicated: false,
          onboarding_pupil_data_stage: false,
          onboarding_parent_data_stage: false,
          setup_checklist: School.default_checklist,
          subscription_start_date: nil,
          finance_email: nil,
          finance_name: nil,
          po_number: nil,
          hubspot_renewal_date: nil,
          renewal_month: 0,
          created_at: DateTime.now
        )
      end

      # assigning school attributes from UK school data for new school
      @school.assign_attributes(school_params)
      
      new_library_curriculum_id = 18 
      country_id = all_params[:country]
      if (country_id != "1") 
        new_library_curriculum_id = NewLibrary::Curriculum.where(country_id: country_id).first&.id
      end

      @school.assign_attributes(
        lead_source: lead_source,
        school_type: :generic,
        independent: true,
        creation_email: user_params[:email],
        name: uk_school.name,
        region: uk_school&.region.present? && School.regions.keys.include?(uk_school.region) ? uk_school.region : user_params[:region],
        postcode: uk_school.postcode,
        created_by_email: user_params[:email],
        organisation_id: all_params[:organisation_id] || nil,
        country_id: all_params[:country],
        actual_country_id: all_params[:actual_country],
        new_library_curriculum_id: new_library_curriculum_id,
      )

      # handle any errors
      if @school.errors.any?
        puts @school.errors.messages
        @school.lead_source = school_params[:lead_source] # reset the lead source so the select on front works
        @lead_source_other = all_params[:lead_source_other]
        render 'static/session_accounts/school_details' and return
      end

      @user.assign_attributes(
        school: @school,
        form_ids: @school.form_ids,
        created_schools: [@school]
      )

      # save and complete sign up
      ActiveRecord::Base.transaction do
        @user.validate!
        @school.save!
        @user.save!
        @school.reload
        # Not sure why, @school loses its id after we create a form, causing the pupil create to fail
        # settings this to a variable seenms to resolve...
        school = @school
        @user.reload

        form = school.forms.create!(name: 'Example Class', school: school)
        form.teachers << @user
        pupil = school.pupils.create!(name: 'Example Pupil')
        form.pupils << pupil

        sign_in(@user, skip_authentication: true)

        # Complete school invite if present
        complete_school_invite(@user) if session[:school_invite_token].present?
        # Complete user referral if present (for referrals that chose school account)
        complete_user_referral(@user) if session[:user_referral_token].present?
        if all_params[:referral_code].present? && !session[:user_referral_token].present? && !session[:school_invite_token].present?
          register_user_referral(@user, all_params[:referral_code])
        end
        
        if params[:ai].present?
          redirect_to subscriptions_user_stripe_subscriptions_path(signup_redirect: 1)
        else
          redirect_to '/accounts/dashboard', notice: 'Account created.', suid: params[:suid], ai: params[:ai]
        end

        begin
          # trackings and events
          track_signup_url(params, @user)
          @user.build_referral_from_code(all_params[:referral_code]) if all_params[:referral_code].present?
          TrackingService.track_user_login(@user)
          Mailchimp::SyncService.new.sync_user(@user, trigger_type: 'user_update')

          # assign school to affiliate if using affiliate code
          if session[:affiliate_id].present?
            affiliate = Affiliate.find(session[:affiliate_id])

            unless affiliate.schools.exists?(school[:id])
              affiliate.schools << school
            end
          end
        rescue => e
          # We can no longer render or redirect here as we have already responded to the in the
          # redirect_to checks so just handle the error
          # log_error(e, request)
          puts 'Error in tracking or mailchimp'
        end
      rescue => e
        print e
        @school.lead_source = school_params[:lead_source] # reset the lead source so the select on front works
        @lead_source_other = all_params[:lead_source_other]
        render 'static/session_accounts/school_details', locals: { suid: params[:suid], ai: params[:ai] } and return
      end
    end

    def home
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
    end

    def international
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
    end

    def oauth_callback
      resource = params[:resource]
      # case resource
      # when 'wonde'
      # when 'my_login'
      # end

      @resource_name = resource.humanize.titleize

      method_error_titles = {
        login: 'There was a problem signing you in',
        connect: 'There was a problem connecting your account',
        signup: 'There was a problem signing you up',
      }.with_indifferent_access

      error_messages = {
        generic_error: 'error attempting to link your account. please try again',
        in_use: 'Account already in use',
        not_integrated: "contact your school to have #{@resource_name} integrated",
        already_registered: 'You have already registered, login via the sign in page',
        already_created:
          'this school has already been created on developing experts, you can login via the sign in page',
        school_conflict: "It looks like a different #{@resource_name} account has been registered to this school",
      }.with_indifferent_access

      @error_title = method_error_titles[params[:method]]
      @error_message = error_messages[params[:error]]

      @fatal = params.include?(:fatal) # set to true to render the fatal error view

      @conflict_school = School.find(params.dig(:conflict, :id)) if params.dig(:conflict, :id).present?

      @signout_url = {
        wonde: 'https://edu.wonde.com/sso/logout?client_id=670562',
        my_login: 'https://app.mylogin.com/oauth/logout?client_id=9dc6de70-ea57-4ebc-8bd7-8ae9cdc2e3aa'
      }[resource&.to_sym]

      if params['access-token'].present?
        render 'static/session_accounts/oauth_callback_success'
      elsif @conflict_school
        render 'static/session_accounts/oauth_callback_conflict'
      elsif @fatal.present? || @error_title.present?
        render 'static/session_accounts/oauth_callback_error'
      else
        # FALLBACK
        redirect_to '/accounts/dashboard'
      end
    end

    # MARK: wonde_oauth_callback
    def wonde_oauth_callback # rubocop:disable Metrics/CyclomaticComplexity
      callback_route = "#{Rails.application.config.x.frontend_url}/accounts/new/oauth/wonde?"
      code = params.require(:code)
      data = nil
      begin
        token = WondeManager.get_access_token_sign_up(code)
        data = WondeManager.get_wonde_sso_user(token)
      rescue => e
        redirect_to callback_route + { fatal: true }.to_param
        return
      end

      if !data || !data.dig('data', 'Me', 'Person')
        redirect_to callback_route + { fatal: true }.to_param
        return
      end

      details = data.dig('data', 'Me', 'Person')
      user_wonde_id = details['id']
      school_wonde_id = details.dig('School', 'id')

      # Try login to existing account
      # MARK: wonde-login
      user = User.find_by({ uid: user_wonde_id, provider: 'wonde' })
      user = User.find_by(wonde_id: user_wonde_id) unless user.present?
      if user.present?
        oauth_login(callback_route, user)
        return
      end

      is_employee = data.dig('data', 'Me', 'Person', '__typename') == 'Employee'

      if data.dig('data', 'Me', 'Person', '__typename') == 'Contact'
        # if user is a contact, we can't sign them up
        redirect_to callback_route + { error: 'not_integrated', method: 'signup' }.to_param
        return
      end

      first_name = details['forename']
      last_name = details['surname']
      email = details.dig('ContactDetails', 'email') || details.dig('ContactDetails', 'email_primary')
      school_name = details.dig('School', 'name')
      postcode = details.dig('School', 'address_postcode')
      country = details.dig('School', 'country')
      urn = details.dig('School', 'urn')
      phase_of_education = details.dig('School', 'phase_of_education')

      # MARK: signup existing school
      # no account to login to, check if school exists by wonde id
      school = school_wonde_id.present? ? School.find_by(wonde_id: school_wonde_id) : nil

      # Is student && no school, return not integrated error
      if !is_employee && !school
        redirect_to callback_route + { error: 'not_integrated', method: 'signup' }.to_param
        return
      end

      # Is student && school exists, create a pupil record
      if !is_employee && !!school
        pupil = school.pupils.create(name: [first_name, last_name].join(' '), school: school, wonde_id: user_wonde_id)
        oauth_login(callback_route, pupil)
        return
      end

      if school
        begin
          # if school exists, try find a user by email
          user = school.users.where('lower(email) = ? AND wonde_id IS NULL', email.downcase).first
          if user
            # update wonde id so we don't have to do this check again
            user.update(wonde_id: user_wonde_id)
            oauth_login(callback_route, user)
            return
          end

          # if no user, create a new user & login
          user = User.create!(
            uid: user_wonde_id,
            provider: 'wonde',
            name: "#{first_name} #{last_name}",
            email: email,
            password: "#{SecureRandom.hex}aA1@",
            job_title: nil,
            is_school_admin: false,
            science_lead: false,
            type: 'Teacher',
            school: school,
            wonde_id: user_wonde_id
          )
          oauth_login(callback_route, user)
          return
        rescue => e
          redirect_to callback_route + { record: e.record.errors.messages, method: 'signup' }.to_param
          return
        end
      end

      # no user matched & no school was matched
      # handle sign up a new school

      # MARK: New school
      uk_school = UkSchool.find_by_urn(urn)

      # Create a UK SCHOOL if there is not one
      uk_school ||= UkSchool.create(name: school_name, postcode: postcode, urn: urn, phase_of_education: phase_of_education)

      conflict_school = uk_school.schools.first
      re_use_school = false
      if conflict_school && uk_school.schools.count == 1 && !conflict_school.subscribed? && !conflict_school.has_had_recent_activity?
        # conflict school is unused
        conflict_school.clear_content
        re_use_school = true
      elsif conflict_school
        # update the conflicted count
        uk_school&.update(conflict_sign_ups_count: uk_school.conflict_sign_ups_count + 1)

        # HANDLE CONFLICT SCHOOL
        redirect_to callback_route + { error: 'school_conflict', method: 'signup', conflict: { id: conflict_school.id, name: conflict_school.name } }.to_param
        return
      end

      country = 'United Kingdom' if country == 'England'

      country_record = Country.find_by_name(country)
      category = %w[primary secondary special all_through international].detect { |item| phase_of_education.include?(item) }

      school = nil
      user = nil

      new_library_curriculum_id = 18 
      if (country_record&.id != 1) 
        new_library_curriculum_id = NewLibrary::Curriculum.where(country_id: country_record&.id).first&.id
      end


      begin
        ActiveRecord::Base.transaction do # rubocop:disable Metrics/BlockLength
          if re_use_school
            conflict_school.update!(
              sign_up_event_id: nil,
              school_type: :generic,
              independent: true,
              creation_email: email,
              country_id: country_record&.id,
              actual_country_id: country_record&.id,
              category: category,
              uk_school_id: uk_school&.id,
              wonde_id: school_wonde_id,
              wonde_enabled: true,
              lead_source: '',
              name: uk_school.present? ? uk_school.name : school_name,
              region: uk_school&.region.present? && School.regions.keys.include?(uk_school.region) ? uk_school.region : '',
              postcode: uk_school.present? ? uk_school.postcode : postcode,
              social: nil,
              trial_lessons_started: false,
              trial_handouts_viewed: false,
              trial_videos_watched: false,
              trial_library_browsed: false,
              onboarding_teacher_data_stage: false,
              onboarding_teacher_communicated: false,
              onboarding_pupil_data_stage: false,
              onboarding_parent_data_stage: false,
              setup_checklist: School.default_checklist,
              subscription_start_date: nil,
              finance_email: nil,
              finance_name: nil,
              po_number: nil,
              hubspot_renewal_date: nil,
              renewal_month: 0,
              created_at: DateTime.now,
              new_library_curriculum_id: new_library_curriculum_id,
            )
            school = conflict_school
            school.after_create_callbacks
          else
            school = School.create!(
              school_type: :generic,
              independent: true,
              creation_email: email,
              country_id: country_record&.id,
              actual_country_id: country_record&.id,
              name: school_name,
              category: category,
              postcode: postcode,
              uk_school_id: uk_school&.id,
              wonde_id: school_wonde_id,
              wonde_enabled: true,
              new_library_curriculum_id: new_library_curriculum_id,
            )
            unless school.persisted?
              redirect_to callback_route + { record: school.errors.messages }.to_param
              return
            end
          end

          user = User.create!(
            uid: user_wonde_id,
            provider: 'wonde',
            name: "#{first_name} #{last_name}",
            email: email,
            password: "#{SecureRandom.hex}aA1@",
            job_title: nil,
            is_school_admin: true,
            type: 'Teacher',
            school: school,
            wonde_id: user_wonde_id,
            science_lead: true
          )

          unless user.persisted?
            redirect_to callback_route + { record: user.errors.messages }.to_param
            return
          end

          result = sign_in(user, skip_authentication: true)
          result['expiry'] = result['expiry'].split('.')[0]
          result['user_id'] = user.id
          redirect_to callback_route + result.to_param
        end
      rescue => e
        if Rails.env.production?
          ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
        else
          puts e
        end
        redirect_to callback_route + { record: e.record.errors.messages, method: 'signup' }.to_param
        return
      end

      begin
        WondeManager.request_access(school) if school&.persisted? && user&.persisted?
        WondeManager.sync_data(school) if school&.persisted? && user&.persisted?
        return
      rescue => e
        if Rails.env.production?
          ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
        else
          puts e
        end
      end

      redirect_to callback_route + { fatal: true, method: state }.to_param
    end

    # MARK: my_login_callback
    def my_login_oauth_callback # rubocop:disable Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
      callback_route = "#{Rails.application.config.x.frontend_url}/accounts/new/oauth/my_login?"

      code = params.require(:code)
      data = nil
      begin
        token = MyLoginManager.get_access_token(code)
        data = MyLoginManager.get_user(token)
      rescue => e
        print e
        redirect_to callback_route + { fatal: true }.to_param
        return
      end
      # {
      #   "data": {
      #     "id": "A1161584171",
      #     "first_name": "Freya",
      #     "last_name": "Lee",
      #     "email": "<EMAIL>",
      #     "type": "student",
      #     "last_online": "2023-08-16T13:02:58.000000Z",
      #     "service_providers": {
      #             "google": {
      #                 "service_provider_id": "A987654321"
      #             },
      #             "microsoft": null,
      #             "wonde": {
      #                 "service_provider_id": "A123456789"
      #             },
      #     },
      #     "organisation": {
      #       "id": "A329362542",
      #             "wonde_id": "A1329183376",
      #       "name": "Furlong School",
      #       "phase_of_education": "16 Plus",
      #       "urn": 20,
      #       "la_code": "111",
      #       "establishment_number": "1222"
      #     }
      #   }
      # }

      if !data || !data.dig('data', 'id')
        print 'FATAL: no data or id'
        redirect_to callback_route + { error: 'not_found' }.to_param
        return
      end

      details = data['data']
      my_login_id = details['id']
      user_wonde_id = details.dig('service_providers', 'wonde', 'service_provider_id')
      school_wonde_id = details.dig('organisation', 'wonde_id')

      # MARK: login
      # find user by provider || my_login_id || wonde_id
      user = User.find_by({ uid: my_login_id, provider: 'my_login' })
      user = User.find_by(my_login_id: my_login_id) unless user.present?
      # only look up by wonde id if we have no other matches and user wonde id is present
      user = User.find_by(wonde_id: user_wonde_id) if user_wonde_id.present? && !user.present?

      if user.present?
        oauth_login(callback_route, user)
        return
      end

      user_type = details['type']

      first_name = details['first_name']
      last_name = details['last_name']
      email = details['email']

      # MARK: signup existing school
      # no account to login to, check if school exists by wonde id
      school = school_wonde_id.present? ? School.find_by(wonde_id: school_wonde_id) : nil

      is_employee = %w[employee admin].include?(user_type)
      # Is student && no school, return not integrated error
      if !is_employee && !school
        redirect_to callback_route + { error: 'not_integrated', method: 'signup' }.to_param
        return
      end

      # Is student && school exists, create a pupil record
      if !is_employee && !!school
        pupil = school.pupils.create(name: [first_name, last_name].join(' '), school: school, wonde_id: user_wonde_id)
        oauth_login(callback_route, pupil)
        return
      end

      if school
        begin
          # if school exists, try find a user by email
          user = school.users.where('lower(email) = ? AND wonde_id IS NULL', email.downcase).first
          if user
            # update my login and wonde ids so we don't have to do this check again
            user.update(my_login_id: my_login_id, wonde_id: user_wonde_id)
            oauth_login(callback_route, user)
            return
          end

          # if no user, create a new user & login
          user = User.create!(
            uid: my_login_id,
            provider: 'my_login',
            my_login_id: my_login_id,
            name: "#{first_name} #{last_name}",
            email: email,
            password: "#{SecureRandom.hex}aA1@",
            job_title: nil,
            is_school_admin: false,
            science_lead: false,
            type: 'Teacher',
            school: school,
            wonde_id: user_wonde_id
          )
          oauth_login(callback_route, user)
          return
        rescue => e
          redirect_to callback_route + { record: e.record.errors.messages, method: 'signup' }.to_param
          return
        end
      end

      school_name = details.dig('organisation', 'name')
      phase_of_education = details.dig('organisation', 'phase_of_education')
      urn = details.dig('organisation', 'urn')

      uk_school = UkSchool.find_by_urn(urn)

      # Create a UK SCHOOL if there is not one
      uk_school ||= UkSchool.create(name: school_name, postcode: '', urn: urn, phase_of_education: phase_of_education)

      unless uk_school
        # HANDLE NO UK SCHOOL
        redirect_to callback_route + { error: 'uk_school_404', method: 'signup' }.to_param
        return
      end

      postcode = uk_school.postcode

      conflict_school = uk_school.schools.first
      re_use_school = false
      if conflict_school && uk_school.schools.count == 1 && !conflict_school.subscribed? && !conflict_school.has_had_recent_activity?
        # conflict school is unused
        conflict_school.clear_content
        re_use_school = true
      elsif conflict_school
        # update the conflicted count
        uk_school&.update(conflict_sign_ups_count: uk_school.conflict_sign_ups_count + 1)

        # HANDLE CONFLICT SCHOOL
        redirect_to callback_route + { error: 'school_conflict', method: 'signup', conflict: { id: conflict_school.id, name: conflict_school.name } }.to_param
        return
      end

      country_record = Country.find(1)
      category = %w[primary secondary special all_through international].detect { |item| phase_of_education.include?(item.split('_').join(' ').downcase) }

      school = nil
      user = nil

      new_library_curriculum_id = 18 
      if (country_record&.id != 1) 
        new_library_curriculum_id = NewLibrary::Curriculum.where(country_id: country_record&.id).first&.id
      end


      begin
        ActiveRecord::Base.transaction do # rubocop:disable Metrics/BlockLength
          if re_use_school
            conflict_school.update!(
              sign_up_event_id: nil,
              school_type: :generic,
              independent: true,
              creation_email: email,
              country_id: country_record&.id,
              actual_country_id: country_record&.id,
              category: category,
              uk_school_id: uk_school&.id,
              wonde_id: school_wonde_id,
              wonde_enabled: true,
              lead_source: '',
              name: uk_school.present? ? uk_school.name : school_name,
              region: uk_school&.region.present? && School.regions.keys.include?(uk_school.region) ? uk_school.region : '',
              postcode: uk_school.present? ? uk_school.postcode : postcode,
              social: nil,
              trial_lessons_started: false,
              trial_handouts_viewed: false,
              trial_videos_watched: false,
              trial_library_browsed: false,
              onboarding_teacher_data_stage: false,
              onboarding_teacher_communicated: false,
              onboarding_pupil_data_stage: false,
              onboarding_parent_data_stage: false,
              setup_checklist: School.default_checklist,
              subscription_start_date: nil,
              finance_email: nil,
              finance_name: nil,
              po_number: nil,
              hubspot_renewal_date: nil,
              renewal_month: 0,
              created_at: DateTime.now,
              new_library_curriculum_id: new_library_curriculum_id,
            )
            school = conflict_school
            school.after_create_callbacks
          else
            school = School.create!(
              school_type: :generic,
              independent: true,
              creation_email: email,
              country_id: country_record&.id,
              actual_country_id: country_record&.id,
              name: school_name,
              category: category,
              postcode: postcode,
              uk_school_id: uk_school&.id,
              wonde_id: school_wonde_id,
              wonde_enabled: true,
              new_library_curriculum_id: new_library_curriculum_id,
            )
            unless school.persisted?
              redirect_to callback_route + { record: school.errors.messages }.to_param
              return
            end
          end

          user = User.create!(
            uid: my_login_id,
            provider: 'my_login',
            my_login_id: my_login_id,
            name: "#{first_name} #{last_name}",
            email: email,
            password: "#{SecureRandom.hex}aA1@",
            job_title: nil,
            is_school_admin: true,
            type: 'Teacher',
            school: school,
            wonde_id: user_wonde_id,
            science_lead: true
          )

          unless user.persisted?
            redirect_to callback_route + { record: user.errors.messages }.to_param
            return
          end

          result = sign_in(user, skip_authentication: true)
          result['expiry'] = result['expiry'].split('.')[0]
          result['user_id'] = user.id
          redirect_to callback_route + result.to_param
        end
      rescue => e
        if Rails.env.production?
          ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
        else
          puts e
        end
        redirect_to callback_route + { record: e.record.errors.messages, method: 'signup' }.to_param
        return
      end

      begin
        WondeManager.request_access(school) if school&.persisted? && user&.persisted?
        WondeManager.sync_data(school) if school&.persisted? && user&.persisted?
        return
      rescue => e
        if Rails.env.production?
          ErrorLog.create!(error: { errorData: e, backtrace: e.backtrace }.to_json)
        else
          puts e
        end
      end
      redirect_to callback_route + { fatal: true, method: state }.to_param
    end

    # def test_oauth_login
    #   # # get '/accounts/new/oauth/test_login', to: 'static/session_accounts#test_oauth_login'
    #   # localhost:3000/accounts/new/oauth/test_login
    #   user = User.find_by(email: '<EMAIL>')
    #   oauth_login("#{Rails.application.config.x.frontend_url}/accounts/new/oauth/wonde?", user)
    # end

    private

    def send_password_reset_for_required(user)
      user.send_password_reset_email(token: user.new_recovery_token)
    end

    def track_signup_url_view(params, user)
      record = CustomSignUpUrl.find_by(id: params[:suid], published: true)
      TrackingService.track_custom_signup_url_view(user, record) if record.present?
    end

    def track_signup_url(params, user)
      record = CustomSignUpUrl.find_by(id: params[:suid], published: true)
      TrackingService.track_custom_signup_url_use(user, record) if record.present?
    end

    def oauth_login(callback_route, user)
      result = sign_in(user, skip_authentication: true)
      result['expiry'] = result['expiry'].split('.')[0]
      result['user_id'] = user.id
      redirect_to callback_route + result.to_param
    end

    def all_params
      params.require(:school).permit(
        :type,
        :uk_school_id,
        :telephone,
        :referral_code,
        :country,
        :actual_country,
        :lead_source,
        :lead_source_other,
        :sign_up_event_id,
        :category,
        :terms_accepted,
        :privacy_policy_accepted,
        :share_data,
        :organisation_id,
        user: %i[first_name last_name email password job_title phone]
      )
    end

    def school_params
      # a subset of all_params that are valid school attributes
      all_params.permit(
        :category,
        :telephone,
        :lead_source,
        :sign_up_event_id,
        :organisation_id,
        :uk_school_id
      )
    end

    def user_params
      # a subset of all_params that are valid user attributes
      all_params.require(:user).permit(
        :first_name,
        :last_name,
        :email,
        :password,
        :job_title,
        :phone
      )
    end

    # params from the form for user specific sign up
    def user_form_params
      # a subset of all_params that are valid user attributes
      params.require(:individual_user).permit(
        :first_name,
        :last_name,
        :email,
        :password,
        :job_title,
        :phone,
        :referral_code
      )
    end

    def region_check
      # Initialize session[:geo_info] if nil
      session[:geo_info] ||= { remote_ip: nil, user_country: nil }

      begin
        if request&.remote_ip && request.remote_ip != session[:geo_info][:remote_ip]
          geocoder_result = Geocoder.search(request.remote_ip)&.first

          session[:geo_info] = {
            remote_ip: request.remote_ip,
            user_country: geocoder_result&.country
          }
        end
      rescue Geocoder::Error => e
        Rails.logger.error("Geocoder error: #{e.message}")
        # Maintain existing session data if geocoding fails
      ensure
        # Ensure session has valid structure even if everything fails
        session[:geo_info] ||= { remote_ip: nil, user_country: nil }
      end

      render 'static/unavailable_region', layout: 'static' and return if session[:geo_info][:user_country]  == 'US'
    end

    def complete_school_invite(user)
      return unless session[:school_invite_token].present?

      school_invite = SchoolInvite.find_by(token: session[:school_invite_token])
      if school_invite&.pending? && school_invite.expires_at > Time.current
        school_invite.update!(
          status: :accepted,
          accepted_at: Time.current
        )

        # Clear session
        session.delete(:school_invite_token)
        session.delete(:invited_email)
        session.delete(:invited_school_id)
        session.delete(:invited_school_name)
      end
    end

    def complete_user_referral(user)
      return unless session[:user_referral_token].present?

      user_referral = UserReferral.find_by(token: session[:user_referral_token])
      if user_referral&.pending? && user_referral.expires_at > Time.current
        user_referral.update!(
          status: :accepted,
          accepted_at: Time.current,
          referred_user: user
        )

        UserReferralMailer.invite_accepted_email(user_referral).deliver_later if user_referral.referred_user.present?

        # Clear session
        session.delete(:user_referral_token)
        session.delete(:invited_email)
        session.delete(:referrer_id)
        session.delete(:referrer_name)
      end
    end

    def register_user_referral(user, referral_code)
      return unless referral_code.present?
      referrer = User.find_by(referral_code: referral_code)
      return unless referrer.present?
      referral = UserReferral.create!(
        email: user.email,
        referrer: referrer,
        status: :accepted,
        expires_at: 30.days.from_now,
        invited_at: Time.current,
        accepted_at: Time.current,
        referred_user: user,
        reward_amount: UserReferral::BASE_REFERRAL_AMOUNT
      )
      # generate a prefixed token containing the code so we can identify the referral
      # and the random token for uniqueness
      # Must update after as theres a before validation that will overwrite token on create
      token = "USR-#{referral_code}-#{referral.random_token}"
      referral.update(token: token)

      UserReferralMailer.referral_code_used_email(referral).deliver_now
    rescue => e
      ErrorLog.create(error: { errorData: e, backtrace: e.backtrace }.to_json) if Rails.env.production?
      Rails.logger.error("Error registering user referral: #{e.message}")
    end

    private

    def set_base_breadcrumbs
      add_breadcrumb "Home", root_path
      add_breadcrumb "Sign up", accounts_new_path
    end
  end
end
