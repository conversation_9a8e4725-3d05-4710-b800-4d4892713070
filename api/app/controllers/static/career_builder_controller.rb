module Static
  class CareerBuilderController < StaticApplicationController
    include BreadcrumbsHelper
    
    layout :dynamic_layout
    before_action :set_language_filter, only: [:generate_career_path, :generate_lead_careers, :regenerate_lead_careers, :generate_career_path_v2, :generate_lead_careers_v2]
    before_action :set_user
    skip_before_action :verify_authenticity_token
    before_action :verify_user_access
    before_action :redirect_to_new_career_builder, only: [:index, :saved, :logs, :lead, :interactive, :suggestions]
    # before_action :verify_user

    ERRORS = {
      image: "Image Service",
      text: "Text Generation Service",
      inappropriate: "Inappropriate Content"
    }

    def verify_user_access
      return redirect_to accounts_new_path unless current_user
      unless current_user.subscribed_to_service?(:ai)
        flash[:notice] = 'You need a subscription to access this content.'
        return redirect_to new_subscription_path
      end
    end

    # GET /career-builder
    def index
      @is_admin = current_user&.admin?
      @last_favourite_user_careers = @user ? UserCareerPath.includes(:career_path).where(user_id: @user.id, is_saved: true, is_favourite: true, career_paths: { status: "completed" }).order(created_at: :desc) : []
      @has_saved_careers = @user ? UserCareerPath.where(user_id: @user.id, is_saved: true).present? : false
    end

    # GET /career-builder/saved
    def saved
      @all_saved_user_careers = UserCareerPath.includes(:career_path).where(user_id: @user.id, career_paths: { status: "completed" }).order(is_favourite: :desc).order(created_at: :desc)
    end

    # GET /career-builder/:id/logs
    def logs
      @career_path = CareerPath.find(params[:id])
      @logs = @career_path.career_path_logs.order(created_at: :desc)
    end

    # GET /career-builder/lead
    def lead
      max_lessons = 12
      age = params[:age].to_s
      log_uuid = params[:uuid].to_s

      CareerPathEventLog.log(@current_user.id, log_uuid, "Starting lead - age: #{age}")

      @lessons ||= []
      current_user_lessons = current_user.lesson_templates.limit(max_lessons).pluck(:name)
      @lessons.concat(current_user_lessons) unless current_user_lessons&.empty?
      remaining_lessons_count = max_lessons - current_user_lessons.length

      if remaining_lessons_count > 0
        additional_lessons = NewLibrary::Year.find(81).lesson_templates.order('RANDOM()').limit(remaining_lessons_count).pluck(:name)
        @lessons.concat(additional_lessons)
      end

      @lessons.flatten

      CareerPathEventLog.log(@current_user.id, log_uuid, "Returned lessons - #{@lessons}}")

      @lessons_state =
        if current_user_lessons.length == max_lessons
          :only_current_user_lessons
        elsif additional_lessons.length == max_lessons
          :only_additional_lessons
        else
          :mixed_lessons
        end

      CareerPathEventLog.log(@current_user.id, log_uuid, "Generating step questions - age: #{age}")
      openai_text_service = OpenaiTextService.new
      @questions = openai_text_service.generate_step_questions(age)
      CareerPathEventLog.log(@current_user.id, log_uuid, "Returned step questions - #{@questions}")
    end

    # GET /career-builder/interactive
    def interactive
      add_root_breadcrumb(current_user, @current_pupil)
      add_breadcrumb 'Careers', careers_index_path
      add_breadcrumb 'Interactive Career Advisor'
      
      # Load the page first without the cache, but generate a UUID to use for caching
      @needs_education_level = params[:education_level].nil?
      render :interactive and return if @needs_education_level

      @is_loading = params[:uuid].nil?
      uuid = params[:uuid] || SecureRandom.uuid

      if @is_loading
        # render the interactive page with a UUID for caching
        # we can then redirect the user BACK here with the UUID so we have our intermediate loading
        # page without creating multiple routes / paths to the same page
        render :interactive, locals: { uuid: uuid } and return
      end

      education_level = params[:education_level].to_s

      redirect_to careers_generate_path, alert: 'Please select an education level' and return unless education_level.present?

      @age = {
        'primary' => 8,
        'secondary' => 13,
        'post-16' => 21,
      }[education_level]

      relevant_years = {
        'primary' => NewLibrary::Year.where('name ilike ?', '%year 1%'),
        'secondary' => NewLibrary::Year.where('name ilike ?', '%year 7%'),
        'post-16' => NewLibrary::Year.where('name ilike ?', '%ks3%'),
      }[education_level]

      redirect_to careers_generate_path, alert: 'An error occurred loading the interactive advisor' and return unless @age.present? && relevant_years.present?

      cache = custom_cache.fetch("interactive-career-#{uuid}", expires_in: 10.minutes) do
        max_lessons = 12

        CareerPathEventLog.log(@current_user.id, uuid, "Starting lead - age: #{@age}")

        lessons ||= []
        current_user_lessons = current_user.lesson_templates.limit(max_lessons)
        lessons.concat(current_user_lessons) unless current_user_lessons&.empty?
        remaining_lessons_count = max_lessons - current_user_lessons.length

        if remaining_lessons_count > 0
          additional_lessons = Lesson::Template.joins(:new_library_years).where(new_library_years: relevant_years).order('RANDOM()').limit(remaining_lessons_count)
          lessons.concat(additional_lessons)
        end

        lessons.flatten!

        CareerPathEventLog.log(@current_user.id, uuid, "Returned lessons - #{lessons}}")

        lessons_state =
          if current_user_lessons.length == max_lessons
            :only_current_user_lessons
          elsif additional_lessons.length == max_lessons
            :only_additional_lessons
          else
            :mixed_lessons
          end

        CareerPathEventLog.log(@current_user.id, uuid, "Generating step questions - age: #{@age}")
        openai_text_service = OpenaiTextService.new
        questions = openai_text_service.generate_step_questions(@age)
        CareerPathEventLog.log(@current_user.id, uuid, "Returned step questions - #{questions}")
        {
          questions: questions,
          lessons: lessons.pluck(:fileboy_image_id, :name),
          lessons_state: lessons_state,
        }
      end

      @questions = cache[:questions]
      @lesson_state = cache[:lesson_state]
      @lessons = cache[:lessons]

      render :interactive
    end

    # GET /career-builder/suggestions
    def suggestions
      @suggestions = CareerSuggestion.find_by(user_id: @user.id)
    end

    def suggestions_v2
      @suggestions = CareerSuggestion.find_by(user_id: @user.id)
    end

    # GET /career-builder/:id
    def show
      redirect_to show_v2_career_builder_path(params[:id])
    end

    def qualifications
      @career_name = CareerPath.find(params[:id]).career_name

      nc_service = NationalCareersService.new
      career_info = nc_service.get_career_info(@career_name)

      @career = career_info[:career] if career_info.present?
      @qualifications = career_info[:qualifications] if career_info.present?

      render :qualifications, layout: nil
    end

    def progressions
      career_path = CareerPath.find(params[:id])
      @career_name = career_path.career_name
      @age = career_path.age

      ifate_service = IfateService.new
      occCode = ifate_service.get_occupation_code(@career_name)
      @occupational_progressions = ifate_service.get_occupational_progression(occCode)

      puts @occupational_progressions.inspect

      render :progressions, layout: nil
    end

    def apprenticeships
      career_path = CareerPath.find(params[:id])
      @career_name = career_path.career_name

      apprenticeships_service = ApprenticeshipsService.new
      @apprenticeships = apprenticeships_service.get_vacancies(@career_name)

      render :apprenticeships, layout: nil
    end

    def generate_career_path
      career = params[:career].to_s.titleize
      age = params[:age].to_i
      log_uuid = params[:logUUID]

      if @language_filter.match?(career)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Career path generation flagged as inappropriate", ERRORS.inappropriate)
        render json: { success: false, result: { flagged: true } }
        return
      end

      # See if there is a matching career for the given name and age
      career_path = CareerPath.find_by(career_name: career, age: age)
      if career_path.nil?
        career_path = CareerPath.create!(career_name: career, age: age)
      end

      if @user.present?
        # Add a userCareerPath so it appears in the users recent career paths
        user_career_path = UserCareerPath.find_or_initialize_by(user_id: @user.id, career_path_id: career_path.id)
        user_career_path.save!
      end

      # This will generate the career if needed (including restarting if it errored), otherwise it will do nothing
      CareerPathService.generate_career_path_by_id(career_path.id)

      redirect_to career_builder_path(career_path.id)
    end

    def career_path_status
      status = custom_cache.read("career_path_status_#{params[:user_id]}")
      Rails.logger.info "Cache read for career_path_status_#{params[:user_id]}: #{status.inspect}"
      if status
        render json: { success: true, status: status }
      else
        render json: { success: false, message: 'No status available' }
      end
    end

    def generate_lead_careers
      lessons = params[:lessons]
      interests = params[:interests]
      raw_interests = params[:rawInterests]
      age = params[:age]
      pupil_id = params[:pupilId]
      changes = nil
      changes = params[:changes] if params[:changes].present?
      log_uuid = params[:logUUID]

      user_actions = nil
      if params[:changes].present?
        user_actions = { changes: changes }
      else
        user_actions = { lessons: lessons, interests: raw_interests, age: age }
      end

      CareerPathEventLog.log(@current_user.id, log_uuid, "Starting lead career generation - #{user_actions}")

      if @language_filter.match?(changes)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Changes for lead careers flagged as inappropriate - #{changes}", ERRORS.inappropriate)
        return render json: { success: false, result: { :flagged => true } }
      end

      if pupil_id.present?
        pupil_lessons = Pupil.find(pupil_id).lesson_templates.pluck(:name)
        a = pupil_lessons.map { |l| l.gsub(/^\d+\.\s/, '') }
        lessons = a.join(",")
      end

      begin
        openai_text_service = OpenaiTextService.new
        CareerPathEventLog.log(@current_user.id, log_uuid, "Starting lead career text generation - { lessons: #{lessons}, interests: #{interests}, age: #{age}, changes: #{changes} }")
        result = openai_text_service.generate_lead_careers(lessons, interests, age, changes)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Returned lead careers suggestions: #{result}")
      rescue StandardError => e
        CareerPathEventLog.log(@current_user.id, log_uuid, "Error in service: #{ERRORS[:text]}", e.message)
      end

      if result["flagged"]
        CareerPathEventLog.log(@current_user.id, log_uuid, "Career path generation flagged as inappropriate", ERRORS.inappropriate)
        return render json: { success: false, result: result }
      end

      result = add_images_to_career_suggestions(@user.id, log_uuid, result)

      career_suggestions = CareerSuggestion.find_or_initialize_by(user_id: @user.id, log_uuid: log_uuid)
      career_suggestions.lessons = lessons.split(',')
      career_suggestions.interests = interests.split(',')
      career_suggestions.age = age.to_i
      career_suggestions.careers = result
      career_suggestions.suggested_changes = nil
      career_suggestions.save

      CareerPathEventLog.log(@current_user.id, log_uuid, "Returned career suggestions: #{career_suggestions.inspect}")

      redirect_to suggestions_career_builder_index_url
    end

    def generate_lead_careers_v2
      lessons = params[:lessons]
      interests = params[:interests]
      raw_interests = params[:rawInterests]
      age = params[:age]
      pupil_id = params[:pupilId]
      changes = nil
      changes = params[:changes] if params[:changes].present?
      log_uuid = params[:logUUID]

      user_actions = nil
      if params[:changes].present?
        user_actions = { changes: changes }
      else
        user_actions = { lessons: lessons, interests: raw_interests, age: age }
      end

      CareerPathEventLog.log(@current_user.id, log_uuid, "Starting lead career generation - #{user_actions}")

      if @language_filter.match?(changes)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Changes for lead careers flagged as inappropriate - #{changes}", ERRORS.inappropriate)
        return render json: { success: false, result: { :flagged => true } }
      end

      if pupil_id.present?
        pupil_lessons = Pupil.find(pupil_id).lesson_templates.pluck(:name)
        a = pupil_lessons.map { |l| l.gsub(/^\d+\.\s/, '') }
        lessons = a.join(",")
      end

      begin
        openai_text_service = OpenaiTextService.new
        CareerPathEventLog.log(@current_user.id, log_uuid, "Starting lead career text generation - { lessons: #{lessons}, interests: #{interests}, age: #{age}, changes: #{changes} }")
        result = openai_text_service.generate_lead_careers(lessons, interests, age, changes)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Returned lead careers suggestions: #{result}")
      rescue StandardError => e
        CareerPathEventLog.log(@current_user.id, log_uuid, "Error in service: #{ERRORS[:text]}", e.message)
      end

      if result["flagged"]
        CareerPathEventLog.log(@current_user.id, log_uuid, "Career path generation flagged as inappropriate", ERRORS.inappropriate)
        return render json: { success: false, result: result }
      end

      result = add_images_to_career_suggestions(@user.id, log_uuid, result)

      career_suggestions = CareerSuggestion.find_or_initialize_by(user_id: @user.id, log_uuid: log_uuid)
      career_suggestions.lessons = lessons.split(',')
      career_suggestions.interests = interests.split(',')
      career_suggestions.age = age.to_i
      career_suggestions.careers = result
      career_suggestions.suggested_changes = nil
      career_suggestions.save

      CareerPathEventLog.log(@current_user.id, log_uuid, "Returned career suggestions: #{career_suggestions.inspect}")

      redirect_to suggestions_v2_career_builder_index_url
    end

    def regenerate_lead_careers
      career_suggestions = CareerSuggestion.find(params[:id])
      log_uuid = career_suggestions.log_uuid

      CareerPathEventLog.log(@current_user.id, log_uuid, "Starting lead career generation with changes - #{params[:changes]}")

      if @language_filter.match?(params[:changes])
        CareerPathEventLog.log(@current_user.id, log_uuid, "Changes for lead careers flagged as inappropriate - #{params[:changes]}", ERRORS.inappropriate)
        return render json: { success: false, result: { :flagged => true } }
      end

      initial_careers = career_suggestions[:careers]["careers"].map { |career| career["name"] }

      begin
        openai_text_service = OpenaiTextService.new
        result = openai_text_service.generate_lead_careers(
          career_suggestions.lessons.join(','),
          career_suggestions.interests.join(','),
          career_suggestions.age,
          initial_careers.join(','),
          params[:changes]
        )
      rescue StandardError => e
        CareerPathEventLog.log(@current_user.id, log_uuid, "Error in service: #{ERRORS[:text]}", e.message)
      end

      if result["flagged"]
        CareerPathEventLog.log(@current_user.id, log_uuid, "Career generation flagged as inappropriate - #{results["explanation"]}", ERRORS.inappropriate)
        return render json: { success: false, result: result }
      end

      begin
        result = add_images_to_career_suggestions(@user.id, log_uuid, result)
      rescue StandardError => e
        CareerPathEventLog.log(@current_user.id, log_uuid, "Error in service: #{ERRORS[:image]}", e.message)
      end

      career_suggestions.suggested_changes = params[:changes]
      career_suggestions.careers = result
      career_suggestions.save

      CareerPathEventLog.log(@current_user.id, log_uuid, "Returned career suggestions: #{career_suggestions.inspect}")

      redirect_to suggestions_career_builder_index_url
    end

    def generate_audio_for_section
      id = params[:id]

      career_path = CareerPath.find(id)
      section = params[:section]

      section_text = section[:steps].map { |s| "#{s[:title]} - #{s[:description]}" }.join(' / ')

      content = "#{section[:title]}: #{section_text}"
      content.gsub!(/([,.!?:;]+)(?!\s)/, '\1 ')

      audio = Audio.find_or_initialize_by(name: section[:keyword], audible_id: id, audible_type: 'CareerPath')
      audio.content = content
      audio.save

      career_path.audios << audio unless career_path.audios.include?(audio)

      render json: { success: true, result: audio }
    end

    def generate_facts_for_career
      career = params[:career]
      age = params[:age]

      career_path = CareerPath.find_by(career_name: career, age: age)
      if career_path.present? && career_path.facts.present?
        render json: { success: true, result: { facts: career_path.facts } }
        return
      end

      log_uuid = params[:logUUID]

      CareerPathEventLog.log(@current_user.id, log_uuid, "Generating facts for - career: #{career}, age: #{age}")

      begin
        openai_text_service = OpenaiTextService.new
        result = openai_text_service.sanitise_text(career)
        if result.flagged
          render json: { success: false }
          return
        end

        career = result.output
        CareerPathEventLog.log(@current_user.id, log_uuid, "Returned sanitised career name - career: #{career}")
        result = openai_text_service.generate_facts_for_career(career, age)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Returned facts: #{result}")

        if career_path.present?
          career_path.facts = result["facts"]
          career_path.save
        end

      rescue => e
        CareerPathEventLog.log(@current_user.id, log_uuid, "Error in service: #{ERRORS[:text]}", e.message)
        render json: { success: false }
      end

      render json: { success: true, result: result }
    end

    def remove_career
      user_career_path = UserCareerPath.find(params[:id])
      log_uuid = user_career_path.log_uuid

      user_career_path.destroy!

      CareerPathEventLog.log(@current_user.id, log_uuid, "User removed career path")

      render json: { ok: true }
    end

    def toggle_favourite_career_path
      user_career_path = UserCareerPath.find(params[:id])

      log_uuid = user_career_path.log_uuid

      user_career_path.is_saved = true
      user_career_path.is_favourite = !user_career_path.is_favourite
      user_career_path.save

      CareerPathEventLog.log(@current_user.id, log_uuid, "User favourite career path - { saved: #{user_career_path.is_saved}, favourite: #{user_career_path.is_favourite} }")

      render json: { saved: user_career_path.is_saved, favourite: user_career_path.is_favourite }
    end

    # GET /career-builder/index_v2
    def index_v2
      @is_admin = current_user&.admin?
      @last_favourite_user_careers = @user ? UserCareerPath.includes(career_path: [:job_family, :career]).where(user_id: @user.id, is_saved: true, is_favourite: true, career_paths: { status: "completed" }).order(created_at: :desc) : []
      @has_saved_careers = @user ? UserCareerPath.where(user_id: @user.id, is_saved: true).present? : false
      
      add_breadcrumb "Home", root_path
      add_breadcrumb "Careers", careers_index_path
      add_breadcrumb "Generate Career", careers_generate_path

      # Use the V2 template
      render :index_v2
    end

    # POST /career-builder/generate_career_path_v2
    def generate_career_path_v2
      career = params[:career].to_s.titleize
      requested_education_level = params[:education_level].to_s
      log_uuid = params[:logUUID]

      if @language_filter.match?(career)
        CareerPathEventLog.log(@current_user.id, log_uuid, "Career path generation flagged as inappropriate", ERRORS[:inappropriate])
        render json: { success: false, result: { flagged: true } }
        return
      end

      ai_service = CareerAIGeneratorService.new
      sanitized_name_result = ai_service.sanitise_career_name(career)
      render json: { success: false, result: { flagged: true, explanation: sanitized_name_result[:explanation] } } and return if sanitized_name_result[:flagged]
      sanitised_career_name = sanitized_name_result[:output]

      education_levels = ['primary', 'secondary', 'post-16']
      requested_career_path = nil
      all_career_paths = []

      # Find or create a CareerPath record for each education level.
      education_levels.each do |level|
        age = {
          'primary' => 8,
          'secondary' => 13,
          'post-16' => 21,
        }[level]

        path = CareerPath.find_or_create_for_generation(
          career_name: sanitised_career_name,
          age: age,
          education_level: level
        )
        all_career_paths << path
        requested_career_path = path if level == requested_education_level
      end

      # For each path, associate it with the user and queue the generation job
      # if it hasn't been generated already.
      all_career_paths.each do |career_path|
        if @user.present?
          # Ensure each path is associated with the user.
          UserCareerPath.find_or_create_by(user_id: @user.id, career_path_id: career_path.id)
        end

        # Only queue a new job if the path isn't already completed or currently generating.
        # This prevents redundant work.
        if career_path.status != 'completed' && career_path.status != 'generating'
          CareerPathEventLog.log(@current_user.id, log_uuid, "Queuing career path generation for: #{career} at level: #{career_path.education_level}")
          CareerGenerationJob.perform_later(career_path.id)
        end
      end

      # Immediately return the career path the user originally asked for.
      # The front-end can poll its status, and the other levels will generate in the background.
      render json: {
        success: true,
        career_path_id: requested_career_path.id,
        status: requested_career_path.status,
        redirect_url: career_builder_path(requested_career_path.id)
      }
    end

    # GET /career-builder/:id/show_v2
    def show_v2
      @career_path = CareerPath.find(params[:id])

      add_breadcrumb "Home", root_path
      add_breadcrumb "Careers", careers_index_path
      add_breadcrumb "Search", careers_search_path
      add_breadcrumb @career_path.career_name, careers_search_show_path(@career_path.id)

      if @user.present?
        @user_career_path = UserCareerPath.find_or_create_by(user_id: @user.id, career_path_id: @career_path.id)
        TrackingService.track_career_path_view(@career_path, @current_pupil || @current_user) if @career_path.status == "completed"
      end

      # Find all related career paths for the same career name but different education levels
      @related_career_paths = CareerPath.where(
        career_name: @career_path.career_name,
        status: "completed"
      ).where.not(education_level: nil).by_education_level

      # Set current education level for navigation
      @current_education_level = @career_path.education_level

      @result = @career_path
      @info = OpenStruct.new(@career_path.info || {})
      @audios = OpenStruct.new(audios: @career_path.audios)
    end

    # GET /career-builder/career_path_status_v2
    def career_path_status_v2
      career_path_id = params[:career_path_id]
      user_id = params[:user_id] || @user&.id

      if career_path_id.present?
        career_path = CareerPath.find_by(id: career_path_id)
        if career_path
          status_data = {
            career_path_id: career_path.id,
            status: career_path.status,
            progress: calculate_generation_progress(career_path),
            message: status_message_for(career_path.status)
          }
          
          # Update cache
          custom_cache.write("career_path_status_#{user_id}", status_data, expires_in: 1.hour)
          
          render json: { success: true, **status_data }
        else
          render json: { success: false, message: 'Career path not found' }
        end
      else
        # Fallback to cache-based lookup for compatibility
        status = custom_cache.read("career_path_status_#{user_id}")
        Rails.logger.info "Cache read for career_path_status_#{user_id}: #{status.inspect}"
        if status
          render json: { success: true, **status }
        else
          render json: { success: false, message: 'No status available' }
        end
      end
    end

    # POST /career-builder/generate_facts_for_career_v2
    def generate_facts_for_career_v2
      career = params[:career]
      age = params[:age].to_i
      log_uuid = params[:logUUID]

      career_path = CareerPath.find_by(career_name: career, age: age)
      if career_path.present? && career_path.facts.present?
        render json: { success: true, result: { facts: career_path.facts } }
        return
      end

      CareerPathEventLog.log(@current_user.id, log_uuid, "Generating facts for - career: #{career}, age: #{age}")

      begin
        service = CareerAIGeneratorService.new
        result = service.generate_career_facts(career, age)

        if result[:flagged]
          CareerPathEventLog.log(@current_user.id, log_uuid, "Career name flagged as inappropriate")
          render json: { success: false, result: { flagged: true } }
          return
        end

        CareerPathEventLog.log(@current_user.id, log_uuid, "Returned facts: #{result}")

        if career_path.present?
          career_path.facts = result["facts"]
          career_path.save
        end

        render json: { success: true, result: result }

      rescue => e
        CareerPathEventLog.log(@current_user.id, log_uuid, "Error in service: #{ERRORS[:text]}", e.message)
        render json: { success: false, error: e.message }
      end
    end

    # POST /career-builder/regenerate_career_path_v2
    def regenerate_career_path_v2
      career_path = CareerPath.find(params[:id])
      log_uuid = params[:logUUID]
      with_siblings = !!params[:with_siblings]

      if with_siblings
        education_levels = ['primary', 'secondary', 'post-16']

        all_career_paths = []

        education_levels.each do |level|
          path = CareerPath.find_by(career_name: career_path.career_name, education_level: level)
          if path.nil?
            age = {
              'primary' => 8,
              'secondary' => 13,
              'post-16' => 21,
            }[level]
            path = CareerPath.create!(career_name: career_path.career_name, age: age, education_level: level)
          end
          all_career_paths << path
        end

        # Ensure the career_path from params[:id] is last in the array
        all_career_paths = all_career_paths.partition { |path| path.id == career_path.id }.flatten.reverse

        CareerPathEventLog.log(@current_user.id, log_uuid, "Regenerating all sibling career paths for: #{career_path.career_name}")

        all_career_paths.each do |path|
          CareerGenerationJob.perform_later(path.id, regen: true)
        end
      else
        CareerPathEventLog.log(@current_user.id, log_uuid, "Regenerating career path with new AI service")
        CareerGenerationJob.perform_later(career_path.id, regen: true)
      end

      # Update cache
      custom_cache.write("career_path_status_#{@user.id}", {
        career_path_id: career_path.id,
        status: 'pending',
        message: 'Regeneration queued'
      }, expires_in: 1.hour)

      render json: { 
        success: true,
        career_path_id: career_path.id,
        status: 'pending',
        message: 'Regeneration started'
      }
    end

        private

    def calculate_generation_progress(career_path)
      case career_path.status
      when "pending"
        0
      when "generating"
        # Could be enhanced to show actual progress based on logs
        50
      when "completed"
        100
      when "error", "flagged"
        0
      else
        0
      end
    end

    def status_message_for(status)
      case status
      when "pending"
        "Waiting to start generation..."
      when "generating"
        "Generating your career path..."
      when "completed"
        "Career path ready!"
      when "error"
        "Generation failed. Please try again."
      when "flagged"
        "Content was flagged as inappropriate."
      else
        "Unknown status"
      end
    end

    def set_language_filter
      @language_filter = LanguageFilter::Filter.new()
    end

    def add_images_to_career_suggestions user_id, log_uuid, careers_obj
      careers = careers_obj

      careers['careers']&.each_with_index do |career, i|
        img = PexelsImageService.new.search(career['name'].gsub(' ', '-'), i + 1)
        career['image'] = img["medium"] if img.present?
      end

      return careers
    end

    def get_lessons(tags, age, name)
      return nil if tags.empty?

      base_lessons = Lesson::Template.includes(new_library_units: :year)

      lessons = base_lessons.joins(:career_tags).where("career_tags.id IN (?)", tags)

      # Filter by age
      lessons = lessons.joins(new_library_units: :year)
                       .where('new_library_years.min_age <= ? AND new_library_years.max_age >= ?', age, age)
                       .distinct

      return nil if lessons.empty?

      # Use AI to filter unrelated lessons
      openai_text_service = OpenaiTextService.new
      lesson_ids = openai_text_service.remove_unrelated_entries(lessons.pluck(:id, :name).map { |id, name| { id: id, name: name } }, name)

      # Hard limit to 6
      base_lessons.where(id: lesson_ids).limit(6)
    end

    def set_user
      if current_user.present? && current_user.pupil?
        @current_pupil = current_user
      end

      if current_user.present? && current_user.teacher?
        pupil_id = session[:pupil_id]
        allowed_pupils = current_user.pupils
        @current_pupil = allowed_pupils.find_by(id: pupil_id)
        @pupil_options = current_user.pupils
      end

      @user = @current_pupil || current_user
    end

    def dynamic_layout
      if @current_pupil&.present?
        'pupil'
      elsif @current_pupil&.teacher?
        'school'
      else
        'static'
      end
    end

    def redirect_to_new_career_builder
      redirect_to careers_generate_path
    end
  end
end


