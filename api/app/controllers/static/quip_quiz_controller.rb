module Static
  class QuipQuizController < WebApplicationController
    def quiz_by_id_submit
      source = QuipQuizResult.result_types.include?(params[:source]) ? params[:source].to_sym : :quip_quiz
      quiz = QuipQuiz.find(params[:id])
      @quiz = quiz.format_for_static
      @submit_path = quip_quiz_by_id_path(quiz, user_id: params[:user_id])

      # { question: raw_question_data, result: { score, total, answer } } []
      result_data = JSON.parse(params['results'])
      track_submission(
        result_data,
        source: source,
        template_id: params[:template_id],
        lesson_id: params[:lesson_id],
        quiz_id: quiz.id
      )

      render :show, layout: false
    end

    def quiz_by_flow_step_submit
      flow_step_id = params.require(:flow_step_id)
      flow_step = FlowStep.find(flow_step_id)
      raise ActiveRecord::RecordNotFound unless flow_step&.quiz&.present?
      progress = flow_step.progress_for(user_from_params.presence || current_user)
      progress.mark_completed! unless progress.completed?

      source = QuipQuizResult.result_types.include?(params[:source]) ? params[:source].to_sym : :quip_quiz
      quiz = flow_step.quiz
      @quiz = quiz.format_for_static
      @submit_path = flow_step_quizzes_path(flow_step, user_id: params[:user_id])

      # { question: raw_question_data, result: { score, total, answer } } []
      result_data = JSON.parse(params['results'])
      track_submission(
        result_data,
        source: source,
        template_id: params[:template_id],
        lesson_id: params[:lesson_id],
        quiz_id: quiz.id,
        flow_progress_id: progress&.id
      )
      response.set_header('HX-Refresh', 'true')
      render :show, layout: false
    end

    def quiz_by_template_submit
      source = QuipQuizResult.result_types.include?(params[:source]) ? params[:source].to_sym : :quip_quiz

      template_id = params[:template_id]
      lesson_id = params[:lesson_id]

      @quiz = fetch_from_template(template_id)
      @submit_path = template_quizzes_path(template_id: template_id, lesson_id: lesson_id, user_id: params[:user_id])

      result_data = JSON.parse(params['results'])
      quiz_id = Lesson::Template.find_by(id: template_id)&.quip_quiz&.id

      track_submission(
        result_data,
        source: source,
        template_id: template_id,
        lesson_id: lesson_id,
        quiz_id: quiz_id
      )

      render :show, layout: false
    end

    def rocket_word_quiz_submit
      template_id = params[:template_id]
      lesson_id = params[:lesson_id]
      @quiz = fetch_rocket_word_quiz(template_id)
      @submit_path = rocket_word_quizzes_path(template_id: template_id, lesson_id: lesson_id, user_id: params[:user_id])
      # { question: raw_question_data, result: { score, total, answer } } []

      result_data = JSON.parse(params['results'])
      # this sets @results
      track_submission(
        result_data,
        source: :rocket_word_quiz,
        template_id: template_id,
        lesson_id: lesson_id
      )

      user = user_from_params
      if user.present?
        template = Lesson::Template.find(template_id)
        time_taken = 0
        if params[:start_time].present?
          start_time = Time.parse(params[:start_time])
          end_time = Time.now
          time_taken = end_time - start_time
        end
        TrackingService.track_rocket_word_quiz_completion(user, template, {
                                                            score: @results['score'],
                                                            time_taken: time_taken,
                                                            lesson_id: lesson_id,
                                                            answers_json: result_data,
                                                          })
        QuizOld::Attempt.create(user_id: user.id, lesson_template_id: template_id, score: @results['score']) if lesson_id.present? && user&.pupil?
      end

      render :show, layout: false
    end

    def result
      # Only available in development
      raise ActionController::RoutingError.new('Not Found') unless current_user&.admin?

      @current_user = current_user

      begin
        result = QuipQuizResult.find(params[:id])
        authorize! :read, result
      rescue CanCan::AccessDenied
        redirect_to helpers.universal_dashboard_link(current_user), alert: 'You are not authorized view this result'
        return
      end

      # Remove JSON.parse since results_json is already a Hash
      result_data = result.results_json['data']
      @results = sum_results(result_data)
      @quiz = QuipQuiz.last

      @results_only = true
      render :show, layout: 'admin'
    end

    private

    def track_submission(
      result_data,
      source: nil,
      template_id: nil,
      lesson_id: nil,
      quiz_id: nil,
      flow_progress_id: nil
    )
      @results = sum_results(result_data)

      time_taken = 0
      if params[:start_time].present?
        start_time = Time.parse(params[:start_time])
        end_time = Time.now
        time_taken = end_time - start_time
      end

      user = user_from_params.presence || current_user

      UserRankEvent.add_points(user, @results['score'].to_i * 2, 'quip_quiz') if lesson_id.present? && user.present? && user.pupil?

      user_record = user&.pupil? ? { pupil: user, user: user } : { user: user }

      QuipQuizResult.create(
        **user_record,
        result_type: source,
        lesson_template_id: template_id,
        score: @results['score'],
        total: @results['total'],
        results_json: @results,
        flow_progress_id: flow_progress_id,
        quip_quiz_id: quiz_id
      )

      template = Lesson::Template.find(template_id) if template_id.present?
      TrackingService.track_quip_quiz_completion(
        user,
        template,
        {
          time_taken: time_taken,
          score: @results['score'],
          total_score: @results['total'],
          lesson_id: lesson_id,
          result_type: source,
          answers_json: result_data,
          quiz_id: quiz_id,
          flow_progress_id: flow_progress_id,
          # noodle_quiz_id:
          # campaign_quiz_id:
        }
      )
    end

    def user_from_params
      User.accessible_by(current_ability).find_by(id: params[:user_id])
    end

    def sum_results(result_data)
      score = result_data.map { |r| r['result']['score'].to_i }.sum
      total = result_data.map { |r| r['result']['total'].to_i }.sum
      {
        score: score,
        total: total,
        data: result_data,
      }.with_indifferent_access
    end

    def fetch_rocket_word_quiz(id)
      template = Lesson::Template.find(id)
      template.rocket_word_quiz
    end

    def fetch_from_template(id)
      template = Lesson::Template.find(id)
      template.quip_quiz.format_for_static
    end
  end
end
