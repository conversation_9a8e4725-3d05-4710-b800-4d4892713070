module Static
  class TrackingsController < AnonController
    def campaign
      campaign = Campaign.find_by(id: params[:id])
      user = User.find_by(id: params[:user_id]) || current_user
      if campaign
        TrackingService.track_campaign_clicks(campaign, user)
        head :ok
      else
        head :not_found
      end
    end

    def link_view
      data = params.permit(:templateId, :linkType, :lessonId, :targetUrl, :userId)

      lesson = Lesson::Lesson.find(data[:lessonId]) if data[:lessonId].present?
      template = lesson&.template
      template = Lesson::Template.find(data[:templateId]) unless template || data[:templateId].nil?

      user = User.find_by(id: data[:userId]) || current_user
      TrackingService.track_link_view(user, template, {
        url: data[:targetUrl],
        link_type: data[:linkType].to_sym,
        lesson_id: lesson&.id,
      })

      head :ok
    end

    def film_view
      data = params.permit(:lesson_template_id, :lesson_id, :jw_id, :fileboy_video_id, :time_viewing, :film_duration, :film_type, :video_id, :slide_id, :video_url)

      lesson = Lesson::Lesson.find_by(id: data[:lesson_id]) if data[:lesson_id].present?
      template = lesson&.template || Lesson::Template.find_by(id: data[:lesson_template_id]) if data[:lesson_template_id].present?

      if template.nil?
        head :ok
        return
      end

      jw_id = data[:jw_id]
      video_id = data[:video_id]
      fileboy_video_id = data[:fileboy_video_id]
      time_viewing = data[:time_viewing]
      film_duration = data[:film_duration]
      film_type = data[:film_type]
      video_url = data[:video_url]
      video_resource = Lesson::Slide.find_by(id: data[:slide_id])

      TrackingService.track_film_view(current_user, template, video_resource, {
        lesson_id: lesson&.id,
        jw_id: jw_id,
        fileboy_video_id: fileboy_video_id,
        time_viewing: time_viewing,
        film_duration: film_duration,
        film_type: film_type,
        video_id: video_id,
        video_url: video_url
      })

      if current_user&.pupil?
        UserRankEvent.add_points(current_user, 1, 'video')
      end

      head :ok
    end

  end
end
