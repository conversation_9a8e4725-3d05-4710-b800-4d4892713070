module Static
  class ImagesController < WebApplicationController
    before_action :authenticate_user!
    include Warden<PERSON>ontroller
    include Fileboy

    def create
      fileboy_image_id = upload_image params[:upload] if params[:upload].present?
      if fileboy_image_id
        render json: { uploaded: 1, url: "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}" }
      else
        render json: { error: { message: 'Something went wrong' } }
      end
    end

    def search
      query = params[:query]
      images = if query.present?
        Image.search(query).limit(100)
      else
        Image.limit(100)
      end
      
      render json: {
        success: true,
        images: images.map do |image|
          {
            id: image.id,
            fileboy_image_id: image.fileboy_image_id,
            title: image.title,
            keywords: image.keywords,
            thumbnail_url: fileboy_image_url(image.fileboy_image_id) + "?transform=resize:300x200~fit:cover"
          }
        end
      }
    end

    # POST /admin/upload_fileboy_image
    # Handles direct file uploads and returns a Fileboy ID
    def upload_fileboy_image
      if params[:file].blank?
        render json: { success: false, error: 'No file provided' }, status: :bad_request
        return
      end
      
      begin
        fileboy_image_id = upload_image(params[:file])
        
        if fileboy_image_id.present?
          render json: { 
            success: true, 
            fileboy_id: fileboy_image_id,
            preview_url: "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:200x200"
          }
        else
          render json: { success: false, error: 'Failed to upload image to Fileboy' }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error("Fileboy upload error: #{e.message}\n#{e.backtrace.join("\n")}")
        render json: { success: false, error: e.message }, status: :internal_server_error
      end
    end
    
    # POST /admin/process_pexels_image
    # Downloads an image from Pexels URL and uploads it to Fileboy
    def process_pexels_image
      url = params[:url]
      
      if url.blank?
        render json: { success: false, error: 'No URL provided' }, status: :bad_request
        return
      end
      
      begin
        fileboy_image_id = upload_image_from_url(url)
        
        if fileboy_image_id.present?
          render json: { 
            success: true, 
            fileboy_id: fileboy_image_id,
            preview_url: "https://www.developingexperts.com/file-cdn/images/get/#{fileboy_image_id}?transform=resize:200x200"
          }
        else
          render json: { success: false, error: 'Failed to process Pexels image' }, status: :unprocessable_entity
        end
      rescue => e
        Rails.logger.error("Pexels image processing error: #{e.message}\n#{e.backtrace.join("\n")}")
        render json: { success: false, error: e.message }, status: :internal_server_error
      end
    end

    private

    def authenticate_user!
      redirect_to root_path unless current_user
    end
  end
end
