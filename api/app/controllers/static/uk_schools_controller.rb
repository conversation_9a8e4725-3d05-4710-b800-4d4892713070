module Static
  class UkSchoolsController < AnonController
    def search
      query = params[:query]

      if query.present? && query.length >= 2
        schools = UkSchool
                    .where("name ILIKE ?", "%#{query}%")
                    .or(UkSchool.where("postcode ILIKE ?", "%#{query}%"))
                    .limit(50)

        # Transform into the standardized format for nice-select
        formatted_schools = schools.map do |school|
          {
            value: school.id,
            label: school.name,
            sub: school.postcode
          }
        end

        render json: formatted_schools, status: :ok
      else
        render json: { error: "Query must be at least 2 characters long" }, status: :unprocessable_entity
      end
    end
  end
end