module Static
  class EventsController < AnonController
    include Pa<PERSON>ationHelper

    def index
      @current_user = current_user

      # SORTING
      orderable = {
        date: 'COALESCE(MIN(event_times.start_date), events.start_date)',
        name: 'name',
        created_at: 'created_at',
      }.with_indifferent_access
      sort_by = orderable[params[:sort]] || orderable[:date]
      sort_dir = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @events = Event.published.left_joins(:event_times).order([sort_by, sort_dir].join(' ')).group('events.id')

      # Filter scope
      @events = @events.where('events.for ILIKE ?', "%#{params[:scope]}%") if params[:scope].present?

      # filter search query
      @events = @events.where('events.name ILIKE ?', "%#{params[:query]}%") if params[:query].present?

      # paginate
      @events = safe_paginate(@events, page: params[:page], per_page: 12)

      # list of scopes
      @event_for = Event.where.not(for: '').map(&:for_arr).flatten.uniq(&:downcase)
    end

    def show
      @event = Event.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      flash[:error] = "Event not found or you don't have access to it"
      redirect_to "/events"
    end
  end
end
