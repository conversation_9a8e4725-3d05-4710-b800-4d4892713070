module Static
  class SponsorsController < AnonController
    def index
      @sponsors = Organisation
                      .accessible_by(current_ability)
                      .where(is_sponsor: true)
    end

    def show
      @sponsor = Organisation.accessible_by(current_ability).friendly.find(params[:id])

      if request.path != sponsor_path(@sponsor)
        return redirect_to sponsor_path(@sponsor), status: :moved_permanently
      end

      id = @sponsor.id
      @units = NewLibrary::Unit.joins(:organisations).where(organisations: {id: id}).order('weight ASC')
      @lessons = Lesson::Template.joins(:organisations).where(organisations: {id: id}).order('weight ASC')
      @tours = Tour.joins(:organisation).where(organisations: {id: id})

      if @sponsor.blank?
        render 'static/not_found', status: :not_found
      end
    end
  end
end