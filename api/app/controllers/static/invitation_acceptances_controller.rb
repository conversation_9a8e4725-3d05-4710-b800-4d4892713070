module Static
  class InvitationAcceptancesController < AnonController
    def accept_school_invite
      @school_invite = SchoolInvite.find_by(token: params[:token])

      if @school_invite.nil?
        redirect_to root_path, alert: 'Invalid invitation link.'
        return
      end

      if @school_invite.expires_at < Time.current
        redirect_to root_path, alert: 'This invitation has expired.'
        return
      end

      if @school_invite.accepted?
        redirect_to root_path, notice: 'This invitation has already been accepted.'
        return
      end

      # Store invitation details in session for signup process
      session[:school_invite_token] = @school_invite.token
      session[:invited_email] = @school_invite.email
      session[:invited_school_id] = @school_invite.school_id
      session[:invited_school_name] = @school_invite.school.name

      # Check if user already exists
      existing_user = User.find_by(email: @school_invite.email)
      if existing_user
        if existing_user.is_a?(Teacher) && existing_user.school_id == @school_invite.school_id
          redirect_to root_path, notice: 'You are already a member of this school.'
        else
          redirect_to root_path, alert: 'An account with this email already exists.'
        end
      else
        # Redirect to custom school invite signup form
        redirect_to invitation_school_signup_path
      end
    end

    def accept_user_referral
      @user_referral = UserReferral.find_by(token: params[:token])
      
      if @user_referral.nil?
        redirect_to root_path, alert: 'Invalid referral link.'
        return
      end
      
      if @user_referral.expires_at < Time.current
        redirect_to root_path, alert: 'This referral has expired.'
        return
      end
      
      if @user_referral.accepted?
        redirect_to root_path, notice: 'This referral has already been used.'
        return
      end
      
      # Store referral details in session for signup process
      session[:user_referral_token] = @user_referral.token
      session[:invited_email] = @user_referral.email
      session[:referrer_id] = @user_referral.referrer_id
      session[:referrer_name] = @user_referral.referrer.name
      
      # Check if user already exists
      existing_user = User.find_by(email: @user_referral.email)
      if existing_user
        redirect_to root_path, notice: 'An account with this email already exists.'
      else
        # Redirect to account type selection
        redirect_to invitation_user_type_selection_path
      end
    end
  end
end
