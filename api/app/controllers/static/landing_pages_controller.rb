module Static
  class LandingPagesController < AnonController
    before_action :set_landing_page, only: [:show]
  
    def show
      # Track page view
      @landing_page.increment!(:views_count)
      @landing_page.track_view(@current_user, request_data)

      @units = NewLibrary::Unit.where(id: @landing_page.units.map { |u| u["id"] })
      @lessons = Lesson::Template.where(id: @landing_page.lessons.map { |l| l["id"] }).first(8)
      
      respond_to do |format|
        format.html
        format.json { render json: @landing_page }
      end
    end
    
    # Index page for browsing landing pages (optional)
    def index
      @landing_pages = LandingPage.published
                                  .page(params[:page])
                                  .limit(20)
      
      # Filters
      @landing_pages = @landing_pages.by_key_stage(params[:key_stage]) if params[:key_stage].present?
      @landing_pages = @landing_pages.by_subject(params[:subject]) if params[:subject].present?
      @landing_pages = @landing_pages.by_resource_type(params[:resource_type]) if params[:resource_type].present?
      
      # Search
      if params[:search].present?
        search_term = "%#{params[:search]}%"
        @landing_pages = @landing_pages.where(
          "title ILIKE ? OR topic ILIKE ? OR content ILIKE ?", 
          search_term, search_term, search_term
        )
      end
      
      @landing_pages = @landing_pages.recent
      
      # For filters in the view
      @key_stages = LandingPage::KEY_STAGES
      @subjects = LandingPage::SUBJECTS
      @resource_types = LandingPage::RESOURCE_TYPES
    end
    
    # Sitemap endpoint for SEO
    def sitemap
      @landing_pages = LandingPage.published.select(:slug, :updated_at)
      
      respond_to do |format|
        format.xml
      end
    end
    
    private
    
    def set_landing_page
      @landing_page = LandingPage.published.friendly.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      # Try to find unpublished page for admins
      if @current_user&.admin?
        @landing_page = LandingPage.friendly.find(params[:id])
        flash.now[:warning] = "This page is not yet published and is only visible to admins."
      else
        raise ActiveRecord::RecordNotFound
      end
    end
    
    def request_data
      {
        user_agent: request.user_agent,
        ip_address: request.remote_ip,
        referrer: request.referer,
        utm_source: params[:utm_source],
        utm_medium: params[:utm_medium],
        utm_campaign: params[:utm_campaign]
      }
    end
  end
end 