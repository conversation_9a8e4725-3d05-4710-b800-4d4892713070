module Static
  class NewPresentationFeedbackController < StaticApplicationController
    def create
      # skip feedback if nothing was submitted
      return unless feedback_params[:rating].present?

      @feedback = NewPresentationFeedback.new(feedback_params)
      @feedback.user = current_user

      @feedback.save

      user = @feedback.user
      if user&.teacher? && user.allows_notification?(:lesson_feedback)
        SchoolMailer.lesson_feedback_submitted(user, @feedback).deliver_now
      end

      render partial: 'success', notice: 'Feedback was successfully submitted'
    end

    private

    def feedback_params
      params.permit(:associate_to, :associate_to_id, :feedback, :rating)
    end
  end
end
