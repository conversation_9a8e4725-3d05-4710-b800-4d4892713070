module Static
  class GlossariesController < AnonController
    include <PERSON>read<PERSON><PERSON>bsHelper

    before_action :set_base_breadcrumbs

    def index
      @current_user = current_user
      @glossaries = []

      if cookies[:glossary_random_ids].blank?
        rand_ids = Glossary.order("RANDOM()").limit(4).pluck(:id)
        cookies[:glossary_random_ids] = {
          value: rand_ids.to_json,
          expires: Time.current.end_of_day
        }
      end

      rand_ids = JSON.parse(cookies[:glossary_random_ids])
      @random_words = Glossary.where(id: rand_ids)

      return unless @current_user

      user_fav_list = @current_user.glossary_list || []

      fav_list_ids = user_fav_list.map(&:to_i)

      @glossaries = Glossary.where(id: fav_list_ids).order(:name)
    end

    def letter
      @current_user = current_user
      user_fav_list = @current_user&.glossary_list || []
      letter = params.require(:letter)

      add_breadcrumb letter, letter_glossary_index_path(letter: letter)

      @glossaries = Glossary
                      .accessible_by(current_ability)
                      .where("name ILIKE ?", "#{letter}%")
                      .order("lower(name) ASC")
      @letter = letter.capitalize()

      @glossaries.each do |glossary|
        glossary.favourite = user_fav_list.include?(glossary.id.to_s)
      end
    end

    def show
      @current_user = current_user
      user_fav_list = @current_user&.glossary_list || []

      info = params[:info]
      
      @glossary = Glossary.friendly.find(params[:id])
      if request.path != glossary_path(@glossary)
        return redirect_to glossary_path(@glossary), status: :moved_permanently
      end

      if @glossary.blank?
        render 'static/not_found', status: :not_found
        return
      end

      add_breadcrumb @glossary.letter, letter_glossary_index_path(letter: @glossary.letter)
      add_breadcrumb @glossary.name

      @glossary_fav = user_fav_list.include?(@glossary.id.to_s)

      TrackingService.track_glossary_view(@glossary, current_user)

      @show_secondary_info = info == 'secondary'

      @audios = @glossary.audios.each_with_object({}) do |audio, obj|
        obj[audio.name] = audio.fileboy_audio_id
      end
    end

    def toggle_favourite
      return unless current_user

      id = params[:id]
      glossary_list = current_user.glossary_list || []
      is_being_added = !glossary_list.include?(id)

      if is_being_added
        glossary_list.push(id)
      else
        glossary_list.delete(id)
      end

      current_user.update(glossary_list: glossary_list)

      render json: { id: id, is_being_added: is_being_added }
    end

    def get_all_entries
      glossary = Glossary.all
      render json: glossary
    end

    private

    def set_base_breadcrumbs
      add_breadcrumb "Home", root_path
      add_breadcrumb "Glossary", glossary_index_path
    end

  end

end
