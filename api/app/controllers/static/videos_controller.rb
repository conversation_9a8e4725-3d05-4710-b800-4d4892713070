module Static
  class VideosController < AnonController
    include Pa<PERSON>ationHelper
    include ActionView::Helpers::DateHelper
    include BreadcrumbsHelper
    
    before_action :set_base_breadcrumbs, only: [:show]

    def index
      @videos = Video.searchable.all
      
      # Handle search queries
      if params[:query].present?
        words_in_query = params[:query].split(/\s+/)
        query = "%#{words_in_query.map!(&:singularize).join('%')}%"
        @videos = @videos
          .left_joins(:tags)
          .where(
            "videos2.name ILIKE :q OR videos2.keywords ILIKE :q OR tags.name ILIKE :q",
            q: query
          ).distinct
      end

      # Handle scope filtering
      case params[:scope]
      when 'sponsored'
        @videos = @videos.where(is_sponsored: true)
      when 'assignment'
        @videos = @videos.where(is_assignment: true)
      when 'expert'
        @videos = @videos.where(is_expert: true)
      when 'career'
        @videos = @videos.where(is_career: true)
      when 'all'
        # Show all videos - no additional filtering
      end

      # If we have filters or search, use pagination
      if params[:query].present? || params[:scope].present? || params[:tags].present?
        total_records = @videos.count
        per_page = 16  # 4 columns x 4 rows for better desktop experience
        @total_pages = (total_records / per_page.to_f).ceil
        @videos = safe_paginate(@videos.order("created_at desc"), page: params[:page], per_page: per_page)
      else
        # For homepage, show sectioned content
        @assignment_videos = @videos.where(is_assignment: true)
          .order("created_at desc")
          .limit(8)
        
        @expert_videos = @videos.where(is_expert: true)
          .order("created_at desc")
          .limit(8)
        
        @career_videos = @videos.where(is_career: true)
          .order("created_at desc")
          .limit(8)

        @recent_videos = @videos
          .order("created_at desc")
          .limit(8)
      end

      respond_to do |format|
        format.html
        format.json do
          html = render_to_string(
            partial: "static/videos/video_card",
            collection: @videos,
            as: :video,
            formats: [:html]
          )
          render json: { html: html, total_pages: @total_pages }
        end
      end
    end
    
    def show
      @video = Video.friendly.find(params[:id])
      if @video && request.path != video_path(@video)
        return redirect_to video_path(@video), status: :moved_permanently
      end

      add_breadcrumb @video.name

      @recommended_videos = @video.similar_videos(8)
      @sponsored_videos = Video.searchable.where(is_sponsored: true).last(4) if @recommended_videos.empty?

      if @video.blank?
        render 'static/not_found', status: :not_found
        return
      end
    end

    def download_video
      respond_to do |format|
        format.json do
          video = Video.find(params[:id])
          if video.fileboyVideoId.present?
            url = "https://www.developingexperts.com/file-cdn/videos/#{video.fileboyVideoId}/info"
            response = HTTParty.get(url, headers: { 'Content-Type': 'application/json' })
            if response.code == 200
              result = JSON.parse(response.body)
              render json: result&.dig('rawVideoUrl') and return
            end
          end
          render json: '' and return
        end
        format.any { head :not_found }
      end
    end

    # POST /admin/videos/from_data
    def video_from_data
      data = params.permit(:video_id, :fileboy_video_id, :video_url, :source, :external_id)

      # video by id
      if data[:video_id].present?
        render html: video_component_from_params({ video_id: data[:video_id] }).render_in(view_context), layout: false
        return
      end

      # video by source and external_id
      if data[:source].present? && data[:external_id].present?
        if data[:source] == 'fileboy'
          data[:fileboy_video_id] = data[:external_id]
        else
          data[:video_url] = VideoUrlHelpers.embed_url_for(data[:source], data[:external_id])
        end
      end

      render html: video_component_from_params({
                                                 id: data[:video_id],
                                                 fileboy_video_id: data[:fileboy_video_id],
                                                 video_url: data[:video_url],
                                               }).render_in(view_context), layout: false
    end

    def convert_url
      data = params.permit(:video_url)
      source, external_id = VideoUrlHelpers.video_url_type(data[:video_url])

      render json: { source: source, external_id: external_id }
    end

    def search
      query = params[:query].presence || ''

      videos = Video.all 
      unless current_user&.admin?
        videos = videos.where(appears_in_search: true)
      end

      allowed_types = params[:allowed_video_types]&.split(',') if params[:allowed_video_types].present?
      videos = videos.where(source: allowed_types) if allowed_types.present?

      videos = videos.left_joins(:tags).where('videos2.name ILIKE :query OR videos2.keywords ILIKE :query OR videos2.ai_summary ILIKE :query OR tags.name ILIKE :query', query: "%#{query}%") if query.present?

      videos = videos.limit(20).map do |video|
        {
          id: video.id,
          name: video.name,
          summary: video.ai_summary,
          source: video.source,
          source_friendly: video.source.titleize,
          external_id: video.external_id,
          thumbnail_url: video.thumbnail_url,
          keywords: video.keywords,
          tags: video.tags.pluck(:name).uniq,
          duration: video.duration,
          formatted_duration: video.duration.present? ? format("%02d:%02d", (video.duration / 60).floor, (video.duration % 60).round) : nil,
          has_transcript: video.transcript.present?,
          created_at: video.created_at.strftime('%d/%m/%Y at %H:%M'),
          created_at_distance: "#{distance_of_time_in_words_to_now(video.created_at).gsub('about ', '')} ago"
        }
      end

      render json: { videos: videos, success: true }
    end

    private

    def set_base_breadcrumbs
      add_breadcrumb "Home", root_path
      add_breadcrumb "Videos", videos_path
    end

    def video_component_from_params(params)
      VideoComponent.new(
        id: params[:id],
        fileboy_video_id: params[:fileboy_video_id],
        video_url: params[:video_url],
        should_track: false
      )
    end
  end
end
