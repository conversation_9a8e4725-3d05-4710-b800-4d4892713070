module Static
  class FlowsController < AnonController
    include ApplicationHelper
    include PaginationHelper
    include BreadcrumbsHelper

    before_action :check_access
    before_action :set_base_breadcrumbs
    before_action :set_flow, only: [:show, :enroll, :unenroll, :summary]
    before_action :require_user_for_enrollment, only: [:enroll, :unenroll]

    def check_access
      redirect_to universal_dashboard_link unless current_user.beta_feature_enabled?('flows')
    end

    def index
      @flows = Flow.published
                  .includes(:job_family, :flow_enrollments)
                  .order(created_at: :desc)

      # Apply filters
      if params[:job_family_id].present?
        @flows = @flows.where(job_family_id: params[:job_family_id])
      end

      if params[:difficulty].present?
        @flows = @flows.where(difficulty_level: params[:difficulty])
      end

      if params[:search].present?
        @flows = @flows.where("name ILIKE ? OR description ILIKE ?", 
                            "%#{params[:search]}%", "%#{params[:search]}%")
      end

      # Pagination
      @flows = @flows.paginate(page: params[:page], per_page: 12)

      # Filter options
      @job_families = JobFamily.joins(:flows).where(flows: { published: true }).distinct
      @difficulties = Flow.published.where.not(difficulty_level: [nil, '']).distinct.pluck(:difficulty_level)
    end

    def show
      raise ActiveRecord::RecordNotFound unless @flow.published?

      if request.path != flow_path(@flow)
        return redirect_to flow_path(@flow), status: :moved_permanently
      end

      add_breadcrumb @flow.name, flow_path(@flow)

      @flow_steps = @flow.flow_steps.order(:weight)
      @is_enrolled = current_user&.flow_enrollments&.exists?(flow: @flow)
      @user_progress = current_user ? @flow.completion_rate_for(current_user) : 0
      @completed_steps = current_user ? @flow.completed_steps_for(current_user) : 0

      # Get next step for enrolled users
      if @is_enrolled && current_user
        completed_step_ids = FlowProgress.joins(:flow_step)
                                        .where(flow_steps: { flow_id: @flow.id }, user: current_user, completed: true)
                                        .pluck(:flow_step_id)

        @next_step = @flow_steps.where.not(id: completed_step_ids).first
      end

      if request.path != flow_path(@flow)
        return redirect_to flow_path(@flow), status: :moved_permanently
      end
    end

    def summary
      redirect_to flow_path(@flow) unless @flow.completed_steps_for(current_user) == @flow.flow_steps.count
      add_breadcrumb @flow.name, flow_path(@flow)
      add_breadcrumb 'Summary', summary_flow_path(@flow)
      @flow_steps = @flow.flow_steps.order(:weight)
      @is_enrolled = current_user&.flow_enrollments&.exists?(flow: @flow)
      redirect_to flow_path(@flow) unless @is_enrolled
    end

    def enroll
      enrollment = current_user.flow_enrollments.find_or_create_by(flow: @flow) do |e|
        e.enrolled_at = Time.current
      end

      if enrollment.persisted?
        redirect_to @flow, notice: 'Successfully enrolled in the course!'
      else
        redirect_to @flow, alert: 'Failed to enroll in the course.'
      end
    end

    def unenroll
      enrollment = current_user.flow_enrollments.find_by(flow: @flow)
      
      if enrollment&.destroy
        # Also remove any progress
        FlowProgress.joins(:flow_step)
                    .where(flow_steps: { flow_id: @flow.id }, user: current_user)
                    .destroy_all
        redirect_to @flow, notice: 'Successfully unenrolled from the course.'
      else
        redirect_to @flow, alert: 'Failed to unenroll from the course.'
      end
    end

    private

    def set_flow
      @flow = Flow.published.friendly.find(params[:id])
    end

    def require_user_for_enrollment
      unless current_user
        redirect_to new_user_session_path, alert: 'You must be signed in to enroll in courses.'
      end
    end

    def set_base_breadcrumbs
      add_breadcrumb "Home", root_path
      add_breadcrumb "Courses", flows_path
    end
  end
end
