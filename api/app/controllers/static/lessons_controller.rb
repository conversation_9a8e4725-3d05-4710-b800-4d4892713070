module Static
  class LessonsController < StaticSchoolController
    before_action :set_lesson, only: %i[show lesson_plan rocket_word_quiz assessment_quiz]

    def show
      @videos = []

      slide = @lesson_template.slides.find_by(slide_type: 'expert')
      if slide&.video_url || slide&.fileboy_video_id || slide&.video_id
        @videos << {
          name: 'Expert film',
          fileboy_video_id: slide.fileboy_video_id,
          video_url: slide.video_url,
          video_id: slide.video_id,
          slide_id: slide.id,
          type: 'expert',
        }
      end
      slide = @lesson_template.slides.find_by(slide_type: 'mission_assignment')
      if slide&.video_url || slide&.fileboy_video_id || slide&.video_id
        @videos << {
          name: 'Mission assignment film',
          fileboy_video_id: slide.fileboy_video_id,
          video_url: slide.video_url,
          video_id: slide.video_id,
          slide_id: slide.id,
          type: 'mission_assignment',
        }
      end

      @recommended_careers = @lesson_template.recommended_careers
      if @recommended_careers.empty?
        @recommended_careers = custom_cache.fetch("recommended_careers_#{@lesson_template.id}", expires_in: 7.days) do
          @lesson_template.generate_recommended_careers
        end
      end
      
      render 'static/lesson_templates/show_available'
    end

    private

    def set_lesson
      @lesson = Lesson::Lesson.accessible_by(current_ability).find(params[:id])
      @lesson_template = @lesson.template
      @curriculum = current_user&.school&.new_library_curriculum
      @return_path = {
        label: @lesson.form.name,
        link: "/classes/#{@lesson.form.id}/lessons",
      }
    end
  end
end
