module Static
  class ToursController < StaticApplicationController
    
    layout 'tour'

    def show
      @tour = Tour.friendly.find(params[:id])

      @return_to = params[:return_to] || '/accounts/dashboard'

      @tour.data.each do |scene|
        scene[1]['panorama'] = fileboy_url(scene[1]['panoramaFileboyId'])
      end
      @image = fileboy_url(@tour.fileboy_image_id)
      @default_scene_key = '' # @tour.data.keys.first
      @return_to = params[:return_to] || '/accounts/dashboard'

      if request.path != tour_path(@tour)
        return redirect_to tour_path(@tour, return_to: @return_to), status: :moved_permanently
      end

      TrackingService.track_tour_view(@tour, current_user)
    end

    # GET /admin/tours/tour_videos?tour_id=
    # Returns all video ids for the tour scenes
    def tour_videos
      param_data = params.permit(:tour_id)
      tour_id = param_data[:tour_id]
      render json: [] and return unless tour_id.present?
      render json: Tour.find(tour_id).tour_videos
    end

    private

    def fileboy_cdn
      'https://www.developingexperts.com/file-cdn'
    end

    def fileboy_url(fileboy_id)
      fileboy_cdn + "/images/get/#{fileboy_id}?transform=format:webp;quality:75"
    end
  end
end
