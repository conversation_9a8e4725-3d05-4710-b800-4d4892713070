module Static
  class FilesController < WebApplicationController
    before_action :authenticate_user!
    include Warden<PERSON>ontroller
    include Fileboy

    def upload_fileboy_file
      begin
        file = params[:file]
        
        if file.blank?
          render json: { success: false, error: 'No file provided' }, status: 400
          return
        end

        # Upload to File<PERSON> using the existing concern method
        fileboy_id = upload_document(file)
        
        if fileboy_id
          render json: {
            success: true,
            fileboy_id: fileboy_id,
            file_name: file.original_filename
          }
        else
          render json: { success: false, error: 'Failed to upload file to Fileboy' }, status: 500
        end
        
      rescue ArgumentError => e
        # Handle unsupported file type error from upload_document
        render json: { success: false, error: e.message }, status: 400
      rescue => e
        Rails.logger.error "File upload error: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        render json: { success: false, error: 'An error occurred while uploading the file' }, status: 500
      end
    end

    private

    def authenticate_user!
      redirect_to root_path unless current_user
    end
  end
end
