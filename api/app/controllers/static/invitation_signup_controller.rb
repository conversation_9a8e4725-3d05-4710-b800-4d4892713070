# app/controllers/static/invitation_signup_controller.rb
module Static
  class InvitationSignupController < AnonController
    before_action :validate_school_invite, only: %i[school_invite_signup create_school_invite_user]

    def school_invite_signup
      @user = Teacher.new(email: session[:invited_email])
      @school = School.find(session[:invited_school_id])
    end

    def create_school_invite_user
      # Load the school from the invite instead of the session (not sure why we have these separated)
      @school = School.find(@school_invite.school_id)

      @user = Teacher.new({
                            name: [user_params[:first_name], user_params[:last_name]].reject(&:blank?).join(' '),
                            email: session[:invited_email],
                            password: user_params[:password],
                            phone: user_params[:phone],
                            job_title: user_params[:job_title],
                            school: @school
                          })

      if @user.save
        @school_invite.update(status: :accepted, invited_user: @user, accepted_at: Time.current)
        notify_invite_accepted(@school_invite)
        sign_in(@user, skip_authentication: true)
        redirect_to '/accounts/dashboard' and return
      else
        flash[:error] = "There was a problem creating your account: #{@user.errors.full_messages.join(', ')}"
        render :school_invite_signup
      end
    end

    def user_type_selection
      @user_referral = UserReferral.find_by(token: session[:user_referral_token])
      @referrer = @user_referral&.referrer
      redirect_to root_path, alert: 'Invalid referral' and return unless @user_referral && @referrer
      render 'static/session_accounts/new'
    end

    private

    def notify_invite_accepted(invite)
      # send email to each school admin who allows invite accepted notifications
      SchoolInviteMailer.invite_accepted_email(invite).deliver_later
    rescue => e
      # catch and log errors but prevent bubbling, as the user has already been created and signed in
      ErrorLog.create(error: { errorData: e, backtrace: e.backtrace }.to_json) if Rails.env.production?
      Rails.logger.error "Failed to send invite accepted notification: #{e.message}"
    end

    def validate_school_invite
      unless session[:school_invite_token].present?
        redirect_to root_path, alert: 'Invalid invitation session.'
        return false
      end

      @school_invite = SchoolInvite.find_by(token: session[:school_invite_token])
      unless @school_invite&.pending? && @school_invite.expires_at > Time.current
        redirect_to root_path, alert: 'This invitation has expired or is invalid.'
        return false
      end

      # verify the school invite is for the school id in the session
      unless @school_invite.school_id.to_s == session[:invited_school_id].to_s
        redirect_to root_path, alert: 'This invitation has expired or is invalid.'
        return false
      end

      true
    end

    def user_params
      params.require(:user).permit(:first_name, :last_name, :password, :phone, :job_title)
    end
  end
end
