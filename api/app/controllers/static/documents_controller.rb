module Static
  class DocumentsController < AnonController
    include Pa<PERSON><PERSON><PERSON>el<PERSON>
    
    def index
      @current_user = current_user
      @documents = Document.all

      # Handle search queries
      if params[:query].present?
        query = "%#{params[:query]}%"
        @documents = @documents
          .where(
            "documents.title ILIKE :q OR documents.keywords ILIKE :q",
            q: query
          ).distinct
      end

      # Handle tag filtering
      if params[:tags].present?
        keywords = params[:tags].split(',').map(&:strip)
        keyword_conditions = keywords.map do |kw|
          Document.arel_table[:keywords].matches("%#{kw}%")
        end
        @documents = @documents.where(keyword_conditions.inject(:and)).distinct
      end

      # Pagination
      total_records = @documents.count
      per_page = 16  # 4 columns x 4 rows for better desktop experience
      @total_pages = (total_records / per_page.to_f).ceil
      @documents = safe_paginate(@documents.order("created_at desc"), page: params[:page], per_page: per_page)

      respond_to do |format|
        format.html
        format.json do
          html = render_to_string(
            partial: "static/documents/document_card",
            collection: @documents,
            as: :document,
            formats: [:html]
          )
          render json: { html: html, total_pages: @total_pages }
        end
      end
    end

    def serve_document
      id = params[:fileboy_id]
      source = params[:source]

      data = {}
      data["lesson_id"] = params[:lesson_id] if params[:lesson_id].present?
      data[:document_source] = source
      data[:document_type] = params[:document_type] if params[:document_type].present?

      document = case source
      when 'curriculum_document'
        CurriculumDocument.find_by(fileboy_id: id)
      when 'youtube'
        LiveStreamDocument.find_by(fileboy_id: id)
      when 'new_library'
        NewLibrary::Document.find_by(fileboy_file_id: id)
      when 'homework_file'
        HomeworkFile.find_by(fileboy_id: id)
      else
        data[:document_source] = 'lesson_document'
        Lesson::Document.find_by(fileboy_file_id: id)
      end

      if document.nil?
        head :not_found and return
      end

      user = User.find_by(id: params[:user_id]) if params[:user_id].present?
      user ||= current_user
      TrackingService.track_document_download(document, user, data)

      redirect_to "https://www.developingexperts.com/file-cdn/files/get/#{id}?download" and return
    end
  end
end