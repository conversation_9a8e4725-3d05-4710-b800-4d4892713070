module Static
  class SessionPasswordsController < StaticApplicationController
    layout 'static'
    before_action :ensure_logged_out, only: [:password_edit, :password_edit_submit]

    def new
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      @user = User.new
    end

    def create
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      email = params.require(:user).require(:email)
      @user = User.find_by(email: email.downcase)

      if @user&.is_blocked
        @user.errors.add(:base, 'This account has been deactiviated. Please contact support.')
      elsif @user&.persisted?
        @submitted = true
        @user.send_password_reset_email(token: @user.new_recovery_token)
      else
        @user = User.new(email: email)
        @user.errors.add(:email, 'not found')
      end

      render :new
    end

    def edit
      @org = Organisation.find_by(id: params[:oid]) if params[:oid].present?
      @token = params[:token]
      return render_token_error('No token provided') unless @token.present?

      @user = User.find_by(recovery_token: @token)
      if @user.nil?
        return render_token_error('Invalid password reset token')
      elsif @user.recovery_requested_at < 1.day.ago
        return render_token_error('Token has expired')
      end
    end

    def update
      token = params[:token]
      new_password = params[:password]

      return render_token_error('No token provided') unless token.present?

      @user = User.find_by(recovery_token: token)
      return render_token_error('Invalid password reset token') unless @user
      return render_token_error('Token has expired') if @user.recovery_requested_at < 1.day.ago

      if @user.update(password: new_password, recovery_token: nil, recovery_requested_at: nil, require_password_reset: false)
        sign_in(@user, skip_authentication: true)
        redirect_to '/accounts/dashboard', notice: 'Password was successfully updated.'
      else
        flash.now[:alert] = 'Error updating password'
        render :edit
      end
    end

    private

    def ensure_logged_out
      if current_user
        render_token_error('Sign out to reset your password')
      end
    end

    def render_token_error(message)
      @token_error = message
      render :edit
    end
  end
end
