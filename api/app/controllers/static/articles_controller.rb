module Static
  class ArticlesController < AnonController
    include PaginationHelper
    include BreadcrumbsHelper

    before_action :set_base_breadcrumbs
    
    def show
      request.format = :html # Force HTML format for blog posts someone tries to access the blog post an an image route sometimes...

      @blog_post = Article.published.friendly.find(params[:id])

      raise ActiveRecord::RecordNotFound if @blog_post.blank?

      add_breadcrumb @blog_post.name, article_path(@blog_post)

      if request.path != article_path(@blog_post)
        return redirect_to article_path(@blog_post), status: :moved_permanently
      end

      TrackingService.track_article_view(@blog_post.id, current_user)
    end

    def index
      @blog_posts = safe_paginate(Article.published.ordered, page: params[:page], per_page: 12)
    end

    private

    def set_base_breadcrumbs
      add_breadcrumb "Home", root_path
      add_breadcrumb "Blog", articles_path
    end
  end
end
