# app/controllers/static/event_registrations_controller.rb
module Static
  class EventRegistrationsController < AnonController
    before_action :set_event_registration
    
    def show
      unless @event_registration.active? && @event_registration.open?
        redirect_to root_path, alert: 'This event registration is not currently available.'
        return
      end
      
      @event_registrant = EventRegistrant.new
    end
    
    def create
      @event_registrant = @event_registration.event_registrants.build(event_registrant_params)
      
      if @event_registrant.save
        redirect_to event_registration_path(@event_registration), 
                    notice: 'Thank you for registering! We will be in touch soon.'
      else
        render :show
      end
    end
    
    private
    
    def set_event_registration
      @event_registration = EventRegistration.active.find(params[:id])
    rescue ActiveRecord::RecordNotFound
      redirect_to root_path, alert: 'Event not found.'
    end
    
    def event_registrant_params
      permitted = params.require(:event_registrant).permit(:name, :email)
      
      # Add registration_data if present
      if params[:registration_data].present?
        permitted[:registration_data] = params[:registration_data].permit!.to_h
      end
      
      permitted
    end
  end
end