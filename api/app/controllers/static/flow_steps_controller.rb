module Static
  class FlowStepsController < AnonController
    include ApplicationHelper
    include BreadcrumbsHelper

    before_action :check_access
    before_action :require_user
    before_action :set_flow_step
    before_action :require_enrollment
    before_action :set_breadcrumbs

    def check_access
      redirect_to universal_dashboard_link unless current_user.beta_feature_enabled?('flows')
    end

    layout 'course_player'

    def show
      # Mark step as started
      @progress = current_user.flow_progresses.find_or_create_by(flow_step: @flow_step) do |p|
        p.started_at = Time.current
      end

      @progress.mark_started! unless @progress.started_at

      # Get navigation info
      @flow = @flow_step.flow
      @all_steps = @flow.flow_steps.order(:weight)
      @current_step_index = @all_steps.index(@flow_step)
      @previous_step = @flow_step.previous_step
      @next_step = @flow_step.next_step

      # Get user's progress
      @user_progress = @flow.completion_rate_for(current_user)
      @completed_steps = @flow.completed_steps_for(current_user)

      prev_step_complete = @previous_step&.completed_by?(current_user)
      # if the previous step is not complete and there is a previous step, redirect to the
      # most recent step available
      if @previous_step && !prev_step_complete
        last_complete_step = @all_steps[@completed_steps] || @all_steps.first
        redirect_to flow_flow_step_path(@flow, last_complete_step) and return
      end

      # Step-specific content
      case @flow_step.step_type
      when 'video'
        @video = @flow_step.video
      when 'quiz'
        @quiz = @flow_step.quiz.format_for_static
        @results = @flow_step.progress_for(current_user)&.quip_quiz_results&.last&.results_json
        @results_only = true
        @submit_path = flow_step_quizzes_path(@flow_step)
      when 'rich_text'
        @content = @flow_step.rich_text_content
      end
    end

    def complete
      @progress = current_user.flow_progresses.find_or_create_by(flow_step: @flow_step)
      
      unless @progress.completed?
        @progress.mark_completed!
        
        # Check if flow is now complete
        if @flow_step.flow.completion_rate_for(current_user) == 100
          flash[:success] = "🎉 Congratulations! You've completed the entire course!"
        else
          flash[:success] = "Step completed! Great job!"
        end
      end

      # Redirect to next step or back to course
      if @flow_step.next_step && !@flow_step.next_step.completed_by?(current_user)
        redirect_to flow_flow_step_path(@flow_step.flow, @flow_step)
      else
        redirect_to summary_flow_path(@flow_step.flow)
      end
    end

    def update_notes
      @progress = current_user.flow_progresses.find_or_create_by(flow_step: @flow_step)
      notes = params[:notes].strip
      @progress.update(notes: notes)
      redirect_to flow_flow_step_path(@flow_step.flow, @flow_step), notice: 'Notes updated.'
    end

    private

    def set_flow_step
      @flow_step = FlowStep.joins(:flow)
                          .where(flows: { published: true })
                          .find(params[:id])
    end

    def require_enrollment
      unless current_user.flow_enrollments.exists?(flow: @flow_step.flow)
        redirect_to flow_path(@flow_step.flow), alert: 'You must be enrolled to access course content.'
      end
    end

    def require_user
      unless current_user
        redirect_to new_user_session_path, alert: 'You must be signed in to access course content.'
      end
    end

    def set_breadcrumbs
      add_breadcrumb "Home", root_path
      add_breadcrumb "Courses", flows_path
      add_breadcrumb @flow_step.flow.name, flow_path(@flow_step.flow)
      add_breadcrumb @flow_step.name, flow_flow_step_path(@flow_step.flow, @flow_step)
    end
  end
end
