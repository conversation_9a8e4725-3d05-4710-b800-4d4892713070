class V2::CampaignLessonsController < V2::ApplicationController

  def index
    records = CampaignLesson.accessible_by(current_ability).left_joins(:lesson_template, :campaign)

    if (params[:ids])
      records = records.where({ id: params[:ids].split(",") })
    end

    if params[:query].present?
      records = records.where("lesson_templates.machine_name ilike :query OR lesson_templates.name ilike :query OR campaigns.name ilike :query", query: "%#{params[:query]}%")
    end

    if params[:templateId].present?
      records = records.where(lesson_template_id: params[:templateId])
    end

    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      records = records.where(campaign_id: organisation.campaign_ids)
    end

    if params[:campaignId].present?
      records = records.where(campaign_id: params[:campaignId])
    end

    if params[:campaignUnitId].present?
      records = records.where(campaign_unit_id: params[:campaignUnitId])
    end

    if params[:newLibraryUnitId].present?
      records = records.where(new_library_unit_id: params[:newLibraryUnitId])
    end

    if (params[:unpaginated] == "true")
      render json: { records: records }
    else
      meta, records = paginate(records)
      render json: { records: records, meta: meta }
    end
  end

  def create
    campaign_lesson = CampaignLesson.new(lesson_campaign_params)

    authorize! :create, campaign_lesson

    if campaign_lesson.save
      render json: { saved: true, record: campaign_lesson }
    else
      render json: { saved: false, errors: campaign_lesson.errors.full_messages }
    end
  end

  private

  def lesson_campaign_params
    params.permit(:lesson_template_id, :video_id, :external_url, :campaign_id)
  end
end
