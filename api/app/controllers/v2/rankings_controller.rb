class V2::RankingsController < ApplicationController

  def achievement_points
    collection = collection_scope(params)
    .joins(user_achievements: :achievement)
    .group("users.id")

    from = params[:from].present? ? Date.parse(params[:from]) : nil
    to = params[:to].present? ? Date.parse(params[:to]) : nil
    if from
      collection = collection.where("user_achievements.created_at > ?", from)
    end
    if to
      collection = collection.where("user_achievements.created_at < ?", to)
    end

    collection = collection.select("
      users.id,
      #{name_query} as name,
      sum(achievements.points) as points
    ")
    .group("users.id")
    .order(Arel.sql("sum(achievements.points) desc"))

    meta, records = paginate(collection)
    render json: {
      meta: meta,
      records: records
    }
  end

private

  def name_query
    return <<-SQL
      (#{current_user.admin? ? "users.name" : "COALESCE(users.alias, REGEXP_REPLACE(TRIM(users.name),'(\s.*)|(,)', '', 'g'))"})
    SQL
  end

  # by school, by global, by region, by class
  def collection_scope params
    country_id = params[:countryId]
    region = params[:region]
    school_id = params[:schoolId]
    form_id = params[:classId]
    query = params[:query]

    collection = Pupil.joins(:school)

    if form_id.present?
      collection = Form.find(form_id).pupils
    end

    if country_id.present?
      collection = collection.where("schools.country_id = ?", country_id)
    end

    if region.present?
      region = School.regions[region]
      collection = collection.where("schools.region = ?", region)
    end

    if school_id.present?
      collection = collection.where(school_id: school_id)
    end

    if query.present?
      collection = collection.where("users.name ilike ?", "%#{query}%")
    end

    collection
  end
end
