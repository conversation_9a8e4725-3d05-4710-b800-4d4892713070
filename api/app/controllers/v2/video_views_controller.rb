class V2::VideoViewsController < V2::ApplicationController
  include CampaignDemographicsFilter
  def index
    records = VideoView.accessible_by(current_ability).left_joins(:video)

    if params[:query].present?
      records = records.where("videos2.name ilike ? ", "%#{params[:query]}%")
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create_for_current_user
    slide_id = params[:slideId]
    time_viewing = params[:timeViewingSeconds]
    slide = Lesson::Slide.find(slide_id)
    VideoView.create(video: slide.video, time_viewing_seconds: time_viewing, user: current_user, resource: slide) if slide.video
    UserRankEvent.add_points(current_user, 1, "video") if current_user.present?
    render json: { saved: !!slide.video }
  end

  def create
    video_tracking = VideoView.new(video_tracking_params)
    authorize! :create, video_tracking
    if video_tracking.save
      UserRankEvent.add_points(current_user, 1, "video")
      render json: { saved: true, record: video_tracking }
    else
      render json: { saved: false, errors: video_tracking.errors.full_messages }
    end
  end

  def view_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = VideoView.left_joins(:video)
    if params[:campaignId].present?
      data = data.where("videos2.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      data = data.where("videos2.campaign_id in (?)", organisation.campaign_ids)
    end
    if start_date
      data = data.where("video_views.created_at < ?", start_date)
    end
    if end_date
      data = data.where("video_views.created_at > ?", end_date)
    end
    if params[:videoIds].present?
      data = data.where(videos2_id: params[:videoIds].split(","))
    end
    data = campaign_demographics_filter(data)
    render json: data
  end

  def demographics_data
    data = VideoView.left_joins(:video, user: :school)
    if params[:campaignId].present?
      data = data.where("videos2.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      data = data.where("videos2.campaign_id in (?)", organisation.campaign_ids)
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
        result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
        result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
      result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end

  private

  def video_tracking_params
    params.permit(:time_viewing_seconds, :user_id, :videos2_id, :resource_id, :resource_type)
  end
end
