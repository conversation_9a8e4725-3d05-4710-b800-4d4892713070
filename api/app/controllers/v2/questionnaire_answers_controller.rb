class V2::QuestionnaireAnswersController < V2::ApplicationController
  def create
    if current_user&.present?
      QuestionnaireAnswer
        .where(
          session_id: answer_params[:session_id],
          user: current_user,
          questionnaire_question_id: answer_params[:questionnaire_question_id],
        )
        .destroy_all
    end
    answers = answer_params[:data]
    saved = []
    answers.each do |answer|
      next unless answer[:answer_value].present?
      selected_count = [answer[:selectedCount] || 1, 50].min
      selected_count.times do
        result = QuestionnaireAnswer.new(
          answer.except(:selectedCount).as_json.merge({
            session_id: answer_params[:session_id],
            questionnaire_question_id: answer_params[:questionnaire_question_id]
          })
        )
        result.user = current_user if current_user&.present?
        result.save!
        saved << result
      end
    end

    is_last_question = QuestionnaireQuestion.order(:weight).last.id == answer_params[:questionnaire_question_id]

    if current_user.present? && is_last_question
      is_onboarding = !!QuestionnaireQuestion.find_by(id: answer_params[:questionnaire_question_id])&.questionnaire&.is_onboarding_questionnaire
      if is_onboarding
        tags = current_user.questionnaire_career_tags.pluck(:name).keep_if(&:present?).uniq
        current_user.user_interest_tags.destroy_all
        tags.each { |tag| current_user.user_interest_tags.find_or_create_by(name: tag) }
      end
    end

    render json: { saved: true, message: 'success', records: saved }
  end

  private

  def answer_params
    params.permit(
      :questionnaire_question_id,
      :session_id,
      data: [:questionnaire_option_id, :answer_value, :selectedCount]
    )
  end
end
