class V2::LeaderboardsController < V2::ApplicationController
  before_action :check_user

  def leaderboard2
    unless current_user&.school&.present?
      render json: { topRows: [], rows: [] }
      return
    end
    type = params[:type]
    user_ids = nil
    if type == 'school'
      user_ids = current_user.school.pupil_ids
    end
    render json: UserRankEvent.leaderboard_data(Time.now, current_user.id, user_ids)
  end

  def my_leaderboard2
    unless current_user&.school&.present?
      render json: {
        points: nil,
        schoolRank: nil,
        globalRank: nil,
      }
      return
    end
    # pull date from params, check if it is a date default to time.Now
    date = params[:date] ? DateTime.parse(params[:date]) : Time.now
    school_user_ids = current_user.school.pupil_ids

    # return { points: number; schoolRank: number, globalRank: number }
    global_rank = UserRankEvent.user_rank(current_user.id, date)
    school_rank = UserRankEvent.user_rank(current_user.id, date, school_user_ids)

    render json: {
      points: global_rank[:score],
      schoolRank: school_rank[:rank],
      globalRank: global_rank[:rank],
    }
  end

  def dashboard2
    school_pupil_ids = current_user.school.pupil_ids
    teacher_pupil_Ids = current_user.pupil_ids

    school_board = UserRankEvent.top_n_users(30, Time.now, school_pupil_ids, aliases = false)
    teacher_board = UserRankEvent.all_user_ranks(Time.now, teacher_pupil_Ids, aliases = false)

    # { school: Row[], teacher: Row[] }
    render json: { school: school_board, teacher: teacher_board }
  end

  private

  def check_user
    render json: { error: "User not found" }, status: 404 if current_user.nil?
  end
end
