class V2::AdminDashboardController < V2::ApplicationController
  def school_engagement_stats
    data = {
      active7Days: {},
      fullActive7Days: {},
      lessThan3: {},
      lessThan3Active: {},
    }
    [*School.regions.keys.each, 'no_region'].each do |region_value|
      region = region_value == 'no_region' ? nil : region_value
      active_schools = School
                       .generic
                       .where(region: region)
                       .joins(users: :active_days)
                       .where('active_days.date >= ?', 7.days.ago)
                       .group('schools.id')

      data[:active7Days][region_value] = School.from(active_schools, :schools).count
      data[:fullActive7Days][region_value] = School.from(active_schools.having('COUNT(DISTINCT users.id) >= 5'), :schools).count

      schools_less_than_3 = School
                            .generic
                            .where(region: region)
                            .left_joins(users: :active_days)
                            .group(:id)
                            .having('COUNT(DISTINCT users.id) < 3')

      data[:lessThan3][region_value] = School.from(schools_less_than_3, :schools).count
      data[:lessThan3Active][region_value] = schools_less_than_3
                                             .where('schools.created_at <= ?', 3.months.ago)
                                             .having('BOOL_OR(active_days.date > ?)', 1.month.ago)
    end

    render json: data
  end

  def school_teacher_engagement_stats
    active_schools = School.generic
    active_schools = active_schools.where(hubspot_subscription_status: School::HS_SUBSCRIPTION_STATUS[:subscribed]) if params['scope'] == 'with_subscription_request'
    active_schools = active_schools.where.not(hubspot_subscription_status: School::HS_SUBSCRIPTION_STATUS[:subscribed]) if params['scope'] == 'without_subscription_request'
    active_schools = active_schools.joins(teachers: :active_days).having('BOOL_OR(active_days.date >= ?)', 30.days.ago).group('schools.id')

    meta, records = paginate(active_schools.order('schools.name'))
    render json: {
      records: records.map do |school|
                 {
                   id: school.id,
                   name: school.name,
                   created_at: school.created_at,
                   hubspot_subscription_status: school.hubspot_subscription_status,
                   teachers: school.teachers.joins(:active_days).where('active_days.date >= ?', 30.days.ago).group('users.id').map do
            |teacher| {
              id: teacher.id,
              name: teacher.name,
              email: teacher.email,
              login_count: teacher.active_days.where('date >= ?', 30.days.ago).count,
              created_at: teacher.created_at,
              last_activity_at: teacher.last_activity_at,
            }
                   end
                 }
               end,
      meta: meta
    }
  end
end
