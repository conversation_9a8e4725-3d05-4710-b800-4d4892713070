class V2::MotdsController < V2::ApplicationController
  def index
    messages = Motd.accessible_by(current_ability)

    if (params[:organisationId].present?)
      messages = messages.where(organisation_id: params[:organisationId])
    end

    if (params[:query])
      messages = messages.where("name ILIKE ?", "%#{params[:query]}%")
    end

    meta, records = paginate(messages)

    render json: { records: records, meta: meta }
  end
end
