class V2::CampaignVacancyExternalClicksController < V2::ApplicationController
  include CampaignDemographicsFilter
  
  def link_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = CampaignVacancyExternalClick.left_joins(career_vacancy: :organisation)
    if params[:campaignId].present?
      campaign = Camapaign.find(params[:campaignId])
      data = data.where("career_vacancies.organisation_id = ?", campaign.organisation_id)
    end
    if params[:organisationId].present?
      data = data.where("career_vacancies.organisation_id = ?", params[:organisationId])
    end
    if params[:careerVacancyIds].present?
      data = data.where(career_vacancy_id: params[:careerVacancyIds].split(","))
    end
    if start_date
      data = data.where("campaign_vacancy_external_clicks.created_at < ?", start_date)
    end
    if end_date
      data = data.where("campaign_vacancy_external_clicks.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)
    render json: data
  end

end
