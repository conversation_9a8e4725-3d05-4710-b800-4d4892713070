class V2::SponsorsController < V2::ApplicationController
  def show
    sponsor = Sponsor.find(params[:id])
    authorize! :read, sponsor
    render json: sponsor
  end

  def create
    sponsor = Sponsor.new(sponsor_params)
    authorize! :create, sponsor
    if sponsor.save
      render json: { saved: true, record: sponsor }
    else
      render json: { saved: false, errors: sponsor.errors.full_messages }
    end
  end

  def update
    sponsor = Sponsor.find(params[:id])
    authorize! :update, sponsor
    if (sponsor.update(sponsor_params))
      render json: { saved: true, record: sponsor }
    else
      render json: { saved: false, errors: sponsor.errors.full_messages }
    end
  end

  def destroy
    sponsor = Sponsor.find(params[:id])
    authorize! :destroy, sponsor
    destroy_record sponsor
  end

  private

  def sponsor_params
    params.permit(:name, :body, :fileboy_image_id, school_ids: [])
  end

end
