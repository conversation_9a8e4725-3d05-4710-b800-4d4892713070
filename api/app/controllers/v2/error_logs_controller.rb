class V2::ErrorLogsController < V2::ApplicationController
  def create
    return if error_log_params[:error].include?("<!DOCTYPE")
    return unless current_user
    @error_log = ErrorLog.new(error_log_params)
    @error_log.user_info = { id: current_user.id, type: current_user&.type, school_id: current_user&.school_id } if current_user
    @error_log.save
  end

  private

  def error_log_params
    params.permit(:error)
  end
end
