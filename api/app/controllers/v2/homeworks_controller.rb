class V2::HomeworksController < V2::ApplicationController
  def index
    collection = Homework.accessible_by(current_ability)

    if (params[:query].present?)
      collection = collection.where("title ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:school_id].present?)
      collection = collection.where(school_id: params[:school_id])
    end

    if (params[:user_id].present?)
      collection = collection.where(created_by_id: params[:user_id])
    end

    if params[:scope] === "upcoming"
      collection = collection.upcoming
    end

    if params[:scope] === "published"
      collection = collection.published
    end

    if params[:unpaginated] == "true"
      collection = collection.map do |record|
        record.as_json.merge({
                               pupils: record.pupils,
                               lesson: record.lesson&.template,
                               tasks: record.tasks.map { |task| task.as_json.merge({ submissions: task.submissions.accessible_by(current_ability), files: task.files }) }
                             })
      end

      render json: { records: collection }
      return
    end

    meta, records = paginate(collection)
    records = records.map do |record|
      record.as_json.merge({
                             pupils: record.pupils,
                             lesson: record.lesson&.template,
                             tasks: record.tasks.map { |task| task.as_json.merge({ submissions: task.submissions.accessible_by(current_ability), files: task.files }) },
                             created_by: record.created_by,
                             school: record.school,
                           })
    end
    render json: { records: records, meta: meta }
  end

  def pupil_index
    collection = Homework.accessible_by(current_ability)

    # This is converting into an array
    collection = collection.map { |record| record.for_user(current_user) }
    render json: collection
  end

  def pupil_show
    record = Homework.accessible_by(current_ability).find(params[:id])

    # This is converting into an array

    tasks = record.tasks.map do |task|
      submission = task.submissions.find_by(user_id: current_user.id)
      submission = submission&.as_json&.merge({ files: submission&.files })
      task.as_json.merge({ submission: submission, files: task.files })
    end
    record = record.as_json.merge({
                                    tasks: tasks,
                                    files: record.files,
                                    lesson: record.lesson&.template&.as_json&.merge({ quip_quiz_id: record.lesson&.template&.quip_quiz&.id }),
                                    lesson_documents: record.show_lesson_files ? record.lesson&.template&.documents&.where(for_pupil: true) || [] : []
                                  })

    render json: record
  end

  def show
    record = Homework.find(params[:id])
    authorize! :read, record
    render json: record.as_json.merge({
                                        created_by: record.created_by,
                                        pupils: record.pupils,
                                        lesson: record.lesson&.template&.as_json&.merge({ quip_quiz_id: record.lesson&.template&.quip_quiz&.id }),
                                        files: record.files,
                                        tasks: record.tasks.map { |task| task.as_json.merge({ submissions: task.submissions.accessible_by(current_ability), files: task.files }) },
                                      })
  end

  def destroy
    record = Homework.accessible_by(current_ability).find(params[:id])
    if record.published
      render json: { success: false, error: "Cannot delete published homework" }
      return
    end
    authorize! :destroy, record
    destroy_record record
  end

  def update
    record = Homework.find(params[:id])
    authorize! :update, record
    if (record.update(record_params))
      render json: { saved: true, data: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  def create
    record = Homework.new(record_params)
    authorize! :create, record
    record.school_id = current_user.school_id
    # prevents setting a lesson id the user doesn't have access too
    record.lesson_lesson_id = current_user.lessons.find_by(id: record_params[:lesson_lesson_id])&.id
    record.cover_image_fileboy_id = "2bc02a6c-23c7-467d-988f-9efe9a43a6fb"
    if record.save
      render json: { saved: true, data: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  def duplicate
    ActiveRecord::Base.transaction do
      homework = Homework.accessible_by(current_ability).where(created_by_id: current_user.id).find(params[:id])
      new_homework = homework.dup
      new_homework.published = false
      new_homework.title = "#{homework.title} (copy)"
      new_homework.save!
      homework.tasks.each do |task|
        new_task = task.dup
        new_task.homework_id = new_homework.id
        new_task.save!
        task.files.map(&:dup).each do |file|
          file.homework_task_id = new_task.id
          file.save!
        end
      end
      homework.files.map(&:dup).each do |file|
        file.homework_id = new_homework.id
        file.save!
      end
      new_homework.pupil_ids = homework.pupils.ids
      render json: { saved: true, data: new_homework }
    rescue => e
      render json: { saved: false, data: nil, error: e.message }
    end
  end

  def pupil_submit
    homework = Homework.find(params[:id])
    submissions = homework.submissions.where(user_id: current_user.id).where.not(complete_at: nil)

    if submissions.count != homework.tasks.count
      render json: { saved: false, error: "You must submit all tasks before submitting the homework" }
      return
    end

    submitDate = DateTime.now
    submissions.update_all(submitted_at: submitDate)
    render json: { saved: true, submitted_at: submitDate, submitted: submissions.count }
  end

  private

  def record_params
    params.permit(
      :title,
      :body,
      :cover_image_fileboy_id,
      :date_set,
      :date_due,
      :lesson_lesson_id,
      :published,
      :school_id,
      :show_lesson_files,
      :created_by_id,
      tasks: [:id, :title, :body, :task_type, :task_score, files: [:id, :fileboy_id, :name]],
      pupil_ids: [],
      files: [:id, :fileboy_id, :name],
    )
  end
end
