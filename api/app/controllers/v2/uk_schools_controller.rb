class V2::UkSchoolsController < V2::ApplicationController
  def index
    uk_schools = UkSchool.accessible_by(current_ability)

    if params[:ids].present?
      uk_schools = uk_schools.where(id: params[:ids].split(","))
    end

    if (params[:query].present?)
      uk_schools = uk_schools.where("uk_schools.name ILIKE :query OR uk_schools.postcode ILIKE :query", query: "%#{params[:query]}%")
    end

    if (params[:postcode].present?)
      uk_schools = uk_schools.where("regexp_replace(uk_schools.postcode, '\s', '', 'g') ILIKE regexp_replace(:query, '\s', '', 'g')", query: "#{params[:postcode]}%")
    end

    if (params[:sort] == "name ASC")
      uk_schools = uk_schools.order(name: :asc)
    elsif (params[:sort] == "name DESC")
      uk_schools = uk_schools.order(name: :desc)
    else
      uk_schools = uk_schools.order(name: :asc)
    end

    meta, records = paginate(uk_schools)

    render json: { records: records, meta: meta }
  end

  def schools_without_assignment
    schools = School.accessible_by(current_ability).where({ uk_school_id: nil, school_type: :generic })
    if (params[:query].present?)
      schools = schools.where("schools.name ILIKE :query OR schools.postcode ILIKE :query", query: "%#{params[:query]}%")
    end
    meta, records = paginate(schools.order({ name: :asc }))
    render json: { meta: meta, records: records }
  end

  def sign_up_attempts
    render json: UkSchool.where("conflict_sign_ups_count > 0").order(conflict_sign_ups_count: :desc).select(:id, :name, :conflict_sign_ups_count)
  end
end
