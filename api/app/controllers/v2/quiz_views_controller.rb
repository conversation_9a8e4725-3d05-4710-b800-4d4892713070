class V2::QuizViewsController < V2::ApplicationController
  include CampaignDemographicsFilter
  def index
    records = QuizView.accessible_by(current_ability).left_joins(quiz: :campaign_unit)

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    quiz_view = QuizView.new(campaign_quiz_view_params)
    authorize! :create, quiz_view
    if quiz_view.save
      render json: { saved: true, record: quiz_view }
    else
      render json: { saved: false, errors: quiz_view.errors.full_messages }
    end
  end

  def view_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = QuizView.left_joins(quiz: :campaign)
    if params[:campaignId].present?
      data = data.where("quizzes.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      data = data.where("campaigns.organisation_id = ?", params[:organisationId])
    end
    if start_date
      data = data.where("quiz_views.created_at < ?", start_date)
    end
    if end_date
      data = data.where("quiz_views.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)
    if params[:quizIds].present?
      data = data.where(quiz_id: params[:quizIds].split(","))
    end
    render json: data
  end

  def demographics_data
    data = QuizView.left_joins(quiz: :campaign, user: :school)
    if params[:campaignId].present?
      data = data.where("quizzes.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      data = data.where("campaigns.organisation_id = ?", params[:organisationId])
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
        result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
        result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
      result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end

  private

  def campaign_quiz_view_params
    params.permit(:quiz_id, :user_id)
  end
end
