class V2::QuestionnairesController < V2::ApplicationController
  def index
    questions = Questionnaire.accessible_by(current_ability)
    if params["query"].present?
      questions = questions.where("name ILIKE ?", "%#{params["query"]}%")
    end
    if params[:unpaginated] == "true"
      records = questions
    else
      meta, records = paginate(questions)
    end
    render json: { records: records, meta: meta }
  end

  def create
    authorize! :manage, Questionnaire
    questionnaire = Questionnaire.new(questionnaire_params)
    if questionnaire.save
      render json: { saved: true, message: 'success', record: questionnaire }
    else
      render json: { saved: false, message: questionnaire.errors.full_messages.join(", ") }
    end
  end

  def update
    questionnaire = Questionnaire.find(params[:id])
    authorize! :manage, questionnaire
    if questionnaire.update(questionnaire_params)
      render json: { saved: true, message: 'success', record: questionnaire }
    else
      render json: { saved: false, message: questionnaire.errors.full_messages.join(", ") }
    end
  end

  def show
    questionnaire = Questionnaire.find(params[:id])
    authorize! :manage, questionnaire
    render json: questionnaire
  end

  def take
    data = params.permit(:id, :is_onboarding_questionnaire)
    if data['is_onboarding_questionnaire']
      questionnaire = Questionnaire.where(published: true).find_by(is_onboarding_questionnaire: true)
    else
      questionnaire = Questionnaire.where(published: true).find(params.require(:id))
    end
    unless questionnaire.present?
      render json: nil
      return
    end
    questions = questionnaire.questionnaire_questions.order(weight: :asc).map do |question|
      question.as_json.merge({ options: question.questionnaire_options })
    end
    render json: { questionnaire: questionnaire, questions: questions }
  end

  def destroy
    questionnaire = Questionnaire.find(params[:id])
    authorize! :manage, questionnaire
    if questionnaire.destroy
      render json: { saved: true }
    else
      render json: { saved: false, message: questionnaire.errors.full_messages.join(", ") }
    end
  end

  def statistics
    id = params.require(:id)
    authorize! :manage, QuestionnaireQuestion
    data = QuestionnaireQuestion.where(questionnaire_id: id).order(:weight).map do |question|
      {
        question: question,
        options_with_data: question.options_with_data(params.permit(:startDate, :endDate)),
      }
    end
    render json: data
  end

  def taken_questionnaire
    unless current_user.present?
      render json: false
      return
    end
    questionnaire = Questionnaire.find_by_id(params.require(:id))
    render json: !!questionnaire.questionnaire_users.find_by(user_id: current_user.id)
  end

  def user_took_questionnaire
    id = params.require(:id)
    questionnaire = Questionnaire.find(id)
    if questionnaire.questionnaire_users.find_by(user_id: current_user.id)
      render json: true
      return
    end
    taken = questionnaire.questionnaire_users.new(user_id: current_user.id)
    render json: taken.save
  end

  def duplicate
    id = params.require(:id)
    questionnaire = Questionnaire.find(id)
    authorize! :manage, questionnaire
    new_questionnaire = questionnaire.duplicate
    if new_questionnaire
      render json: { saved: true, message: 'success', record: new_questionnaire }
    else
      render json: { saved: false, message: new_questionnaire.errors.full_messages.join(", ") }
    end
  end

  private

  def questionnaire_params
    params.permit(:name, :include_demographics_questions, :is_onboarding_questionnaire, :published, :new_library_unit_id)
  end
end
