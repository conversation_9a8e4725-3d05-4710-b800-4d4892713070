class V2::SlackController < V2::ApplicationController
  def send_log_message
    begin
      video_id = params[:videoId]
      url = params[:url]
      detail = params[:detail]
      user = current_user ? "#{current_user.id} | #{current_user.email}" : "Anonymous User"
      message_parts = [
        "Video Id:",
        video_id,
        "Location:",
        url,
        "User:",
        user,
      ].select { |str| str != nil }

      blocks = [
        {
          type: "section",
          text: {
            type: "mrkdwn",
            text: "*Video failed to load*",
          },
          fields: message_parts.map { |part|
            {
              "type": "mrkdwn",
              "text": part
            }
          }
        }
      ]

      if detail.present?
        blocks << { type: "section", text: { type: "mrkdwn", text: detail } }
      end

      client.chat_postMessage(
        channel: '#developing_experts',
        blocks: blocks
      )
      render json: { sent: true }
    rescue => e
      render json: { sent: false, error: e.message }
    end
  end

private

  def client
    @client ||= Slack::Web::Client.new(token: ENV['SLACK_BOT_ACCESS_TOKEN'])
  end
end
