class V2::ToursController < V2::ApplicationController

  def index
    records = Tour.accessible_by(current_ability).left_joins(:campaign, :organisation)

    if params[:availableToPupils].present?
      if params[:availableToPupils] == "true"
        records = records.where(available_to_pupils: true)
      elsif params[:availableToPupils] == "false"
        records = records.where(available_to_pupils: false)
      end
    end

    if params[:query].present?
      records = records.where("tours.name ilike ?", "%#{params[:query]}%")
    end

    if params[:campaignId].present?
      records = records.where(campaign_id: params[:campaignId])
    end

    if params[:organisationId].present?
      records = records.where(campaigns: { organisation_id:  params[:organisationId] })
    end

    if (params[:unpaginated] == "true")
      render json: { records: records }
    else
      meta, tours = paginate(records)
      render json: { records: tours, meta: meta }
    end
  end

  def show
    tour = Tour.accessible_by(current_ability).find(params[:id])

    authorize! :read, tour

    render json: tour
  end

  def create
    tour = Tour.new(tour_params)

    authorize! :create, tour

    if tour.save
      render json: { saved: true, record: tour }
    else
      render json: { saved: false, errors: tour.errors.full_messages }
    end
  end

  def update
    tour = Tour.find(params[:id])
    authorize! :update, tour
    if (tour.update(tour_params))
      render json: { saved: true, record: tour }
    else
      render json: { saved: false, errors: tour.errors.full_messages }
    end
  end

  private

  def tour_params
    params.permit(:campaign_id, :name, :fileboy_image_id, :available_to_pupils, data: {})
  end
end
