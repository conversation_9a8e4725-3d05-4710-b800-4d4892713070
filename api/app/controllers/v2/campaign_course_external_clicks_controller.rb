class V2::CampaignCourseExternalClicksController < V2::ApplicationController
  include CampaignDemographicsFilter
  

  def link_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = CampaignCourseExternalClick.left_joins(career_course: :organisation)
    if params[:campaignId].present?
      campaign = Camapaign.find(params[:campaignId])
      data = data.where("career_courses.organisation_id = ?", campaign.organisation_id)
    end
    if params[:organisationId].present?
      data = data.where("career_courses.organisation_id = ?", params[:organisationId])
    end
    if start_date
      data = data.where("campaign_course_external_clicks.created_at < ?", start_date)
    end
    if end_date
      data = data.where("campaign_course_external_clicks.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)
    if params[:careerCourseIds].present?
      data = data.where(career_course_id: params[:careerCourseIds].split(","))
    end
    render json: data
  end

  private

  def campaign_course_tracking_params
    params.permit(:user_id, :career_course_id)
  end
end
