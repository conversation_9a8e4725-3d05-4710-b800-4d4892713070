require 'lesson_plan_pdf'
class V2::User<PERSON>essonTemplatesController < V2::ApplicationController
  def index
    lesson_templates = Lesson::Template.accessible_by(current_ability).where(user_id: current_user.id)

    lesson_templates = lesson_templates.where(lesson_template_folder_id: nil) if params[:without_folder] == 'true'

    lesson_templates = lesson_templates.where('name ILIKE ? OR machine_name ILIKE ?', "%#{params[:query]}%", "%#{params[:query]}%") if params[:query]

    lesson_templates = if params[:order_recent]
                         # sort by templates/presentation/quiz_quiz updated at (whichever is the biggest)
                         lesson_templates.left_joins(:new_presentation, :quip_quiz).order(
                           'GREATEST(lesson_templates.updated_at, new_presentations.updated_at, quip_quizzes.updated_at) DESC'
                         )
                       else
                         lesson_templates.order(created_at: :desc)
                       end

    if params[:unpaginated] == 'true'
      render json: { records: lesson_templates.map { |template| template.as_json.merge({ new_presentation_index_id: template.new_presentation&.id }) } }
    else
      meta, records = paginate(lesson_templates)
      render json: { records: records.map { |template| template.as_json.merge({ new_presentation_index_id: template.new_presentation&.id }) }, meta: meta }
    end
  end

  def get_default
    template = Lesson::Template.find(params[:id])
    parent_template = template.source_template || template
    render json: parent_template.default_template_id_for_user(current_user)
  end

  def quip_quiz
    if !current_user || !current_user.can_create_lessons?
      render json: { message: 'Unauthorized' }, status: 401
    else
      quiz = Lesson::Template.find(params[:id]).quip_quiz
      if quiz
        authorize! :read, quiz
        quiz = quiz.as_json.merge({ questions: quiz.quip_questions })
        render json: quiz
      else
        render json: nil
      end
    end
  end

  def show
    if !current_user || !current_user.can_create_lessons?
      render json: { message: 'Unauthorized' }, status: 401
    else
      template = Lesson::Template.find(params[:id])
      authorize! :manage, template
      render json: template.data_for_show
    end
  end

  def create
    if !current_user || !current_user.can_create_lessons?
      render json: { message: 'Unauthorized' }, status: 401
    else
      data = params.permit(:name, :fileboy_image_id)
      previd = Lesson::Template.last.id
      machine_name = "ug_#{previd + 1}_#{current_user.id}"

      # Setting default author to the relevant author profile when creating a new template.
      author_record = current_user.employer? ? current_user.organisation&.author : current_user.author

      template = Lesson::Template.new(
        name: data[:name],
        fileboy_image_id: data[:fileboy_image_id],
        machine_name: machine_name,
        user: current_user,
        organisation: current_user.organisation,
        user_generated: true,
        use_new_presentation: true,
        author_ids: author_record ? [author_record.id] : []
      )
      if template.save
        render json: { saved: true, record: template }
      else
        render json: { saved: false, errors: template.errors.full_messages }
      end
    end
  end

  def update
    template = Lesson::Template.find(params[:id])
    authorize! :manage, template
    data = params.permit(
      :name,
      :fileboy_image_id,
      :new_lesson_plan_resources,
      objectives: [],
      author_ids: [],
      recommended_career_ids: []
    )

    data['use_2022_lesson_plan'] = true
    if template.update(data)
      render json: { saved: true, record: template }
    else
      render json: { saved: false, errors: template.errors.full_messages }
    end
  end

  def destroy
    template = Lesson::Template.find(params[:id])
    authorize! :manage, template
    if template.destroy
      render json: { saved: true }
    else
      render json: { saved: false, errors: template.errors.full_messages }
    end
  end

  def publish
    template = Lesson::Template.find(params[:id])
    authorize! :manage, template
    if template.update(available: true)
      render json: { published: true, saved: true }
    else
      render json: { saved: false, errors: template.errors.full_messages }
    end
  end

  def unpublish
    template = Lesson::Template.find(params[:id])
    authorize! :manage, template
    if template.update(available: false)
      render json: { published: false, saved: true }
    else
      render json: { saved: false, errors: template.errors.full_messages }
    end
  end

  def template_versions
    id = params.require(:id)
    template = Lesson::Template.find(id)
    authorize! :read, template
    original_template = template.source_template || template
    user_templates = original_template
                     .user_templates
                     .left_joins(:new_presentation, :user)
                     .where(user: current_user)
                     .order(:created_at)
    source_template_user = original_template.user_generated ? original_template.user ? { name: original_template.user.name } : nil : { name: 'Developing Experts' }
    render json: [
      {
        id: original_template.id,
        name: original_template.name,
        user: source_template_user,
        is_default: user_templates.where(is_default: true).none?,
        is_source: true,
      },
      *user_templates.map do |user_template|
        {
          id: user_template.id,
          name: user_template.name,
          user: user_template.user ? { name: user_template.user.name } : nil,
          is_default: user_template.is_default,
          is_source: false,
        }
      end
    ]
  end

  def duplicate
    if !current_user.can_create_lessons?
      render json: { message: 'Unauthorized' }, status: 401
    else
      template_id = params.require(:id)
      template = Lesson::Template.find(template_id)

      return render json: { message: 'Unauthorized' }, status: 401 if template.viewable_only

      previd = Lesson::Template.last.id
      machine_name = "ug_#{previd + 1}_#{current_user.id}"

      new_template = template.duplicate({ machine_name: machine_name, lite: true })

      # Setting default author to the relevant author profile when creating a new template.
      author_record = current_user.employer? ? current_user.organisation&.author : current_user.author

      new_template.update(
        machine_name: machine_name,
        user: current_user,
        organisation: current_user.organisation,
        user_generated: true,
        use_new_presentation: true,
        use_2022_lesson_plan: true,
        # legacy plan layout is not available for user generated templates
        lesson_plan_layout: template.lesson_plan_layout != 'legacy' ? template.lesson_plan_layout : 'ks1_3',
        author_ids: author_record ? [author_record.id] : [],
        created_at: DateTime.now,
        updated_at: DateTime.now,
        new_library_units: [],
        source_template_id: template_id
      )

      quiz_saved = { saved: true }
      if template.quip_quiz.present?
        quiz_saved = QuipQuiz.duplicate_from_local(template.quip_quiz, new_template)
      elsif template.quip_quiz_key.present?
        quiz_saved = QuipQuiz.duplicate_from_quip(template.quip_quiz_key, new_template)
      end

      errors = []
      errors << 'Failed to make a copy of the quiz' if quiz_saved.is_a?(Hash) && quiz_saved[:saved] == false

      render json: { saved: true, record: new_template, errors: errors }
    end
  end

  def duplicate_files
    template = Lesson::Template.find(params[:id])
    authorize! :manage, template
    ids = params[:template_ids]
    templates = Lesson::Template.accessible_by(current_ability).where(id: ids).where.not(id: template.id)
    templates.each do |lt|
      lt.documents.each do |org_document|
        document = org_document.dup
        document.template_id = template.id
        document.save
      end
    end
    render json: { saved: true }
  end

  def reset
    options = params.permit(
      :id,
      :lessonDetails,
      :lessonPlan,
      :presentation,
      :quiz,
      :documents,
      :keywords
    )
    template = Lesson::Template.accessible_by(current_ability).find(options[:id])
    authorize! :manage, template
    unless template.user_generated
      render json: { saved: false, message: 'Supplied template is not a user generated template' }
      return
    end
    source_template = template.source_template
    unless source_template
      render json: { saved: false, message: 'No source template found to restore from' }
      return
    end

    lesson_details_state = false
    if options[:lessonDetails]
      template.name = source_template.name
      template.fileboy_image_id = source_template.fileboy_image_id
      template.objectives = source_template.objectives
      template.recommended_career_ids = source_template.recommended_career_ids
      template.save
      lesson_details_state = true
    end

    lesson_plan_state = false
    if options[:lessonPlan]
      template.intent = source_template.intent
      template.new_lesson_plan_resources = source_template.new_lesson_plan_resources
      template.implementation = source_template.implementation
      template.impact_assessment = source_template.impact_assessment
      template.save
      lesson_plan_state = true
    end

    presentation_state = false
    if options[:presentation]
      template.new_presentation.slides.destroy_all if template.new_presentation
      template.new_presentation.destroy if template.new_presentation
      if source_template.new_presentation.present?
        new_presentation = source_template.new_presentation.dup
        new_presentation.lesson_template_id = template.id
        new_presentation.user = template.user
        new_presentation.save!

        source_template.new_presentation.slides.each do |slide|
          new_slide = slide.dup
          new_slide.presentation_id = new_presentation.id
          new_slide.save
        end
        presentation_state = true
      else
        # send state back as nil if there is no new_presentation, will clone the source_presentation
        # via the cloning method on front end as it requires converting slides etc
        presentation_state = nil
      end
    end

    quiz_state = false
    if options[:quiz] && (source_template.quip_quiz_key.present? || source_template.quip_quiz.present?)
      template.quip_quiz.destroy if template.quip_quiz

      if source_template.quip_quiz.present?
        QuipQuiz.duplicate_from_local(source_template.quip_quiz, template)
      elsif source_template.quip_quiz_key.present?
        QuipQuiz.duplicate_from_quip(source_template.quip_quiz_key, template)
      end

      quiz_state = true
    end

    documents_state = false
    if options[:documents]
      # delete and rebuild documents
      template.documents.destroy_all
      source_template.documents.each do |document|
        new_document = document.dup
        new_document.template_id = template.id
        new_document.save
      end

      documents_state = true
    end

    keywords_state = false
    if options[:keywords]
      template.keywords.destroy_all
      source_template.keywords.each do |keyword|
        new_keyword = keyword.dup
        new_keyword.template_id = template.id
        new_keyword.save
      end
      keywords_state = true
    end

    render json: {
      saved: true,
      lessonDetails: lesson_details_state,
      lessonPlan: lesson_plan_state,
      presentation: presentation_state,
      quiz: quiz_state,
      documents: documents_state,
      keywords: keywords_state,
      record: template,
    }
  end

  def set_as_default
    template = Lesson::Template.find_by(id: params[:id])
    # Get the original template to find the user templates from
    parent_template = template.user_generated ? template.source_template : template
    # Update all user templates for this user's school to is_default false (to prevent multiple default templates)
    child_templates_for_user = parent_template
                               .user_templates.left_joins(:user)
                               .where(user_generated: true, user: current_user)
    child_templates_for_user.update_all(is_default: false)
    template.update(is_default: true) if template.user_generated
    render json: { saved: true }
  end

  def presentation
    if !current_user.can_create_lessons?
      render json: { message: 'Unauthorized' }, status: 401
    else
      template = Lesson::Template.find(params[:id])
      if template.user_generated || (template.new_presentation.present? && template.new_presentation.published != 'unpublished')
        render json: template.new_presentation
        return
      end
      render json: nil
    end
  end
end
