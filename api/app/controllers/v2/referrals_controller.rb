class V2::ReferralsController < V2::ApplicationController
  def index
    collection = current_user.referred_users.select(:id, :name, :referral_share_name, :type, :deleted, :created_at)

    meta, records = paginate(collection)

    render json: { records: records, meta: meta }
  end

  def referrals_for
    user = User.find(params[:id])
    authorize! :read, user
    collection = user.referred_users.select(:id, :name, :referral_share_name, :type, :deleted, :created_at)

    meta, records = paginate(collection)
    render json: { records: records, meta: meta }
  end

  def my_data
    return render json: { count: 0, code: '', share_name: '' } unless current_user

    count = current_user.referred_users.size

    code = current_user.get_or_build_referral_code

    share_name = current_user.referral_share_name

    render json: { count: count, code: code, share_name: share_name }
  end
end
