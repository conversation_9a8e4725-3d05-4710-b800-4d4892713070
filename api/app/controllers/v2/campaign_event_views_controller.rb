class V2::CampaignEventViewsController < V2::ApplicationController
  include CampaignDemographicsFilter
  
  def demographics_data
    data = CampaignEventView.left_joins(event: :organisation, user: :school)
    if params[:campaignId].present?
      campaign = Camapaign.find(params[:campaignId])
      data = data.where("events.organisation_id = ?", campaign.organisation_id)
    end
    if params[:organisationId].present?
      data = data.where("events.organisation_id = ?", params[:organisationId])
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
        result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
        result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
      result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end

  private

  def campaign_course_tracking_params
    params.permit(:user_id, :event_id)
  end
end
