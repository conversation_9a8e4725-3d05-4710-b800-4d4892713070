class V2::ExemplarWorksController < V2::ApplicationController
  def index
    exemplar_work = ExemplarWork.accessible_by(current_ability)

    if (approver_id = params[:approver_id])&.present?
      exemplar_work = exemplar_work.where(approved_by: approver_id)
    end
    if (user_id = params[:user_id])&.present?
      exemplar_work = exemplar_work.where(user_id: user_id)
    end
    if (template_id = params[:template_id])&.present?
      exemplar_work = exemplar_work.where(lesson_template_id: template_id)
    end
    if (unit_id = params[:unit_id])&.present?
      exemplar_work = exemplar_work.where(new_library_unit_id: unit_id)
    end

    if (school_id = params[:school_id])&.present?
      exemplar_work = exemplar_work.left_joins(user: :school).where(school_id: school_id)
    end

    if (query = params[:query])&.present?
      exemplar_work = exemplar_work.joins(:user).where(
        "exemplar_works.title ilike :query OR users.name ilike :query OR users.email ilike :query", query: "%#{query}%"
      )
    end

    if (status = params[:status])&.present?
      exemplar_work = exemplar_work.where(status: status)
    end

    meta, records = paginate(exemplar_work, params[:per_page].present? ? params[:per_page] : 30)

    render json: { records: records, meta: meta }
  end

  def show
    exemplar_work = ExemplarWork.find(params[:id])
    authorize! :manage, exemplar_work
    render json: exemplar_work
  end

  def destroy
    exemplar_work = ExemplarWork.find(params[:id])
    authorize! :destroy, exemplar_work
    destroy_record exemplar_work
  end

  def update
    exemplar_work = ExemplarWork.find(params[:id])
    authorize! :update, exemplar_work
    if (exemplar_work.update(exemplar_work_params))
      render json: { saved: true, record: exemplar_work }
    else
      render json: { saved: false, errors: exemplar_work.errors.full_messages }
    end
  end

  def create
    exemplar_work = ExemplarWork.new(exemplar_work_params)
    authorize! :create, exemplar_work
    if exemplar_work.save
      render json: { saved: true, record: exemplar_work }
    else
      render json: { saved: false, errors: exemplar_work.errors.full_messages }
    end
  end

  private

  def exemplar_work_params
    params.permit(
      :display_name,
      :lesson_template_id,
      :new_library_unit_id,
      :user_id,
      :approved_by_id,
      :fileboy_id,
      :status,
      :title,
      :body,
    )
  end
end
