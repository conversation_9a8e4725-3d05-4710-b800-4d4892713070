class V2::QuizzesController < V2::ApplicationController

  def index
    records = Quiz.accessible_by(current_ability).left_joins(:campaign)

    if params[:query].present?
      records = records.where("quizzes.name ilike :query", query: "%#{params[:query]}%")
    end

    if params[:organisationId].present?
      records = records.where(campaigns: { organisation_id: params[:organisationId] })
    end

    if params[:campaignId].present?
      records = records.where(campaign_id: params[:campaignId])
    end

    if (params[:unpaginated] == "true")
      render json: { records: records }
    else
      meta, records = paginate(records)
      render json: { records: records, meta: meta }
    end
  end

  def show
    quiz = Quiz.accessible_by(current_ability).find(params[:id])

    authorize! :read, quiz

    render json: quiz
  end

  def create
    quiz = Quiz.new(camapign_quiz_params)

    authorize! :create, quiz

    if quiz.save
      render json: { saved: true, record: quiz }
    else
      render json: { saved: false, errors: quiz.errors.full_messages }
    end
  end

  def update
    quiz = Quiz.find(params[:id])
    authorize! :update, quiz
    if (quiz.update(camapign_quiz_params))
      render json: { saved: true, record: quiz }
    else
      render json: { saved: false, errors: quiz.errors.full_messages }
    end
  end

  private

  def camapign_quiz_params
    params.permit(:campaign_unit_id, :noodle_quiz_key, :name, :campaign_id)
  end
end
