class V2::NewLibraryCurriculaController < V2::ApplicationController
  before_action :set_curriculum, only: %i[show edit update destroy]

  def index
    collection = NewLibrary::Curriculum
      .joins(lesson_templates: :available_countries)
      .group(:id)
      .where(
        country: current_user&.country || Country.find(1),
        lesson_templates: { available: true },
        countries: { id: current_user&.country&.id || 1 },
      )

    if params[:curriculum_id].present?
      collection = collection.where(id: params[:curriculum_id])
    end

    list_with_response(
      collection,
      params,
      search_params: %w[new_library_curricula.name],
      allowed_scopes: %w[published],
      default_sort: { name: :asc },
    )
  end

  def show_data
    curriculum = NewLibrary::Curriculum.accessible_by(current_ability).find(params[:id])
    authorize! :read, curriculum
    render json: curriculum
  end

  def all_curricula
    curriculum = NewLibrary::Curriculum.accessible_by(current_ability).order(name: :asc)
    render json: curriculum
  end

  private

  def set_curriculum
    @curriculum = NewLibrary::Curriculum.find(params[:id])
  end
end
