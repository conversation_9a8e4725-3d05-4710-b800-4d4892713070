class V2::QuestionnaireQuestionsController < V2::ApplicationController
  def index
    questions = QuestionnaireQuestion.all.order(weight: :asc)
    if params["questionnaireId"].present?
      questions = questions.where(questionnaire_id: params["questionnaireId"])
    end
    questions = questions.map do |question|
      if params[:questions_only] == 'true'
        question
      else
        question.as_json.merge({ options: question.questionnaire_options })
      end
    end
    render json: questions
  end

  def update
    question = QuestionnaireQuestion.find(params[:id])
    authorize! :manage, question
    if question.update(question_params)
      render json: { saved: true, message: 'success', record: question }
    else
      render json: { saved: false, message: question.errors.full_messages.join(", ") }
    end
  end

  def create
    question = QuestionnaireQuestion.new(question_params)
    authorize! :manage, question
    unless question.weight.present?
      questionnaire = Questionnaire.find(question_params[:questionnaire_id])
      question.weight = questionnaire.questionnaire_questions.count
    end
    if question.save!(question_params)
      if question.freeText?
        QuestionnaireOption.create!(questionnaire_question: question, option_data: { value: 'free_text_answer' }, weight: 0)
      end
      render json: { saved: true, message: 'success', record: question }
    else
      render json: { saved: false, message: question.errors.full_messages.join(", ") }
    end
  end

  def destroy
    question = QuestionnaireQuestion.find(params[:id])
    authorize! :manage, question
    if question.destroy
      render json: { saved: true }
    else
      render json: { saved: false, message: question.errors.full_messages.join(", ") }
    end
  end

  def show
    question = QuestionnaireQuestion.find(params[:id])
    authorize! :manage, question
    render json: question.as_json.merge({
      options: question.questionnaire_options.map do |option|
        option.as_json.merge({ career_tags: option.career_tags })
      end
    })
  end

  def reorder
    data = params.require(:data)
    data.each do |question|
      QuestionnaireQuestion.find(question["id"]).update(weight: question["weight"])
    end
    render json: { saved: true }
  end

  def demographics_counties
    authorize! :manage, QuestionnaireQuestion
    authorize! :manage, QuestionnaireOption
    authorize! :manage, QuestionnaireAnswer
    question = QuestionnaireQuestion.find_by(id: params[:id])
    answers = question.questionnaire_answers.left_joins(user: { school: :uk_school })
    render json: answers.pluck("uk_schools.county").uniq.compact.sort + ['Not specified']
  end

  def demographics_stats
    param_data = params.permit(:ethnicity, :age_range_start, :age_range_end, :gender, :county, :date_from, :date_to)

    authorize! :manage, QuestionnaireQuestion
    authorize! :manage, QuestionnaireOption
    authorize! :manage, QuestionnaireAnswer

    sql_query = sql(<<-SQL)
      SELECT
        (SELECT row_to_json(questionnaire_answers.*)) as answer,
        COALESCE(users.gender, 'not specified') as gender,
        (SELECT CASE
          WHEN users.ethnicity = 0 THEN 'white'
          WHEN users.ethnicity = 1 THEN 'mixed_or_multiple_ethnic_groups'
          WHEN users.ethnicity = 2 THEN 'asian_or_asian_british'
          WHEN users.ethnicity = 3 THEN 'black_african_caribbean_or_black_british'
          WHEN users.ethnicity = 4 THEN 'other'
          WHEN users.ethnicity IS NULL THEN 'not specified'
        END) as ethnicity,
        users.dob as date_of_birth,
        users.id as user_id,
        COALESCE(uk_schools.county, 'Not specified') as county
      FROM questionnaire_answers
      LEFT JOIN users ON questionnaire_answers.user_id = users.id AND NOT users.deleted
      LEFT JOIN schools ON users.school_id = schools.id AND NOT schools.deleted
      LEFT JOIN uk_schools ON schools.uk_school_id = uk_schools.id
      WHERE questionnaire_answers.questionnaire_question_id = :question_id
      AND CASE WHEN :from_date IS NOT NULL THEN questionnaire_answers.created_at >= :from_date ELSE TRUE END
      AND CASE WHEN :to_date IS NOT NULL THEN questionnaire_answers.created_at <= :to_date ELSE TRUE END
      AND CASE WHEN :ethnicity IS NOT NULL THEN users.ethnicity = :ethnicity ELSE TRUE END
      AND CASE WHEN :gender IS NOT NULL THEN users.gender = :gender ELSE TRUE END
      AND CASE WHEN :age_range_start IS NOT NULL THEN users.dob <= :age_range_start ELSE TRUE END
      AND CASE WHEN :age_range_end IS NOT NULL THEN users.dob >= :age_range_end ELSE TRUE END
      AND CASE WHEN :county IS NOT NULL THEN uk_schools.county = :county ELSE TRUE END
    SQL
                .compact.assign(
                  question_id: params[:id].to_i,
                  from_date: params[:date_from].present? ? params[:date_from] : nil,
                  to_date: params[:date_to].present? ? params[:date_to] : nil,
                  ethnicity: param_data[:ethnicity].present? ? param_data[:ethnicity] : nil,
                  gender: param_data[:gender].present? ? param_data[:gender] : nil,
                  age_range_start: param_data[:age_range_start].present? ? param_data[:age_range_start] : nil,
                  age_range_end: param_data[:age_range_end].present? ? param_data[:age_range_end] : nil,
                  county: param_data[:county].present? ? param_data[:county] : nil
                )
    print sql_query
    data = JSONString.generate(sql(sql_query))

    render json: data
  end

  private

  def question_params
    params.permit(
      :question,
      :description,
      :weight,
      :question_type,
      :questionnaire_id,
      question_data: [:id, :name]
    )
  end
end
