class V2::IndustryTagsController < V2::ApplicationController
  def index
    industry_tags = IndustryTag.accessible_by(current_ability).includes(:lesson_templates)

    if (params[:ids].present?)
      industry_tags = industry_tags.where({ id: params[:ids].split(",") })
    end

    if (params[:query])
      industry_tags = industry_tags.where("industry_tags.name ILIKE ?", "%#{params[:query]}%")
    end

    meta, records = paginate(industry_tags)

    render json: { records: records, meta: meta }
  end

  def show
    industry_tag = IndustryTag.find(params[:id])
    authorize! :read, industry_tag
    render json: industry_tag
  end

  def update
    industry_tag = IndustryTag.find(params[:id])
    authorize! :update, industry_tag
    if (industry_tag.update(industry_tag_params))
      render json: { saved: true, record: industry_tag }
    else
      render json: { saved: false, errors: industry_tag.errors.full_messages }
    end
  end

  def destroy
    industry_tag = IndustryTag.find(params[:id])
    authorize! :destroy, industry_tag
    destroy_record industry_tag
  end

  def create
    industry_tag = IndustryTag.new(industry_tag_params)
    authorize! :create, industry_tag
    if industry_tag.save
      render json: { saved: true, record: industry_tag }
    else
      render json: { saved: false, errors: industry_tag.errors.full_messages }
    end
  end

  private

  def industry_tag_params
    params.permit(:name, :description)
  end
end
