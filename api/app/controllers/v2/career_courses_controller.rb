class V2::CareerCoursesController < V2::ApplicationController
  def index
    courses = CareerCourse.accessible_by(current_ability).joins(:organisation)

    if (params[:organisationId].present?)
      courses = courses.where({ organisation_id: params[:organisationId] })
    end

    if (params[:ids])
      courses = courses.where({ id: params[:ids].split(",") })
    end

    if (params[:query].present?)
      courses = courses.where("career_courses.name ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:organisations].present?)
      organisations = params[:organisations].to_s.split(';').map { |organisation| ApplicationRecord.escape organisation }
      courses = courses.where("lower(organisations.name) IN (#{organisations.join(',').downcase})")
    end

    if(params[:hideStarted].present? && params[:hideStarted] === "true")
      courses = courses.where("start_date > ?", Time.current)
    end

    if (params[:sort] == "name ASC")
      courses = courses.order(name: :asc)
    elsif (params[:sort] == "name DESC")
      courses = courses.order(name: :desc)
    elsif (params[:sort] == "date DESC")
      courses = courses.order(start_date: :desc)
    elsif (params[:sort] == "date ASC")
      courses = courses.order(start_date: :asc)
    else
      courses = courses.order(name: :asc)
    end

    if params[:sort] === "date RECENT"
      courses = courses.where("start_date >= ?", Time.current).order(start_date: :desc)
    end

    if (params[:unpaginated] == "true")
      render json: { records: courses }
    else
      meta, records = paginate(courses)
      render json: { records: records, meta: meta }
    end
  end

  def show
    course = CareerCourse.find(params[:id])
    authorize! :read, course
    render json: course
  end

  def update
    course = CareerCourse.find(params[:id])
    authorize! :update, course
    if (course.update(career_course_params))
      render json: { saved: true, record: course }
    else
      render json: { saved: false, errors: course.errors.full_messages }
    end
  end

  def destroy
    course = CareerCourse.find(params[:id])
    authorize! :destroy, course
    destroy_record course
  end

  def create
    course = CareerCourse.new(career_course_params)
    authorize! :create, course
    if course.save
      render json: { saved: true, record: course }
    else
      render json: { saved: false, errors: course.errors.full_messages }
    end
  end

  def related_careers
    course = CareerCourse.find(params[:id])
    careers = Career.accessible_by(current_ability)
    careers = careers.by_related_words(course.related_words)
    meta, records = paginate(careers)
    render json: { records: records, meta: meta }
  end

  def related_courses
    course = CareerCourse.find(params[:id])
    courses = CareerCourse.accessible_by(current_ability)
    courses = courses.by_related_words(course.related_words).where.not(id: course.id)
    meta, records = paginate(courses)
    render json: { records: records, meta: meta }
  end

  def related_vacancies
    course = CareerCourse.find(params[:id])
    vacancies = CareerVacancy.accessible_by(current_ability)
    vacancies = vacancies.by_related_words(course.related_words)
    meta, records = paginate(vacancies)
    render json: { records: records, meta: meta }
  end

  def related_events
    course = CareerCourse.find(params[:id])
    events = Event.accessible_by(current_ability)
    events = events.by_related_words(course.related_words)
    meta, records = paginate(events)
    render json: { records: records, meta: meta }
  end

  def related_lesson_templates
    course = CareerCourse.find(params[:id])
    lesson_templates = Lesson::Template.accessible_by(current_ability)
    lesson_templates = lesson_templates.by_related_words(course.related_words)
    meta, records = paginate(lesson_templates)
    render json: { records: records, meta: meta }
  end

  private

  def career_course_params
    params.permit(
      :name,
      :start_date,
      :body,
      :length,
      :organisation_id,
      :description,
      :location,
      :published,
      :fileboy_image_id,
      :video_url,
      :video_id,
      :external_url,
      career_tag_ids: [],
    )
  end
end
