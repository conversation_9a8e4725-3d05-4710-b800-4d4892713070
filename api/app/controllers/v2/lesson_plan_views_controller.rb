class V2::LessonPlanViewsController < V2::ApplicationController

  def create_for_self
    return render json: { success: false, error: 'User not found' } if current_user.blank?
    authorize! :create, LessonPlanView

    lesson_template = Lesson::Template.find(params.require(:lesson_template_id))
    record = TrackingService.track_lesson_plan_view(current_user, lesson_template)

    render json: { success: true, record: record }
  end

  private

  def lesson_plan_view_params
    params.permit(:user_id, :lesson_template_id)
  end
end
