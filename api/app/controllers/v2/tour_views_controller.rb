class V2::TourViewsController < V2::ApplicationController
  include CampaignDemographicsFilter


  def index
    records = TourView.accessible_by(current_ability).left_joins(:tour)

    if params[:query].present?
      records = records.where("tours.name ilike ? ", "%#{params[:query]}%")
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def view_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = TourView.left_joins(tour: :campaign)
    if params[:campaignId].present?
      data = data.where("tours.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      data = data.where("campaigns.organisation_id = ?", params[:organisationId])
    end
    if start_date
      data = data.where("tour_views.created_at < ?", start_date)
    end
    if end_date
      data = data.where("tour_views.created_at > ?", end_date)
    end
    if params[:tourIds].present?
      data = data.where(tour_id: { id: params[:tourIds].split(",") })
    end
    data = campaign_demographics_filter(data)
    render json: data
  end

  def demographics_data
    data = TourView.left_joins(tour: :campaign, user: :school)
    if params[:campaignId].present?
      data = data.where("tours.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      data = data.where("campaigns.organisation_id = ?", params[:organisationId])
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
        result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
        result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
        result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end

  private

  def tour_view_params
    params.permit(:user_id, :tour_id)
  end
end
