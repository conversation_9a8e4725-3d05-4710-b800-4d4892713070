class V2::CareersController < V2::ApplicationController
  def index
    careers = Career.accessible_by(current_ability)

    if (params[:ids])
      careers = careers.where({ id: params[:ids].split(",") })
    end

    if (params[:organisationId].present?)
      careers = careers.where({ organisation_id: params[:organisationId] })
    end

    if (params[:query].present?)
      careers = careers.where("careers.name ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:families].present?)
      families = params[:families].to_s.split(';').map { |family| ApplicationRecord.escape family }
      careers = careers.where("lower(careers.family) IN (#{families.join(',').downcase})")
    end

    if params[:organisationId_many].present?
      careers = careers.left_joins(:organisations, :organisation).scoping do
        Career.where(organisations: { id: params[:organisationId_many] })
        .or(Career.where(organisation_id: params[:organisationId_many]))
      end
    end


    if (params[:taggedWithAll])
      careers = careers.career_tagged_with_all(params[:taggedWithAll])
    end

    if (params[:taggedWithAny])
      careers = careers.career_tagged_with_any(params[:taggedWithAny])
    end

    salary_range = {"max_salary"=>100000, "min_salary"=>1}

    if (careers.ids.any?)
      salary_range = ApplicationRecord.sql(
        "SELECT MAX(careers.salary_to::INT) as max_salary, MIN(careers.salary_from::INT) as min_salary FROM careers WHERE id in (#{careers.ids.join(",")})"
      ).to_json.first

    end

    if (params[:salaryMin].present?)
      careers = careers.where("careers.salary_from::int >= ?", params[:salaryMin].to_i)
    end

    if (params[:salaryMax].present? && params[:salaryMax].to_i > 0)
      careers = careers.where("careers.salary_to::int <= ?", params[:salaryMax].to_i)
    end

    if (params[:sort] == "name ASC")
      careers = careers.order(name: :asc)
    elsif (params[:sort] == "name DESC")
      careers = careers.order(name: :desc)
    elsif (params[:sort] == "salary DESC")
      careers = careers.order(salary_from: :desc)
    elsif (params[:sort] == "salary ASC")
      careers = careers.order(salary_from: :asc)
    else
      careers = careers.order(name: :asc)
    end

    if (params[:unpaginated] == "true")
      render json: { records: careers }
    else
      meta, records = paginate(careers)
      render json: { records: records, meta: meta }
    end
  end

  def show
    career = Career.find(params[:id])
    authorize! :read, career

    TrackingService.track_career_view(career, current_user)
    render json: career
  end

  def update
    career = Career.find(params[:id])
    authorize! :update, career
    if (career.update(career_params))
      render json: { saved: true, record: career }
    else
      render json: { saved: false, errors: career.errors.full_messages }
    end
  end

  def destroy
    career = Career.find(params[:id])
    authorize! :destroy, career
    destroy_record career
  end

  def career_families
    careers = Career.accessible_by(current_ability)
    if params[:query].present?
      careers = careers.where("careers.family ILIKE ?", "%#{params[:query]}%")
    end
    render json: { families: careers.pluck("DISTINCT ON (lower(family)) family") }
  end

  def create
    career = Career.new(career_params)
    authorize! :create, career
    if career.save
      render json: { saved: true, record: career }
    else
      render json: { saved: false, errors: career.errors.full_messages }
    end
  end

  def related_careers
    career = Career.find(params[:id])
    careers = Career.accessible_by(current_ability)
    careers = careers.by_related_words(career.related_words).where.not(id: career)
    meta, records = paginate(careers)
    render json: { records: records, meta: meta }
  end

  def related_courses
    career = Career.find(params[:id])
    courses = CareerCourse.accessible_by(current_ability)
    courses = courses.by_related_words(career.related_words)
    meta, records = paginate(courses)
    render json: { records: records, meta: meta }
  end

  def related_vacancies
    career = Career.find(params[:id])
    vacancies = CareerVacancy.accessible_by(current_ability)
    vacancies = vacancies.by_related_words(career.related_words)
    meta, records = paginate(vacancies)
    render json: { records: records, meta: meta }
  end

  def related_events
    career = Career.find(params[:id])
    events = Event.accessible_by(current_ability)
    events = events.by_related_words(career.related_words)
    meta, records = paginate(events)
    render json: { records: records, meta: meta }
  end

  def related_lesson_templates
    career = Career.find(params[:id])
    lesson_templates = Lesson::Template.accessible_by(current_ability)
    lesson_templates = lesson_templates.by_related_words(career.related_words)
    meta, records = paginate(lesson_templates)
    render json: { records: records, meta: meta }
  end

  private

  def career_params
    params.permit(
      :name,
      :body,
      :fileboy_image_id,
      :competencies_technical,
      :competencies_non_technical,
      :work_location,
      :sectors,
      :keywords,
      :family,
      :qualifications,
      :salary_from,
      :salary_to,
      :definition,
      :complete,
      :tour_id,
      :organisation_id,
      :video_id,
      career_tag_ids: [],
    )
  end
end
