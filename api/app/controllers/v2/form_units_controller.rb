class V2::FormUnitsController < V2::ApplicationController
  def index
    dir = params[:sort_dir] == 'desc' ? :desc : :asc
    form_units = FormUnit.accessible_by(current_ability).joins(:new_library_unit).order(start_date: dir)


    if params[:user_id].present?
      user = User.find_by(id: params[:user_id])
      form_units = form_units.where(form_id: user.forms.ids)
    end

    if params[:start_date].present?
      form_units = form_units.where("start_date >= ?", params[:start_date])
    end
    if params[:end_date].present?
      form_units = form_units.where("end_date <= ?", params[:end_date])
    end
    if params[:active] == 'true'
      form_units = form_units.where("start_date <= :date AND end_date >= :date", date: Date.today)
    end

    if params[:query].present?
      form_units = form_units.where("new_library_units.name ILIKE ?", "%#{params[:query]}%")
    end

    if params[:form_id].present?
      form_units = form_units.where(form_id: params[:form_id])
    end

    if params[:unpaginated] == 'true'
      meta = {}
      records = form_units
    else
      meta, records = paginate(form_units)
    end

    render json: { records: records, meta: meta }
  end

  def active_upcoming_units
    user = User.find(params[:user_id])
    forms = user.forms
    forms = forms.where(id: params[:form_id]) if params[:form_id]

    data = forms.map do |form|
      # if we have an active lesson with a form unit, use that instead of the form unit dates
      active_lesson = form.lessons.joins(:form_unit).where('time <= ?', DateTime.now.end_of_day).last
      if active_lesson.present?
        upcoming_lesson = form.lessons.joins(:form_unit).where.not(form_unit_id: active_lesson.form_unit.id).where('time >= ?', DateTime.now.beginning_of_day).first
        {
          active: active_lesson.form_unit,
          upcoming: upcoming_lesson&.form_unit,
          form: form
        }
      else
        form_units = form.form_units
        active = form_units.where('start_date <= :date AND end_date >= :date', date: Date.today).order(start_date: :asc).first
        upcoming = form_units.where.not(id: active&.id).where('start_date >= :date', date: Date.today).order(start_date: :asc).first
        {
          active: active,
          upcoming: upcoming,
          form: form
        }
      end
    end

    render json: data
  end

  def show
    form_unit = FormUnit.accessible_by(current_ability).find(params[:id])

    authorize! :read, form_unit

    render json: form_unit
  end

  def forms_without
    forms = current_user.forms.left_joins(:form_units).having("COUNT(form_units.id) = 0").group("forms.id")
    render json: forms
  end

  private

  def form_unit_params
    params.permit(:start_date, :end_date, :form_id, :new_library_unit_id)
  end
end
