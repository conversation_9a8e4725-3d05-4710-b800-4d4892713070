class V2::PresentationsController < V2::ApplicationController

  def index
    presentations = NewPresentation.accessible_by(current_ability)

    if params[:query].present?
      presentations = presentations.where("name ILIKE ?", "%#{params[:query]}%")
    end
    if params[:lessonTemplateId].present?
      presentations = presentations.where(lesson_template_id: params[:lessonTemplateId])
    end

    meta, records = paginate(presentations)

    render json: { records: records, meta: meta }
  end

  def show
    record = NewPresentation.accessible_by(current_ability).find_by(id: params[:id])
    unless record.present?
      head :not_found
      return
    end
    authorize! :read, record

    render json: record
  end

  def create
    record = NewPresentation.new
    record.user = current_user
    record.name = presentation_params[:name]
    record.settings = presentation_params[:settings]

    record.lesson_template_id = params[:lesson_template_id] || nil

    slides_data = presentation_params[:slides]

    ActiveRecord::Base.transaction do
      authorize! :create, record

      unless record.save
        render json: { success: false, errors: record.errors.full_messages }
        raise ActiveRecord::Rollback
      end

      slides_data.each do |slide|
        record.slides.create!(slide)
      end

      render json: { success: true, record: record }
    end

  end

  def set_published_to
    record = NewPresentation.accessible_by(current_ability).find(params[:id])
    authorize! :update, record
    record.update(published: params[:published].to_i)
    render json: { published: record.published }
  end

  def update
    record = NewPresentation.accessible_by(current_ability).find(params[:id])

    authorize! :update, record

    record.name = presentation_params[:name]
    slides_data = presentation_params[:slides]
    record.settings = presentation_params[:settings]
    record.updated_at = DateTime.now

    ActiveRecord::Base.transaction do
      existing_slide_ids = record.slide_ids

      new_slides = slides_data.filter { |slide| !slide[:id] }
      new_slides.each do |slide|
        record.slides.create!(slide)
      end

      update_slides = slides_data.filter { |slide| slide[:id] }
      update_slides.each do |slide|
        record.slides.find(slide[:id]).update!(slide)
      end

      # any slide id we did not receive, we need to delete
      ids_to_destroy = existing_slide_ids - update_slides.map { |s| s[:id] }
      NewSlide.where(id: ids_to_destroy).destroy_all

      if record.save
        render json: { success: true, record: record }
      else
        render json: { success: false, errors: record.errors.full_messages }
        raise ActiveRecord::Rollback
      end
    end
  end

  def leave_review
    record = NewPresentation.accessible_by(current_ability).find(params[:id])
    params = feedback_params()
    feedback = NewPresentationFeedback.new({
      user: current_user,
      rating: params[:rating],
      feedback: params[:feedback],
      new_presentation_id: record.id
    })
    if (feedback.save())
      render json: { saved: true, record: feedback }
    else
      render json: { saved: false, errors: feedback.errors.full_messages }
    end
  end

  def presentation_with_reviews
    record = NewPresentation.accessible_by(current_ability).find(params[:id])

    authorize! :manage, record

    reviews = record.reviews.order(created_at: :desc)

    if params[:rating].present?
      reviews = reviews.where(rating: params[:rating])
    end

    if params[:with_feedback].present? && params[:with_feedback] == "true"
      reviews = reviews.where.not(feedback: "").where.not(feedback: nil)
    end

    render json: record.as_json.merge({ reviews: reviews })
  end

  private

  def feedback_params
    params.permit(:rating, :feedback)
  end

  def presentation_params
    params.permit(:name, slides: [:id, :weight, :type, data: {}], settings: {})
  end

end
