class V2::CampaignUnitExternalClicksController < V2::ApplicationController
  include CampaignDemographicsFilter
  def index
    records = CampaignUnitExternalClick.accessible_by(current_ability).left_joins(campaign_unit: :campaign)

    if params[:query].present?
      records = records.where("campaigns.name ilike ? ", "%#{params[:query]}%")
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    tracking = CampaignUnitExternalClick.new(campaign_unit_tracking_params)
    authorize! :create, tracking
    if tracking.save
      render json: { saved: true, record: tracking }
    else
      render json: { saved: false, errors: tracking.errors.full_messages }
    end
  end

  def link_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = CampaignUnitExternalClick.left_joins(campaign_unit: :new_library_units)
    if params[:campaignId].present?
      data = data.where("campaign_units.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      data = data.where("campaign_units.campaign_id in (?)", organisation.campaign_ids)
    end
    if start_date
      data = data.where("campaign_unit_external_clicks.created_at < ?", start_date)
    end
    if end_date
      data = data.where("campaign_unit_external_clicks.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)
    if params[:newLibraryUnitIds].present?
      data = data.where(new_library_units: { id: params[:newLibraryUnitIds].split(",") })
    end
    render json: data
  end

  private

  def campaign_unit_tracking_params
    params.permit(:user_id, :campaign_unit_id)
  end
end
