class V2::CareerVacanciesController < V2::ApplicationController
  def index
    vacancies = CareerVacancy.accessible_by(current_ability).joins(:organisation)

    if (params[:organisationId].present?)
      vacancies = vacancies.where({ organisation_id: params[:organisationId] })
    end

    if (params[:ids])
      vacancies = vacancies.where({ id: params[:ids].split(",") })
    end

    if (params[:query].present?)
      vacancies = vacancies.where("career_vacancies.name ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:categories].present?)
      categories = params[:categories].to_s.split(';').map { |category| ApplicationRecord.escape category }
      vacancies = vacancies.where("lower(career_vacancies.category) IN (#{categories.join(',').downcase})")
    end

    if (params[:organisations].present?)
      organisations = params[:organisations].to_s.split(';').map { |organisation| ApplicationRecord.escape organisation }
      vacancies = vacancies.where("lower(organisations.name) IN (#{organisations.join(',').downcase})")
    end

    if (params[:sort] == "name ASC")
      vacancies = vacancies.order(name: :asc)
    elsif (params[:sort] == "name DESC")
      vacancies = vacancies.order(name: :desc)
    elsif (params[:sort] == "salary DESC")
      vacancies = vacancies.order(salary_min: :desc)
    elsif (params[:sort] == "salary ASC")
      vacancies = vacancies.order(salary_min: :asc)
    elsif (params[:sort] == "date ASC")
      vacancies = vacancies.order(created_at: :asc)
    elsif (params[:sort] == "date DESC")
      vacancies = vacancies.order(created_at: :desc)
    else
      vacancies = vacancies.order(name: :asc)
    end

    if params[:sort] === "date RECENT"
      vacancies = vacancies.where("career_vacancies.created_at >= ?", 7.days.ago).order(created_at: :desc)
    end

    if (vacancies.ids.any?)
      salary_range = ApplicationRecord.sql("
        SELECT
        MAX(career_vacancies.salary_max::INT) as max_salary,
        MIN(career_vacancies.salary_min::INT) as min_salary
        FROM career_vacancies
        WHERE id in (#{vacancies.ids.join(",")})
      ").to_json.first
    end

    salary_range = {"max_salary"=>100000, "min_salary"=>1}

    if (params[:salaryMin].present?)
      vacancies = vacancies.where("career_vacancies.salary_min::int >= ?", params[:salaryMin].to_i)
    end

    if (params[:salaryMax].present? && params[:salaryMax].to_i > 0)
      vacancies = vacancies.where("career_vacancies.salary_max::int <= ?", params[:salaryMax].to_i)
    end
    if (params[:unpaginated] == "true")
      render json: { records: vacancies }
    else
      meta, records = paginate(vacancies)
      render json: { records: records, meta: meta, salary_range: salary_range }
    end
  end

  def show
    vacancy = CareerVacancy.find(params[:id])
    authorize! :read, vacancy
    render json: vacancy
  end

  def update
    vacancy = CareerVacancy.find(params[:id])
    authorize! :update, vacancy
    if (vacancy.update(career_vacancy_params))
      render json: { saved: true, record: vacancy }
    else
      render json: { saved: false, errors: vacancy.errors.full_messages }
    end
  end

  def destroy
    vacancy = CareerVacancy.find(params[:id])
    authorize! :destroy, vacancy
    destroy_record vacancy
  end

  def create
    vacancy = CareerVacancy.new(career_vacancy_params)
    authorize! :create, vacancy
    if vacancy.save
      render json: { saved: true, record: vacancy }
    else
      render json: { saved: false, errors: vacancy.errors.full_messages }
    end
  end

  def related_careers
    vacancy = CareerVacancy.find(params[:id])
    careers = Career.accessible_by(current_ability)
    careers = careers.by_related_words(vacancy.related_words)
    meta, records = paginate(careers)
    render json: { records: records, meta: meta }
  end

  def related_courses
    vacancy = CareerVacancy.find(params[:id])
    courses = CareerCourse.accessible_by(current_ability)
    courses = courses.by_related_words(vacancy.related_words)
    meta, records = paginate(courses)
    render json: { records: records, meta: meta }
  end

  def related_vacancies
    vacancy = CareerVacancy.find(params[:id])
    vacancies = CareerVacancy.accessible_by(current_ability)
    vacancies = vacancies.by_related_words(vacancy.related_words).where.not(id: vacancy.id)
    meta, records = paginate(vacancies)
    render json: { records: records, meta: meta }
  end

  def related_events
    vacancy = CareerVacancy.find(params[:id])
    events = Event.accessible_by(current_ability)
    events = events.by_related_words(vacancy.related_words)
    meta, records = paginate(events)
    render json: { records: records, meta: meta }
  end

  def related_lesson_templates
    vacancy = CareerVacancy.find(params[:id])
    lesson_templates = Lesson::Template.accessible_by(current_ability)
    lesson_templates = lesson_templates.by_related_words(vacancy.related_words)
    meta, records = paginate(lesson_templates)
    render json: { records: records, meta: meta }
  end

  def vacancy_categories
    vacancies = CareerVacancy.accessible_by(current_ability)
    if params[:query].present?
      vacancies = vacancies.where("career_vacancies.category ILIKE ?", "%#{params[:query]}%")
    end
    render json: { categories: vacancies.pluck("DISTINCT ON (lower(category)) category") }
  end

  private

  def career_vacancy_params
    params.permit(
      :name,
      :body,
      :description,
      :category,
      :organisation_id,
      :contract_type,
      :contract_time,
      :lat,
      :lng,
      :salary_min,
      :salary_max,
      :location,
      :origin,
      :video_id,
      :external_url,
      :fileboy_image_id,
      :published,
      career_tag_ids: [],
    )
  end
end
