class V2::LiveStreamDocumentsController < V2::ApplicationController
  def create
    document = LiveStreamDocument.new(live_stream_documents_params)
    authorize! :manage, document
    if document.save
      render json: { success: true, record: document }
    else
      render json: { success: false, errors: document.errors.full_messages }
    end
  end

  def update
    document = LiveStreamDocument.find(params.require(:id))
    authorize! :manage, document
    if document.update(live_stream_documents_params)
      render json: { success: true, record: document }
    else
      render json: { success: false, errors: document.errors.full_messages }
    end
  end

  def destroy
    document = LiveStreamDocument.find(params.require(:id))
    authorize! :destroy, document

    if document.destroy
      render json: { success: true }
    else
      render json: { success: false }
    end
  end

  def reorder
    authorize! :manage, LiveStreamDocument
    params.require(:data).each do |item|
      LiveStreamDocument.find(item['id']).update(weight: item['weight'])
    end
  end

  private

  def live_stream_documents_params
    params.permit(:fileboy_id, :name, :published, :live_stream_id)
  end
end
