class V2::LiveStreamMessagesController < V2::ApplicationController
  def index
    messages = LiveStreamMessage
               .accessible_by(current_ability)
               .where(is_reply: false)
               .order(created_at: :desc)

    messages = messages.where(pinned: true) if params[:pinned] == 'true'

    messages = messages.where(live_stream_id: params[:live_stream_id]) if params[:live_stream_id].present?

    if params[:unpaginated] == 'true'
      render json: { records: messages }
    else
      meta, records = paginate(messages)
      render json: { records: records, meta: meta }
    end
  end

  def create
    message = LiveStreamMessage.new(live_stream_message_params)
    message.user = current_user
    authorize! :create, message

    if message.save
      render json: { success: true, record: message }
    else
      render json: { success: false, errors: message.errors.full_messages }
    end
  end

  def reply
    original_message = LiveStreamMessage.find(params.require(:id))
    message = LiveStreamMessage.new(live_stream_message_params)
    message.live_stream_message_id = original_message.id
    message.live_stream_id = original_message.live_stream_id
    message.is_reply = true
    message.user = current_user

    authorize! :manage, message

    if message.save
      render json: { success: true, record: message }
    else
      render json: { success: false, errors: message.errors.full_messages }
    end
  end

  private

  def live_stream_message_params
    params.permit(:live_stream_id, :message)
  end
end
