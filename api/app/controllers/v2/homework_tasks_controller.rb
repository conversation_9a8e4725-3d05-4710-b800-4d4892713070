class V2::HomeworkTasksController < V2::ApplicationController
  def index
    collection = HomeworkTask.accessible_by(current_ability)

    if (params[:query].present?)
      collection = collection.where("title ILIKE ?", "%#{params[:query]}%")
    end

    if params[:homework_id].present?
      collection = collection.where(homework_id: params[:homework_id])
    end

    if params[:unpaginated] == "true"
      render json: { records: collection }
      return
    end

    meta, records = paginate(collection)
    render json: { records: records, meta: meta }
  end

  def show
    record = HomeworkTask.find(params[:id])
    authorize! :read, record
    render json: record
  end

  def destroy
    record = HomeworkTask.find(params[:id])
    authorize! :destroy, record
    destroy_record record
  end

  def update
    record = HomeworkTask.find(params[:id])
    authorize! :update, record
    record.quiz_question_data = params[:quiz_question_data] || nil
    if (record.update(record_params))
      render json: { saved: true, data: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  def create
    record = HomeworkTask.new(record_params.except(:files))
    authorize! :create, record
    record.quiz_question_data = params[:quiz_question_data] || nil
    if record.save
      # have to do files after, otherwise files.create errors
      record.update(files: record_params[:files])
      render json: { saved: true, data: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  private

  def record_params
    params.permit(:homework_id, :id, :title, :body, :task_type, :task_score, files: [:id, :fileboy_id, :name])
  end
end
