class V2::AuthorsController < V2::ApplicationController
  def index
    collection = Author.where(user_id: nil)

    if (template_ids = params[:template_ids])
      author_ids = Lesson::Template.where(id: template_ids.split(",")).joins(:authors).pluck('authors.id').uniq
      collection = collection.where(id: author_ids)
    end

    render json: { records: collection }
  end

  def update
    author = Author.find(params[:id])
    authorize! :update, author
    if (author.update(author_params))
      render json: { saved: true, record: author }
    else
      render json: { saved: false, errors: author.errors.full_messages }
    end
  end

  def create
    author = Author.new(author_params)
    author.user = current_user
    author.organisation = current_user.organisation

    authorize! :create, author

    if author.save
      render json: { saved: true, record: author }
    else
      render json: { saved: false, errors: author.errors.full_messages }
    end
  end

  def my_author
    render json: current_user&.author
  end

  def my_organisation_author
    render json: current_user&.organisation&.author
  end

  private

  def author_params
    params.permit(
      :name,
      :title,
      :body,
      :fileboy_image_id,
    )
  end
end
