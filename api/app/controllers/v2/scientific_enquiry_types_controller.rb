class V2::ScientificEnquiryTypesController < V2::ApplicationController
  def index
    types = ScientificEnquiryType.accessible_by(current_ability)

    if (ids = (params[:ids]&.values || [])) && ids.present? && ids.length > 0
      types = types.where(id: ids)
    end

    if params[:query].present?
      types = types.where("title ILIKE ?", "%#{params[:query]}%")
    end

    if params[:unpaginated].present? && params[:unpaginated].present? == "true"
      render json: { records: types }
    else
      meta, records = paginate(types)
      render json: { records: records, meta: meta }
    end
  end

  def create
    stype = ScientificEnquiryType.new(scientific_enquiry_type_params)
    authorize! :create, stype
    if stype.save
      render json: { saved: true, record: stype }
    else
      render json: { saved: false, errors: stype.errors.full_messages }
    end
  end

  private

  def scientific_enquiry_type_params
    params.permit(:title, :body, :fileboy_icon_id, lesson_template_ids: [])
  end
end
