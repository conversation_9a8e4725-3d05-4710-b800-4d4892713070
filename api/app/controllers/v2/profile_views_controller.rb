class V2::ProfileViewsController < V2::ApplicationController
  def index
    records = ProfileView.accessible_by(current_ability).left_joins(:organisation, :campaign)

    if params[:query].present?
      records = records.where("campaigns.name ilike ? ", "%#{params[:query]}%")
    end

    if params[:organisationId].present?
      records = records.where(organisation_id: params[:organisationId])
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    profile_view = ProfileView.new(profile_view_params)
    profile_view.campaign = Campaign.where(organisation: profile_view.organisation).last if !profile_view.campaign_id
    authorize! :create, profile_view
    if profile_view.save
      render json: { saved: true, record: profile_view }
    else
      render json: { saved: false, errors: profile_view.errors.full_messages }
    end
  end

  private

  def profile_view_params
    params.permit(:user_id, :organisation_id, :campaign_id)
  end
end
