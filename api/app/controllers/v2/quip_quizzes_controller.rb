class V2::QuipQuizzesController < V2::ApplicationController
  def show
    quiz = QuipQuiz.find(params[:id])
    authorize! :read, quiz
    quiz = quiz.as_json().merge({ questions: quiz.quip_questions })
    render json: quiz
  end

  def update
    quiz = QuipQuiz.find(params[:id])
    authorize! :update, quiz
    if (quiz.update(quip_quiz_params))
      render json: { saved: true, record: quiz }
    else
      render json: { saved: false, errors: quiz.errors.full_messages }
    end
  end

  def destroy
    quiz = QuipQuiz.find(params[:id])
    authorize! :destroy, quiz
    destroy_record quiz
  end

  def build_quiz
    ActiveRecord::Base.transaction do
      template_id = params.required(:lesson_template_id)
      template = Lesson::Template.find(template_id)
      quiz = QuipQuiz.new(name: template.name, lesson_template_id: template_id)
      quiz.user_id = current_user.id
      quiz.organisation_id = current_user.organisation&.id
      quiz.user_generated = true

      quiz.save!

      params[:questions].map do |question|
        q = QuipQuestion.new(question.permit(:question_type, :weight, data_json: {}))
        q.quip_quiz_id = quiz.id
        q.save!
      end

      render json: { saved: true, record: quiz }
    rescue => e
      puts e
      render json: { saved: false, error: e.full_messages }
    end
  end

  def update_quiz
    ActiveRecord::Base.transaction do
      quiz = QuipQuiz.find(params[:id])
      template_id = params.required(:lesson_template_id)
      template = Lesson::Template.find(template_id)
      quiz.update(name: template.name, lesson_template_id: template_id)
      quiz.user_id = current_user.id
      quiz.organisation_id = current_user.organisation&.id
      quiz.user_generated = true

      quiz.save!

      params[:questions].map do |question|
        if question[:id].present?
          if question[:destroy]
            QuipQuestion.find(question[:id]).destroy()
          else
            q = QuipQuestion.find(question[:id])
            q.update(question.permit(:question_type, :weight, data_json: {}))
          end
        else
          q = QuipQuestion.new(question.permit(:question_type, :weight, data_json: {}))
          q.quip_quiz_id = quiz.id
          q.save!
        end
      end

      render json: { saved: true, record: quiz }
    rescue => e
      puts e
      render json: { saved: false, error: e.full_messages }
    end
  end

  private

  def quip_quiz_params
    params.permit(
      :name,
      :organisation_id,
      :user_id,
      :lesson_template_id,
      :published,
      quip_question_ids: [],
    )
  end
end
