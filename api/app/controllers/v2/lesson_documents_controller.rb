class V2::LessonDocumentsController < V2::ApplicationController
  def index
    collection = Lesson::Document.accessible_by(current_ability).all

    if (template_id = params[:template_id])
      collection = Lesson::Template.find(template_id).documents
    end

    meta, records = paginate(collection)
    render json: { records: records, meta: meta }
  end

  def update
    document = Lesson::Document.find(params[:id])
    authorize! :update, document
    if (document.update(document_params))
      render json: { saved: true, record: document }
    else
      render json: { saved: false, errors: document.errors.full_messages }
    end
  end

  def create
    document = Lesson::Document.new(document_params)
    authorize! :create, document

    if document.save
      render json: { saved: true, record: document }
    else
      render json: { saved: false, errors: document.errors.full_messages }
    end
  end

  def destroy
    document = Lesson::Document.find(params[:id])
    authorize! :destroy, document
    destroy_record document
  end

  private

  def set_document
    @document = Lesson::Document.find(params[:id])
  end

  def document_params
    params.permit(
      :template_id,
      :name,
      :body,
      :fileboy_file_id,
    )
  end
end
