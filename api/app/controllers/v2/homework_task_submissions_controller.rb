class V2::HomeworkTaskSubmissionsController < V2::ApplicationController
  def index
    collection = HomeworkTaskSubmission.accessible_by(current_ability)

    if (params[:query].present?)
      collection = collection.where("title ILIKE ?", "%#{params[:query]}%")
    end

    if params[:homework_id].present?
      collection = collection.join(:homework).where("homeworks.id = ?", params[:homework_id])
    end

    if params[:homework_task_id].present?
      collection = collection.where(homework_task_id: params[:homework_task_id])
    end

    if params[:unpaginated] == "true"
      render json: { records: collection }
      return
    end

    meta, records = paginate(collection)
    render json: { records: records, meta: meta }
  end

  def show
    record = HomeworkTaskSubmission.find(params[:id])
    authorize! :read, record
    render json: record
  end

  def destroy
    record = HomeworkTaskSubmission.find(params[:id])
    authorize! :destroy, record
    destroy_record record
  end

  def update
    record = HomeworkTaskSubmission.find(params[:id])
    record.quiz_result = params[:quiz_result] || record.quiz_result || nil
    authorize! :update, record
    if (record.update(record_params))
      render json: { saved: true, data: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  def create
    record = HomeworkTaskSubmission.new(record_params)
    record.quiz_result = params[:quiz_result] || nil
    authorize! :create, record
    if record.save
      render json: { saved: true, data: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  private

  def record_params
    params.permit(:user_id, :homework_task_id, :body, :complete_at, :score, :notes, files: [:fileboy_id, :id, :name])
  end
end
