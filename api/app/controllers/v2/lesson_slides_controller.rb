class V2::LessonSlidesController < V2::ApplicationController

  def for_end_of_unit_assessment
    unit = NewLibrary::Unit.find(params[:unitId])
    slides = Lesson::Slide.where(slide_type: "quiz", template_id: unit.lesson_template_ids, end_of_unit_assessment: true)
    render json: slides
  end

  def show
    slide = Lesson::Slide.find(params[:id])
    authorize! :read, slide
    render json: slide
  end

  def narration
    slide = Lesson::Slide.find(params[:id])
    authorize! :read, slide
    render json: slide.get_narration_audio(current_locale)
  end

  def set_as_current
    slide = Lesson::Slide.find(params[:id])
    PresentationProgress.set_slide(current_user.id, slide.template_id, slide.id) if current_user.present?
    render json: {}
  end
end
