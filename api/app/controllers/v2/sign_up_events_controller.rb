class V2::SignUpEventsController < V2::ApplicationController

  def index
    events = SignUpEvent.accessible_by(current_ability)

    if params[:query].present?
      events = events.where("name ILIKE ?", "%#{params[:query]}%")
    end

    if params[:published] == 'true'
      events = events.where(published: true)
    end

    if params[:unpaginated]
      render json: { records: events }
    else
      meta, records = paginate(events)
      render json: { records: records, meta: meta }
    end
  end

  def create
    event = SignUpEvent.new(event_params)

    authorize! :create, event

    if event.save
      render json: { saved: true, data: event }
    else
      render json: { saved: false, errors: event.errors.full_messages }
    end
  end

  def destroy
    event = SignUpEvent.accessible_by(current_ability).find(params[:id])
    authorize! :destroy,  event
    destroy_record  event
  end

  private

  def event_params
    params.permit(:name, :published)
  end
end
