class V2::PupilsController < V2::ApplicationController
  def index
    pupils = Pupil.accessible_by(current_ability).includes(:teachers, :school)

    if (params[:userId])
      pupils = pupils.where({ teachers_users: { id: params[:userId] } })
    end

    if (params[:schoolId])
      pupils = pupils.where({ school_id: params[:schoolId] })
    end

    if params[:ids].present?
      pupils = collection.where({ id: params[:ids].split(",") })
    end

    if (params[:query])
      pupils = pupils.where("users.name ILIKE ?", "%#{params[:query]}%")
    end

    meta, records = paginate(pupils)

    render json: { records: records, meta: meta }
  end

  def show
    pupil = Pupil.find(params[:id])
    authorize! :read, pupil
    render json: pupil
  end

  def update
    pupil = Pupil.find(params[:id])
    authorize! :update, pupil
    if (pupil.update(pupil_params))
      render json: { saved: true, record: pupil }
    else
      render json: { saved: false, errors: pupil.errors.full_messages }
    end
  end

  def destroy
    pupil = Pupil.find(params[:id])
    authorize! :destroy, pupil
    destroy_record pupil
  end

  def login_code_csv
    pupils = Pupil.accessible_by(current_ability)

    if params[:school_id].present?
      pupils = pupils.where(school_id: params[:school_id])
    end

    if params[:teacher_id].present?
      pupils = Teacher.find(params[:teacher_id]).pupils.group(:id).order("users.name ASC").select('users.name AS "Name"', 'users.identifier AS "Login Code"')
      render json: { csv: pupils.to_csv }
      return
    end

    if params[:form_id].present?
      pupils = pupils
        .joins(:forms)
        .where(forms: { id: params[:form_id] })
        .group(:id)
        .order("users.name ASC")
        .select('users.name AS "Name"', 'users.identifier AS "Login Code"')
    else
      pupils = pupils
        .joins(:forms)
        .order("forms.name ASC", "users.name ASC")
        .select('forms.name AS "Class"', 'users.name AS "Name"', 'users.identifier AS "Login Code"')
    end

    render json: { csv: pupils.to_csv }
  end

  def my_tracking_data
    authorize! :read, current_user
    render json: current_user.as_json.merge({
      tracking_rocket_words: current_user.tracking_rocket_words,
      tracking_films: current_user.tracking_films,
      tracking_word_searches: current_user.tracking_word_searches,
      tracking_summative_quizzes: current_user.tracking_summative_quizzes,
      tracking_link_trackings: current_user.tracking_link_trackings,
      tracking_documents: current_user.tracking_documents,
      tracking_lesson_template_views: current_user.tracking_lesson_template_views,
      tracking_lesson_template_favourites: current_user.tracking_lesson_template_favourites,
      tracking_presentation_views: current_user.tracking_presentation_views,
    })
  end

  def remove_guardian
    pupil = Pupil.find(params[:id])

    if pupil.guardian_ids.include?(params[:guardianId])
      next_guardian_ids = pupil.guardian_ids
      next_guardian_ids -= [params[:guardianId]]

      pupil.update!(guardian_ids: next_guardian_ids)
    end

    render json: { saved: true }

  rescue => e
    render json: { saved: false, errors: [e.message]  }
  end

  def guardian_ids
    pupil = Pupil.find(params[:id])
    authorize! :read, pupil

    render json: { guardian_ids: pupil.guardians.ids }
  end

  def qr_signin_link
    render json: { qr_code: Pupil.find(params[:id]).qr_signin_link }
  end

  def active_homework
    pupil = Pupil.find(params[:id])
    authorize! :read, pupil
    render json: pupil.active_homework.map { |record| record.for_user(pupil) }
  end

  private

  def pupil_params
    params.permit(
      :name,
      :identifier,
      :dob,
      :gender,
      :ethnicity,
      :location,
      :alias,
      :fileboy_background_image_id,
      avatar_configuration: {},
      profile_color: {}
    )
  end
end
