class V2::LessonsController < V2::ApplicationController
  def index
    lessons = Lesson::Lesson.accessible_by(current_ability).includes(:pupils, :template, :form)

    if (params[:userId])
      lessons = lessons.where({ users: { id: params[:userId] } })
    end

    if (params[:teacherId])
      lessons = lessons.joins(:teachers).where({ users: { id: params[:teacherId] } })
    end

    if (params[:template_id].present?)
      lessons = lessons.where({ template_id: params[:template_id] })
    end

    if (params[:templateIds].present?)
      ids = params[:templateIds].split(",").map(&:to_i)
      lessons = lessons.where({ template_id: ids })
    end

    if (params[:query])
      lessons = lessons.joins(:template).where("lesson_templates.name ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:from_date].present?)
      lessons = lessons.where("time >= ?", params[:from_date])
    end

    if (params[:unpaginated] == "true")
      render json: { records: lessons }
      return
    end

    meta, records = paginate(lessons)

    render json: { records: records, meta: meta }
  end

  def my_lessons
    lessons = current_user.lessons
    if (params[:from_date].present?)
      lessons = lessons.where("time >= ?", params[:from_date])
    end
    render json: { records: lessons }
  end

  def show
    lesson = Lesson::Lesson.find_by(id: params[:id])

    unless lesson.present?
      return render json: { error: 'Not found' }, status: 404
    end

    authorize! :read, lesson

    render json: {
      lesson: lesson,
      template: lesson.template.as_json.merge({ quip_quiz_id: lesson.template.quip_quiz&.id }),
      form: lesson.form,
      homeworks: lesson.homeworks.accessible_by(current_ability).where("date_due >= ?", DateTime.now).map { |hw|
        hw.as_json.merge({
          tasks: hw.tasks.map { |task|
            task.as_json.merge({ submission: task.submissions.find_by(user_id: current_user.id) })
          }
        })
      },
      progress: {
        presentation_viewed: current_user.type === "Pupil" ? !!current_user.tracking_presentation_views.find_by(lesson_id: lesson.id) : false,
        rocket_word_quiz: current_user.type === "Pupil" ? !!current_user.tracking_rocket_words.find_by(lesson_id: lesson.id) : false,
        summative_quiz: current_user.type === "Pupil" ? !!current_user.tracking_summative_quizzes.find_by(lesson_id: lesson.id) : false,
        tracking_word_search: current_user.type === "Pupil" ? !!current_user.tracking_word_searches.find_by(lesson_id: lesson.id) : false,
      }
    }
  end

  def toggle_taught
    lesson = current_user.admin? ? Lesson::Lesson.find(params[:id]) : current_user.lessons.find(params[:id])
    lesson.update(lesson_taught: !lesson.lesson_taught)
    render json: lesson
  end

  def generate_presentation_with_ai
    lesson = Lesson::Template.find(params[:id])
    authorize! :update, lesson
    PresentationGenerator.new.generate_for_lesson(params[:id])
    render json: { success: true }
  end

  def generate_keywords_with_ai
    lesson = Lesson::Template.find(params[:id])
    authorize! :update, lesson
    LessonKeywordsGenerator.new.generate_for_lesson(params[:id])
    render json: { success: true }
  end

  def generate_keyword_from_name
    id = params[:id]
    name = params[:name]
    if name.blank?
      render json: { error: "Name is required" }, status: :bad_request
      return
    end
    data = LessonKeywordsGenerator.new.generate_content_from_name(id, name)
    render json: data
  end
end
