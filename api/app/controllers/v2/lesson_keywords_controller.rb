class V2::LessonKeywordsController < V2::ApplicationController
  def index
    collection = Lesson::Keyword.accessible_by(current_ability).all

    if (template_id = params[:template_id])
      collection = Lesson::Template.find(template_id).keywords
    end

    meta, records = paginate(collection)

    if params[:with_question_data] == 'true'
      records = records.map(&:v1_as_json)
    end

    render json: { records: records, meta: meta }
  end

  def update
    keyword = Lesson::Keyword.find(params[:id])
    authorize! :update, keyword
    if (keyword.update(keyword_params))
      render json: { saved: true, record: keyword }
    else
      render json: { saved: false, errors: keyword.errors.full_messages }
    end
  end

  def create
    keyword = Lesson::Keyword.new(keyword_params)
    authorize! :create, keyword

    if keyword.save
      render json: { saved: true, record: keyword }
    else
      render json: { saved: false, errors: keyword.errors.full_messages }
    end
  end

  def destroy
    keyword = Lesson::Keyword.find(params[:id])
    authorize! :destroy, keyword
    destroy_record keyword
  end

  def update_order
    params[:keyword_ids].each_with_index do |id, i|
      Lesson::Keyword.find(id).update(weight: i)
    end
    render json: { saved: true }
  end

  def narration
    keyword = Lesson::Keyword.find(params[:id])

    # Cache the audio data based on keyword id and locale
    narration_audio = custom_cache.fetch("keyword/#{params[:id]}/narration/#{current_locale}", expires_in: 12.hours) do
      keyword.get_narration_audio(current_locale)
    end

    render json: narration_audio
  end

  private

  def set_keyword
    @keyword = Lesson::Keyword.find(params[:id])
  end

  def keyword_params
    params.permit(
      :template_id,
      :name,
      :body,
      :weight,
      :fileboy_image_id,
      quiz_question_attributes: [
        :id,
        :body,
        :lesson_template_id,
        :answer,
        :lesson_slide_id,
        :use_data,
        :question_body,
        :question_video_url,
        :question_fileboy_image_id,
      ]
    )
  end
end
