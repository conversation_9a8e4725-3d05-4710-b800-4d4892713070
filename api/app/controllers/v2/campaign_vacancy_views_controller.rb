class V2::CampaignVacancyViewsController < V2::ApplicationController
  include CampaignDemographicsFilter
  def index
    records = CampaignVacancyView.accessible_by(current_ability).left_joins(career_vacancy: { organisation: :campaigns })

    if params[:query].present?
      records = records.where("campaigns.name ilike ? ", "%#{params[:query]}%")
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    tracking = CampaignVacancyView.new(campaign_vacancy_tracking_params)
    authorize! :create, tracking
    if tracking.save
      render json: { saved: true, record: tracking }
    else
      render json: { saved: false, errors: tracking.errors.full_messages }
    end
  end

  def view_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = CampaignVacancyView.left_joins(career_vacancy: :organisation)
    if params[:campaignId].present?
      campaign = Camapaign.find(params[:campaignId])
      data = data.where("career_vacancies.organisation_id = ?", campaign.organisation_id)
    end
    if params[:organisationId].present?
      data = data.where("career_vacancies.organisation_id = ?", params[:organisationId])
    end
    if params[:careerVacancyIds].present?
      data = data.where(career_vacancy_id: params[:careerVacancyIds].split(","))
    end
    if start_date
      data = data.where("campaign_vacancy_views.created_at < ?", start_date)
    end
    if end_date
      data = data.where("campaign_vacancy_views.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)
    render json: data
  end

  def demographics_data
    data = CampaignVacancyView.left_joins(career_vacancy: :organisation, user: :school)
    if params[:campaignId].present?
      campaign = Camapaign.find(params[:campaignId])
      data = data.where("career_vacancies.organisation_id = ?", campaign.organisation_id)
    end
    if params[:organisationId].present?
      data = data.where("career_vacancies.organisation_id = ?", params[:organisationId])
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
        result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
        result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
      result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end


  private

  def campaign_vacancy_tracking_params
    params.permit(:user_id, :career_vacancy_id)
  end
end
