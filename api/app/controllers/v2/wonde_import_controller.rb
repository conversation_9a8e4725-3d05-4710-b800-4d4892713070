class V2::WondeImportController < V2::ApplicationController
  def wonde_import_forms
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    forms = import_record.wonde_import_forms
    if params[:ids].present?
      forms = forms.where({ id: params[:ids].split(",") })
    end
    if params[:query].present?
      forms = forms.left_joins(:form).where("wonde_import_forms.name ILIKE :query", query: "%#{params[:query]}%")
    end
    meta, records = paginate(forms.order(created_at: :asc))
    render json: { records: records, meta: meta }
  end

  def update
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    if (import_record.update(import_params))
      render json: { saved: true, record: import_record }
    else
      render json: { saved: false, errors: import_record.errors.full_messages }
    end
  end

  def wonde_import_teacher_ids
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    render json: { ids: import_record.teachers.ids }
  end

  def wonde_teachers_valid
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    render json: { invalid_teachers: import_record.invalid_teachers }
  end

  def wonde_import_teachers
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    teachers = import_record.wonde_import_teachers
    if params[:query].present?
      teachers = teachers.left_joins(:user).where("wonde_import_teachers.email ILIKE :query OR wonde_import_teachers.name ILIKE :query", query: "%#{params[:query]}%")
    end
    meta, records = paginate(teachers.order(created_at: :asc))
    render json: { records: records, meta: meta }
  end

  def wonde_import_pupils
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    pupils = import_record.wonde_import_pupils
    if params[:query].present?
      pupils = pupils.left_joins(:user).where("wonde_import_pupils.name ILIKE :query", query: "%#{params[:query]}%")
    end
    meta, records = paginate(pupils.order(created_at: :asc))
    render json: { records: records, meta: meta }
  end

  def save_teacher_changes
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    record = import_record.wonde_import_teachers.find(params[:recordId])
    record = record.update!(params.permit(:user_id, :email))
    render json: record
  end

  def save_import
    import_record = WondeImport.find(params[:id])
    authorize! :manage, import_record
    error_record = import_record.save_import_data()
    render json: { saved: true, errors: error_record&.error_data, warnings: error_record&.warning_data }
  end

  private

  def import_params
    params.permit(:import_status)
  end
end
