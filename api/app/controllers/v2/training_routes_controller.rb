class V2::TrainingRoutesController < V2::ApplicationController
  def index
    training_routes = TrainingRoute.accessible_by(current_ability)

    if params[:ids].present?
      training_routes = training_routes.where(id: params[:ids].split(","))
    end

    if (params[:query].present?)
      training_routes = training_routes.where("training_routes.name ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:sort] == "name ASC")
      training_routes = training_routes.order(name: :asc)
    elsif (params[:sort] == "name DESC")
      training_routes = training_routes.order(name: :desc)
    else
      training_routes = training_routes.order(name: :asc)
    end

    meta, records = paginate(training_routes)

    render json: { records: records, meta: meta }
  end

  def show
    training_route = TrainingRoute.find(params[:id])
    authorize! :read, training_route
    render json: training_route
  end

  def update
    training_route = TrainingRoute.find(params[:id])
    authorize! :update, training_route
    if (training_route.update(training_route_params))
      render json: { saved: true, record: training_route }
    else
      render json: { saved: false, errors: training_route.errors.full_messages }
    end
  end

  def destroy
    training_route = TrainingRoute.find(params[:id])
    authorize! :destroy, training_route
    destroy_record training_route
  end

  def create
    training_route = TrainingRoute.new(training_route_params)
    authorize! :create, training_route
    if training_route.save
      render json: { saved: true, record: training_route }
    else
      render json: { saved: false, errors: training_route.errors.full_messages }
    end
  end

  private

  def training_route_params
    params.permit(:name, :qualifications, :career_id, :route_type)
  end

end
