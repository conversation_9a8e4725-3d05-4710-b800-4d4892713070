require 'lesson_plan_pdf'

class V2::LessonTemplatesController < V2::ApplicationController
  def index
    lesson_templates = Lesson::Template.ordered.accessible_by(current_ability)

    lesson_templates = lesson_templates.where(user_generated: false) if params[:with_user_generated] != 'true'

    lesson_templates = lesson_templates.joins(:new_library_units).where({ new_library_units: { id: params[:unit_id] } }) if params[:unit_id].present?

    lesson_templates = lesson_templates.where(user_id: params[:user_id]) if params[:user_id].present?

    lesson_templates = lesson_templates.joins(:organisations).where(organisations: { id: params[:organisationId] }) if params[:organisationId].present?

    lesson_templates = lesson_templates.where(id: params[:ids].split(',')) if params[:ids].present?

    lesson_templates = lesson_templates.where('lesson_templates.name ILIKE ? OR lesson_templates.machine_name ILIKE ?', "%#{params[:query]}%", "%#{params[:query]}%") if params[:query]

    lesson_templates = lesson_templates.career_tagged_with_all(params[:taggedWithAll]) if params[:taggedWithAll]

    lesson_templates = lesson_templates.career_tagged_with_any(params[:taggedWithAny]) if params[:taggedWithAny]

    lesson_templates = lesson_templates.where('(SELECT COUNT(lesson_keywords.id) FROM lesson_keywords WHERE lesson_keywords.template_id = lesson_templates.id) > 0') if params[:withRocketWords] == 'true'

    if params[:unpaginated] == 'true'
      render json: { records: lesson_templates }
    else
      meta, records = paginate(lesson_templates)
      render json: { records: records, meta: meta }
    end
  end

  def primary_unit_for
    template = Lesson::Template.find(params[:id])
    units = template.new_library_units.order(weight: :asc)
    national_curriculum_units = units.select { |unit| unit.curriculum&.id == 18 }
    if national_curriculum_units.any?
      render json: national_curriculum_units[0]
    else
      render json: units.first
    end
  end

  def recommended_careers
    id = params[:id]

    result = custom_cache.fetch("recommended_careers_#{id}", expires_in: 7.days) do
      generate_recommended_careers(id)
    end

    render json: result
  end

  def show
    template = Lesson::Template.find(params[:id])
    authorize! :read, template

    data = custom_cache.fetch("lesson_template_show_#{params[:id]}", expires_in: 1.day) do
      template.data_for_show
    end

    # override visibility states
    data['anonymous'] = template.anonymous
    data['demo'] = template.demo
    data['disable_viewing'] = template.disable_viewing
    data['available'] = template.available
    data['is_published'] = template.user_generated && !!template.available
    data['published_state'] = if template.user_generated
                                 template.available ? 'all_users' : 'unpublished'
                               else
                                 template.new_presentation&.published
                               end


    render json: data
  end

  def data_for_user
    template = Lesson::Template.find(params[:id])
    authorize! :read, template

    render json: template.data_for_user(current_user)
  end

  def load_rocket_words_quiz
    template = Lesson::Template.find(params[:id])
    authorize! :read, template
    render json: template.quiz_questions_formatted_for_quip
  end

  def load_quiz_questions
    template = Lesson::Template.find(params[:id])
    authorize! :read, template
    questions = template.quiz_questions.map do |question|
      question.as_json.merge({
                               fileboy_image_id: question&.lesson_keyword&.fileboy_image_id || "",
                             })
    end
    render json: questions
  end

  def new_lesson_template_plans_index
    ids = params['ids'].split(',')
    templates = Lesson::Template.available.where(id: ids).where(disable_viewing: false).where.not(lesson_plan_layout: 'legacy')
    render json: templates.map { |t| t.as_json.merge({ keywords: t.keywords }) }
  end

  def update
    template = Lesson::Template.find(params[:id])
    authorize! :manage, template
    data = params.permit(
      :use_2022_lesson_plan,
      :intent,
      :new_lesson_plan_resources,
      :implementation,
      :impact_assessment,
      :use_new_presentation,
      :specification,
      :ks4_learning_outcomes,
      :core_knowledge,
      :analogies_models,
      :ks4_teacher_mastery,
      :lesson_plan_layout
    )
    if template.update(data)
      render json: { saved: true, record: template }
    else
      render json: { saved: false, errors: template.errors.full_messages }
    end
  end

  def new_lesson_plan_pdf
    template = Lesson::Template.find(params[:id])
    name = template.name.parameterize + '_lesson_plan'

    body = request.body.read
    override_data = JSON.parse(body) if body.present?

    begin
      fileboy_id = LessonPlanPdf.get_single(params[:id], name, override_data: override_data&.dig('override_data'))
      render json: { redirect: "https://www.developingexperts.com/file-cdn/files/get/#{fileboy_id}" }
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error("Lesson::Template with id #{params[:id]} not found: #{e.message}")
      render json: { error: "Lesson plan template not found." }
    rescue JSON::ParserError => e
      Rails.logger.error("JSON parsing error: #{e.message}")
      render json: { error: "Invalid JSON data" }
    rescue StandardError => e
      Rails.logger.error("Unexpected error in new_lesson_plan_pdf: #{e.message}")
      render json: { error: "An unexpected error occurred. Please try again later." }
    end
  end

  def new_lesson_plan_pdfs
    name = params[:name] ? "#{params[:name].parameterize}_" : ''
    template_ids = Lesson::Template.where(id: params[:ids].split(',')).order(weight: :asc).pluck(:id)

    begin
      fileboy_id = LessonPlanPdf.get_multiple(params[:unit_id], template_ids, name)
      render json: { redirect: "https://www.developingexperts.com/file-cdn/files/get/#{fileboy_id}" }
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error("Lesson::Template with id #{params[:id]} not found: #{e.message}")
      render json: { error: "Lesson plan template not found." }
    rescue JSON::ParserError => e
      Rails.logger.error("JSON parsing error: #{e.message}")
      render json: { error: "Invalid JSON data" }
    rescue StandardError => e
      Rails.logger.error("Unexpected error in new_lesson_plan_pdf: #{e.message}")
      render json: { error: "An unexpected error occurred. Please try again later." }
    end
  end

  private

  def generate_recommended_careers(id)
    template = Lesson::Template.find(id)
    words = template.related_words
    careers = Career.career_tagged_with_all(template.career_tag_ids)
    careers = Career.career_tagged_with_any(template.career_tag_ids) if careers.empty?
    careers = Career.by_related_words(words) if careers.empty?

    words_sql = words.is_a?(ActiveRecord::Relation) ? words.select(:name).to_sql : escape(words)

    count_related_words_sql = <<-SQL.squish
    SELECT COUNT(related_words.name)
    FROM related_words
    JOIN career_taggings ON career_taggings.career_tag_id = related_words.career_tag_id
    JOIN lesson_templates ON career_taggings.taggable_id = #{template.id} AND career_taggings.taggable_type = 'Lesson::Template'
    JOIN careers ON career_taggings.taggable_id = careers.id AND career_taggings.taggable_type = 'Career'
    WHERE related_words.name IN (#{words_sql})
    SQL

    careers.order(Arel.sql("(#{count_related_words_sql}) DESC")).limit(6)
  end
end
