class V2::CampaignUnitsController < V2::ApplicationController

  def index
    records = CampaignUnit.accessible_by(current_ability).left_joins(:campaign)

    if params[:query].present?
      records = records.where("camapigns.name ilike ?", "%#{params[:query]}%")
    end

    if params[:campaignId].present?
      records = records.where(campaign_id: params[:campaignId])
    end

    if params[:ids].present?
      records = records.where({ id: params[:ids].split(",") })
    end

    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      records = records.where(campaign_id: organisation.campaign_ids)
    end

    if (params[:unpaginated] == "true")
      render json: { records: records }
    else
      meta, records = paginate(records)
      render json: { records: records, meta: meta }
    end
  end

  def show
    unit = CampaignUnit.accessible_by(current_ability).find(params[:id])

    authorize! :read, unit

    render json: unit
  end

  def create
    unit = CampaignUnit.new(campaign_unit_params)

    authorize! :create, unit

    if unit.save
      unit.new_library_units.each do |nlu|
        nlu.lesson_templates.each do |template|
          CampaignLesson.create(
            campaign_unit_id: unit.id,
            lesson_template_id: template.id,
            new_library_unit_id: nlu.id,
            campaign_id: unit.campaign_id
          )
        end
      end

      render json: { saved: true, record: unit }
    else
      render json: { saved: false, errors: unit.errors.full_messages }
    end
  end

  def update
    unit = CampaignUnit.find(params[:id])
    authorize! :update, unit

    removed_ids = unit.new_library_unit_ids - params[:new_library_unit_ids]
    add_ids = params[:new_library_unit_ids] - unit.new_library_unit_ids

    if (unit.update(campaign_unit_params))
      if(removed_ids.any?)
        CampaignLesson.where(new_library_unit_id: removed_ids, campaign_unit_id: unit.id).destroy_all
      end
      if(add_ids.any?)
        add_ids.each do |id|
          libUnit = NewLibrary::Unit.find(id)
          libUnit.lesson_templates.each do |template|
            CampaignLesson.create(
              campaign_unit_id: unit.id,
              lesson_template_id: template.id,
              new_library_unit_id: libUnit.id,
              campaign_id: unit.campaign_id
            )
          end
        end
      end

      render json: { saved: true, record: unit }
    else
      render json: { saved: false, errors: unit.errors.full_messages }
    end
  end

  def units_index
    records = NewLibrary::Unit.accessible_by(current_ability).left_joins(campaign_units: :campaign)
    if params[:campaignId].present?
      records = records.where(campaign_units: { campaign_id: params[:campaignId] })
    end
    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      records = records.where(campaign_units: { campaign_id: organisation.campaign_ids })
    end
    if params[:query].present?
      records = records.where("new_library_units.name ilike ?", "%#{params[:query]}%")
    end
    if params[:campaignUnitIds].present?
      records = records.where("campaign_units.id IN (?)", params[:campaignUnitIds].split(","))
    end
    if params[:unpaginated]
      render json: { records: records }
    else
      meta, records = paginate(records)
      render json: { records: records, meta: meta }
    end
  end

  def update_lesson_templates
    data = params[:data]
    campaignId = params[:campaignId]
    campaignUnitId = params[:campaignUnitId]
    ActiveRecord::Base.transaction do
      data.keys.map do |libraryUnitId|
        templateIds = data[libraryUnitId]

        existing_lessons = CampaignLesson.where(
          campaign_unit_id: campaignUnitId,
          new_library_unit_id: libraryUnitId,
          campaign_id: campaignId
        )
        existing_template_ids = existing_lessons.map { |lesson| lesson.lesson_template_id }
        removed_ids = existing_template_ids - templateIds
        added_ids = templateIds - existing_template_ids

        if removed_ids.any?
          CampaignLesson.where(
            campaign_unit_id: campaignUnitId,
            lesson_template_id: removed_ids,
            new_library_unit_id: libraryUnitId,
            campaign_id: campaignId
          ).destroy_all
        end

        if added_ids.any?
          added_ids.each do |tid|
            CampaignLesson.create!(
              campaign_unit_id: campaignUnitId,
              lesson_template_id: tid,
              new_library_unit_id: libraryUnitId,
              campaign_id: campaignId
            )
          end
        end
      end
    end
    render json: { saved: true }
  end

  private

  def campaign_unit_params
    params.permit(:campaign_id, :videos2_id, :external_url, :quiz_id, :tour_id, :body, :show_on_unit_page, new_library_unit_ids: [])
  end
end
