class V2::QuestionnaireOptionsController < V2::ApplicationController
  def create
    option = QuestionnaireOption.new(option_params)
    authorize! :manage, option
    if option.save
      render json: { saved: true, message: 'success', record: option }
    else
      render json: { saved: false, message: option.errors.full_messages.join(", ") }
    end
  end

  def update
    option = QuestionnaireOption.find(params[:id])
    authorize! :manage, option
    if option.update(option_params)
      render json: { saved: true, message: 'success', record: option }
    else
      render json: { saved: false, message: option.errors.full_messages.join(", ") }
    end
  end

  def destroy
    option = QuestionnaireOption.find(params[:id])
    authorize! :manage, option
    if option.destroy()
      render json: { saved: true, message: 'success' }
    else
      render json: { saved: false, message: option.errors.full_messages.join(", ") }
    end
  end

  private

  def option_params
    params.permit(:questionnaire_question_id, :weight, :option_data, option_data: {}, career_tag_ids: [])
  end
end
