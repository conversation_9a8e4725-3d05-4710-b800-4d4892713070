class V2::TrackingsController < V2::ApplicationController
  
  def presentation_view_end
    if current_user
      presentation_viewing = TrackingPresentationView.where({
        lesson_template: params[:templateId],
        user: current_user,
      }).order({ created_at: :DESC })
      presentation_viewing.first&.update({ time_viewed: params[:timeViewed] })
      render json: { presentation: presentation_viewing.first }
    else
      render json: { presentation: nil }
    end
  end

  def track_film_viewed
    if current_user&.pupil?
      lesson_id = params[:lessonId]
      jw_id = params[:jwId]
      fileboy_video_id = params[:fileboyVideoId]
      time_viewing = params[:timeViewing]
      film_duration = params[:filmDuration]
      film_type = params[:filmType]

      lesson = Lesson::Lesson.find(lesson_id)

      tracking_film = TrackingService.track_film_view(current_user, lesson.template, nil, {
        lesson_id: lesson.id,
        jw_id: jw_id,
        fileboy_video_id: fileboy_video_id,
        time_viewing: time_viewing,
        film_duration: film_duration,
        film_type: film_type
      })

      UserRankEvent.add_points(current_user, 1, "video")
      render json: { tracking_film: tracking_film }
    else
      render json: { tracking_film: nil }
    end
  end

  def chronological_data
    user_id = params[:userId]
    escaped_user_id = escape(user_id)

    per_page = params[:perPage] ? params[:perPage].to_i : 20
    page = params[:page].to_i > 0 ? params[:page].to_i - 1 : 0

    count = 0
    count += TrackingLogin.where(user_id: user_id).count
    count += TrackingRocketWord.where(pupil_id: user_id).count
    count += TrackingFilm.where(pupil_id: user_id).count
    count += TrackingWordSearch.where(pupil_id: user_id).count
    count += TrackingSummativeQuiz.where(pupil_id: user_id).count
    count += TrackingLinkTracking.where(pupil_id: user_id).count
    count += TrackingDocument.where(pupil_id: user_id).count
    count += TrackingLessonTemplateViewed.where(pupil_id: user_id).count
    count += TrackingLessonTemplateFavourite.where(pupil_id: user_id).count
    count += TrackingPresentationView.where(user_id: user_id).count
    count += TrackingAccountEdit.where(user_id: user_id).count
    count += TrackingMarkAssignment.where(user_id: user_id).count

    offset = page * per_page
    full_sql_query = <<-SQL
    SELECT *
      FROM (
        SELECT 'tracking_login' AS type, to_json(tracking_logins) AS data FROM tracking_logins WHERE user_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_rocket_word' AS type, to_json(tracking_rocket_words) AS data FROM tracking_rocket_words WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_film' AS type, to_json(tracking_films) AS data FROM tracking_films WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_word_search' AS type, to_json(tracking_word_searches) AS data FROM tracking_word_searches WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_quiz' AS type, to_json(tracking_summative_quizzes) AS data FROM tracking_summative_quizzes WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_link' AS type, to_json(tracking_link_trackings) AS data FROM tracking_link_trackings WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_document' AS type, to_json(tracking_documents) AS data FROM tracking_documents WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_template_view' AS type, to_json(tracking_lesson_template_vieweds) AS data FROM tracking_lesson_template_vieweds WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_favourite' AS type, to_json(tracking_lesson_template_favourites) AS data FROM tracking_lesson_template_favourites WHERE pupil_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_presentation_view' AS type, to_json(tracking_presentation_views) AS data FROM tracking_presentation_views WHERE user_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_account_edit' AS type, to_json(tracking_account_edits) AS data FROM tracking_account_edits WHERE user_id = #{escaped_user_id}
        UNION ALL
        SELECT 'tracking_mark_assignment' AS type, to_json(tracking_mark_assignments) AS data FROM tracking_mark_assignments WHERE user_id = #{escaped_user_id}
      ) results
      ORDER BY data ->> 'created_at' DESC
      LIMIT #{per_page}
      OFFSET #{offset} ROWS
    SQL

    result = sql(full_sql_query).to_json.map do |record|
      record_data = record["data"]
      record_data[:type] = record["type"]
      record_data
    end

    render json: {
      records: result,
      meta: {
        page: page + 1,
        perPage: per_page,
        totalCount: count,
        itemCount: result.length
      }
    }
  end
end
