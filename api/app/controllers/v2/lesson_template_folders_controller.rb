class V2::LessonTemplateFoldersController < V2::ApplicationController
  def index
    render json: LessonTemplateFolder.folder_tree_for_user(current_user)
  end

  def create
    folder = LessonTemplateFolder.new(folder_params)
    folder.user = current_user
    authorize! :create, folder

    if folder.save
      render json: { saved: true, record: folder }
    else
      render json: { saved: false, errors: folder.errors.full_messages }
    end
  end

  def update
    folder = LessonTemplateFolder.find(params[:id])
    authorize! :update, folder
    if (folder.update(folder_params))
      render json: { saved: true, record: folder }
    else
      render json: { saved: false, errors: folder.errors.full_messages }
    end
  end

  def destroy
    folder = LessonTemplateFolder.find(params[:id])
    authorize! :destroy, folder
    destroy_record folder
  end

  def add_templates_to_folder
    folder_id = params.require(:id)
    template_ids = params.require(:template_ids)
    templates = Lesson::Template.where(user_generated: true, user: current_user, id: template_ids)
    templates.update_all(lesson_template_folder_id: folder_id)
    render json: { saved: true }
  end

  private

  def folder_params
    params.permit(:name, :parent_folder_id)
  end
end
