class V2::CareerTagsController < V2::ApplicationController
  def index
    tags = CareerTag.accessible_by(current_ability).including_association_counts

    if (params[:ids])
      tags = tags.where({ id: params[:ids].split(",") })
    end

    if (params[:query])
      tags = tags.where("career_tags.name ILIKE ?", "%#{params[:query]}%")
    end

    tags = tags.order(name: :asc)

    meta, records = paginate(tags)
    render json: { records: records, meta: meta }
  end

  def show
    tag = CareerTag.including_association_counts.find(params[:id])
    authorize! :read, tag
    render json: tag
  end

  def update
    tag = CareerTag.find(params[:id])
    authorize! :update, tag
    if (tag.update(tag_params))
      render json: { saved: true, record: tag }
    else
      render json: { saved: false, errors: tag.errors.full_messages }
    end
  end

  def destroy
    tag = CareerTag.find(params[:id])
    authorize! :destroy, tag
    destroy_record tag
  end

  def create
    tag = CareerTag.new(tag_params)
    authorize! :create, tag
    if tag.save
      render json: { saved: true, record: tag }
    else
      render json: { saved: false, errors: tag.errors.full_messages }
    end
  end

  def create_from_array
    new_tags = params[:tags]
    tags = []
    new_tags.each do |name|
      tags << CareerTag.find_or_create_by(name: name)
    end

    render json: tags
  end

  private

  def tag_params
    params.permit(:name)
  end
end
