class V2::TeachersController < V2::ApplicationController

  def index
    teachers = Teacher.accessible_by(current_ability)

    if (params[:userId])
      # pupils_users used here in the where as that's the auto generated name of the association
      teachers = teachers.left_joins(:pupils).where({ pupils_users: { id: params[:userId] } })
    end

    if (params[:schoolId])
      teachers = teachers.left_joins(:school).where({ school_id: params[:schoolId] })
    end

    if (params[:query].present?)
      teachers = teachers.where('users.name ILIKE ? OR users.email ILIKE ?', "%#{params[:query]}%", "%#{params[:query]}%")
    end

    if (params[:schoolQuery].present?)
      teachers = teachers.left_joins(:school).where('schools.name ILIKE ?', "%#{params[:schoolQuery]}%")
    end

    if params[:referralQuery].present?
      teachers = teachers
                  .joins('LEFT JOIN users ref_users ON users.referred_from_user_id = ref_users.id')
                  .where('ref_users.referral_code ILIKE :q', { q: "%#{params[:referralQuery]}%" })
    end

    if params[:unpaginated]
      records = teachers
      meta = {}
    else
      meta, records = paginate(teachers)
    end

    if params[:serialized].present?
      records = records.serialized_for_index_request
    end

    render json: { records: records, meta: meta }
  end

  def show
    teacher = Teacher.find(params[:id])
    authorize! :read, teacher
    render json: teacher
  end

  def task_progress
    teacher = Teacher.find(params[:id])
    authorize! :read, teacher
    render json: teacher.task_progress
  end

  def update
    teacher = Teacher.find(params[:id])
    authorize! :update, teacher
    if (teacher.update(teacher_params))
      render json: { saved: true, record: teacher }
    else
      render json: { saved: false, errors: teacher.errors.full_messages }
    end
  end

  def destroy
    teacher = Teacher.find(params[:id])
    authorize! :destroy, teacher
    destroy_record teacher
  end

  private

  def teacher_params
    params.permit(
      :job_title,
      :name,
      :alias,
      :email,
      :dob,
      :gender,
      :working_days,
      :password,
      :is_school_admin,
      :can_create_lessons,
    )
  end
end
