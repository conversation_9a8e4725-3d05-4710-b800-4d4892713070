class V2::ProfileExternalClicksController < V2::ApplicationController
  def index
    records = ProfileExternalClick.accessible_by(current_ability).left_joins(:organisation, :campaign)

    if params[:query].present?
      records = records.where("campaigns.name ilike ? ", "%#{params[:query]}%")
    end

    if params[:organisationId].present?
      records = records.where(organisation_id: params[:organisationId])
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    profile_external_click = ProfileExternalClick.new(profile_view_params)
    profile_external_click.campaign = Campaign.where(organisation: profile_external_click.organisation).last if !profile_external_click.campaign_id
    authorize! :create, profile_external_click
    if profile_external_click.save
      render json: { saved: true, record: profile_external_click }
    else
      render json: { saved: false, errors: profile_external_click.errors.full_messages }
    end
  end

  private

  def profile_view_params
    params.permit(:user_id, :organisation_id, :campaign_id)
  end
end
