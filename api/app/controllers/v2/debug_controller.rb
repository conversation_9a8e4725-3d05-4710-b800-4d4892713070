class V2::DebugController < V2::ApplicationController
  def user_lesson_template_stats
    stats = {}
    userTemplates = Lesson::Template.where(user_generated: true)
    stats["CustomLessonTemplates"] = {
      "total created templates": userTemplates.count,
      "total published templates": userTemplates.where(available: true).count,
      "# Schools that have used": userTemplates.joins(:school).pluck("schools.id").uniq.count,
      "Added to classes lessons": userTemplates.joins(:lessons).count,
      "last created": userTemplates.maximum(:created_at)
    }
    render json: stats
  end
end
