class V2::QuipQuestionsController < V2::ApplicationController
  def index
    questions = QuipQuestion.accessible_by(current_ability)

    if (params[:slide_id].present?)
      questions = questions.where(lesson_slide_id: params[:slide_id])
    end

    if (params[:type].present?)
      questions = questions.where(type: params[:type])
    end

    if params[:ids].present?
      questions = questions.where({ id: params[:ids].split(",") })
    end

    if (params[:query])
      # questions = questions.where("users.name ILIKE ?", "%#{params[:query]}%")
    end

    meta, records = paginate(questions)

    render json: { records: records, meta: meta }
  end

  def create
    question = QuipQuestion.new(quip_question_params)
    authorize! :create, question
    if question.save
      render json: { saved: true, record: question }
    else
      render json: { saved: false, errors: question.errors.full_messages }
    end
  end


  def show
    question = QuipQuestion.find(params[:id])
    authorize! :read, question
    render json: question
  end

  def update
    question = QuipQuestion.find(params[:id])
    authorize! :update, question
    if (question.update(quip_question_params))
      render json: { saved: true, record: question }
    else
      render json: { saved: false, errors: question.errors.full_messages }
    end
  end

  def destroy
    question = QuipQuestion.find(params[:id])
    authorize! :destroy, question
    destroy_record question
  end

  private

  def quip_question_params
    params.permit(
      :question_type,
      :weight,
      data_json: {},
    )
  end
end
