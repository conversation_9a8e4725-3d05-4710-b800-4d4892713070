class V2::CampaignUnitViewsController < V2::ApplicationController
  include CampaignDemographicsFilter
  def index
    records = CampaignUnitView.accessible_by(current_ability).left_joins(campaign_unit: :campaign)

    if params[:query].present?
      records = records.where("campaigns.name ilike ? ", "%#{params[:query]}%")
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    campaign_unit_tracking = CampaignUnitView.new(campaign_unit_tracking_params)
    authorize! :create, campaign_unit_tracking
    if campaign_unit_tracking.save
      render json: { saved: true, record: campaign_unit_tracking }
    else
      render json: { saved: false, errors: campaign_unit_tracking.errors.full_messages }
    end
  end

  def view_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = CampaignUnitView.left_joins(campaign_unit: :new_library_units)
    if params[:campaignId].present?
      data = data.where("campaign_units.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      data = data.where("campaign_units.campaign_id in (?)", organisation.campaign_ids)
    end
    if start_date
      data = data.where("campaign_unit_views.created_at < ?", start_date)
    end
    if end_date
      data = data.where("campaign_unit_views.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)
    if params[:newLibraryUnitIds].present?
      data = data.where(new_library_units: { id: params[:newLibraryUnitIds].split(",") })
    end
    render json: data
  end

  def demographics_data
    data = CampaignUnitView.left_joins(campaign_unit: :new_library_units, user: :school)
    if params[:campaignId].present?
      data = data.where("campaign_units.campaign_id = ?", params[:campaignId])
    end
    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      data = data.where("campaign_units.campaign_id in (?)", organisation.campaign_ids)
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
        result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
        result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
      result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end

  private

  def campaign_unit_tracking_params
    params.permit(:user_id, :campaign_unit_id)
  end
end
