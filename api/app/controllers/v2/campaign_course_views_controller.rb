class V2::CampaignCourseViewsController < V2::ApplicationController
  include CampaignDemographicsFilter
  def index
    records = CampaignCourseView.accessible_by(current_ability).left_joins(campaign_course: :campaign)

    if params[:query].present?
      records = records.where("campaigns.name ilike ? ", "%#{params[:query]}%")
    end

    meta, records = paginate(records)

    render json: { records: records, meta: meta }
  end

  def create
    tracking = CampaignCourseView.new(campaign_course_tracking_params)
    authorize! :create, tracking
    if tracking.save
      render json: { saved: true, record: tracking }
    else
      render json: { saved: false, errors: tracking.errors.full_messages }
    end
  end

  def view_statistics
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    data = CampaignCourseView.left_joins(career_course: :organisation)
    if params[:campaignId].present?
      campaign = Camapaign.find(params[:campaignId])
      data = data.where("career_courses.organisation_id = ?", campaign.organisation_id)
    end
    if params[:organisationId].present?
      data = data.where("career_courses.organisation_id = ?", params[:organisationId])
    end
    if start_date
      data = data.where("campaign_course_views.created_at < ?", start_date)
    end
    if end_date
      data = data.where("campaign_course_views.created_at > ?", end_date)
    end
    data = campaign_demographics_filter(data)

    if params[:careerCourseIds].present?
      data = data.where(career_course_id: params[:careerCourseIds].split(","))
    end
    render json: data
  end

  def demographics_data
    data = CampaignCourseView.left_joins(career_course: :organisation, user: :school)
    if params[:campaignId].present?
      campaign = Campaign.find(params[:campaignId])
      data = data.where(career_courses: { organisation_id: campaign.organisation_id })
    end
    if params[:organisationId].present?
      data = data.where(career_courses: { organisation_id: params[:organisationId] })
    end
    result_data = {}
    if params[:type] === "ethnicity"
      User.ethnicities.each do |ethnicity|
      result_data[ethnicity[0]] = data.where("users.ethnicity = ?", ethnicity[1]).count
      end
    end
    if params[:type] === "gender"
      ["male", "female", "other"].each do |gender|
      result_data[gender] = data.where("users.gender = ?", gender).count
      end
    end
    if params[:type] === "region"
      School.regions.each do |region|
      result_data[region[0]] = data.where("schools.region = ?", region[1]).count
      end
    end

    render json: result_data
  end

  private

  def campaign_course_tracking_params
    params.permit(:user_id, :career_course_id)
  end
end
