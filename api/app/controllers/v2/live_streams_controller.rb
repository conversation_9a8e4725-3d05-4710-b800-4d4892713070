class V2::LiveStreamsController < V2::ApplicationController
  def active_live_stream
    return nil unless current_user&.school
    stream = LiveStream
             .where(published: true)
             .left_joins(:schools)
             .having('count(schools.id) = 0 OR schools.id IN (?)', current_user.school.id)
             .where('start_time <= :dt AND end_time >= :dt', dt: DateTime.now)
             .group('live_streams.id', 'schools.id')
             .first
    render json: stream&.as_json&.merge(
      { documents: stream&.live_stream_documents&.accessible_by(current_ability)&.order(weight: :asc) || [] }
    )
  end

  def upcoming_live_streams
    streams = LiveStream
              .where(published: true)
              .left_joins(:schools)
              .having('count(schools.id) = 0 OR schools.id IN (?)', current_user.school.id)
              .where('start_time >= :dt', dt: DateTime.now).order(start_time: :asc)
              .group('live_streams.id', 'schools.id')
    result = streams.map do |stream|
      stream&.as_json&.merge(
        { documents: stream&.live_stream_documents&.accessible_by(current_ability)&.order(weight: :asc) || [] }
      )
    end

    render json: result
  end

  def past_live_streams
    streams = LiveStream
              .where(published: true)
              .left_joins(:schools)
              .having('count(schools.id) = 0 OR schools.id IN (?)', current_user.school.id)
              .where('end_time <= :dt', dt: DateTime.now).order(start_time: :desc)
              .group('live_streams.id', 'schools.id')

    result = streams.map do |stream|
      stream&.as_json&.merge(
        { documents: stream&.live_stream_documents&.accessible_by(current_ability)&.order(weight: :asc) || [] }
      )
    end
    render json: result
  end

  def index
    messages = LiveStream.accessible_by(current_ability).order(:start_time)

    meta, records = paginate(messages)
    render json: { records: records, meta: meta }
  end

  def create
    stream = LiveStream.new(live_stream_params)
    authorize! :manage, stream
    if stream.save
      render json: { success: true, record: stream }
    else
      render json: { success: false, errors: stream.errors.full_messages }
    end
  end

  def update
    stream = LiveStream.find(params.require(:id))
    authorize! :manage, stream
    if stream.update(live_stream_params)
      render json: { success: true, record: stream }
    else
      render json: { success: false, errors: stream.errors.full_messages }
    end
  end

  def destroy
    stream = LiveStream.find(params.require(:id))
    authorize! :manage, stream
    if stream.destroy
      render json: { success: true }
    else
      render json: { success: false, errors: stream.errors.full_messages }
    end
  end

  def show
    stream = LiveStream.find(params.require(:id))
    authorize! :read, stream
    render json: stream.as_json.merge({ documents: stream.live_stream_documents.accessible_by(current_ability) })
  end

  def publish_stream
    stream = LiveStream.find(params.require(:id))
    authorize! :manage, stream
    stream.update(published: true)
    stream.reload
    render json: stream.published
  end

  def unpublish_stream
    stream = LiveStream.find(params.require(:id))
    authorize! :manage, stream
    stream.update(published: false)
    stream.reload
    render json: stream.published
  end

  def pin_message
    stream = LiveStream.find(params.require(:id))
    authorize! :manage, stream
    message = stream.live_stream_messages.find(params.require(:message_id))
    authorize! :manage, message
    render json: message&.pin
  end

  private

  def live_stream_params
    params.permit(:url, :title, :published, :start_time, :end_time, :fileboy_image_id, school_ids: [])
  end
end
