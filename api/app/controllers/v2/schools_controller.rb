class V2::SchoolsController < V2::ApplicationController

  def index
    schools = School.accessible_by(current_ability)

    schools = schools.where({ id: params[:ids].split(',') }) if params[:ids]

    schools = schools.where('schools.name ILIKE ?', "%#{params[:query]}%") if params[:query]

    schools = schools.where(organisation_id: params[:organisationId]) if params[:organisationId].present?

    schools = schools.where(school_type: params[:scope]) if params[:scope].present? && School.school_types.include?(params[:scope])

    if params[:unpaginated]
      render json: { records: schools }
    else
      meta, records = paginate(schools)
      render json: { records: records, meta: meta }
    end
  end

  def extended_index
    collection = extended_index_data(params)

    if params[:unpaginated]
      render json: { records: collection }
    else
      meta, records = paginate(collection)
      render json: { records: records, meta: meta }
    end
  end

  def extended_index_csv
    collection = extended_index_data(params).except(:select).select(
      'schools.id',
      "COALESCE(NULLIF(uk_schools.name,''), NULLIF(schools.name, '')) as \"School Name\"",
      "COALESCE(NULLIF(uk_schools.postcode, ''), NULLIF(schools.postcode, '')) as \"Postcode\"",
      "(ARRAY_TO_STRING((SELECT ARRAY_AGG(users.email) FROM users WHERE users.school_id = schools.id AND users.is_school_admin = true AND users.type = 'Teacher' AND NOT DELETED), ', ')) as \"Admin Teachers\"",
      'schools.hubspot_subscription_status as "Subscription Status"',
      'schools.cache_activity_30_days as "Active Teachers 30 days"',
      "(CAST(schools.cache_activity_30_days AS DOUBLE PRECISION) / NULLIF(CAST((SELECT COUNT(users.id) FROM users WHERE users.school_id = schools.id AND users.type = 'Teacher' AND NOT DELETED) AS DOUBLE PRECISION), 0.0) * 100) as \"Active Teachers 30 days %\"",
      'schools.cache_activity_60_days as "Active Teachers 30 days"',
      "(CAST(schools.cache_activity_60_days AS DOUBLE PRECISION) / NULLIF(CAST((SELECT COUNT(users.id) FROM users WHERE users.school_id = schools.id AND users.type = 'Teacher' AND NOT DELETED) AS DOUBLE PRECISION), 0.0) * 100) as \"Active Teachers 30 days %\"",
      "to_char(schools.created_at, 'DD/MM/YYYY') as \"Created At\""
    )

    filename = "DE_Schools_Index_#{Date.today}"
    render json: { csv: collection.to_csv, filename: filename }
  end

  def location_list
    schools = School.generic
                 .where.not(postcode: [nil, ''])

    if params[:subscribed] == 'true'
      schools = schools.where("hubspot_subscription_status = '#{School::HS_SUBSCRIPTION_STATUS[:subscribed]}' OR free_subscription")
    end

    schools = schools.pluck(:id, :postcode, :category, :uk_school_id)

    render json: schools.map { |id, postcode, category, uk_school_id| { id: id, postcode: postcode, category: category, uk_school_id: uk_school_id } }
  end

  def update
    school = School.find(params[:id])
    authorize! :manage, school
    sub_status = school.hubspot_subscription_status
    if school.update(school_params)
      if params[:hubspot_subscription_status].present? && params[:hubspot_subscription_status] != sub_status
        resource_changes.create(
          source_name: 'update',
          attribute_name: 'hubspot_subscription_status',
          old_value: sub_status,
          new_value: params[:hubspot_subscription_status],
          reason: "Subscription status was manually updated by #{current_user.name}"
        )
      end
      render json: { saved: true, record: school }
    else
      render json: { saved: false, errors: school.errors.full_messages }
    end
  end

  def show
    school = School.find(params[:id])
    authorize! :read, school
    render json: school
  end

  def sync_hubspot
    school = School.find(params[:id])
    if school.is_industry_account?
      render json: { saved: false, message: 'syncing is disabled for example schools' }
      return
    end
    authorize! :read, school
    begin
      school.sync_hubspot
    rescue => e
      puts e
      render json: { saved: false, error: e }
    end
    render json: { saved: true }
  end

  def sync_hubspot_signup
    school = School.find(params[:id])
    authorize! :read, school

    if school.is_industry_account?
      render json: { saved: false, message: 'syncing is disabled for example schools' }
      return
    end
    unless school.generic?
      render json: { saved: false, message: 'syncing is disabled for example schools' }
      return
    end

    result = school.sync_hubspot

    if result.success
      render json: { saved: true }
    else
      render json: { saved: false, message: result.error }
    end
  end

  def sync_hubspot_subscribe
    school = School.find(params[:id])
    authorize! :read, school

    result = school.sync_hubspot_school_subscribed

    if result.success
      render json: { saved: true }
    else
      render json: { saved: false, message: result.error }
    end
  end

  def wonde_import
    school = School.find(params[:id])
    authorize! :update, school
    WondeManager.sync_data(school)
    render json: { running: true }
  end

  def request_wonde_access
    school = School.find(params[:id])
    authorize! :update, school
    response = WondeManager.request_access(school)
    render json: response
  end

  def last_wonde_import
    school = School.find(params[:id])
    authorize! :read, school
    render json: school.wonde_imports.where.not(import_status: 'cancelled').order(:created_at).last
  end

  def latest_wonde_import
    school = School.find(params[:id])
    authorize! :read, school
    render json: school.wonde_imports.order(:created_at).last
  end

  def delete_all_pupils
    school = School.find(params[:id])
    authorize! :manage, school
    school.transaction { school.pupils.each(&:destroy!) }
    render json: { success: true }, status: 200
  rescue ActiveRecord::InvalidForeignKey => e
    render(
      json: {
        success: false,
        error: 'ForeignKeyConstraint',
        details: 'Cannot delete due to foreign key constraint'
      },
      status: 500
    )
  rescue => e
    render json: { success: false, error: e }, status: 500
  end

  def can_sync_hubspot
    school = School.find(params[:id])
    authorize! :read, school
    render json: school.user_emails_valid
  end

  def merge_with_school
    school = School.find(params[:id])
    other_school = School.find(params[:other_school_id])
    authorize! :manage, school
    authorize! :manage, other_school
    school.merge_with_school(other_school.id)
    render json: { success: true }
  end

  def schools_subscriptions_stats
    # uk schools created trial
    # overseas schools trials
    # uk schools subscribed
    # overseas schools subscribed
    # renewing
    # not renewing
    range = (DateTime.now - 4.months).beginning_of_month
    uk_schools = School.where(country_id: 1)
    overseas_schools = School.where.not(country_id: 1)
    data_keys = %w[uk_trial_created overseas_trial_created uk_subscribed overseas_subscribed renewing not_renewing]
    date_order = []
    mtd_start = DateTime.now.beginning_of_month
    mtd_end = DateTime.now
    dates_to_check = [{ start: mtd_start, end: mtd_end, name: 'MTD', name_last: 'MTD' }]
    data_items = []

    3.times do |i|
      range = (range + i.months).beginning_of_month
      range_last_year = (range - 1.year).beginning_of_month
      dates_to_check << { start: range_last_year, end: range_last_year.end_of_month }
      dates_to_check << { start: range, end: range.end_of_month }
      date_order << range_last_year.strftime('%b %y')
      date_order << range.strftime('%b %y')
    end

    data_keys.each do |key|
      data = { name: key }
      dates_to_check.each do |date|
        range = date[:start]

        date_start = date[:start]
        date_end = date[:end]
        dates = { start: date_start, end: date_end }

        range_last_year_start = (date_start - 1.year)
        range_last_year_end = (date_end - 1.year)
        dates_last_year = { start: range_last_year_start, end: range_last_year_end }

        dataNameCurrent = date[:name].present? ? "#{date[:name]} #{range.strftime('%y')}" : range.strftime('%b %y')
        dataNamePrevious = date[:name].present? ? "#{date[:name]} #{range_last_year_start.strftime('%y')}" : range_last_year_start.strftime('%b %y')

        if key == 'uk_trial_created'
          data[dataNameCurrent] = uk_schools.where('created_at >= :start AND created_at <= :end', dates).count
          data[dataNamePrevious] = uk_schools.where('created_at >= :start AND created_at <= :end', dates_last_year).count
        end

        if key == 'overseas_trial_created'
          data[dataNameCurrent] = overseas_schools.where('created_at >= :start AND created_at <= :end', dates).count
          data[dataNamePrevious] = overseas_schools.where('created_at >= :start AND created_at <= :end', dates_last_year).count
        end

        if key == 'uk_subscribed'
          data[dataNameCurrent] = 0
          data[dataNamePrevious] = 0
        end

        if key == 'overseas_subscribed'
          data[dataNameCurrent] = 0
          data[dataNamePrevious] = 0
        end

        if key == 'renewing'
          data[dataNameCurrent] = 0
          data[dataNamePrevious] = 0
        end

        if key == 'not_renewing'
          data[dataNameCurrent] = 0
          data[dataNamePrevious] = 0
        end
      end
      data_items << data
    end
    date_order << "MTD #{(mtd_start - 1.year).strftime('%y')}"
    date_order << "MTD #{mtd_start.strftime('%y')}"
    render json: { data: data_items, date_order: date_order }
  end

  def update_science_leaders
    school = current_user.school
    unless current_user.teacher?
      authorize! :update, school
      return
    end
    data = params.permit(
      science_leader_ids: []
    )
    if school.update(data)
      school.sync_hubspot
      render json: { saved: true }
    else
      render json: { saved: false, errors: school.errors.full_messages }
    end
  end

  def resource_history
    school = School.find(params[:id])
    authorize! :update, school
    render json: school.resource_changes.order(created_at: :desc)
  end

  def get_non_latest_wonde_import_data
    school = School.find(params[:id])
    authorize! :update, school

    wonde_import = school.wonde_imports.last

    if wonde_import.nil?
      render json: { error: true, message: 'No import data found' }
      return
    end

    extra_pupil_ids = school.pupils.where("wonde_id is null OR wonde_id not in (select wonde_id from wonde_import_pupils where wonde_import_id=#{wonde_import.id})").pluck(:id)
    extra_teacher_ids = school.teachers.where("NOT is_school_admin AND (wonde_id is null OR wonde_id not in (select wonde_id from wonde_import_teachers where wonde_import_id=#{wonde_import.id}))").pluck(:id)
    extra_form_ids = school.forms.where("wonde_id is null OR wonde_id not in (select wonde_id from wonde_import_forms where wonde_import_id=#{wonde_import.id})").pluck(:id)

    render json: {
      wonde_import: wonde_import,
      extra_pupil_ids: extra_pupil_ids,
      extra_pupils: school.pupils.where(id: extra_pupil_ids).map { |record| %i[id name identifier wonde_id].map { |key| [key, record[key]] }.to_h },
      extra_teacher_ids: extra_teacher_ids,
      extra_teachers: school.teachers.where(id: extra_teacher_ids).map { |record| %i[id name email wonde_id].map { |key| [key, record[key]] }.to_h },
      extra_form_ids: extra_form_ids,
      extra_forms: school.forms.where(id: extra_form_ids).map { |record| %i[id name wonde_id].map { |key| [key, record[key]] }.to_h },
    }
  end

  def delete_non_latest_wonde_import_data
    school = School.find(params[:id])
    authorize! :update, school

    wonde_import = school.wonde_imports.last

    if wonde_import.nil?
      render json: { error: true, message: 'No import data found' }
      return
    end

    extra_pupils = school.pupils.where("wonde_id is null OR wonde_id not in (select wonde_id from wonde_import_pupils where wonde_import_id=#{wonde_import.id})")
    extra_teachers = school.teachers.where("NOT is_school_admin AND (wonde_id is null OR wonde_id not in (select wonde_id from wonde_import_teachers where wonde_import_id=#{wonde_import.id}))")
    extra_forms = school.forms.where("wonde_id is null OR wonde_id not in (select wonde_id from wonde_import_forms where wonde_import_id=#{wonde_import.id})")

    extra_pupils.destroy_all
    extra_teachers.destroy_all
    extra_forms.destroy_all

    render json: { success: true }
  end

  def hubspot_details
    school = School.find(params[:id])
    authorize! :read, school

    structure = Struct.new(:hubspotId, :hubspotIdError, :hubspotDealId, :hubspotDealIdError)
    data = structure.new(
      school.hubspot_id,
      '',
      school.hubspot_deal_id,
      ''
    )

    if school.hubspot_id.present?
      begin
        HubspotApi.hs_find_company(school.hubspot_id)
      rescue => e
        puts e
        data.hubspotIdError = e
      end
    end

    if school.hubspot_deal_id.present?
      begin
        HubspotApi.hs_find_deal(school.hubspot_deal_id)
      rescue => e
        puts e
        data.hubspotDealIdError = e
      end
    end

    render json: data
  end

  def update_hubspot_details
    school = School.find(params[:id])
    authorize! :update, school

    # { hubspotId: string | nil; hubspotDealId: string | nil }
    school_params = params.permit(:hubspotId, :hubspotDealId)

    school.update!({
                     hubspot_id: school_params[:hubspotId],
                     hubspot_deal_id: school_params[:hubspotDealId],
                   })
    render json: { ok: true }
  end

  def admin_wipe_data
    school = School.find(params[:id])
    authorize! :manage, school
    throw 'Permission denied' unless current_user.admin?
    # TODO
    # Add a clear data button for after a school is no longer subscribed. Wipes all data from the account, apart from the admin teacher
    time_took = school.purge_content
    render json: { ok: true, took: time_took }
  end

  def disable_wonde_auto_sync
    school = School.find(params[:id])
    school.update(sync_wonde_weekly: false)
    render json: { ok: true }
  end

  private

  def school_params
    params.permit(:trial_end_date, :uk_school_id, :finance_email, :finance_name, :annual_subscription, :po_number, :subscription_externally_covered, :show_lesson_plan_pupil_area, hubspot_errors: [])
  end

  def extended_index_data(params)
    collection = School.accessible_by(current_ability).left_joins(:uk_school, :teachers)
    collection = collection.where(country_id: params[:country_id]) if params[:country_id].present?
    collection = collection.where(region: params[:region]) if params[:region].present? && params[:country_id].to_i == 1
    collection = collection.where(hubspot_subscription_status: School::HS_SUBSCRIPTION_STATUS[:subscribed]) if params[:hasAnnualSubscription] === 'true'

    collection = collection.joins(:organisation).where('organisations.use_white_labelling IS True') if params[:whiteLabelled] == 'true'

    collection = collection.where({ id: params[:ids].split(',') }) if params[:ids]

    collection = collection.where(school_type: params[:schoolType]) if params[:schoolType].present?

    if params[:localAuthority].present?
      authority = params[:localAuthority].strip.split(',').compact
      collection = collection.where('uk_schools.local_authority in (?)', authority)
    end

    collection = collection.where('schools.created_at >= ?', Date.parse(params[:created_at]).beginning_of_day) if params[:created_at].present? && params[:created_at] != 'null'
    if params[:renewal_date_start].present?
      renewal_start = params[:renewal_date_start].instance_of?(::String) ? School.renewal_months[params[:renewal_date_start]] : params[:renewal_date_start]
      collection = collection.where('schools.renewal_month >= ?', renewal_start) if renewal_start.present?
    end
    if params[:renewal_date_end].present?
      renewal_end = params[:renewal_date_end].instance_of?(::String) ? School.renewal_months[params[:renewal_date_end]] : params[:renewal_date_end]
      collection = collection.where('schools.renewal_month <= ?', renewal_end) if renewal_end.present?
    end

    if params[:postcodes].present?
      postcodes = params[:postcodes].strip.split(',').map { |v| v.strip }.compact
      fields = ['schools.postcode', 'uk_schools.postcode']
      condition = postcodes.map do |term|
        condition = fields.map { |field| "#{field} ILIKE #{escape "%#{term.strip}%"}" }.join(' OR ')
        "(#{condition})"
      end.join(' OR ')
      collection = collection.where(condition)
    end

    collection = collection.where(hubspot_subscription_status: params[:hubspot_subscription_status]) if params[:hubspot_subscription_status].present?

    if params[:scopes].present?
      allowedScopes = School.categories.keys
      scopes = params[:scopes].strip.split(',').compact
      if scopes.present?
        scopes.each do |scope|
          collection = collection.send(scope) if allowedScopes.include?(scope)
        end
      end
    end

    if params[:query].present?
      terms = params[:query].strip.split(',').map { |v| v.strip }.compact
      fields = ['schools.name', 'uk_schools.name']
      condition = terms.map do |term|
        condition = fields.map { |field| "#{field} ILIKE #{escape "%#{term}%"}" }.join(' OR ')
        "(#{condition})"
      end.join(' OR ')
      collection = collection.where(condition)
    end

    if (sort = params[:sort_by]).present? && (dir = params[:sort_dir]).present?
      if sort == 'name'
        collection = collection.order("COALESCE(NULLIF(uk_schools.name,''), NULLIF(schools.name, '')) #{dir}")
      elsif sort === 'renewal_month'
        collection = collection.order("schools.renewal_month #{dir}")
      elsif sort === 'postcode'
        collection = collection.order("COALESCE(NULLIF(uk_schools.postcode, ''), NULLIF(schools.postcode, '')) #{dir}")
      elsif sort === 'hubspot_subscription_status'
        collection = collection.order("schools.hubspot_subscription_status #{dir}")
      elsif sort === 'created_at'
        collection = collection.order("schools.created_at #{dir}")
      elsif sort === 'activity_30'
        collection = collection.order("schools.cache_activity_30_days #{dir}")
      elsif sort === 'activity_60'
        collection = collection.order("schools.cache_activity_60_days #{dir}")
      end
    end
    collection.select(
      'uk_schools.*',
      'schools.*',
      "(SELECT JSON_AGG(JSON_BUILD_OBJECT('id', users.id, 'email', users.email)) FROM users WHERE users.school_id = schools.id AND users.is_school_admin = true AND users.type = 'Teacher' AND NOT DELETED) as admin_teachers",
      "(CAST(schools.cache_activity_30_days AS DOUBLE PRECISION) / NULLIF(CAST((SELECT COUNT(users.id) FROM users WHERE users.school_id = schools.id AND users.type = 'Teacher' AND NOT DELETED) AS DOUBLE PRECISION), 0.0) * 100) as active_teachers_30_percent",
      "(CAST(schools.cache_activity_60_days AS DOUBLE PRECISION) / NULLIF(CAST((SELECT COUNT(users.id) FROM users WHERE users.school_id = schools.id AND users.type = 'Teacher' AND NOT DELETED) AS DOUBLE PRECISION), 0.0) * 100) as active_teachers_60_percent"
    ).group('schools.id', 'uk_schools.id', 'uk_schools.name', 'uk_schools.postcode')
  end
end
