class V2::QuipQuizProxyController < V2::ApplicationController
  def quiz_data
    quiz_key = params.require(:quizKey)
    uri = join_url("quizzes/by-quiz-key/#{quiz_key}")

    req = Net::HTTP::Post.new(uri, { "content-type": "application/json", "x-quip-public": ENV["QUIP2_PUBLIC_KEY"] })
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = (uri.scheme == "https")
    res = http.request(req)

    render json: res.body
  end

  def increment_view
    quiz_id = params.require(:quizId)
    uri = join_url("quizzes/increment-quiz-views/#{quiz_id}")

    req = Net::HTTP::Post.new(uri, { "content-type": "application/json", "x-quip-public": ENV["QUIP2_PUBLIC_KEY"] })
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = (uri.scheme == "https")
    res = http.request(req)

    render json: res.body
  end

  def increment_complete
    quiz_id = params.require(:quizId)
    uri = join_url("quizzes/increment-complete-count/#{quiz_id}")

    req = Net::HTTP::Post.new(uri, { "content-type": "application/json", "x-quip-public": ENV["QUIP2_PUBLIC_KEY"] })
    http = Net::HTTP.new(uri.host, uri.port)
    http.use_ssl = (uri.scheme == "https")
    res = http.request(req)

    render json: res.body
  end

private

  def join_url path
    URI("#{ENV["QUIP2_API_PATH"]}#{path}")
  end
end