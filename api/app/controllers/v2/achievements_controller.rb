class V2::AchievementsController < V2::ApplicationController
  def index
    achievements = Achievement.accessible_by(current_ability)

    if params[:query].present?
      achievements = achievements.where("name ILIKE ?", "%#{params[:query]}%")
    end

    meta, records = paginate(achievements)
    render json: { records: records, meta: meta }
  end

  def show
    achievement = Achievement.find(params[:id])
    authorize! :read, achievement
    render json: achievement
  end

  def create
    achievement = Achievement.new(achievement_params)
    authorize! :create, achievement
    if (achievement.save)
      render json: { saved: true, record: achievement }
    else
      render json: { saved: false, errors: achievement.errors.full_messages }
    end
  end

  def update
    achievement = Achievement.find(params[:id])
    authorize! :update, achievement
    if (achievement.update(achievement_params))
      render json: { saved: true, record: achievement }
    else
      render json: { saved: false, errors: achievement.errors.full_messages }
    end
  end

  def destroy
    achievement = Achievement.find(params[:id])
    authorize! :destroy, achievement
    destroy_record achievement
  end

  def data_for_pupil
    pupil = Pupil.find(params[:pupil_id])
    achievements = Achievement.accessible_by(current_ability).data_for_pupil(pupil)
    render json: { records: achievements }
  end

  private

  def achievement_params
    params.permit(:name, :body, :fileboy_image_id, :achievement_type, :target, :points)
  end
end
