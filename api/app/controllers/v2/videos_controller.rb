class V2::VideosController < V2::ApplicationController
  def index
    records = Video.accessible_by(current_ability).left_joins(:campaign).group("videos2.id")

    if params[:query].present?
      records = records.where("
        videos2.name ilike :query
        OR campaigns.name ilike :query
        OR videos2.\"fileboyVideoId\" ilike :query
        OR videos2.video_url ilike :query",
                              query: "%#{params[:query]}%"
      )
    end

    if params[:campaignId].present?
      records = records.where(campaign_id: params[:campaignId])
    end

    if params[:organisationId].present?
      organisation = Organisation.find(params[:organisationId])
      records = records.where(campaign_id: organisation.campaign_ids)
    end

    records = records.order(created_at: :desc)

    if (params[:unpaginated] == "true")
      render json: { records: records }
    else
      meta, records = paginate(records)
      render json: { records: records, meta: meta }
    end
  end

  def create
    video = Video.new(video_params)
    authorize! :create, video
    if video.save
      render json: { saved: true, record: video }
    else
      render json: { saved: false, errors: video.errors.full_messages }
    end
  end

  def update
    video = Video.find(params[:id])
    authorize! :update, video
    if (video.update(video_params))
      render json: { saved: true, record: video }
    else
      render json: { saved: false, errors: video.errors.full_messages }
    end
  end

  def show
    video_json = custom_cache.fetch("video/#{params[:id]}/json", expires_in: 12.hours) do
      video = Video.find(params[:id])
      video.as_json
    end

    render json: video_json
  end

  private

  def video_params
    params.permit(:name, :fileboyVideoId, :campaign_id, :video_url, :tour_id, :tour_scene)
  end
end
