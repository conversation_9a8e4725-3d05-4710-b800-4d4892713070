class V2::CacheStatisticsController < V2::ApplicationController
  def get_cache
    cache = CacheStatistic.find_by({
      cache_table_name: params[:table_name],
      cache_region: params[:region] || nil,
      cache_ethnicity: params[:ethnicity] || nil,
      cache_gender: params[:gender] || nil,
      cache_school_category: params[:category] || nil,
    })

    if !cache
      render json: [], status: 404
      return
    end

    data = JSON.parse(cache.data)
    start_date = Date.parse(params[:startDate]).end_of_day + 1.second if params[:startDate].present?
    end_date = Date.parse(params[:endDate]).beginning_of_day if params[:endDate].present?
    if start_date
      data = data.select { |item| item["date"] <= start_date.to_i * 1000 }
    end
    if end_date
      data = data.select { |item| item["date"] >= end_date.to_i * 1000 }
    end

    render json: data
  end
end
