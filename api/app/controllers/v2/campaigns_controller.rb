class V2::CampaignsController < V2::ApplicationController
  def index
    campaigns = Campaign.accessible_by(current_ability)
    campaigns = campaigns.where('name ilike ?', "%#{params[:query]}%") if params[:query].present?

    campaigns = campaigns.where(organisation_id: params[:organisationId]) if params[:organisationId].present?

    meta, records = paginate(campaigns)

    render json: { records: records, meta: meta }
  end

  def show
    campaign = Campaign.accessible_by(current_ability).find(params[:id])

    authorize! :read, campaign

    render json: campaign
  end

  def create
    campaign = Campaign.new(campaign_params)

    campaign.organisation_id = if current_user.admin?
                                 params[:organisation_id]
                               else
                                 current_user.organisation.id
                               end

    authorize! :create, campaign

    if campaign.save
      render json: { saved: true, record: campaign }
    else
      render json: { saved: false, errors: campaign.errors.full_messages }
    end
  end

  def update
    campaign = Campaign.find(params[:id])

    campaign.organisation_id = if current_user.admin?
                                 params[:organisation_id]
                               else
                                 current_user.organisation.id
                               end

    authorize! :update, campaign
    if campaign.update(campaign_params)
      render json: { saved: true, record: campaign }
    else
      render json: { saved: false, errors: campaign.errors.full_messages }
    end
  end

  def predicted_reach
    campaign = Campaign.find(params[:id])
    schools = campaign.related_schools
    units = campaign.related_units
    unitRecommendations = nil
    unitRecommendations = if campaign.number_of_units === campaign.campaign_units.length
                            campaign.new_library_units
                          else
                            units.limit((campaign.number_of_units || 1) + 2)
                          end
    render json: {
      returningCurrentUnits: campaign.number_of_units === campaign.campaign_units.length,
      # based on location data
      schoolCount: schools.count,
      # pupils in the above schools which are for the correct year, and are doing lessons from the discovered units
      pupilCount: Pupil.where(school: schools).count,
      # number of lessons in the units
      lessonCount: NewLibrary::Unit
        .joins(lesson_templates: { lessons: :school })
        .where(id: units, schools: { id: schools })
        .count('DISTINCT lesson_lessons.id'),

      unitRecommendations: unitRecommendations.map.with_index do |unit, i|
        unitSchools = unit.schools

        {
          tags: unit.related_words.pluck(:name) & campaign.related_words.pluck(:name),
          id: unit.id,
          name: unit.name,
          potentialUnit: i >= (campaign.number_of_units || 1),
          pupilCount: Pupil.where(school: unitSchools).count,
          schoolCount: unitSchools.count,
          lessonCount: NewLibrary::Unit
            .where(id: unit.id).joins(lesson_templates: { lessons: :school })
            .where(id: units, schools: { id: schools })
            .count('DISTINCT lesson_lessons.id'),
          lessonTemplateIds: unit.lesson_template_ids.first(3),
          isAttached: campaign.new_library_units.pluck(:id).include?(unit.id)
        }
      end
    }
  end

  def school_interactions_index
    campaign = Campaign.find(params[:id])

    r_min = params[:range_min]
    range_min = r_min.present? ? DateTime.parse(r_min) : DateTime.now.beginning_of_year

    r_max = params[:range_max]
    range_max = r_max.present? ? DateTime.parse(r_max) : DateTime.now

    collection = School
                 .left_joins(:uk_school, :user_tracking_lesson_template_vieweds)
                 .where(
                   'tracking_lesson_template_vieweds.lesson_template_id in (?) AND tracking_lesson_template_vieweds.created_at > ? AND tracking_lesson_template_vieweds.created_at < ?',
                   campaign.lesson_templates.ids, range_min.beginning_of_day, range_max.end_of_day
                 ).group('schools.id', 'uk_schools.name')

    collection = collection.where('schools.name ILIKE ?', "%#{params[:query]}%") if params[:query]

    collection = collection.where(organisation_id: params[:organisationId]) if params[:organisationId].present?

    collection = collection.where(school_type: params[:scope]) if params[:scope].present? && School.school_types.include?(params[:scope])

    if (sort = params[:sort_by]).present? && (dir = params[:sort_dir]).present?
      if sort == 'name'
        collection = collection.order("COALESCE(NULLIF(uk_schools.name,''), NULLIF(schools.name, '')) #{dir}")
      elsif sort === 'postcode'
        collection = collection.order("COALESCE(NULLIF(uk_schools.postcode, ''), NULLIF(schools.postcode, '')) #{dir}")
      elsif sort === 'hubspot_subscription_status'
        collection = collection.order("schools.hubspot_subscription_status #{dir}")
      elsif sort === 'created_at'
        collection = collection.order("schools.created_at #{dir}")
      end
    end

    render json: { records: collection }

    # records = records.select(
    #   "uk_schools.*",
    #   "schools.*",
    #   "(SELECT COUNT(tracking_lesson_template_vieweds.id) FROM tracking_lesson_template_vieweds WHERE tracking_lesson_template_vieweds.pupil_id = users.id AND tracking_lesson_template_vieweds.created_at > '#{range_min}' AND tracking_lesson_template_vieweds.created_at < '#{range_max}') as lesson_template_views",
    #   "(SELECT COUNT(tracking_presentation_views.id) FROM tracking_presentation_views WHERE tracking_presentation_views.user_id = users.id AND tracking_presentation_views.created_at > '#{range_min}' AND tracking_presentation_views.created_at < '#{range_max}') as presentation_views",
    #   "(SELECT COUNT(tracking_summative_quizzes.id) FROM tracking_summative_quizzes WHERE tracking_summative_quizzes.pupil_id = users.id AND tracking_summative_quizzes.created_at > '#{range_min}' AND tracking_summative_quizzes.created_at < '#{range_max}') as summative_quizzes",
    #   "(SELECT COUNT(tracking_rocket_words.id) FROM tracking_rocket_words WHERE tracking_rocket_words.pupil_id = users.id AND tracking_rocket_words.created_at > '#{range_min}' AND tracking_rocket_words.created_at < '#{range_max}') as rocket_words",
    #   "(SELECT COUNT(tracking_word_searches.id) FROM tracking_word_searches WHERE tracking_word_searches.pupil_id = users.id AND tracking_word_searches.created_at > '#{range_min}' AND tracking_word_searches.created_at < '#{range_max}') as word_searches",
    # ).group("schools.id", "uk_schools.id", "uk_schools.name", "uk_schools.postcode", "users.id")
  end

  private

  def campaign_params
    params.permit(
      :contract_expiry_date,
      :number_of_units,
      :contact_name,
      :contact_number,
      :contact_email,
      :name,
      :body,
      :gender,
      :employment_year,
      :postcodes,
      :campaign_page_link,
      ethnicities: [],
      placements: [],
      location: %i[radiusMiles postcode], interest_tags: []
    )
  end
end
