class V2::WordSearchLobbiesController < V2::ApplicationController
  before_action :runLobbyJob, only: [:lobby_index]
  before_action :set_lobby, only: [:show, :join, :leave]

  def lobby_index
    collection = WordSearchLobby
                   .where('"startsAt" > ?', DateTime.now)
                   .accessible_by(current_ability)
                   .order(created_at: :desc)
                   .limit(3)
    render json: collection.map { |lobby| lobby.as_json }
  end

  def show
    if @lobby.nil?
      render json: { error: 'Lobby not found' }, status: :not_found
      return
    end
    # Fetch or cache the JSON response for 10 seconds
    cached_json = WordSearchLobby.find(params[:id]).generate_cache

    # Render the cached or freshly generated JSON response
    render json: cached_json
  end

  def join
    if @lobby.nil?
      render json: { error: 'Lobby not found' }, status: :not_found
      return
    end
    unless @lobby.word_search_lobby_users.exists?(user: current_user)
      lobby_user = @lobby.word_search_lobby_users.create(user: current_user)
      lobby_user.generate_words 5
    end

    @lobby.delete_cache
    render json: @lobby.generate_cache
  end

  def leave
    if @lobby.nil?
      render json: { error: 'Lobby not found' }, status: :not_found
      return
    end
    @lobby.word_search_lobby_users.delete(@lobby.word_search_lobby_users.where(user: current_user))

    @lobby.delete_cache
    render json: @lobby.generate_cache
  end

  def user_stats
    user = User.find(params[:user_id])
    best_time = WordSearchLobbyUser
                  .joins(:word_search_lobby)
                  .where(user: user)
                  .minimum("date_part('epoch', \"finishedAt\" - \"startsAt\")")

    render json: { best_time: best_time }
  end

  private

  # This function should run all of the periodic tasks for managing the lobbies.
  def runLobbyJob
    # ensure a pending lobby exists
    hasPendingLobby = WordSearchLobby.exists?(['"startsAt" > ?', DateTime.now])
    if (!hasPendingLobby)
      WordSearchLobby.create! startsAt: 10.minutes.from_now
    end

    # remove lobbies which have started and have no players
    staleLobbies = WordSearchLobby
                     .where('"startsAt" <= NOW()') # already started
                     .left_outer_joins(:word_search_lobby_users).where(word_search_lobby_users: { id: nil }) # no users

    timedOutLobbies = WordSearchLobby
                        .where('"startsAt" <= ?', Time.now - 10.minutes) # 10 minutes old
                        .left_outer_joins(:word_search_lobby_users)
                        .where.not(word_search_lobby_users: { id: nil })
                        .where(word_search_lobby_users: { finishedAt: nil }) # has a user without a finished at time

    timedOutLobbies.each do |lobby|
      if lobby.word_search_lobby_users.any?
        lobby
          .word_search_lobby_users
          .where(word_search_lobby_users: { finishedAt: nil })
          .update_all({ finishedAt: Time.now })
      end
    end

    # get lobbies where all users have completed the game
    # and the lobby was created recently (so we don't get a huge backlog that will make this task)
    # take a long time to run
    WordSearchLobby
      .where('"startsAt" <= ?', Time.now - 10.minutes) # at least 10 minutes old
      .where('created_at > ?', 1.day.ago) # created in the last day
      .left_outer_joins(:word_search_lobby_users)
      .where.not(word_search_lobby_users: { id: nil }) # has users
      .where.not(word_search_lobby_users: { finishedAt: nil }) # all users have finished
      .destroy_all

    staleLobbies.destroy_all
  end

  def set_lobby
    @lobby = WordSearchLobby
               .includes(:word_search_lobby_users, :users)
               .find_by(id: params[:id])
  end
end
