class V2::GuardiansController < V2::ApplicationController
  def index
    guardians = Guardian.accessible_by(current_ability).includes(:pupils)

    if params[:pupilId].present?
      guardians = guardians.where({ guardian_pupils: { pupil_id: params[:pupilId] }})
    end

    if params[:query].present?
      guardians = guardians.where("name ilike ? OR email ilike ?", "%#{params[:query]}%", "%#{params[:query]}%")
    end

    meta, records = paginate(guardians)

    render json: { records: records, meta: meta }
  end

  def show
    guardian = Guardian.find(params[:id])
    authorize! :read, guardian
    render json: guardian
  end

  def destroy
    guardian = Guardian.find(params[:id])
    authorize! :destroy, guardian
    destroy_record guardian
  end

  def update
    guardian = Guardian.find(params[:id])
    authorize! :update, guardian
    if (guardian.update(guardian_params))
      render json: { saved: true, record: guardian }
    else
      render json: { saved: false, errors: guardian.errors.full_messages }
    end
  end

  def create
    pupil = Pupil.find(params[:pupilId])
    authorize! :read, pupil
    authorize! :update, pupil
    authorize! :create, Guardian
    guardian = pupil.guardians.create(
      email: params[:email],
      password: SecureRandom.hex + 'aA1@',
      school: pupil.school,
      name: params[:name],
    )
    if guardian.errors.any?
      render json: { success: false, errors: guardian.errors.full_messages }
    else
      render json: { success: true }
    end
  end

  private

  def guardian_params
    params.permit(
      :name,
      :email,
      :passowrd,
      :dob,
      :gender
    )
  end
end
