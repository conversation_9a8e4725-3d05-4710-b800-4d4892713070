class V2::OrganisationsController < V2::ApplicationController
  def index
    organisations = Organisation.accessible_by(current_ability)

    if(params[:has_image].present? && params[:has_image] == "true")
      organisations = organisations.where("fileboy_image_id IS NOT NULL AND fileboy_image_id != ''")
    end

    if (params[:ids])
      organisations = organisations.where({ id: params[:ids].split(",") })
    end

    if (params[:scope].present?)
      organisations = organisations.where(organisation_type: params[:scope])
    end

    if params[:white_labelled].present?
      organisations = organisations.where(use_white_labelling: params[:white_labelled] === "true")
    end

    if params[:sponsor].present?
      organisations = organisations.where(is_sponsor: params[:sponsor] === "true")
    end

    if (params[:query])
      organisations = organisations.where("organisations.name ILIKE ?", "%#{params[:query]}%")
    end

    if (params[:sort] == "name ASC")
      organisations = organisations.order(featured: :desc, name: :asc)
    elsif (params[:sort] == "name DESC")
      organisations = organisations.order(featured: :desc, name: :desc)
    elsif (params[:sort] === "Featured")
      organisations = organisations.order(featured: :desc, name: :asc)
    else
      organisations = organisations.order(featured: :desc, name: :asc)
    end

    meta, records = paginate(organisations, params[:per_page] || 30)

    render json: { records: records, meta: meta }
  end

  def show
    if params[:is_sponsor].present? && params[:is_sponsor] == "true"
      organisation = Organisation.find_by(id: params[:id], is_sponsor: true)
    else
      organisation = Organisation.find(params[:id])
    end

    authorize! :read, organisation
    render json: organisation
  end

  def update
    organisation = Organisation.find(params[:id])

    if params[:promotional_video_link].present?
      video_params = { name: "#{organisation.name} Promotional Video", video_url: params[:promotional_video_link], campaign_id: organisation.campaigns.first&.id }
      if organisation.promotional_video
        organisation.promotional_video.update(video_params)
      else
        organisation.promotional_video = Video.create(video_params)
      end
    end

    if params[:testimonial_video_link].present?
      video_params = { name: "#{organisation.name} Testimonial Video", video_url: params[:testimonial_video_link], campaign_id: organisation.campaigns.first&.id }
      if organisation.testimonial_video
        organisation.testimonial_video.update(video_params)
      else
        organisation.testimonial_video = Video.create(video_params)
      end
    end

    authorize! :update, organisation
    org_params = organisation_params
    org_params[:promotional_video_id] = nil if params[:promotional_video_link] == ""
    org_params[:testimonial_video_id] = nil if params[:testimonial_video_link] == ""

    if (organisation.update(org_params))
      render json: { saved: true, record: organisation }
    else
      render json: { saved: false, errors: organisation.errors.full_messages }
    end
  end

  def destroy
    organisation = Organisation.find(params[:id])
    authorize! :destroy, organisation
    destroy_record organisation
  end

  def create
    organisation = Organisation.new(organisation_params)
    if params[:promotional_video_link]
      organisation.promotional_video = Video.new(name: "#{organisation.name} Promotional Video", video_url: params[:promotional_video_link] )
    end
    if params[:testimonial_video_link]
      organisation.testimonial_video = Video.new(name: "#{organisation.name} Testimonial Video", video_url: params[:promotional_video_link] )
    end
    authorize! :create, organisation
    if organisation.save
      render json: { saved: true, record: organisation }
    else
      render json: { saved: false, errors: organisation.errors.full_messages }
    end
  end

  def related_careers
    organisation = Organisation.find(params[:id])
    authorize! :read, organisation
    words = organisation.career_vacancies.joins(:related_words).pluck("DISTINCT related_words.name")
    #TODO improve this request, currently limit 3 as request is slow, and only displaying 3 on front end anyway
    render json: Career.by_related_words(words).limit(3)
  end

private

  def organisation_params
    params.permit(
      :organisation_type,
      :establishment_type,
      :uid,
      :establishment_number,
      :establishment_name,
      :name,
      :alias,
      :lat,
      :lng,
      :address_line_1,
      :address_line_2,
      :address_line_3,
      :town,
      :postcode,
      :county,
      :website,
      :phone_number,
      :fax,
      :open_date,
      :education_range,
      :head_teacher_name,
      :for_gender,
      :body,
      :gtr_id,
      :hesa_id,
      :published,
      :fileboy_image_id,
      :promotional_video_id,
      :testimonial_video_id,
      :featured,
      :use_white_labelling,
      :is_sponsor,
      :testimonial,
      :testimonial_by,
      :expert_video_id,
      career_ids: [],
      white_label_style: [
        :primary,
        :primaryLight,
        :primaryAlt,
        :secondary,
        :secondaryLight,
        :highlight,
      ],
      white_label_content: [
        :signUpAccount,
        :signUpTypeSelect,
        :signUpDetails,
        :fileboyBackgroundImageId,
        :fileboySignInBackgroundImageId,
      ],
    )
  end
end
