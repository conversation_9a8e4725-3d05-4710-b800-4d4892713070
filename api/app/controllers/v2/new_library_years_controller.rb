class V2::NewLibraryYearsController < V2::ApplicationController
  before_action :set_year, only: %i[show edit update destroy]

  def index
    records = NewLibrary::Year.accessible_by(current_ability)

    if params[:query].present?
      records = records.where("name ilike :query", query: "%#{params[:query]}%")
    end

    if params[:curriculum_id].present?
      records = records.where(curriculum_id: params[:curriculum_id])
    end
    records = records.order(weight: :asc)
    if (params[:unpaginated] == "true")
      render json: { records: records }
    else
      meta, records = paginate(records)
      render json: { records: records, meta: meta }
    end
  end

  def show
    render json: @year.data_for_show(current_user)
  end

  private

  def set_year
    @year = NewLibrary::Year.find(params[:id])
  end
end
