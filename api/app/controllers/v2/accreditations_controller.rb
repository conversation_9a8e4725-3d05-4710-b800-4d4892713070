class V2::AccreditationsController < V2::ApplicationController

  def index
    accreds = Accreditation.accessible_by(current_ability).order(weight: :asc)

    if params[:query].present?
      accreds = accreds.where("name ILIKE ?", "%#{params[:query]}%")
    end

    meta, records = paginate(accreds)

    render json: { records: records, meta: meta }
  end

  def unpaginated_index
    accreds = Accreditation.accessible_by(current_ability).order(weight: :asc)
    render json: { records: accreds }
  end

  def show
    accred = Accreditation.accessible_by(current_ability).find(params[:id])

    authorize! :read, accred

    render json:  accred
  end

  def create
    accred = Accreditation.new(accred_params)

    last_weight =  Accreditation.maximum(:weight) || 0

    accred.weight = last_weight + 1

    authorize! :create, accred

    if accred.save
      render json: { success: true, record: accred }
    else
      render json: { success: false, errors: accred.errors.full_messages }
    end
  end

  def update
    accred = Accreditation.accessible_by(current_ability).find(params[:id])

    authorize! :update, accred

    if accred.update(accred_params)
      render json: { success: true, record: accred }
    else
      render json: { success: false, errors: accred.errors.full_messages }
    end
  end

  def destroy
    accred = Accreditation.accessible_by(current_ability).find(params[:id])
    authorize! :destroy,  accred
    destroy_record  accred
  end

  def update_weights
    data = params[:weight_data]
    data.each { |obj|
      Accreditation.accessible_by(current_ability).find(obj["id"]).update!(weight: obj["weight"])
    }
    render json: { success: true, errors: [] }
  end

  private

  def accred_params
    params.permit(:name, :weight, :fileboy_image_id)
  end
end
