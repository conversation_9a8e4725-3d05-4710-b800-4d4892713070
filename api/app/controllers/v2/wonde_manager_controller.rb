require 'uri'

class V2::WondeManagerController < V2::ApplicationController
  def authorise
    params = {
      client_id: ENV["WONDE_CLIENT_ID"],
      redirect_uri: ENV["WONDE_REDIRECT_URI"],
      response_type: 'code',
    }
    redirect_to "https://edu.wonde.com/oauth/authorize?#{URI.encode_www_form(params)}"
    return
  end

  def request_access
    school = School.find(params[:id])
    authorize :manage, school
    WondeManager.request_access school
    render json: { requested: true }
  end

  def dev_set_school
    return unless Rails.env.development?
    id = params.require(:id)
    school = School.find(id)

    existing = School.find_by(wonde_id: "A1930499544")
    if existing
      existing.update(wonde_id: nil) if existing
      existing.teachers.where.not(wonde_id: nil).update_all(wonde_id: nil) if existing
      existing.pupils.where.not(wonde_id: nil).update_all(wonde_id: nil) if existing
      existing.forms.where.not(wonde_id: nil).update_all(wonde_id: nil) if existing
    end

    school.update(wonde_id: "A1930499544", wonde_request_status: "active")
    render json: school.errors.messages
  end

  def webhook_authorised_school
    if params["payload_type"] == "schoolApproved"
      id = params["school_id"]
      school = School.find_by(wonde_id: id)
      WondeManager.get_wonde_school school
    end
    render status: 200
  end
end
