class V2::FormsController < V2::ApplicationController

  def with_pupils
    forms = Form.accessible_by(current_ability).includes(:users).where({ users: { id: params[:user_id] } })
    render json: forms.map { |form| form.as_json.merge({ pupils: form.pupils }) }
  end

  def show
    form = Form.accessible_by(current_ability).find(params[:id])
    authorize! :read, form
    render json: form
  end

  def index
    forms = Form.accessible_by(current_ability).includes(:users)
    if (params[:userId].present?)
      forms = forms.where({ users: { id: params[:userId] } })
    end
    if (params[:schoolId].present?)
      forms = forms.where({ school_id: params[:schoolId] })
    end
    if (params[:query].present?)
      forms = forms.where("forms.name ILIKE ?", "%#{params[:query]}%")
    end
    meta, records = paginate(forms)
    render json: { records: records, meta: meta }
  end


  def removeTeacher
    form = Form.find(params[:id])
    teacher = Teacher.find(params[:teacherId])

    authorize! :update, teacher
    authorize! :update, form

    form.teacher_ids -= [teacher.id]

    render json: { saved: true }

  rescue => e
    render json: { saved: false, errors: [e.message]  }
  end

  def addTeacher
    form = Form.find(params[:id])
    teacher = Teacher.find(params[:teacherId])

    authorize! :update, teacher
    authorize! :update, form

    if form.teacher_ids.include? teacher.id
      render json: { saved: false, errors: ["Teacher already in class"] }
      return
    end

    form.teacher_ids += [teacher.id]

    render json: { saved: true }

  rescue => e
    render json: { saved: false, errors: [e.message]  }
  end

  def removePupil
    form = Form.find(params[:id])
    pupil = Pupil.find(params[:pupilId])

    authorize! :update, pupil
    authorize! :update, form

    form.pupil_ids -= [pupil.id]

    render json: { saved: true }

  rescue => e
    render json: { saved: false, errors: [e.message]  }
  end

  def addPupil
    form = Form.find(params[:id])
    pupil = Pupil.find(params[:pupilId])

    authorize! :update, pupil
    authorize! :update, form

    form.pupil_ids += [pupil.id]

    render json: { saved: true }

  rescue => e
    render json: { saved: false, errors: [e.message]  }
  end

  def remove_all_lessons
    form = Form.find(params[:id])
    form.lessons.destroy_all
    render json: { saved: true }
  rescue => e
    render json: { saved: false, errors: [e.message]  }
  end
end
