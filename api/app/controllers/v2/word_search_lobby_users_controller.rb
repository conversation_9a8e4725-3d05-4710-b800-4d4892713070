class V2::WordSearchLobbyUsersController < V2::ApplicationController
  def increment_score
    lobby_user = WordSearchLobbyUser.find(params[:id])
    lobby_user.score += 1

    if lobby_user.score >= lobby_user.words.length
      lobby_user.finishedAt = Time.zone.now
      UserRankEvent.add_points(lobby_user.user, lobby_user.score * 2, "blast_words")

      # rebuild the cache on complete so it is up to date for the user complete screen
      lobby_user.word_search_lobby.delete_cache
      lobby_user.word_search_lobby.generate_cache
    end

    if (lobby_user.save)
      render json: lobby_user
    else
      # Some error handling
    end
  end
end
