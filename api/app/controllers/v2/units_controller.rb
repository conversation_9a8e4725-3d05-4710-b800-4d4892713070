class V2::UnitsController < V2::ApplicationController
  def index
    units = NewLibrary::Unit.accessible_by(current_ability)
    units = units.order(weight: :asc, name: :asc)

    if (params[:organisationId].present?)
      units = units.joins(:organisations).where(organisations: { id: params[:organisationId] })
    end

    if params[:ids].present?
      units = units.where(id: params[:ids].split(","))
    end

    if (params[:curriculum_id].present?)
      units = units.left_joins(year: :curriculum).where({ new_library_curricula: { id: params[:curriculum_id] } })
    end

    if (params[:year_id].present?)
      units = units.where(year_id: params[:year_id])
    end

    if (params[:query].present?)
      units = units.where("new_library_units.name ilike ?", "%#{params[:query]}%")
    end

    meta, records = paginate(units)
    render json: { records: records, meta: meta }
  end

  def show
    unit_json = custom_cache.fetch("unit/#{params[:id]}/json", expires_in: 12.hours) do
      unit = NewLibrary::Unit.find(params[:id])
      unit.as_json
    end

    render json: unit_json
  end
end
