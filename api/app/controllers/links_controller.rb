class LinksController < ApplicationController
  api_version 1

  def view_external_link
    completion_params = params.permit(:lesson_id, :url, :link_type, :id)
    if completion_params[:lesson_id].present?
      @pupil = Pupil.find(completion_params[:id])
      lesson = Lesson::Lesson.find(completion_params[:lesson_id])
      TrackingService.track_link_view(@pupil, lesson.template, {
        url: completion_params[:url],
        link_type: completion_params[:link_type].to_sym,
        lesson_id: lesson.id,
      })
    end

    redirect_to completion_params[:url] and return
  end

end
