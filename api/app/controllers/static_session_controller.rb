class StaticSessionController < WebApplicationController
  before_action :set_current_user
  include <PERSON><PERSON><PERSON>roll<PERSON>

  def get_sign_in_token
    user = User.find(params[:id])
    authorize! :manage, user
    tokens = {
      target_token: user.generate_sign_in_token,
      return_token: current_user.generate_sign_in_token,
    }
    render json: tokens
  end

  def swap_accounts
    token = params[:token]
    return_token = params[:return_token]
    session[:return_token] = return_token if return_token

    if sign_out
      if sign_in_with_token(token)
        return redirect_to '/accounts/dashboard'
      elsif return_token && sign_in_with_token(return_token)
        return redirect_to request.referer || root_path
      else
        return redirect_to root_path
      end
    end
    redirect_to '/accounts/swapping'
  end

  def set_current_user
    @current_user = current_user
  end

  def hubspot_visitor_verification
    return render json: {} unless @current_user.present?
    token = HubspotManager.verify_visitor(@current_user)
    render json: { token: token, email: @current_user.email, id: @current_user.id, type: @current_user.class.name }
  rescue HubspotApiError => error
    Rails.logger.error("Hubspot API Error: #{error.message}\n#{error.backtrace.join("\n")}")
    render json: { error: { message: error.message } }
  rescue => error
    Rails.logger.error("Unexpected error: #{error.message}")
    render json: { error: { message: "Unknown error" } }
  end


  private

  def sign_in_with_token token
    user = User.find_by(sign_in_token: token)
    if user
      user.update!(sign_in_token: nil)
      sign_in(user, skip_authentication: true)
    end
  end
end
