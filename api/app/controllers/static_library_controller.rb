class StaticLibraryController < WebApplicationController
  include BreadcrumbsHelper

  layout :dynamic_layout
  before_action :set_user
  before_action :set_base_breadcrumbs

  YEAR_UNTAGGED_CATEGORY = "Other Years".freeze

  def curriculum_index
    @curricula = NewLibrary::Curriculum.accessible_by(current_ability)
                                       .joins(lesson_templates: :available_countries)
                                       .group(:id)
                                       .where(
                                         country: current_user&.country || Country.find(1),
                                         lesson_templates: { available: true },
                                         countries: { id: current_user&.country&.id || 1 },
                                       )
                                       .order(name: :asc)
  end

  def curriculum_show
    @curriculum = find_resource(NewLibrary::Curriculum, redirect_path: '/unit-library')
    return unless @curriculum

    @all_tags = NewLibrary::Year.all_tags
    @years = @curriculum.years.order(weight: :asc)
    @years = @years.where(subject_id: params[:subject_id]) if params[:subject_id].present?
    if params[:tag].present?
      @years = @years.where("tags @> ?", [params[:tag]].to_json)
    end

    grouped_data = @years.each_with_object({}) do |year, hash|
      if year.tags.present?
        year.tags.each do |tag|
          hash[tag] ||= []
          hash[tag] << year
        end
      else
        hash[YEAR_UNTAGGED_CATEGORY] ||= []
        hash[YEAR_UNTAGGED_CATEGORY] << year
      end
    end

    if params[:tag].present?
      filtered_groups = grouped_data.slice(params[:tag])
    else
      filtered_groups = grouped_data
    end

    @grouped_years = filtered_groups.sort_by do |tag, _years|
      (tag == YEAR_UNTAGGED_CATEGORY) ? [1, tag] : [0, tag]
    end.to_h

    add_breadcrumb @curriculum.name, unit_library_curriculum_path(@curriculum)
  end

  def years_show
    @curriculum = find_resource(NewLibrary::Curriculum, param_key: :curriculum_id, redirect_path: '/unit-library')
    return unless @curriculum

    @year = find_resource(NewLibrary::Year, redirect_path: '/unit-library')

    if !@year
      redirect_to unit_library_path, alert: 'Year not found.'
      return
    end

    add_breadcrumb @curriculum.name, unit_library_curriculum_path(@curriculum)
    add_breadcrumb @year.name, unit_library_years_path(@curriculum, @year)
  end

  def units_show
    @unit = NewLibrary::Unit.eager_load(:year).accessible_by(current_ability).find_by(id: params[:id])
    if !@unit
      redirect_to unit_library_path, alert: 'Unit not found.'
    else
      TrackingService.track_unit_view(@unit, current_user) if @unit

      year = @unit.year
      curriculum = year&.curriculum

      add_breadcrumb curriculum.name, unit_library_curriculum_path(curriculum) if curriculum
      add_breadcrumb year.name, unit_library_years_path(curriculum, year) if year && curriculum
      add_breadcrumb @unit.name
    end 
  end

  private

  def dynamic_layout
    if current_user&.type == 'Teacher'
      'school'
    else
      'static'
    end
  end

  def set_user
    @current_user = current_user
  end

  def set_base_breadcrumbs
    add_root_breadcrumb @current_user, @current_pupil
    add_breadcrumb 'Unit Library', unit_library_path
  end
end
