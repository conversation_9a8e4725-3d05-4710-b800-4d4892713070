class Pupil::QuizAttemptsController < PupilController
  def create
    data = params.permit(:lesson_id, :score, :lesson_template_id, :time, answers_json: {})
    lesson = Lesson::Lesson.find(data[:lesson_id])

    TrackingService.track_rocket_word_quiz_completion(current_user, lesson.template, {
      score: data[:score],
      time_taken: data[:time],
      lesson_id: lesson.id,
      answers_json: data[:answers_json]
    })

    save_with_response QuizOld::Attempt.new, attempt_params
  end

  private

  def attempt_params
    params.permit(:score, :lesson_template_id, :time).merge(user_id: current_user.id)
  end
end
