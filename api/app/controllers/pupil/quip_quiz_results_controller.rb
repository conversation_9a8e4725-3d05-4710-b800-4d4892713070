class Pupil::QuipQuizResultsController < PupilController
  def submit_results
    data = params.permit(:score, :total, :lesson_id, :lesson_template_id, :time_seconds, results_json: {})
    data["pupil"] = current_user
    quiz_result = QuipQuizResult.create({
      result_type: :quip_quiz,
      score: data[:score],
      total: data[:total],
      lesson_template_id: data[:lesson_template_id],
      time_seconds: data[:time_seconds],
      results_json: data[:results_json],
      pupil: current_user
    })

    lesson = Lesson::Lesson.find(data[:lesson_id])
    TrackingService.track_quip_quiz_completion(current_user, lesson.template, {
      quiz_id: lesson.template.quip_quiz&.id,
      time_taken: data[:time_seconds],
      answers_json: data[:results_json],
      score: data[:score],
      total_score: data[:total],
      lesson_id: lesson.id,
    })
    UserRankEvent.add_points(current_user, data[:score].to_i * 2, "quip_quiz")
    render json: quiz_result
  end
end
