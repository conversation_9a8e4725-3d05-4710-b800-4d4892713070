class Pupil::LessonsController < PupilController
  def index
    @pupil = current_user

    available_lesson_ids = @pupil.unlocked_lesson_lesson_ids

    if (params[:sort] === "time" && (params[:sortBy] === "DESC" || params[:sortBy] === "ASC"))
      lessons = @pupil.lessons.joins(:template).order("time #{params[:sortBy]}")
    else
      lessons = @pupil.lessons.joins(:template).order('lesson_templates.name ASC')
    end

    if params[:query].present?
      lessons = lessons.where('lesson_templates.name ILIKE ?', "%#{params[:query]}%")
    end

    pagy, records = pagy(lessons, page: params[:page], items: params[:perPage])

    done_ids = Lesson::Lesson.done.where(id: records.map(&:id)).ids

    lessons_objects = records.pluck_to_hash(
      :id,
      :time,
      template_id: 'lesson_templates.id',
      name: 'lesson_templates.name',
      fileboy_image_id: 'lesson_templates.fileboy_image_id',
    ).map do |obj|
      obj[:available] = available_lesson_ids.include?(obj[:id])
      obj[:done] = done_ids.include?(obj[:id])
      obj
    end

    render json: @pupil.as_json.merge({ lessons: lessons_objects, meta: pagy })
  end
end
