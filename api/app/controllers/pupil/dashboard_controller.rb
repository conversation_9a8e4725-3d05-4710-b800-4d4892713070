# frozen_string_literal: true
class Pupil::Dash<PERSON>Controller < PupilController

  def show
    @pupil = current_user

    available_lessons = Lesson::Lesson
      .where(id: @pupil.unlocked_lesson_lesson_ids)
      .joins(:template)
      .joins(:form)
      .order(time: :desc)

    pagy, records = pagy(available_lessons, page: params[:page], items: params[:perPage])

    quiz_attempt_template_ids = @pupil.quiz_attempts.pluck(:lesson_template_id)
    quip_quiz_result_template_ids = @pupil.quip_quiz_results.pluck(:lesson_template_id)
    word_search_results_template_ids = @pupil.word_search_results.pluck(:lesson_template_id)
    completed_presentation_template_ids = @pupil.presentation_progresses.where(completed: true).pluck(:lesson_template_id)
    documents = Lesson::Document.where(template_id: records.pluck(:template_id)).where(for_pupil: true)

    available_lessons_objects = records.pluck_to_hash(
      :id,
      :time,
      template_id: 'lesson_templates.id',
      name: 'lesson_templates.name',
      form_name: 'forms.name',
      fileboy_image_id: 'lesson_templates.fileboy_image_id',
      quip_quiz_key: 'lesson_templates.quip_quiz_key',
      film_link: 'lesson_templates.film_link',
      film_jw_id: 'lesson_templates.film_jw_id',
      employer_link: 'lesson_templates.employer_link',
      employer_jw_id: 'lesson_templates.employer_jw_id',
      post_16_link: 'lesson_templates.post_16_link',
      post_16_jw_id: 'lesson_templates.post_16_jw_id',
    ).map do |obj|
      obj[:has_rocket_word_quiz_attempt] = quiz_attempt_template_ids.include?(obj[:template_id])
      obj[:has_quip_quiz_attempt] = quip_quiz_result_template_ids.include?(obj[:template_id])
      obj[:has_word_search_attempt] = word_search_results_template_ids.include?(obj[:template_id])
      obj[:has_completed_presentation] = completed_presentation_template_ids.include?(obj[:template_id])
      obj[:documents] = documents.where(template_id: obj[:template_id])
      # TODO progress copied from api/app/controllers/v2/lessons_controller.rb
      # can prolly delete the previous code for has completed when old area deleted
      obj[:progress] = {
        presentation_viewed: !!current_user.tracking_presentation_views.find_by(lesson_id: obj[:id]),
        rocket_word_quiz: !!current_user.tracking_rocket_words.find_by(lesson_id: obj[:id]),
        summative_quiz: !!current_user.tracking_summative_quizzes.find_by(lesson_id: obj[:id]),
        tracking_word_search: !!current_user.tracking_word_searches.find_by(lesson_id: obj[:id]),
      }
      obj
    end

    render json: @pupil.as_json.merge({ available_lessons: available_lessons_objects, meta: pagy })
  end
end
