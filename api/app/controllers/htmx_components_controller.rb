class HtmxComponentsController < StaticApplicationController
  layout false

  def glossary_search_block; end

  def footer; end

  def school_sidebar; end

  def session_header; end

  def dev_toolbar; end

  def de_social_panel
    @social_posts = DeSocial.joins(:de_media)
                            .where(published: true)
                            .group('de_socials.id')
                            .order(created_at: :desc)
                            .limit(3)
                            .select('de_socials.*, ARRAY_AGG(de_media.fileboy_image_id) AS fileboy_image_ids')
  end
end
