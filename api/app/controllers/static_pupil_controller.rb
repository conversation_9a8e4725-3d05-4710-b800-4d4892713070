class StaticPupilController < WebApplicationController
  layout 'pupil'
  before_action :authenticate!
  before_action :set_current_pupil
  before_action :set_pupil_options
  before_action :set_pupil_rankings
  before_action :set_current_user
  include WardenController

  private

  def authenticate!
    is_teacher_or_pupils = current_user.present? && (current_user&.teacher? || current_user&.pupil?)

    redirect_to root_path unless is_teacher_or_pupils
  end

  def set_current_pupil
    if current_user.present? && current_user.pupil?
      @current_pupil = current_user
    end

    if current_user.present? && current_user.teacher?
      pupil_id = session[:pupil_id]
      allowed_pupils = current_user.pupils
      @current_pupil = allowed_pupils.find_by(id: pupil_id)
    end

    if @current_pupil.nil?
      redirect_to "/pupil" unless request.path == "/pupil" || request.path == "/pupil/set_pupil"
    end
  end

  def set_pupil_options
    if current_user.present? && current_user.teacher?
      @pupil_options = current_user.pupils.order(name: :asc)
    end
  end

  def set_pupil_rankings
    if @current_pupil.present?
      global_rank = UserRankEvent.user_rank(@current_pupil.id, Time.now)
      school_rank = UserRankEvent.user_rank(@current_pupil.id, Time.now, @current_pupil.school.pupil_ids)
      
      # Safely handle the case where a pupil might not belong to any form
      form = @current_pupil.forms.first
      class_rank = form.present? ? UserRankEvent.user_rank(@current_pupil.id, Time.now, form.pupil_ids) : { rank: nil }
  
      @pupil_rankings = {
        points: global_rank[:score],
        class_rank: class_rank[:rank],
        school_rank: school_rank[:rank],
        global_rank: global_rank[:rank]
      }
    end
  end

  def set_current_user
    @current_user = current_user
  end
end
