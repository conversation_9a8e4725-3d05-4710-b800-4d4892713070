class Guardian::<PERSON>on<PERSON><PERSON>platesController < GuardianController
  before_action :set_lesson_template, only: %i[show load_rocket_words_quiz]

  private

  def set_lesson_template
    @lesson_template = current_user.accessible_lesson_templates.find(params[:id])
  end

  def params_with_image
    params_image = params['image']
    params_sponsor_logo = params['sponsor_logo']
    update_params = new_library_lesson_template_params
    update_params['image'] = params_image unless params_image.is_a? String
    update_params['sponsor_logo'] = params_sponsor_logo unless params_sponsor_logo.is_a? String
    update_params['plan_learning_outcomes_attributes'] = update_params.delete('plan_learning_outcomes')
    update_params
  end
end
