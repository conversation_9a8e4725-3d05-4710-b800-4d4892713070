module CampaignDemographicsFilter
  extend ActiveSupport::Concern

  def campaign_demographics_filter(collection)
    collection = collection.left_joins(user: :school)
    if params[:gender].present?
      collection = collection.where("users.gender = ?", params[:gender])
    end
    if User.ethnicities[params[:ethnicity]]
      collection = collection.where("users.ethnicity = ?", User.ethnicities[params[:ethnicity]])
    end
    if params[:countryId].present?
      collection = collection.where("schools.country_id = ?", params[:countryId])
    end
    if  School.regions[params[:region]]
      collection = collection.where("schools.region = ?",  School.regions[params[:region]])
    end
    if School.categories[params[:category]]
      collection = collection.where("schools.category = ?", School.categories[params[:category]])
    end

    return collection.select("#{collection.table_name}.*, users.gender as gender")
  end
end
