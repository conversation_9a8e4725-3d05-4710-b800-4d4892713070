module WardenController
  extend ActiveSupport::Concern

  included do
    before_action :verify_current_token
  end

  def set_auth_headers(headers)
    response.set_header('access-token', headers['access-token'])
    response.set_header('client', headers['client'])
    response.set_header('expiry', headers['expiry'])
    response.set_header('provider', headers['provider'])
    response.set_header('uid', headers['uid'])
  end

  def verify_current_token
    set_auth_headers(request.env['warden'].authenticate(:token) ? request.headers : {})
  end

  def current_user
    request.env['warden'].user || User.find_by(id: cookies.encrypted[:user_id])
  end

  def sign_in(user, password = nil, skip_authentication: false)
    return unless user
    session = user.new_session(password, skip_authentication: skip_authentication)
    return unless session

    set_auth_headers(session)
    cookies.encrypted[:user_id] = { value: user.id, expires: 2.weeks.from_now, httponly: true }
    Rails.logger.debug "Encrypted user_id cookie: #{cookies.encrypted[:user_id]}"
    user.after_sign_in

    request.env['warden'].set_user(user)

    track_device(user)

    session
  end

  def sign_out
    remove_device
    cookies.delete(:user_id)
    return false unless current_user
    current_user.close_session(request.headers['client'])
    set_auth_headers({})
    request.env['warden'].set_user(nil)
    request.env['warden'].logout
    true
  end

  private

  def track_device(user)
    device_token = cookies[:device_token] || SecureRandom.hex(20)

    # Set the device_token cookie
    cookies[:device_token] = { value: device_token, expires: 1.year.from_now }

    bypass_emails = ['<EMAIL>', '<EMAIL>']

    bypass_login_limit = Rails.env.development? || bypass_emails.include?(user.email.downcase)

    # Remove the oldest device if the limit is exceeded
    if user.device_logins.count >= 3 && !bypass_login_limit
      devices_to_delete = user.device_logins.where.not(device_token: device_token)
      if devices_to_delete.exists?
        oldest_device = devices_to_delete.order(:last_logged_in_at).first
        oldest_device.destroy
      end
    end

    # Find or initialize the device_login
    device = user.device_logins.find_or_initialize_by(device_token: device_token)
    device.assign_attributes(
      user_agent: request.user_agent,
      ip_address: request.remote_ip,
      last_logged_in_at: Time.current
    )
    device.save!

    Rails.logger.debug "Device tracked: #{device_token} (#{request.user_agent})"
  end

  def remove_device
    device_token = cookies[:device_token]
    return unless device_token && current_user

    current_user.device_logins.where(device_token: device_token).destroy_all
    cookies.delete(:device_token)
  end
end
