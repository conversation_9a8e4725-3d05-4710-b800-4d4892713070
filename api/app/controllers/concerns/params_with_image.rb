module ParamsWithImage
  extend ActiveSupport::Concern

    def params_with_image processed_params, image_param
      update_params = processed_params
      update_params["image"] = image_param unless image_param.is_a? String
      update_params
    end

    def params_with_form_data processed_params, raw_params, form_data_key
      update_params = processed_params
      update_params[form_data_key] = raw_params[form_data_key] unless raw_params[form_data_key].is_a? String
      update_params
    end

end
