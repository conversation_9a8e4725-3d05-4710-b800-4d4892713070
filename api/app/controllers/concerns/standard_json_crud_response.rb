module StandardJsonCrudResponse
  extend ActiveSupport::Concern

  def save_with_response record, params = nil
    record.assign_attributes(params) if params
    if record.save
      render json: { saved: true, record: record }
    else
      render json: { saved: false, errors: record.errors.full_messages }
    end
  end

  def list_with_response(collection, params, opts = {})
    allowed_scopes = opts[:allowed_scopes]
    search_params = opts[:search_params]
    serialized = opts[:serialized]
    limit = opts[:limit]

    # Allowed options
    school_id = params[:school_id]
    resource_type = params[:resource_type]
    resource_id = params[:resource_id]

    sort = opts[:default_sort] || { created_at: :desc }

    processed_collection = collection

    if params[:query] && params[:query].present? && search_params
      processed_collection = processed_collection.where(
        search_params.map { |str| "#{str} ILIKE :query" }.join(" OR "),
        query: "%#{params[:query]}%",
      )
    end

    if params[:scope] && allowed_scopes && allowed_scopes.include?(params[:scope])
      processed_collection = processed_collection.send(params[:scope])
    end

    if school_id
      processed_collection = processed_collection.where(school_id: school_id)
    end

    if resource_type
      processed_collection = processed_collection.where(resource_type: resource_type)
    end

    if resource_id
      processed_collection = processed_collection.where(resource_id: resource_id)
    end

    processed_collection = processed_collection.order(sort)

    if params[:skip_pagination]
      records = serialized ? processed_collection.serialized_for_index_request : processed_collection

      if limit && records.respond_to?("limit")
        records = records.limit(limit)
      end

      render json: { records: records, meta: {} }
    else
      pagy, records = pagy(processed_collection)
      records = records.serialized_for_index_request if serialized

      render json: { records: records, meta: pagy }
    end
  end

  def destroy_record(record)
    begin
      result = record.destroy!
      render json: { success:true }, status: 200
    rescue ActiveRecord::InvalidForeignKey => e
      render json: {
        error: "ForeignKeyConstraint",
        details: "Cannot delete due to foreign key constraint"
      }, status: 500
    rescue => e
      render json: {
        error: e
      }, status: 500
    end
  end

  def mass_destroy_records(records)
    records.transaction { records.each(&:destroy!) }
    render json: { success: true }, status: 200
  rescue ActiveRecord::InvalidForeignKey => e
    render(
      json: {
        error: "ForeignKeyConstraint",
        details: "Cannot delete due to foreign key constraint"
      },
      status: 500
    )
  rescue => e
    render json: { error: e }, status: 500
  end
end
