class SitemapsController < ActionController::Base

  def show
    @static_urls = [
      [root_url, 'daily', 1.0],
      [about_url, 'monthly', 0.8],
      [enquiry_url, 'monthly', 0.8],
      [privacy_url, 'yearly', 0.6],
      [terms_url, 'yearly', 0.6],
      [gdpr_url, 'yearly', 0.6],
      [cookies_url, 'yearly', 0.6],
      [childrens_code_url, 'yearly', 0.6],
      [subjects_science_url, 'monthly', 0.7],
      [subjects_geography_url, 'monthly', 0.7],
      [lessons_ai_url, 'monthly', 0.7],
      [careers_ai_url, 'monthly', 0.7],
      [teacher_and_schools_url, 'monthly', 0.7],
      [product_url, 'monthly', 0.7],
      [reporting_url, 'monthly', 0.7],
      [research_url, 'monthly', 0.7],
      [plans_url, 'monthly', 0.7],
      [careers_info_url, 'monthly', 0.7],
      [affiliates_url, 'monthly', 0.7],
      [unit_library_url, 'monthly', 0.7],
      [glossary_index_url, 'monthly', 0.7],
      [sponsors_url, 'monthly', 0.7],
      [articles_url, 'monthly', 0.7],
      [events_url, 'monthly', 0.7],
      [landing_pages_index_url, 'weekly', 0.8]
    ]

    @curriculums = NewLibrary::Curriculum.all
    @years = NewLibrary::Year.includes(:curriculum).all
    @units = NewLibrary::Unit.all
    @lessons = Lesson::Template.platform.available
    @glossary_letters = ("A".."Z").to_a
    @glossary_terms = Glossary.all
    @sponsors = Organisation.where(is_sponsor: true)
    @articles = Article.published
    @events = Event.published
    @landing_pages = LandingPage.published.order(:updated_at)
    @videos = Video.searchable.order(:updated_at)

    respond_to do |format|
      format.xml
    end
  end
end