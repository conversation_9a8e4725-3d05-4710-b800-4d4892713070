# app/controllers/email_preview_controller.rb
class EmailPreviewController < WebApplicationController
  # Only allow in development environment
  before_action :ensure_development_environment
  before_action :load_preview_classes
  
  def index
    @preview_methods = []
    
    # Get methods from all preview classes
    @preview_classes.each do |klass|
      @preview_methods += klass.instance_methods(false).map do |method|
        {
          name: method,
          class_name: klass.name,
          display_name: "#{klass.name.gsub('Preview', '')}: #{method.to_s.titleize}"
        }
      end
    end
    
    # Sort methods by display name
    @preview_methods.sort_by! { |m| m[:display_name] }
  end
  
  def show
    @preview_method = params[:method]
    @preview_class_name = params[:class_name] || 'SubscriptionMailerPreview'
    
    # Find the preview class
    @preview_class = @preview_classes.find { |klass| klass.name == @preview_class_name }
    
    if @preview_class.nil?
      flash[:error] = "Preview class not found: #{@preview_class_name}"
      redirect_to email_preview_index_path
      return
    end
    
    # Get the preview class instance
    preview = @preview_class.new
    
    # Call the preview method to get the mail object
    @email = preview.send(@preview_method)
    
    # Render the email inline
    render :show
  end
  
  private
  
  def ensure_development_environment
    unless Rails.env.development?
      render plain: "Email previews are only available in the development environment", status: :forbidden
    end
  end
  
  def load_preview_classes
    # Set the preview classes
    @preview_classes = []
    
    # Try to load SubscriptionMailerPreview
    begin
      @preview_classes << SubscriptionMailerPreview
    rescue NameError => e
      Rails.logger.error "Failed to load SubscriptionMailerPreview: #{e.message}"
    end
    
    # Try to load AdminMailerPreview
    begin
      @preview_classes << AdminMailerPreview
    rescue NameError => e
      Rails.logger.error "Failed to load AdminMailerPreview: #{e.message}"
    end
    
    # If no preview classes were loaded, create a dummy class
    if @preview_classes.empty?
      dummy_class = Class.new do
        def self.instance_methods(include_super = true)
          []
        end
        
        def self.name
          "DummyPreview"
        end
      end
      
      @preview_classes << dummy_class
      flash[:error] = "Unable to load email previews. Make sure the preview classes exist at app/previews/subscription_mailer_preview.rb and app/previews/admin_mailer_preview.rb"
    end
  end
end