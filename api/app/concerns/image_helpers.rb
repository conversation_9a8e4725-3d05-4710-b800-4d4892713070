module ImageHelpers
  extend ActiveSupport::Concern

  def self.fileboy_cdn
    'https://www.developingexperts.com/file-cdn'
  end

  def self.transform_params(params = {})
    param_string = ''
    params.each do |key, value|
      next if value.blank?
      param_string += "#{key}:#{value};"
    end
    return "?transform=#{param_string}" if param_string.present?
    ''
  end

  def self.image_url(fileboy_image_id, transform = {})
    return nil unless fileboy_image_id.present?
    "#{fileboy_cdn}/images/get/#{fileboy_image_id}#{transform_params(transform)}"
  end

  def self.thumbnail_url(fileboy_image_id, transform = { format: 'webp', resize: '300x_', quality: 75 })
    return nil unless fileboy_image_id.present?
    image_url(fileboy_image_id, transform)
  end

  def self.video_thumbnail(source, external_id, transform = { format: 'webp', resize: '300x_', quality: 75 })
    return nil unless source.present? && external_id.present?
    case source
    when 'youtube'
      "https://img.youtube.com/vi/#{external_id}/maxresdefault.jpg"
    when 'fileboy'
      "#{fileboy_cdn}/videos/#{external_id}/poster#{transform_params(transform)}"
    when 'vimeo'
      "https://vumbnail.com/#{external_id}.jpg"
    end
  end
end
