module VideoUrlHelpers
  extend ActiveSupport::Concern

  # Regex to extract the 11-character YouTube video ID from a variety of URL formats
  YOUTUBE_REGEX = %r{
    (?:https?:\/\/)?                # Optional protocol
    (?:www\.)?                      # Optional www
    (?:youtube\.com|youtu\.be)\/    # Domain
    (?:watch\?v=|embed\/|shorts\/|v\/)?  # Possible prefixes
    ([\w-]{11})                     # 11-character video ID
  }x

  # Regex to extract the final numeric Vimeo video ID from different URL formats
  VIMEO_REGEX = %r{
    (?:https?:\/\/)?                  # Optional protocol
    (?:www\.)?                        # Optional www
    (?:player\.)?vimeo\.com\/         # Domain
    (?:.*?/)?                         # Optional intermediate segments
    (\d+)(?:\/)?$                     # Final number at end of path
  }x

  # Constants for embed URL base paths
  YOUTUBE_EMBED_URL = 'https://www.youtube.com/embed'.freeze
  VIMEO_EMBED_URL = 'https://player.vimeo.com/video'.freeze
  FILEBOY_VIDEO_PATH = 'https://fileboy.io/video/watch/'.freeze
  DE_VIDEO_PATH = 'https://www.developingexperts.com/video/'.freeze

  # Constants for external URL base paths
  YOUTUBE_EXTERNAL_URL = 'https://www.youtube.com/watch?v='.freeze
  VIMEO_EXTERNAL_URL = 'https://vimeo.com/'.freeze

  # Instance method to match a YouTube video URL and extract the video ID
  def youtube_url_check(url = '')
    url = url.to_s
    YOUTUBE_REGEX.match(url)
  end

  # Class method version of youtube_url_check
  def self.youtube_url_check(url = '')
    url = url.to_s
    YOUTUBE_REGEX.match(url)
  end

  # Converts a YouTube URL into an embed URL if it matches
  # Returns original URL if not matched
  def yt_convert_to_embed_url(url)
    match = youtube_url_check(url)
    return "#{YOUTUBE_EMBED_URL}/#{match[1]}" if match && match[1].present?
    url
  end

  # Instance method to match a Vimeo video URL and extract the video ID
  def vimeo_url_check(url = '')
    VIMEO_REGEX.match(url.to_s)
  end

  # Class method version of vimeo_url_check
  def self.vimeo_url_check(url = '')
    VIMEO_REGEX.match(url.to_s)
  end

  # Converts a Vimeo URL into an embed URL if it matches
  # Returns original URL if not matched
  def vimeo_convert_to_embed_url(url)
    match = vimeo_url_check(url)
    return "#{VIMEO_EMBED_URL}/#{match[1]}" if match && match[1].present?
    url
  end

  # Instance method to match a Fileboy video URL and extract the video ID
  def fileboy_url_check(url = '')
    return nil unless url.present?
    ['', url.split('/')[-1]] if url.include?(FILEBOY_VIDEO_PATH)
  end

  # Class method version of fileboy_url_check
  def self.fileboy_url_check(url = '')
    return nil unless url.present?
    ['', url.split('/')[-1]] if url.include?(FILEBOY_VIDEO_PATH)
  end

  # Instance method to match a Fileboy video URL and extract the video ID
  def de_url_check(url = '')
    return nil unless url.present?
    ['', url.split('/')[-1]] if url.include?(DE_VIDEO_PATH)
  end

  # Instance method to match a Fileboy video URL and extract the video ID
  def self.de_url_check(url = '')
    return nil unless url.present?
    ['', url.split('/')[-1]] if url.include?(DE_VIDEO_PATH)
  end


  # Converts a Fileboy URL into an embed URL if it matches
  # Returns original URL if not matched
  def fileboy_convert_to_embed_url(url)
    match = fileboy_url_check(url)
    return "#{FILEBOY_VIDEO_PATH}/#{match[1]}" if match && match[1].present?
    url
  end

  # Given a video type and ID, returns the appropriate embed URL
  # Returns nil if type or id is blank
  def self.embed_url_for(type, id)
    return nil if type.blank? || id.blank?
    case type
    when 'youtube'
      "#{YOUTUBE_EMBED_URL}/#{id}"
    when 'vimeo'
      "#{VIMEO_EMBED_URL}/#{id}"
    end
  end

  # Given a video type and ID, returns the appropriate external URL
  # Returns nil if type or id is blank
  def self.external_url_for(type, id)
    return nil if type.blank? || id.blank?
    case type
    when 'youtube'
      "#{YOUTUBE_EXTERNAL_URL}#{id}"
    when 'vimeo'
      "#{VIMEO_EXTERNAL_URL}#{id}"
    end
  end

  # Detects the video platform type and ID from a URL
  # Returns ['youtube', id] or ['vimeo', id], or [nil, nil] if not matched
  def self.video_url_type(video_url)
    youtube_match = VideoUrlHelpers.youtube_url_check(video_url)
    return ['youtube', youtube_match[1]] if youtube_match.present?

    vimeo_match = VideoUrlHelpers.vimeo_url_check(video_url)
    return ['vimeo', vimeo_match[1]] if vimeo_match.present?

    fileboy_match = VideoUrlHelpers.fileboy_url_check(video_url)
    return ['fileboy', fileboy_match[1]] if fileboy_match.present?

    de_match = VideoUrlHelpers.de_url_check(video_url)
    return ['de', de_match[1]] if de_match.present?

    [nil, nil]
  end

  # Returns the full embed URL and type (e.g., ['https://...', 'youtube'])
  # Returns [nil, nil] if not recognized
  def self.video_embed_url(video_url)
    type, id = Video.video_url_type(video_url)
    return [embed_url_for(type, id), type] if type.present? && id.present?
    [nil, nil]
  end

  # Returns the full external URL and type (e.g., ['https://...', 'youtube'])
  # Returns [nil, nil] if not recognized
  def self.video_external_url(video_url)
    type, id = Video.video_url_type(video_url)
    return [external_url_for(type, id), type] if type.present? && id.present?
    [nil, nil]
  end

  def self.de_video_url(id)
    return nil unless id.present?
    "https://www.developingexperts.com/video/#{id}"
  end

  def self.fileboy_video_url(id)
    return nil unless id.present?
    "https://fileboy.io/video/watch/2fce0711-7ec8-47b1-b628-f4cdb1db9103/#{id}"
  end
end
